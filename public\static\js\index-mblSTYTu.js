var k=(w,a,s)=>new Promise((d,c)=>{var y=p=>{try{v(s.next(p))}catch(P){c(P)}},_=p=>{try{v(s.throw(p))}catch(P){c(P)}},v=p=>p.done?d(p.value):Promise.resolve(p.value).then(y,_);v((s=s.apply(w,a)).next())});import{g as Q,a8 as r,u as j,q as G,B as re,ai as ae,a7 as ne,r as b,fL as le,e as R,fM as ie,o as de,j as ue,fN as ce,m as M,n as T,D as g,k as e,E as o,a4 as me,G as h,N as l,Q as H,X as pe,a6 as fe,P as q,M as C,ap as we,I as ge,J as _e,H as ve,L as ye,aC as he,fQ as Pe,ax as V,i as xe,ad as be,_ as Ce}from"./index-ZVLuktk4.js";import{u as F}from"./hooks-CuzZ-_om.js";import{a as Ve,b as Ee,d as Re}from"./dark-C3BJic5q.js";import{b as Te,i as qe,a as Ie,T as Le}from"./index-BnPdKjjT.js";import{u as Ne,g as Se}from"./useTranslation-Bgn2GAKO.js";import De from"./AuthNavigation-DpjgmbE5.js";import{d as A}from"./lock-fill-BZPnsoM7.js";import"./useLayout-B65ZqU-V.js";import"./epTheme-DzIV6EF-.js";const ke=(w,a,s)=>{if(!a||a.trim()===""){s(new Error(r("OTP required")));return}if(!/^\d+$/.test(a)){s(new Error(r("OTP must be numbers only")));return}if(a.length!==6){s(new Error(r("OTP must be {count} digits",{count:6})));return}s()},Fe=(w,a,s)=>{var E;if(!a||a.trim()===""){s(new Error(r("Password is required")));return}const c=((E=j().settings)==null?void 0:E.security)||{},y=c.minPasswordLength||6,_=c.requireNumbers||!1,v=c.requireSymbols||!1,p=c.requireUppercase||!1,P=c.requireLowercase||!1;if(a.length<y){s(new Error(r("Password too short (min {count})",{count:y})));return}if(_&&!/\d/.test(a)){s(new Error(r("Password needs numbers")));return}if(v&&!/[!@#$%^&*(),.?":{}|<>]/.test(a)){s(new Error(r("Password needs symbols")));return}if(p&&!/[A-Z]/.test(a)){s(new Error(r("Password needs uppercase")));return}if(P&&!/[a-z]/.test(a)){s(new Error(r("Password needs lowercase")));return}s()},Oe=w=>(a,s,d)=>{if(!s||s.trim()===""){d(new Error(r("Confirm password required")));return}if(w.password&&w.password!==s){d(new Error(r("Passwords don't match")));return}d()},Ue=w=>Q({otp:[{validator:ke,trigger:"blur"}],password:[{validator:Fe,trigger:"blur"}],confirmPassword:[{validator:Oe(w),trigger:"blur"}]}),x=G({name:"Motion",props:{delay:{type:Number,default:50}},render(){const{delay:w}=this,a=ne("motion");return re(ae("div",{},{default:()=>{var s,d;return[(d=(s=this.$slots).default)==null?void 0:d.call(s)]}}),[[a,{initial:{opacity:0,y:100},enter:{opacity:1,y:0,transition:{delay:w}}}]])}}),ze={class:"select-none"},Be=["src"],$e={class:"flex-c absolute right-5 top-3"},Me={class:"login-container"},He={class:"img"},Ae={class:"login-box"},Qe={class:"login-form"},je={class:"outline-none"},Ge={class:"text-center mb-4 text-gray-500"},Je={class:"text-center mt-4"},We={class:"text-gray-500 text-sm"},Xe=G({name:"ResetPassword",__name:"index",setup(w){const a=xe(),s=ue(),d=b(!1),c=b(!1),y=b(),_=b(0),v=b(null),{dataTheme:p,dataThemeChange:P}=Ve(),E=Ne(),O=le();j();const J=R(()=>d.value||c.value),I=b(!1),W=R(()=>{var n;return(n=O.languages)==null?void 0:n.map(t=>({locale:t.code,native:t.nativeName}))}),U=R(()=>O.locale||ie.global.locale.value),i=Q({email:"",otp:"",password:"",confirmPassword:""}),L=R(()=>_.value===0&&!c.value),X=(n,t)=>({color:n===t?"#409eff":""}),Z=(n,t)=>n===t?"!font-medium":"",K=n=>{E.switchLanguage(n)},N=()=>{I.value=!0};de(()=>{s.query.email&&(i.email=s.query.email),z()});const z=()=>{_.value=60,v.value=setInterval(()=>{_.value--,_.value<=0&&(clearInterval(v.value),v.value=null)},1e3)},B=n=>k(null,null,function*(){var t,u;if(n)try{I.value=!0,yield n.validate(),d.value=!0;const f=yield Pe({email:i.email,code:i.otp,password:i.password,passwordConfirmation:i.confirmPassword});if(!f.success){V(f.message,{type:"error"});return}V(f.message,{type:"success"}),yield a.push("/login")}catch(f){V(((u=(t=f==null?void 0:f.response)==null?void 0:t.data)==null?void 0:u.message)||(f==null?void 0:f.message)||r("Some information is incorrect. Please review and try again."),{type:"error"})}finally{d.value=!1}}),Y=we(n=>B(n),1e3,!0),ee=()=>k(null,null,function*(){var n,t;if(!(!L.value||!i.email))try{c.value=!0;const u=yield be().forgotPassword({email:i.email});if(!u.success){V(u.message,{type:"error"});return}V(u.message,{type:"success"}),z()}catch(u){V(((t=(n=u==null?void 0:u.response)==null?void 0:n.data)==null?void 0:t.message)||(u==null?void 0:u.message),{type:"error"})}finally{c.value=!1}});return ce(document,"keydown",({code:n})=>{["Enter","NumpadEnter"].includes(n)&&!d.value&&Y(y.value)}),(n,t)=>{const u=h("el-switch"),f=h("el-dropdown-item"),se=h("el-dropdown-menu"),oe=h("el-dropdown"),S=h("el-input"),D=h("el-form-item"),$=h("el-button"),te=h("el-form");return T(),M("div",ze,[g("img",{src:e(Te),class:"wave",alt:"Bg"},null,8,Be),g("div",$e,[o(u,{modelValue:e(p),"onUpdate:modelValue":t[0]||(t[0]=m=>me(p)?p.value=m:null),"inline-prompt":"","active-icon":e(Re),"inactive-icon":e(Ee),onChange:e(P)},null,8,["modelValue","active-icon","inactive-icon","onChange"]),o(oe,{trigger:"click"},{dropdown:l(()=>[o(se,{class:"translation"},{default:l(()=>[(T(!0),M(ge,null,_e(W.value,m=>(T(),H(f,{key:m.locale,style:ve(X(U.value,m.locale)),class:ye(["dark:!text-white",Z(U.value,m.locale)]),onClick:Ze=>K(m.locale)},{default:l(()=>[q(C(m.native),1)]),_:2},1032,["style","class","onClick"]))),128))]),_:1})]),default:l(()=>[o(e(Se),{class:"hover:text-primary hover:!bg-[transparent] w-[20px] h-[20px] ml-1.5 cursor-pointer outline-none duration-300"})]),_:1})]),g("div",Me,[g("div",He,[(T(),H(pe(fe(e(qe)))))]),g("div",Ae,[g("div",Qe,[o(e(Ie),{class:"avatar"}),o(e(x),null,{default:l(()=>[g("h2",je,[o(e(Le),{options:{strings:[e(r)("Reset Password")],cursor:!1,speed:100}},null,8,["options"])])]),_:1}),g("div",Ge,[q(C(e(r)("Enter the OTP code sent to"))+" ",1),g("strong",null,C(i.email),1)]),o(te,{ref_key:"ruleFormRef",ref:y,model:i,rules:I.value?e(Ue)(i):{},size:"large"},{default:l(()=>[o(e(x),{delay:100},{default:l(()=>[o(D,{prop:"otp"},{default:l(()=>[o(S,{modelValue:i.otp,"onUpdate:modelValue":t[1]||(t[1]=m=>i.otp=m),class:"text-center",maxlength:"6",placeholder:e(r)("OTP Code"),"prefix-icon":e(F)("ri:shield-keyhole-line"),onFocus:N},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),o(e(x),{delay:150},{default:l(()=>[o(D,{prop:"password"},{default:l(()=>[o(S,{modelValue:i.password,"onUpdate:modelValue":t[2]||(t[2]=m=>i.password=m),type:"password","show-password":"",placeholder:e(r)("New Password"),"prefix-icon":e(F)(e(A)),onFocus:N},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),o(e(x),{delay:200},{default:l(()=>[o(D,{prop:"confirmPassword"},{default:l(()=>[o(S,{modelValue:i.confirmPassword,"onUpdate:modelValue":t[3]||(t[3]=m=>i.confirmPassword=m),type:"password","show-password":"",placeholder:e(r)("Confirm New Password"),"prefix-icon":e(F)(e(A)),onFocus:N},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),o(e(x),{delay:250},{default:l(()=>[o($,{class:"w-full !uppercase",size:"large",type:"danger",round:"",loading:d.value,disabled:J.value,onClick:t[4]||(t[4]=m=>B(y.value))},{default:l(()=>[o(e(he),{icon:"ri:lock-password-line",width:"20",class:"mr-2"}),q(" "+C(e(r)("Reset Password")),1)]),_:1},8,["loading","disabled"])]),_:1}),o(e(x),{delay:300},{default:l(()=>[g("div",Je,[g("span",We,C(e(r)("Didn't receive the code?")),1),o($,{link:"",type:"primary",disabled:!L.value,loading:c.value,class:"ml-1",onClick:ee},{default:l(()=>[q(C(L.value?e(r)("Resend OTP"):e(r)("Resend in {seconds}s",{seconds:_.value})),1)]),_:1},8,["disabled","loading"])])]),_:1}),o(e(x),{delay:350},{default:l(()=>[o(De,{"show-login":!0,"show-register":!1,"show-forgot-password":!1})]),_:1})]),_:1},8,["model","rules"])])])])])}}}),is=Ce(Xe,[["__scopeId","data-v-c7ab5283"]]);export{is as default};
