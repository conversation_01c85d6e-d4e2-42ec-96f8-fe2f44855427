# ProcMS Widget CSS & Theme System

A comprehensive CSS framework and theme system for the ProcMS Chatbot Widget with complete isolation from host websites.

## 🎨 Features

- **CSS Isolation** - Prevents conflicts with host website styles
- **Theme System** - Light, dark, and auto themes with custom theme support
- **CSS Variables** - Dynamic theming with CSS custom properties
- **Component-based** - Modular SCSS architecture
- **Utility Classes** - Atomic CSS utilities for rapid development
- **Responsive Design** - Mobile-first responsive design
- **Accessibility** - WCAG compliant with high contrast and reduced motion support
- **Performance** - Optimized for minimal bundle size and fast rendering

## 📁 File Structure

```
src/widget/styles/
├── variables.scss      # CSS variables and design tokens
├── base.scss          # Base styles and CSS reset
├── components.scss    # Component-specific styles
├── utilities.scss     # Utility classes
├── widget.scss        # Main stylesheet (imports all)
├── theme-manager.ts   # JavaScript theme management
└── README.md         # This documentation
```

## 🚀 Quick Start

### Basic Usage

```scss
// Import the complete widget stylesheet
@import '../styles/widget.scss';
```

```typescript
// Use theme manager for dynamic theming
import { ThemeManager } from '../styles/theme-manager';

const themeManager = new ThemeManager(containerElement);
themeManager.setTheme('dark');
```

### HTML Structure

```html
<div class="procms-chatbot-widget" data-procms-theme="light">
  <div class="procms-widget-header">
    <div class="procms-widget-header__content">
      <img class="procms-widget-header__avatar" src="..." alt="Bot">
      <div class="procms-widget-header__info">
        <h3 class="procms-widget-header__title">Bot Name</h3>
        <span class="procms-widget-header__status">Online</span>
      </div>
    </div>
  </div>
  
  <div class="procms-widget-body">
    <div class="procms-messages-container">
      <div class="procms-message procms-message--bot">
        <img class="procms-message__avatar" src="..." alt="Bot">
        <div class="procms-message__content">
          <div class="procms-message__text">Hello! How can I help you?</div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="procms-widget-footer">
    <div class="procms-input-area">
      <input class="procms-input-area__input" placeholder="Type a message...">
      <button class="procms-input-area__send">Send</button>
    </div>
  </div>
</div>
```

## 🎨 Theme System

### Built-in Themes

#### Light Theme (Default)
```css
[data-procms-theme="light"] {
  --procms-widget-bg: #f9fafb;
  --procms-widget-surface: #ffffff;
  --procms-text-primary: #111827;
  /* ... */
}
```

#### Dark Theme
```css
[data-procms-theme="dark"] {
  --procms-widget-bg: #1f2937;
  --procms-widget-surface: #374151;
  --procms-text-primary: #f3f4f6;
  /* ... */
}
```

#### Auto Theme (System Preference)
```css
[data-procms-theme="auto"] {
  /* Follows system preference */
}

@media (prefers-color-scheme: dark) {
  [data-procms-theme="auto"] {
    /* Dark theme variables */
  }
}
```

### Custom Themes

#### Using Theme Manager
```typescript
import { ThemeManager } from '../styles/theme-manager';

const themeManager = new ThemeManager(containerElement);

// Apply custom theme
themeManager.setCustomTheme({
  primaryColor: '#ff6b6b',
  backgroundColor: '#f8f9fa',
  textPrimary: '#2d3436',
  borderRadius: '12px',
  fontFamily: 'Inter, sans-serif'
});
```

#### Using CSS Variables
```css
.procms-chatbot-widget {
  --procms-primary: #ff6b6b;
  --procms-primary-hover: #ff5252;
  --procms-widget-bg: #f8f9fa;
  --procms-text-primary: #2d3436;
  --procms-widget-border-radius: 12px;
  --procms-font-family: 'Inter', sans-serif;
}
```

#### Bot Configuration Theme
```typescript
// Theme applied from bot configuration
const botTheme = {
  primaryColor: '#007bff',
  backgroundColor: '#ffffff',
  textColor: '#333333',
  borderRadius: '8px',
  fontFamily: 'Roboto, sans-serif',
  customVariables: {
    '--custom-accent': '#28a745',
    '--custom-spacing': '16px'
  }
};

themeManager.applyBotTheme(botTheme);
```

## 🎯 CSS Variables Reference

### Color Variables
```css
/* Primary Colors */
--procms-primary: #3b82f6;
--procms-primary-hover: #2563eb;
--procms-primary-active: #1d4ed8;
--procms-primary-light: #eff6ff;

/* Background Colors */
--procms-widget-bg: #f9fafb;
--procms-widget-surface: #ffffff;
--procms-widget-border: #e5e7eb;

/* Text Colors */
--procms-text-primary: #111827;
--procms-text-secondary: #6b7280;
--procms-text-muted: #9ca3af;
--procms-text-inverse: #ffffff;

/* Message Colors */
--procms-user-message-bg: var(--procms-primary);
--procms-user-message-text: var(--procms-text-inverse);
--procms-bot-message-bg: var(--procms-widget-surface);
--procms-bot-message-text: var(--procms-text-primary);
```

### Typography Variables
```css
--procms-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
--procms-font-size-xs: 0.75rem;
--procms-font-size-sm: 0.875rem;
--procms-font-size-base: 1rem;
--procms-font-size-lg: 1.125rem;
--procms-font-weight-normal: 400;
--procms-font-weight-medium: 500;
--procms-font-weight-semibold: 600;
--procms-font-weight-bold: 700;
```

### Spacing Variables
```css
--procms-space-1: 0.25rem;  /* 4px */
--procms-space-2: 0.5rem;   /* 8px */
--procms-space-3: 0.75rem;  /* 12px */
--procms-space-4: 1rem;     /* 16px */
--procms-space-5: 1.25rem;  /* 20px */
--procms-space-6: 1.5rem;   /* 24px */
```

### Layout Variables
```css
--procms-widget-width: 400px;
--procms-widget-height: 600px;
--procms-widget-border-radius: 8px;
--procms-header-height: 60px;
--procms-input-height: 44px;
--procms-avatar-size: 32px;
```

## 🧩 Component Classes

### Widget Container
```css
.procms-chatbot-widget          /* Main container */
.procms-chatbot-widget.procms-widget-floating  /* Floating widget */
.procms-chatbot-widget.procms-widget-embedded  /* Embedded widget */
```

### Header
```css
.procms-widget-header           /* Header container */
.procms-widget-header__content  /* Header content area */
.procms-widget-header__avatar   /* Bot avatar */
.procms-widget-header__info     /* Bot info container */
.procms-widget-header__title    /* Bot name */
.procms-widget-header__status   /* Bot status */
.procms-widget-header__actions  /* Header actions */
.procms-widget-header__action   /* Individual action button */
```

### Messages
```css
.procms-messages-container      /* Messages scroll container */
.procms-message                 /* Message wrapper */
.procms-message--user           /* User message modifier */
.procms-message--bot            /* Bot message modifier */
.procms-message--system         /* System message modifier */
.procms-message__avatar         /* Message avatar */
.procms-message__content        /* Message content */
.procms-message__text           /* Message text */
.procms-message__meta           /* Message metadata */
.procms-message__time           /* Message timestamp */
```

### Input Area
```css
.procms-widget-footer           /* Footer container */
.procms-input-area              /* Input area container */
.procms-input-area__container   /* Input container */
.procms-input-area__input       /* Text input */
.procms-input-area__send        /* Send button */
.procms-input-area__attachment  /* Attachment button */
```

### Typing Indicator
```css
.procms-typing-indicator        /* Typing indicator container */
.procms-typing-indicator__avatar /* Typing avatar */
.procms-typing-indicator__content /* Typing content */
.procms-typing-indicator__dots  /* Typing dots */
```

### Starter Messages
```css
.procms-starter-messages        /* Starter messages container */
.procms-starter-messages__title /* Starter messages title */
.procms-starter-messages__button /* Starter message button */
```

## 🛠️ Utility Classes

### Spacing
```css
.procms-p-0, .procms-p-1, .procms-p-2, .procms-p-3, .procms-p-4
.procms-m-0, .procms-m-1, .procms-m-2, .procms-m-3, .procms-m-4
.procms-px-*, .procms-py-*, .procms-mx-*, .procms-my-*
```

### Display
```css
.procms-block, .procms-inline-block, .procms-flex, .procms-hidden
.procms-flex-col, .procms-flex-row, .procms-items-center, .procms-justify-between
```

### Typography
```css
.procms-text-xs, .procms-text-sm, .procms-text-base, .procms-text-lg
.procms-font-normal, .procms-font-medium, .procms-font-semibold, .procms-font-bold
.procms-text-left, .procms-text-center, .procms-text-right
```

### Colors
```css
.procms-text-primary, .procms-text-secondary, .procms-text-muted
.procms-bg-primary, .procms-bg-surface, .procms-bg-transparent
```

### Borders & Radius
```css
.procms-border, .procms-border-t, .procms-border-b
.procms-rounded, .procms-rounded-lg, .procms-rounded-full
```

## 📱 Responsive Design

### Breakpoints
```css
/* Mobile First Approach */
@media (max-width: 480px)  /* Small mobile */
@media (max-width: 768px)  /* Mobile/tablet */
@media (max-width: 1024px) /* Tablet */
```

### Responsive Utilities
```css
.procms-sm\:hidden     /* Hidden on small screens */
.procms-md\:block      /* Block on medium screens */
.procms-lg\:flex       /* Flex on large screens */
```

### Mobile Optimizations
- Larger touch targets (48px minimum)
- Optimized font sizes
- Adjusted spacing
- Full-screen on small devices

## ♿ Accessibility

### High Contrast Support
```css
@media (prefers-contrast: high) {
  .procms-chatbot-widget {
    --procms-widget-border: currentColor;
    --procms-text-primary: ButtonText;
    /* Enhanced contrast colors */
  }
}
```

### Reduced Motion Support
```css
@media (prefers-reduced-motion: reduce) {
  .procms-chatbot-widget * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }
}
```

### Focus Management
```css
.procms-chatbot-widget:focus-within {
  outline: 2px solid var(--procms-primary);
  outline-offset: 2px;
}
```

### Screen Reader Support
```css
.procms-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
```

## 🎭 Animations

### Built-in Animations
```css
/* Fade transitions */
.procms-fade-enter-active, .procms-fade-leave-active
.procms-fade-enter-from, .procms-fade-leave-to

/* Slide transitions */
.procms-slide-up-enter-active, .procms-slide-up-leave-active
.procms-slide-up-enter-from, .procms-slide-up-leave-to

/* Bounce animations */
.procms-bounce-enter-active, .procms-bounce-leave-active

/* Typing indicator */
@keyframes procms-typing-bounce
@keyframes procms-spin
```

## 🔧 Customization Examples

### Custom Brand Colors
```css
.procms-chatbot-widget {
  --procms-primary: #ff6b6b;
  --procms-primary-hover: #ff5252;
  --procms-primary-light: rgba(255, 107, 107, 0.1);
}
```

### Custom Typography
```css
.procms-chatbot-widget {
  --procms-font-family: 'Inter', 'Roboto', sans-serif;
  --procms-font-size-base: 15px;
  --procms-line-height-normal: 1.6;
}
```

### Custom Spacing
```css
.procms-chatbot-widget {
  --procms-space-4: 20px;
  --procms-message-spacing: 20px;
  --procms-widget-border-radius: 16px;
}
```

### Custom Shadows
```css
.procms-chatbot-widget {
  --procms-widget-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  --procms-widget-shadow-lg: 0 16px 64px rgba(0, 0, 0, 0.16);
}
```

## 🚀 Performance Tips

1. **CSS Containment** - Uses `contain: layout style paint` for performance
2. **Hardware Acceleration** - Uses `transform: translateZ(0)` for GPU acceleration
3. **Efficient Selectors** - Uses BEM methodology for optimal CSS performance
4. **Minimal Repaints** - Optimized animations and transitions
5. **Tree Shaking** - Only includes used styles in production builds

## 🐛 Troubleshooting

### Common Issues

#### Styles Not Applied
```css
/* Ensure proper CSS isolation */
.procms-chatbot-widget {
  all: initial; /* Reset inherited styles */
  /* Your styles here */
}
```

#### Theme Not Switching
```typescript
// Ensure theme manager is properly initialized
const themeManager = new ThemeManager(containerElement);
themeManager.setTheme('dark');
```

#### CSS Conflicts
```css
/* Use higher specificity if needed */
.procms-chatbot-widget.procms-chatbot-widget {
  /* Your styles with higher specificity */
}
```

## 📚 Best Practices

1. **Use CSS Variables** - For dynamic theming
2. **Follow BEM** - For maintainable CSS
3. **Mobile First** - Design for mobile, enhance for desktop
4. **Accessibility First** - Consider all users
5. **Performance** - Optimize for fast loading and rendering
6. **Isolation** - Prevent conflicts with host styles

## 🔄 Migration Guide

### From v1.0 to v2.0
```css
/* Old */
.widget-header { /* ... */ }

/* New */
.procms-widget-header { /* ... */ }
```

### Theme Variables
```css
/* Old */
--primary-color: #007bff;

/* New */
--procms-primary: #007bff;
```

## 📝 License

MIT License - see LICENSE file for details.
