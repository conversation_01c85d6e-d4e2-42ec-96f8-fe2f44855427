<?php

namespace Modules\ChatBot\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WidgetBotConfigResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'uuid' => $this->uuid,
            'name' => $this->name,
            'description' => $this->description,
            'logoUrl' => $this->logo_url,
            'greetingMessage' => $this->greeting_message ?: "Xin chào! Tôi là {$this->name}. Tôi có thể giúp gì cho bạn?",
            'starterMessages' => $this->starter_messages ?: [],
            'status' => $this->status->value,
            'theme' => [
                'primaryColor' => $this->theme['primary_color'] ?? '#007bff',
                'backgroundColor' => $this->theme['background_color'] ?? '#ffffff',
                'textColor' => $this->theme['text_color'] ?? '#333333',
                'borderRadius' => $this->theme['border_radius'] ?? '8px',
                'fontFamily' => $this->theme['font_family'] ?? 'system-ui',
            ],
        ];
    }
}
