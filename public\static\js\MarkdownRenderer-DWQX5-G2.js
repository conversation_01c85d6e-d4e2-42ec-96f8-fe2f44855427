var Gn=Object.defineProperty,Wn=Object.defineProperties;var qn=Object.getOwnPropertyDescriptors;var Qt=Object.getOwnPropertySymbols;var Zn=Object.prototype.hasOwnProperty,Yn=Object.prototype.propertyIsEnumerable;var st=(t,e,n)=>e in t?Gn(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,U=(t,e)=>{for(var n in e||(e={}))Zn.call(e,n)&&st(t,n,e[n]);if(Qt)for(var n of Qt(e))Yn.call(e,n)&&st(t,n,e[n]);return t},ue=(t,e)=>Wn(t,qn(e));var _=(t,e,n)=>st(t,typeof e!="symbol"?e+"":e,n);import{_ as jn,m as Xn,n as Qn}from"./index-ZVLuktk4.js";function mt(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var ne=mt();function hn(t){ne=t}var Ae={exec:()=>null};function b(t,e=""){let n=typeof t=="string"?t:t.source,r={replace:(s,l)=>{let c=typeof l=="string"?l:l.source;return c=c.replace(P.caret,"$1"),n=n.replace(s,c),r},getRegex:()=>new RegExp(n,e)};return r}var P={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:t=>new RegExp(`^( {0,3}${t})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:t=>new RegExp(`^ {0,${Math.min(3,t-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:t=>new RegExp(`^ {0,${Math.min(3,t-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:t=>new RegExp(`^ {0,${Math.min(3,t-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:t=>new RegExp(`^ {0,${Math.min(3,t-1)}}#`),htmlBeginRegex:t=>new RegExp(`^ {0,${Math.min(3,t-1)}}<(?:[a-z].*>|!--)`,"i")},Vn=/^(?:[ \t]*(?:\n|$))+/,Kn=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,Jn=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,Ee=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,er=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,kt=/(?:[*+-]|\d{1,9}[.)])/,fn=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,gn=b(fn).replace(/bull/g,kt).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),tr=b(fn).replace(/bull/g,kt).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),xt=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,nr=/^[^\n]+/,bt=/(?!\s*\])(?:\\.|[^\[\]\\])+/,rr=b(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",bt).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),sr=b(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,kt).getRegex(),He="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Tt=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,ir=b("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",Tt).replace("tag",He).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),dn=b(xt).replace("hr",Ee).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",He).getRegex(),lr=b(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",dn).getRegex(),_t={blockquote:lr,code:Kn,def:rr,fences:Jn,heading:er,hr:Ee,html:ir,lheading:gn,list:sr,newline:Vn,paragraph:dn,table:Ae,text:nr},Vt=b("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Ee).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",He).getRegex(),ar=ue(U({},_t),{lheading:tr,table:Vt,paragraph:b(xt).replace("hr",Ee).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Vt).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",He).getRegex()}),or=ue(U({},_t),{html:b(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Tt).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Ae,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:b(xt).replace("hr",Ee).replace("heading",` *#{1,6} *[^
]`).replace("lheading",gn).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()}),cr=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,ur=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,mn=/^( {2,}|\\)\n(?!\s*$)/,pr=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,Ge=/[\p{P}\p{S}]/u,wt=/[\s\p{P}\p{S}]/u,kn=/[^\s\p{P}\p{S}]/u,hr=b(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,wt).getRegex(),xn=/(?!~)[\p{P}\p{S}]/u,fr=/(?!~)[\s\p{P}\p{S}]/u,gr=/(?:[^\s\p{P}\p{S}]|~)/u,dr=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,bn=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,mr=b(bn,"u").replace(/punct/g,Ge).getRegex(),kr=b(bn,"u").replace(/punct/g,xn).getRegex(),Tn="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",xr=b(Tn,"gu").replace(/notPunctSpace/g,kn).replace(/punctSpace/g,wt).replace(/punct/g,Ge).getRegex(),br=b(Tn,"gu").replace(/notPunctSpace/g,gr).replace(/punctSpace/g,fr).replace(/punct/g,xn).getRegex(),Tr=b("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,kn).replace(/punctSpace/g,wt).replace(/punct/g,Ge).getRegex(),_r=b(/\\(punct)/,"gu").replace(/punct/g,Ge).getRegex(),wr=b(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Ar=b(Tt).replace("(?:-->|$)","-->").getRegex(),Er=b("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Ar).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Ue=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Sr=b(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",Ue).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),_n=b(/^!?\[(label)\]\[(ref)\]/).replace("label",Ue).replace("ref",bt).getRegex(),wn=b(/^!?\[(ref)\](?:\[\])?/).replace("ref",bt).getRegex(),yr=b("reflink|nolink(?!\\()","g").replace("reflink",_n).replace("nolink",wn).getRegex(),At={_backpedal:Ae,anyPunctuation:_r,autolink:wr,blockSkip:dr,br:mn,code:ur,del:Ae,emStrongLDelim:mr,emStrongRDelimAst:xr,emStrongRDelimUnd:Tr,escape:cr,link:Sr,nolink:wn,punctuation:hr,reflink:_n,reflinkSearch:yr,tag:Er,text:pr,url:Ae},Rr=ue(U({},At),{link:b(/^!?\[(label)\]\((.*?)\)/).replace("label",Ue).getRegex(),reflink:b(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Ue).getRegex()}),pt=ue(U({},At),{emStrongRDelimAst:br,emStrongLDelim:kr,url:b(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/}),Lr=ue(U({},pt),{br:b(mn).replace("{2,}","*").getRegex(),text:b(pt.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()}),Me={normal:_t,gfm:ar,pedantic:or},me={normal:At,gfm:pt,breaks:Lr,pedantic:Rr},Ir={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Kt=t=>Ir[t];function G(t,e){if(e){if(P.escapeTest.test(t))return t.replace(P.escapeReplace,Kt)}else if(P.escapeTestNoEncode.test(t))return t.replace(P.escapeReplaceNoEncode,Kt);return t}function Jt(t){try{t=encodeURI(t).replace(P.percentDecode,"%")}catch(e){return null}return t}function en(t,e){var l;let n=t.replace(P.findPipe,(c,a,u)=>{let o=!1,p=a;for(;--p>=0&&u[p]==="\\";)o=!o;return o?"|":" |"}),r=n.split(P.splitPipe),s=0;if(r[0].trim()||r.shift(),r.length>0&&!((l=r.at(-1))!=null&&l.trim())&&r.pop(),e)if(r.length>e)r.splice(e);else for(;r.length<e;)r.push("");for(;s<r.length;s++)r[s]=r[s].trim().replace(P.slashPipe,"|");return r}function ke(t,e,n){let r=t.length;if(r===0)return"";let s=0;for(;s<r&&t.charAt(r-s-1)===e;)s++;return t.slice(0,r-s)}function Or(t,e){if(t.indexOf(e[1])===-1)return-1;let n=0;for(let r=0;r<t.length;r++)if(t[r]==="\\")r++;else if(t[r]===e[0])n++;else if(t[r]===e[1]&&(n--,n<0))return r;return n>0?-2:-1}function tn(t,e,n,r,s){let l=e.href,c=e.title||null,a=t[1].replace(s.other.outputLinkReplace,"$1");r.state.inLink=!0;let u={type:t[0].charAt(0)==="!"?"image":"link",raw:n,href:l,title:c,text:a,tokens:r.inlineTokens(a)};return r.state.inLink=!1,u}function Cr(t,e,n){let r=t.match(n.other.indentCodeCompensation);if(r===null)return e;let s=r[1];return e.split(`
`).map(l=>{let c=l.match(n.other.beginningSpace);if(c===null)return l;let[a]=c;return a.length>=s.length?l.slice(s.length):l}).join(`
`)}var Fe=class{constructor(t){_(this,"options");_(this,"rules");_(this,"lexer");this.options=t||ne}space(t){let e=this.rules.block.newline.exec(t);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(t){let e=this.rules.block.code.exec(t);if(e){let n=e[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?n:ke(n,`
`)}}}fences(t){let e=this.rules.block.fences.exec(t);if(e){let n=e[0],r=Cr(n,e[3]||"",this.rules);return{type:"code",raw:n,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:r}}}heading(t){let e=this.rules.block.heading.exec(t);if(e){let n=e[2].trim();if(this.rules.other.endingHash.test(n)){let r=ke(n,"#");(this.options.pedantic||!r||this.rules.other.endingSpaceChar.test(r))&&(n=r.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(t){let e=this.rules.block.hr.exec(t);if(e)return{type:"hr",raw:ke(e[0],`
`)}}blockquote(t){let e=this.rules.block.blockquote.exec(t);if(e){let n=ke(e[0],`
`).split(`
`),r="",s="",l=[];for(;n.length>0;){let c=!1,a=[],u;for(u=0;u<n.length;u++)if(this.rules.other.blockquoteStart.test(n[u]))a.push(n[u]),c=!0;else if(!c)a.push(n[u]);else break;n=n.slice(u);let o=a.join(`
`),p=o.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");r=r?`${r}
${o}`:o,s=s?`${s}
${p}`:p;let x=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(p,l,!0),this.lexer.state.top=x,n.length===0)break;let g=l.at(-1);if((g==null?void 0:g.type)==="code")break;if((g==null?void 0:g.type)==="blockquote"){let A=g,k=A.raw+`
`+n.join(`
`),O=this.blockquote(k);l[l.length-1]=O,r=r.substring(0,r.length-A.raw.length)+O.raw,s=s.substring(0,s.length-A.text.length)+O.text;break}else if((g==null?void 0:g.type)==="list"){let A=g,k=A.raw+`
`+n.join(`
`),O=this.list(k);l[l.length-1]=O,r=r.substring(0,r.length-g.raw.length)+O.raw,s=s.substring(0,s.length-A.raw.length)+O.raw,n=k.substring(l.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:r,tokens:l,text:s}}}list(t){let e=this.rules.block.list.exec(t);if(e){let n=e[1].trim(),r=n.length>1,s={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");let l=this.rules.other.listItemRegex(n),c=!1;for(;t;){let u=!1,o="",p="";if(!(e=l.exec(t))||this.rules.block.hr.test(t))break;o=e[0],t=t.substring(o.length);let x=e[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,pe=>" ".repeat(3*pe.length)),g=t.split(`
`,1)[0],A=!x.trim(),k=0;if(this.options.pedantic?(k=2,p=x.trimStart()):A?k=e[1].length+1:(k=e[2].search(this.rules.other.nonSpaceChar),k=k>4?1:k,p=x.slice(k),k+=e[1].length),A&&this.rules.other.blankLine.test(g)&&(o+=g+`
`,t=t.substring(g.length+1),u=!0),!u){let pe=this.rules.other.nextBulletRegex(k),ye=this.rules.other.hrRegex(k),Q=this.rules.other.fencesBeginRegex(k),y=this.rules.other.headingBeginRegex(k),V=this.rules.other.htmlBeginRegex(k);for(;t;){let K=t.split(`
`,1)[0],J;if(g=K,this.options.pedantic?(g=g.replace(this.rules.other.listReplaceNesting,"  "),J=g):J=g.replace(this.rules.other.tabCharGlobal,"    "),Q.test(g)||y.test(g)||V.test(g)||pe.test(g)||ye.test(g))break;if(J.search(this.rules.other.nonSpaceChar)>=k||!g.trim())p+=`
`+J.slice(k);else{if(A||x.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||Q.test(x)||y.test(x)||ye.test(x))break;p+=`
`+g}!A&&!g.trim()&&(A=!0),o+=K+`
`,t=t.substring(K.length+1),x=J.slice(k)}}s.loose||(c?s.loose=!0:this.rules.other.doubleBlankLine.test(o)&&(c=!0));let O=null,Se;this.options.gfm&&(O=this.rules.other.listIsTask.exec(p),O&&(Se=O[0]!=="[ ] ",p=p.replace(this.rules.other.listReplaceTask,""))),s.items.push({type:"list_item",raw:o,task:!!O,checked:Se,loose:!1,text:p,tokens:[]}),s.raw+=o}let a=s.items.at(-1);if(a)a.raw=a.raw.trimEnd(),a.text=a.text.trimEnd();else return;s.raw=s.raw.trimEnd();for(let u=0;u<s.items.length;u++)if(this.lexer.state.top=!1,s.items[u].tokens=this.lexer.blockTokens(s.items[u].text,[]),!s.loose){let o=s.items[u].tokens.filter(x=>x.type==="space"),p=o.length>0&&o.some(x=>this.rules.other.anyLine.test(x.raw));s.loose=p}if(s.loose)for(let u=0;u<s.items.length;u++)s.items[u].loose=!0;return s}}html(t){let e=this.rules.block.html.exec(t);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(t){let e=this.rules.block.def.exec(t);if(e){let n=e[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),r=e[2]?e[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:n,raw:e[0],href:r,title:s}}}table(t){var c;let e=this.rules.block.table.exec(t);if(!e||!this.rules.other.tableDelimiter.test(e[2]))return;let n=en(e[1]),r=e[2].replace(this.rules.other.tableAlignChars,"").split("|"),s=(c=e[3])!=null&&c.trim()?e[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],l={type:"table",raw:e[0],header:[],align:[],rows:[]};if(n.length===r.length){for(let a of r)this.rules.other.tableAlignRight.test(a)?l.align.push("right"):this.rules.other.tableAlignCenter.test(a)?l.align.push("center"):this.rules.other.tableAlignLeft.test(a)?l.align.push("left"):l.align.push(null);for(let a=0;a<n.length;a++)l.header.push({text:n[a],tokens:this.lexer.inline(n[a]),header:!0,align:l.align[a]});for(let a of s)l.rows.push(en(a,l.header.length).map((u,o)=>({text:u,tokens:this.lexer.inline(u),header:!1,align:l.align[o]})));return l}}lheading(t){let e=this.rules.block.lheading.exec(t);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(t){let e=this.rules.block.paragraph.exec(t);if(e){let n=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:n,tokens:this.lexer.inline(n)}}}text(t){let e=this.rules.block.text.exec(t);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(t){let e=this.rules.inline.escape.exec(t);if(e)return{type:"escape",raw:e[0],text:e[1]}}tag(t){let e=this.rules.inline.tag.exec(t);if(e)return!this.lexer.state.inLink&&this.rules.other.startATag.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(t){let e=this.rules.inline.link.exec(t);if(e){let n=e[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(n)){if(!this.rules.other.endAngleBracket.test(n))return;let l=ke(n.slice(0,-1),"\\");if((n.length-l.length)%2===0)return}else{let l=Or(e[2],"()");if(l===-2)return;if(l>-1){let c=(e[0].indexOf("!")===0?5:4)+e[1].length+l;e[2]=e[2].substring(0,l),e[0]=e[0].substring(0,c).trim(),e[3]=""}}let r=e[2],s="";if(this.options.pedantic){let l=this.rules.other.pedanticHrefTitle.exec(r);l&&(r=l[1],s=l[3])}else s=e[3]?e[3].slice(1,-1):"";return r=r.trim(),this.rules.other.startAngleBracket.test(r)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(n)?r=r.slice(1):r=r.slice(1,-1)),tn(e,{href:r&&r.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer,this.rules)}}reflink(t,e){let n;if((n=this.rules.inline.reflink.exec(t))||(n=this.rules.inline.nolink.exec(t))){let r=(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal," "),s=e[r.toLowerCase()];if(!s){let l=n[0].charAt(0);return{type:"text",raw:l,text:l}}return tn(n,s,n[0],this.lexer,this.rules)}}emStrong(t,e,n=""){let r=this.rules.inline.emStrongLDelim.exec(t);if(!(!r||r[3]&&n.match(this.rules.other.unicodeAlphaNumeric))&&(!(r[1]||r[2])||!n||this.rules.inline.punctuation.exec(n))){let s=[...r[0]].length-1,l,c,a=s,u=0,o=r[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(o.lastIndex=0,e=e.slice(-1*t.length+s);(r=o.exec(e))!=null;){if(l=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!l)continue;if(c=[...l].length,r[3]||r[4]){a+=c;continue}else if((r[5]||r[6])&&s%3&&!((s+c)%3)){u+=c;continue}if(a-=c,a>0)continue;c=Math.min(c,c+a+u);let p=[...r[0]][0].length,x=t.slice(0,s+r.index+p+c);if(Math.min(s,c)%2){let A=x.slice(1,-1);return{type:"em",raw:x,text:A,tokens:this.lexer.inlineTokens(A)}}let g=x.slice(2,-2);return{type:"strong",raw:x,text:g,tokens:this.lexer.inlineTokens(g)}}}}codespan(t){let e=this.rules.inline.code.exec(t);if(e){let n=e[2].replace(this.rules.other.newLineCharGlobal," "),r=this.rules.other.nonSpaceChar.test(n),s=this.rules.other.startingSpaceChar.test(n)&&this.rules.other.endingSpaceChar.test(n);return r&&s&&(n=n.substring(1,n.length-1)),{type:"codespan",raw:e[0],text:n}}}br(t){let e=this.rules.inline.br.exec(t);if(e)return{type:"br",raw:e[0]}}del(t){let e=this.rules.inline.del.exec(t);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(t){let e=this.rules.inline.autolink.exec(t);if(e){let n,r;return e[2]==="@"?(n=e[1],r="mailto:"+n):(n=e[1],r=n),{type:"link",raw:e[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}}}url(t){var n,r;let e;if(e=this.rules.inline.url.exec(t)){let s,l;if(e[2]==="@")s=e[0],l="mailto:"+s;else{let c;do c=e[0],e[0]=(r=(n=this.rules.inline._backpedal.exec(e[0]))==null?void 0:n[0])!=null?r:"";while(c!==e[0]);s=e[0],e[1]==="www."?l="http://"+e[0]:l=e[0]}return{type:"link",raw:e[0],text:s,href:l,tokens:[{type:"text",raw:s,text:s}]}}}inlineText(t){let e=this.rules.inline.text.exec(t);if(e){let n=this.lexer.state.inRawBlock;return{type:"text",raw:e[0],text:e[0],escaped:n}}}},j=class ht{constructor(e){_(this,"tokens");_(this,"options");_(this,"state");_(this,"tokenizer");_(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||ne,this.options.tokenizer=this.options.tokenizer||new Fe,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let n={other:P,block:Me.normal,inline:me.normal};this.options.pedantic?(n.block=Me.pedantic,n.inline=me.pedantic):this.options.gfm&&(n.block=Me.gfm,this.options.breaks?n.inline=me.breaks:n.inline=me.gfm),this.tokenizer.rules=n}static get rules(){return{block:Me,inline:me}}static lex(e,n){return new ht(n).lex(e)}static lexInline(e,n){return new ht(n).inlineTokens(e)}lex(e){e=e.replace(P.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let n=0;n<this.inlineQueue.length;n++){let r=this.inlineQueue[n];this.inlineTokens(r.src,r.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,n=[],r=!1){var s,l,c;for(this.options.pedantic&&(e=e.replace(P.tabCharGlobal,"    ").replace(P.spaceLine,""));e;){let a;if((l=(s=this.options.extensions)==null?void 0:s.block)!=null&&l.some(o=>(a=o.call({lexer:this},e,n))?(e=e.substring(a.raw.length),n.push(a),!0):!1))continue;if(a=this.tokenizer.space(e)){e=e.substring(a.raw.length);let o=n.at(-1);a.raw.length===1&&o!==void 0?o.raw+=`
`:n.push(a);continue}if(a=this.tokenizer.code(e)){e=e.substring(a.raw.length);let o=n.at(-1);(o==null?void 0:o.type)==="paragraph"||(o==null?void 0:o.type)==="text"?(o.raw+=`
`+a.raw,o.text+=`
`+a.text,this.inlineQueue.at(-1).src=o.text):n.push(a);continue}if(a=this.tokenizer.fences(e)){e=e.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.heading(e)){e=e.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.hr(e)){e=e.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.blockquote(e)){e=e.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.list(e)){e=e.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.html(e)){e=e.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.def(e)){e=e.substring(a.raw.length);let o=n.at(-1);(o==null?void 0:o.type)==="paragraph"||(o==null?void 0:o.type)==="text"?(o.raw+=`
`+a.raw,o.text+=`
`+a.raw,this.inlineQueue.at(-1).src=o.text):this.tokens.links[a.tag]||(this.tokens.links[a.tag]={href:a.href,title:a.title});continue}if(a=this.tokenizer.table(e)){e=e.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.lheading(e)){e=e.substring(a.raw.length),n.push(a);continue}let u=e;if((c=this.options.extensions)!=null&&c.startBlock){let o=1/0,p=e.slice(1),x;this.options.extensions.startBlock.forEach(g=>{x=g.call({lexer:this},p),typeof x=="number"&&x>=0&&(o=Math.min(o,x))}),o<1/0&&o>=0&&(u=e.substring(0,o+1))}if(this.state.top&&(a=this.tokenizer.paragraph(u))){let o=n.at(-1);r&&(o==null?void 0:o.type)==="paragraph"?(o.raw+=`
`+a.raw,o.text+=`
`+a.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=o.text):n.push(a),r=u.length!==e.length,e=e.substring(a.raw.length);continue}if(a=this.tokenizer.text(e)){e=e.substring(a.raw.length);let o=n.at(-1);(o==null?void 0:o.type)==="text"?(o.raw+=`
`+a.raw,o.text+=`
`+a.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=o.text):n.push(a);continue}if(e){let o="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(o);break}else throw new Error(o)}}return this.state.top=!0,n}inline(e,n=[]){return this.inlineQueue.push({src:e,tokens:n}),n}inlineTokens(e,n=[]){var a,u,o;let r=e,s=null;if(this.tokens.links){let p=Object.keys(this.tokens.links);if(p.length>0)for(;(s=this.tokenizer.rules.inline.reflinkSearch.exec(r))!=null;)p.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(r=r.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(s=this.tokenizer.rules.inline.anyPunctuation.exec(r))!=null;)r=r.slice(0,s.index)+"++"+r.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(s=this.tokenizer.rules.inline.blockSkip.exec(r))!=null;)r=r.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let l=!1,c="";for(;e;){l||(c=""),l=!1;let p;if((u=(a=this.options.extensions)==null?void 0:a.inline)!=null&&u.some(g=>(p=g.call({lexer:this},e,n))?(e=e.substring(p.raw.length),n.push(p),!0):!1))continue;if(p=this.tokenizer.escape(e)){e=e.substring(p.raw.length),n.push(p);continue}if(p=this.tokenizer.tag(e)){e=e.substring(p.raw.length),n.push(p);continue}if(p=this.tokenizer.link(e)){e=e.substring(p.raw.length),n.push(p);continue}if(p=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(p.raw.length);let g=n.at(-1);p.type==="text"&&(g==null?void 0:g.type)==="text"?(g.raw+=p.raw,g.text+=p.text):n.push(p);continue}if(p=this.tokenizer.emStrong(e,r,c)){e=e.substring(p.raw.length),n.push(p);continue}if(p=this.tokenizer.codespan(e)){e=e.substring(p.raw.length),n.push(p);continue}if(p=this.tokenizer.br(e)){e=e.substring(p.raw.length),n.push(p);continue}if(p=this.tokenizer.del(e)){e=e.substring(p.raw.length),n.push(p);continue}if(p=this.tokenizer.autolink(e)){e=e.substring(p.raw.length),n.push(p);continue}if(!this.state.inLink&&(p=this.tokenizer.url(e))){e=e.substring(p.raw.length),n.push(p);continue}let x=e;if((o=this.options.extensions)!=null&&o.startInline){let g=1/0,A=e.slice(1),k;this.options.extensions.startInline.forEach(O=>{k=O.call({lexer:this},A),typeof k=="number"&&k>=0&&(g=Math.min(g,k))}),g<1/0&&g>=0&&(x=e.substring(0,g+1))}if(p=this.tokenizer.inlineText(x)){e=e.substring(p.raw.length),p.raw.slice(-1)!=="_"&&(c=p.raw.slice(-1)),l=!0;let g=n.at(-1);(g==null?void 0:g.type)==="text"?(g.raw+=p.raw,g.text+=p.text):n.push(p);continue}if(e){let g="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(g);break}else throw new Error(g)}}return n}},Be=class{constructor(t){_(this,"options");_(this,"parser");this.options=t||ne}space(t){return""}code({text:t,lang:e,escaped:n}){var l;let r=(l=(e||"").match(P.notSpaceStart))==null?void 0:l[0],s=t.replace(P.endingNewline,"")+`
`;return r?'<pre><code class="language-'+G(r)+'">'+(n?s:G(s,!0))+`</code></pre>
`:"<pre><code>"+(n?s:G(s,!0))+`</code></pre>
`}blockquote({tokens:t}){return`<blockquote>
${this.parser.parse(t)}</blockquote>
`}html({text:t}){return t}heading({tokens:t,depth:e}){return`<h${e}>${this.parser.parseInline(t)}</h${e}>
`}hr(t){return`<hr>
`}list(t){let e=t.ordered,n=t.start,r="";for(let c=0;c<t.items.length;c++){let a=t.items[c];r+=this.listitem(a)}let s=e?"ol":"ul",l=e&&n!==1?' start="'+n+'"':"";return"<"+s+l+`>
`+r+"</"+s+`>
`}listitem(t){var n;let e="";if(t.task){let r=this.checkbox({checked:!!t.checked});t.loose?((n=t.tokens[0])==null?void 0:n.type)==="paragraph"?(t.tokens[0].text=r+" "+t.tokens[0].text,t.tokens[0].tokens&&t.tokens[0].tokens.length>0&&t.tokens[0].tokens[0].type==="text"&&(t.tokens[0].tokens[0].text=r+" "+G(t.tokens[0].tokens[0].text),t.tokens[0].tokens[0].escaped=!0)):t.tokens.unshift({type:"text",raw:r+" ",text:r+" ",escaped:!0}):e+=r+" "}return e+=this.parser.parse(t.tokens,!!t.loose),`<li>${e}</li>
`}checkbox({checked:t}){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:t}){return`<p>${this.parser.parseInline(t)}</p>
`}table(t){let e="",n="";for(let s=0;s<t.header.length;s++)n+=this.tablecell(t.header[s]);e+=this.tablerow({text:n});let r="";for(let s=0;s<t.rows.length;s++){let l=t.rows[s];n="";for(let c=0;c<l.length;c++)n+=this.tablecell(l[c]);r+=this.tablerow({text:n})}return r&&(r=`<tbody>${r}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+r+`</table>
`}tablerow({text:t}){return`<tr>
${t}</tr>
`}tablecell(t){let e=this.parser.parseInline(t.tokens),n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong({tokens:t}){return`<strong>${this.parser.parseInline(t)}</strong>`}em({tokens:t}){return`<em>${this.parser.parseInline(t)}</em>`}codespan({text:t}){return`<code>${G(t,!0)}</code>`}br(t){return"<br>"}del({tokens:t}){return`<del>${this.parser.parseInline(t)}</del>`}link({href:t,title:e,tokens:n}){let r=this.parser.parseInline(n),s=Jt(t);if(s===null)return r;t=s;let l='<a href="'+t+'"';return e&&(l+=' title="'+G(e)+'"'),l+=">"+r+"</a>",l}image({href:t,title:e,text:n,tokens:r}){r&&(n=this.parser.parseInline(r,this.parser.textRenderer));let s=Jt(t);if(s===null)return G(n);t=s;let l=`<img src="${t}" alt="${n}"`;return e&&(l+=` title="${G(e)}"`),l+=">",l}text(t){return"tokens"in t&&t.tokens?this.parser.parseInline(t.tokens):"escaped"in t&&t.escaped?t.text:G(t.text)}},Et=class{strong({text:t}){return t}em({text:t}){return t}codespan({text:t}){return t}del({text:t}){return t}html({text:t}){return t}text({text:t}){return t}link({text:t}){return""+t}image({text:t}){return""+t}br(){return""}},X=class ft{constructor(e){_(this,"options");_(this,"renderer");_(this,"textRenderer");this.options=e||ne,this.options.renderer=this.options.renderer||new Be,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new Et}static parse(e,n){return new ft(n).parse(e)}static parseInline(e,n){return new ft(n).parseInline(e)}parse(e,n=!0){var s,l;let r="";for(let c=0;c<e.length;c++){let a=e[c];if((l=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&l[a.type]){let o=a,p=this.options.extensions.renderers[o.type].call({parser:this},o);if(p!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){r+=p||"";continue}}let u=a;switch(u.type){case"space":{r+=this.renderer.space(u);continue}case"hr":{r+=this.renderer.hr(u);continue}case"heading":{r+=this.renderer.heading(u);continue}case"code":{r+=this.renderer.code(u);continue}case"table":{r+=this.renderer.table(u);continue}case"blockquote":{r+=this.renderer.blockquote(u);continue}case"list":{r+=this.renderer.list(u);continue}case"html":{r+=this.renderer.html(u);continue}case"paragraph":{r+=this.renderer.paragraph(u);continue}case"text":{let o=u,p=this.renderer.text(o);for(;c+1<e.length&&e[c+1].type==="text";)o=e[++c],p+=`
`+this.renderer.text(o);n?r+=this.renderer.paragraph({type:"paragraph",raw:p,text:p,tokens:[{type:"text",raw:p,text:p,escaped:!0}]}):r+=p;continue}default:{let o='Token with "'+u.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return r}parseInline(e,n=this.renderer){var s,l;let r="";for(let c=0;c<e.length;c++){let a=e[c];if((l=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&l[a.type]){let o=this.options.extensions.renderers[a.type].call({parser:this},a);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(a.type)){r+=o||"";continue}}let u=a;switch(u.type){case"escape":{r+=n.text(u);break}case"html":{r+=n.html(u);break}case"link":{r+=n.link(u);break}case"image":{r+=n.image(u);break}case"strong":{r+=n.strong(u);break}case"em":{r+=n.em(u);break}case"codespan":{r+=n.codespan(u);break}case"br":{r+=n.br(u);break}case"del":{r+=n.del(u);break}case"text":{r+=n.text(u);break}default:{let o='Token with "'+u.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return r}},ut,ve=(ut=class{constructor(t){_(this,"options");_(this,"block");this.options=t||ne}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}provideLexer(){return this.block?j.lex:j.lexInline}provideParser(){return this.block?X.parse:X.parseInline}},_(ut,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),ut),Dr=class{constructor(...t){_(this,"defaults",mt());_(this,"options",this.setOptions);_(this,"parse",this.parseMarkdown(!0));_(this,"parseInline",this.parseMarkdown(!1));_(this,"Parser",X);_(this,"Renderer",Be);_(this,"TextRenderer",Et);_(this,"Lexer",j);_(this,"Tokenizer",Fe);_(this,"Hooks",ve);this.use(...t)}walkTokens(t,e){var r,s;let n=[];for(let l of t)switch(n=n.concat(e.call(this,l)),l.type){case"table":{let c=l;for(let a of c.header)n=n.concat(this.walkTokens(a.tokens,e));for(let a of c.rows)for(let u of a)n=n.concat(this.walkTokens(u.tokens,e));break}case"list":{let c=l;n=n.concat(this.walkTokens(c.items,e));break}default:{let c=l;(s=(r=this.defaults.extensions)==null?void 0:r.childTokens)!=null&&s[c.type]?this.defaults.extensions.childTokens[c.type].forEach(a=>{let u=c[a].flat(1/0);n=n.concat(this.walkTokens(u,e))}):c.tokens&&(n=n.concat(this.walkTokens(c.tokens,e)))}}return n}use(...t){let e=this.defaults.extensions||{renderers:{},childTokens:{}};return t.forEach(n=>{let r=U({},n);if(r.async=this.defaults.async||r.async||!1,n.extensions&&(n.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){let l=e.renderers[s.name];l?e.renderers[s.name]=function(...c){let a=s.renderer.apply(this,c);return a===!1&&(a=l.apply(this,c)),a}:e.renderers[s.name]=s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");let l=e[s.level];l?l.unshift(s.tokenizer):e[s.level]=[s.tokenizer],s.start&&(s.level==="block"?e.startBlock?e.startBlock.push(s.start):e.startBlock=[s.start]:s.level==="inline"&&(e.startInline?e.startInline.push(s.start):e.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(e.childTokens[s.name]=s.childTokens)}),r.extensions=e),n.renderer){let s=this.defaults.renderer||new Be(this.defaults);for(let l in n.renderer){if(!(l in s))throw new Error(`renderer '${l}' does not exist`);if(["options","parser"].includes(l))continue;let c=l,a=n.renderer[c],u=s[c];s[c]=(...o)=>{let p=a.apply(s,o);return p===!1&&(p=u.apply(s,o)),p||""}}r.renderer=s}if(n.tokenizer){let s=this.defaults.tokenizer||new Fe(this.defaults);for(let l in n.tokenizer){if(!(l in s))throw new Error(`tokenizer '${l}' does not exist`);if(["options","rules","lexer"].includes(l))continue;let c=l,a=n.tokenizer[c],u=s[c];s[c]=(...o)=>{let p=a.apply(s,o);return p===!1&&(p=u.apply(s,o)),p}}r.tokenizer=s}if(n.hooks){let s=this.defaults.hooks||new ve;for(let l in n.hooks){if(!(l in s))throw new Error(`hook '${l}' does not exist`);if(["options","block"].includes(l))continue;let c=l,a=n.hooks[c],u=s[c];ve.passThroughHooks.has(l)?s[c]=o=>{if(this.defaults.async)return Promise.resolve(a.call(s,o)).then(x=>u.call(s,x));let p=a.call(s,o);return u.call(s,p)}:s[c]=(...o)=>{let p=a.apply(s,o);return p===!1&&(p=u.apply(s,o)),p}}r.hooks=s}if(n.walkTokens){let s=this.defaults.walkTokens,l=n.walkTokens;r.walkTokens=function(c){let a=[];return a.push(l.call(this,c)),s&&(a=a.concat(s.call(this,c))),a}}this.defaults=U(U({},this.defaults),r)}),this}setOptions(t){return this.defaults=U(U({},this.defaults),t),this}lexer(t,e){return j.lex(t,e!=null?e:this.defaults)}parser(t,e){return X.parse(t,e!=null?e:this.defaults)}parseMarkdown(t){return(e,n)=>{let r=U({},n),s=U(U({},this.defaults),r),l=this.onError(!!s.silent,!!s.async);if(this.defaults.async===!0&&r.async===!1)return l(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof e>"u"||e===null)return l(new Error("marked(): input parameter is undefined or null"));if(typeof e!="string")return l(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected"));s.hooks&&(s.hooks.options=s,s.hooks.block=t);let c=s.hooks?s.hooks.provideLexer():t?j.lex:j.lexInline,a=s.hooks?s.hooks.provideParser():t?X.parse:X.parseInline;if(s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(e):e).then(u=>c(u,s)).then(u=>s.hooks?s.hooks.processAllTokens(u):u).then(u=>s.walkTokens?Promise.all(this.walkTokens(u,s.walkTokens)).then(()=>u):u).then(u=>a(u,s)).then(u=>s.hooks?s.hooks.postprocess(u):u).catch(l);try{s.hooks&&(e=s.hooks.preprocess(e));let u=c(e,s);s.hooks&&(u=s.hooks.processAllTokens(u)),s.walkTokens&&this.walkTokens(u,s.walkTokens);let o=a(u,s);return s.hooks&&(o=s.hooks.postprocess(o)),o}catch(u){return l(u)}}}onError(t,e){return n=>{if(n.message+=`
Please report this to https://github.com/markedjs/marked.`,t){let r="<p>An error occurred:</p><pre>"+G(n.message+"",!0)+"</pre>";return e?Promise.resolve(r):r}if(e)return Promise.reject(n);throw n}}},te=new Dr;function T(t,e){return te.parse(t,e)}T.options=T.setOptions=function(t){return te.setOptions(t),T.defaults=te.defaults,hn(T.defaults),T};T.getDefaults=mt;T.defaults=ne;T.use=function(...t){return te.use(...t),T.defaults=te.defaults,hn(T.defaults),T};T.walkTokens=function(t,e){return te.walkTokens(t,e)};T.parseInline=te.parseInline;T.Parser=X;T.parser=X.parse;T.Renderer=Be;T.TextRenderer=Et;T.Lexer=j;T.lexer=j.lex;T.Tokenizer=Fe;T.Hooks=ve;T.parse=T;T.options;T.setOptions;T.use;T.walkTokens;T.parseInline;X.parse;j.lex;/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:An,setPrototypeOf:nn,isFrozen:Nr,getPrototypeOf:Mr,getOwnPropertyDescriptor:Pr}=Object;let{freeze:z,seal:F,create:En}=Object,{apply:gt,construct:dt}=typeof Reflect!="undefined"&&Reflect;z||(z=function(e){return e});F||(F=function(e){return e});gt||(gt=function(e,n,r){return e.apply(n,r)});dt||(dt=function(e,n){return new e(...n)});const Pe=v(Array.prototype.forEach),zr=v(Array.prototype.lastIndexOf),rn=v(Array.prototype.pop),xe=v(Array.prototype.push),vr=v(Array.prototype.splice),$e=v(String.prototype.toLowerCase),it=v(String.prototype.toString),sn=v(String.prototype.match),be=v(String.prototype.replace),$r=v(String.prototype.indexOf),Ur=v(String.prototype.trim),B=v(Object.prototype.hasOwnProperty),M=v(RegExp.prototype.test),Te=Fr(TypeError);function v(t){return function(e){e instanceof RegExp&&(e.lastIndex=0);for(var n=arguments.length,r=new Array(n>1?n-1:0),s=1;s<n;s++)r[s-1]=arguments[s];return gt(t,e,r)}}function Fr(t){return function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return dt(t,n)}}function m(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:$e;nn&&nn(t,null);let r=e.length;for(;r--;){let s=e[r];if(typeof s=="string"){const l=n(s);l!==s&&(Nr(e)||(e[r]=l),s=l)}t[s]=!0}return t}function Br(t){for(let e=0;e<t.length;e++)B(t,e)||(t[e]=null);return t}function Y(t){const e=En(null);for(const[n,r]of An(t))B(t,n)&&(Array.isArray(r)?e[n]=Br(r):r&&typeof r=="object"&&r.constructor===Object?e[n]=Y(r):e[n]=r);return e}function _e(t,e){for(;t!==null;){const r=Pr(t,e);if(r){if(r.get)return v(r.get);if(typeof r.value=="function")return v(r.value)}t=Mr(t)}function n(){return null}return n}const ln=z(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),lt=z(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),at=z(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Hr=z(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),ot=z(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Gr=z(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),an=z(["#text"]),on=z(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),ct=z(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),cn=z(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),ze=z(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Wr=F(/\{\{[\w\W]*|[\w\W]*\}\}/gm),qr=F(/<%[\w\W]*|[\w\W]*%>/gm),Zr=F(/\$\{[\w\W]*/gm),Yr=F(/^data-[\-\w.\u00B7-\uFFFF]+$/),jr=F(/^aria-[\-\w]+$/),Sn=F(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Xr=F(/^(?:\w+script|data):/i),Qr=F(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),yn=F(/^html$/i),Vr=F(/^[a-z][.\w]*(-[.\w]+)+$/i);var un=Object.freeze({__proto__:null,ARIA_ATTR:jr,ATTR_WHITESPACE:Qr,CUSTOM_ELEMENT:Vr,DATA_ATTR:Yr,DOCTYPE_NAME:yn,ERB_EXPR:qr,IS_ALLOWED_URI:Sn,IS_SCRIPT_OR_DATA:Xr,MUSTACHE_EXPR:Wr,TMPLIT_EXPR:Zr});const we={element:1,text:3,progressingInstruction:7,comment:8,document:9},Kr=function(){return typeof window=="undefined"?null:window},Jr=function(e,n){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let r=null;const s="data-tt-policy-suffix";n&&n.hasAttribute(s)&&(r=n.getAttribute(s));const l="dompurify"+(r?"#"+r:"");try{return e.createPolicy(l,{createHTML(c){return c},createScriptURL(c){return c}})}catch(c){return console.warn("TrustedTypes policy "+l+" could not be created."),null}},pn=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function Rn(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Kr();const e=d=>Rn(d);if(e.version="3.2.6",e.removed=[],!t||!t.document||t.document.nodeType!==we.document||!t.Element)return e.isSupported=!1,e;let{document:n}=t;const r=n,s=r.currentScript,{DocumentFragment:l,HTMLTemplateElement:c,Node:a,Element:u,NodeFilter:o,NamedNodeMap:p=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:x,DOMParser:g,trustedTypes:A}=t,k=u.prototype,O=_e(k,"cloneNode"),Se=_e(k,"remove"),pe=_e(k,"nextSibling"),ye=_e(k,"childNodes"),Q=_e(k,"parentNode");if(typeof c=="function"){const d=n.createElement("template");d.content&&d.content.ownerDocument&&(n=d.content.ownerDocument)}let y,V="";const{implementation:K,createNodeIterator:J,createDocumentFragment:Ln,getElementsByTagName:In}=n,{importNode:On}=r;let N=pn();e.isSupported=typeof An=="function"&&typeof Q=="function"&&K&&K.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:We,ERB_EXPR:qe,TMPLIT_EXPR:Ze,DATA_ATTR:Cn,ARIA_ATTR:Dn,IS_SCRIPT_OR_DATA:Nn,ATTR_WHITESPACE:St,CUSTOM_ELEMENT:Mn}=un;let{IS_ALLOWED_URI:yt}=un,R=null;const Rt=m({},[...ln,...lt,...at,...ot,...an]);let I=null;const Lt=m({},[...on,...ct,...cn,...ze]);let E=Object.seal(En(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),he=null,Ye=null,It=!0,je=!0,Ot=!1,Ct=!0,re=!1,Re=!0,ee=!1,Xe=!1,Qe=!1,se=!1,Le=!1,Ie=!1,Dt=!0,Nt=!1;const Pn="user-content-";let Ve=!0,fe=!1,ie={},le=null;const Mt=m({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Pt=null;const zt=m({},["audio","video","img","source","image","track"]);let Ke=null;const vt=m({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Oe="http://www.w3.org/1998/Math/MathML",Ce="http://www.w3.org/2000/svg",W="http://www.w3.org/1999/xhtml";let ae=W,Je=!1,et=null;const zn=m({},[Oe,Ce,W],it);let De=m({},["mi","mo","mn","ms","mtext"]),Ne=m({},["annotation-xml"]);const vn=m({},["title","style","font","a","script"]);let ge=null;const $n=["application/xhtml+xml","text/html"],Un="text/html";let L=null,oe=null;const Fn=n.createElement("form"),$t=function(i){return i instanceof RegExp||i instanceof Function},tt=function(){let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(oe&&oe===i)){if((!i||typeof i!="object")&&(i={}),i=Y(i),ge=$n.indexOf(i.PARSER_MEDIA_TYPE)===-1?Un:i.PARSER_MEDIA_TYPE,L=ge==="application/xhtml+xml"?it:$e,R=B(i,"ALLOWED_TAGS")?m({},i.ALLOWED_TAGS,L):Rt,I=B(i,"ALLOWED_ATTR")?m({},i.ALLOWED_ATTR,L):Lt,et=B(i,"ALLOWED_NAMESPACES")?m({},i.ALLOWED_NAMESPACES,it):zn,Ke=B(i,"ADD_URI_SAFE_ATTR")?m(Y(vt),i.ADD_URI_SAFE_ATTR,L):vt,Pt=B(i,"ADD_DATA_URI_TAGS")?m(Y(zt),i.ADD_DATA_URI_TAGS,L):zt,le=B(i,"FORBID_CONTENTS")?m({},i.FORBID_CONTENTS,L):Mt,he=B(i,"FORBID_TAGS")?m({},i.FORBID_TAGS,L):Y({}),Ye=B(i,"FORBID_ATTR")?m({},i.FORBID_ATTR,L):Y({}),ie=B(i,"USE_PROFILES")?i.USE_PROFILES:!1,It=i.ALLOW_ARIA_ATTR!==!1,je=i.ALLOW_DATA_ATTR!==!1,Ot=i.ALLOW_UNKNOWN_PROTOCOLS||!1,Ct=i.ALLOW_SELF_CLOSE_IN_ATTR!==!1,re=i.SAFE_FOR_TEMPLATES||!1,Re=i.SAFE_FOR_XML!==!1,ee=i.WHOLE_DOCUMENT||!1,se=i.RETURN_DOM||!1,Le=i.RETURN_DOM_FRAGMENT||!1,Ie=i.RETURN_TRUSTED_TYPE||!1,Qe=i.FORCE_BODY||!1,Dt=i.SANITIZE_DOM!==!1,Nt=i.SANITIZE_NAMED_PROPS||!1,Ve=i.KEEP_CONTENT!==!1,fe=i.IN_PLACE||!1,yt=i.ALLOWED_URI_REGEXP||Sn,ae=i.NAMESPACE||W,De=i.MATHML_TEXT_INTEGRATION_POINTS||De,Ne=i.HTML_INTEGRATION_POINTS||Ne,E=i.CUSTOM_ELEMENT_HANDLING||{},i.CUSTOM_ELEMENT_HANDLING&&$t(i.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(E.tagNameCheck=i.CUSTOM_ELEMENT_HANDLING.tagNameCheck),i.CUSTOM_ELEMENT_HANDLING&&$t(i.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(E.attributeNameCheck=i.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),i.CUSTOM_ELEMENT_HANDLING&&typeof i.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(E.allowCustomizedBuiltInElements=i.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),re&&(je=!1),Le&&(se=!0),ie&&(R=m({},an),I=[],ie.html===!0&&(m(R,ln),m(I,on)),ie.svg===!0&&(m(R,lt),m(I,ct),m(I,ze)),ie.svgFilters===!0&&(m(R,at),m(I,ct),m(I,ze)),ie.mathMl===!0&&(m(R,ot),m(I,cn),m(I,ze))),i.ADD_TAGS&&(R===Rt&&(R=Y(R)),m(R,i.ADD_TAGS,L)),i.ADD_ATTR&&(I===Lt&&(I=Y(I)),m(I,i.ADD_ATTR,L)),i.ADD_URI_SAFE_ATTR&&m(Ke,i.ADD_URI_SAFE_ATTR,L),i.FORBID_CONTENTS&&(le===Mt&&(le=Y(le)),m(le,i.FORBID_CONTENTS,L)),Ve&&(R["#text"]=!0),ee&&m(R,["html","head","body"]),R.table&&(m(R,["tbody"]),delete he.tbody),i.TRUSTED_TYPES_POLICY){if(typeof i.TRUSTED_TYPES_POLICY.createHTML!="function")throw Te('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof i.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Te('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');y=i.TRUSTED_TYPES_POLICY,V=y.createHTML("")}else y===void 0&&(y=Jr(A,s)),y!==null&&typeof V=="string"&&(V=y.createHTML(""));z&&z(i),oe=i}},Ut=m({},[...lt,...at,...Hr]),Ft=m({},[...ot,...Gr]),Bn=function(i){let h=Q(i);(!h||!h.tagName)&&(h={namespaceURI:ae,tagName:"template"});const f=$e(i.tagName),w=$e(h.tagName);return et[i.namespaceURI]?i.namespaceURI===Ce?h.namespaceURI===W?f==="svg":h.namespaceURI===Oe?f==="svg"&&(w==="annotation-xml"||De[w]):!!Ut[f]:i.namespaceURI===Oe?h.namespaceURI===W?f==="math":h.namespaceURI===Ce?f==="math"&&Ne[w]:!!Ft[f]:i.namespaceURI===W?h.namespaceURI===Ce&&!Ne[w]||h.namespaceURI===Oe&&!De[w]?!1:!Ft[f]&&(vn[f]||!Ut[f]):!!(ge==="application/xhtml+xml"&&et[i.namespaceURI]):!1},H=function(i){xe(e.removed,{element:i});try{Q(i).removeChild(i)}catch(h){Se(i)}},ce=function(i,h){try{xe(e.removed,{attribute:h.getAttributeNode(i),from:h})}catch(f){xe(e.removed,{attribute:null,from:h})}if(h.removeAttribute(i),i==="is")if(se||Le)try{H(h)}catch(f){}else try{h.setAttribute(i,"")}catch(f){}},Bt=function(i){let h=null,f=null;if(Qe)i="<remove></remove>"+i;else{const S=sn(i,/^[\r\n\t ]+/);f=S&&S[0]}ge==="application/xhtml+xml"&&ae===W&&(i='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+i+"</body></html>");const w=y?y.createHTML(i):i;if(ae===W)try{h=new g().parseFromString(w,ge)}catch(S){}if(!h||!h.documentElement){h=K.createDocument(ae,"template",null);try{h.documentElement.innerHTML=Je?V:w}catch(S){}}const C=h.body||h.documentElement;return i&&f&&C.insertBefore(n.createTextNode(f),C.childNodes[0]||null),ae===W?In.call(h,ee?"html":"body")[0]:ee?h.documentElement:C},Ht=function(i){return J.call(i.ownerDocument||i,i,o.SHOW_ELEMENT|o.SHOW_COMMENT|o.SHOW_TEXT|o.SHOW_PROCESSING_INSTRUCTION|o.SHOW_CDATA_SECTION,null)},nt=function(i){return i instanceof x&&(typeof i.nodeName!="string"||typeof i.textContent!="string"||typeof i.removeChild!="function"||!(i.attributes instanceof p)||typeof i.removeAttribute!="function"||typeof i.setAttribute!="function"||typeof i.namespaceURI!="string"||typeof i.insertBefore!="function"||typeof i.hasChildNodes!="function")},Gt=function(i){return typeof a=="function"&&i instanceof a};function q(d,i,h){Pe(d,f=>{f.call(e,i,h,oe)})}const Wt=function(i){let h=null;if(q(N.beforeSanitizeElements,i,null),nt(i))return H(i),!0;const f=L(i.nodeName);if(q(N.uponSanitizeElement,i,{tagName:f,allowedTags:R}),Re&&i.hasChildNodes()&&!Gt(i.firstElementChild)&&M(/<[/\w!]/g,i.innerHTML)&&M(/<[/\w!]/g,i.textContent)||i.nodeType===we.progressingInstruction||Re&&i.nodeType===we.comment&&M(/<[/\w]/g,i.data))return H(i),!0;if(!R[f]||he[f]){if(!he[f]&&Zt(f)&&(E.tagNameCheck instanceof RegExp&&M(E.tagNameCheck,f)||E.tagNameCheck instanceof Function&&E.tagNameCheck(f)))return!1;if(Ve&&!le[f]){const w=Q(i)||i.parentNode,C=ye(i)||i.childNodes;if(C&&w){const S=C.length;for(let $=S-1;$>=0;--$){const Z=O(C[$],!0);Z.__removalCount=(i.__removalCount||0)+1,w.insertBefore(Z,pe(i))}}}return H(i),!0}return i instanceof u&&!Bn(i)||(f==="noscript"||f==="noembed"||f==="noframes")&&M(/<\/no(script|embed|frames)/i,i.innerHTML)?(H(i),!0):(re&&i.nodeType===we.text&&(h=i.textContent,Pe([We,qe,Ze],w=>{h=be(h,w," ")}),i.textContent!==h&&(xe(e.removed,{element:i.cloneNode()}),i.textContent=h)),q(N.afterSanitizeElements,i,null),!1)},qt=function(i,h,f){if(Dt&&(h==="id"||h==="name")&&(f in n||f in Fn))return!1;if(!(je&&!Ye[h]&&M(Cn,h))){if(!(It&&M(Dn,h))){if(!I[h]||Ye[h]){if(!(Zt(i)&&(E.tagNameCheck instanceof RegExp&&M(E.tagNameCheck,i)||E.tagNameCheck instanceof Function&&E.tagNameCheck(i))&&(E.attributeNameCheck instanceof RegExp&&M(E.attributeNameCheck,h)||E.attributeNameCheck instanceof Function&&E.attributeNameCheck(h))||h==="is"&&E.allowCustomizedBuiltInElements&&(E.tagNameCheck instanceof RegExp&&M(E.tagNameCheck,f)||E.tagNameCheck instanceof Function&&E.tagNameCheck(f))))return!1}else if(!Ke[h]){if(!M(yt,be(f,St,""))){if(!((h==="src"||h==="xlink:href"||h==="href")&&i!=="script"&&$r(f,"data:")===0&&Pt[i])){if(!(Ot&&!M(Nn,be(f,St,"")))){if(f)return!1}}}}}}return!0},Zt=function(i){return i!=="annotation-xml"&&sn(i,Mn)},Yt=function(i){q(N.beforeSanitizeAttributes,i,null);const{attributes:h}=i;if(!h||nt(i))return;const f={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:I,forceKeepAttr:void 0};let w=h.length;for(;w--;){const C=h[w],{name:S,namespaceURI:$,value:Z}=C,de=L(S),rt=Z;let D=S==="value"?rt:Ur(rt);if(f.attrName=de,f.attrValue=D,f.keepAttr=!0,f.forceKeepAttr=void 0,q(N.uponSanitizeAttribute,i,f),D=f.attrValue,Nt&&(de==="id"||de==="name")&&(ce(S,i),D=Pn+D),Re&&M(/((--!?|])>)|<\/(style|title)/i,D)){ce(S,i);continue}if(f.forceKeepAttr)continue;if(!f.keepAttr){ce(S,i);continue}if(!Ct&&M(/\/>/i,D)){ce(S,i);continue}re&&Pe([We,qe,Ze],Xt=>{D=be(D,Xt," ")});const jt=L(i.nodeName);if(!qt(jt,de,D)){ce(S,i);continue}if(y&&typeof A=="object"&&typeof A.getAttributeType=="function"&&!$)switch(A.getAttributeType(jt,de)){case"TrustedHTML":{D=y.createHTML(D);break}case"TrustedScriptURL":{D=y.createScriptURL(D);break}}if(D!==rt)try{$?i.setAttributeNS($,S,D):i.setAttribute(S,D),nt(i)?H(i):rn(e.removed)}catch(Xt){ce(S,i)}}q(N.afterSanitizeAttributes,i,null)},Hn=function d(i){let h=null;const f=Ht(i);for(q(N.beforeSanitizeShadowDOM,i,null);h=f.nextNode();)q(N.uponSanitizeShadowNode,h,null),Wt(h),Yt(h),h.content instanceof l&&d(h.content);q(N.afterSanitizeShadowDOM,i,null)};return e.sanitize=function(d){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},h=null,f=null,w=null,C=null;if(Je=!d,Je&&(d="<!-->"),typeof d!="string"&&!Gt(d))if(typeof d.toString=="function"){if(d=d.toString(),typeof d!="string")throw Te("dirty is not a string, aborting")}else throw Te("toString is not a function");if(!e.isSupported)return d;if(Xe||tt(i),e.removed=[],typeof d=="string"&&(fe=!1),fe){if(d.nodeName){const Z=L(d.nodeName);if(!R[Z]||he[Z])throw Te("root node is forbidden and cannot be sanitized in-place")}}else if(d instanceof a)h=Bt("<!---->"),f=h.ownerDocument.importNode(d,!0),f.nodeType===we.element&&f.nodeName==="BODY"||f.nodeName==="HTML"?h=f:h.appendChild(f);else{if(!se&&!re&&!ee&&d.indexOf("<")===-1)return y&&Ie?y.createHTML(d):d;if(h=Bt(d),!h)return se?null:Ie?V:""}h&&Qe&&H(h.firstChild);const S=Ht(fe?d:h);for(;w=S.nextNode();)Wt(w),Yt(w),w.content instanceof l&&Hn(w.content);if(fe)return d;if(se){if(Le)for(C=Ln.call(h.ownerDocument);h.firstChild;)C.appendChild(h.firstChild);else C=h;return(I.shadowroot||I.shadowrootmode)&&(C=On.call(r,C,!0)),C}let $=ee?h.outerHTML:h.innerHTML;return ee&&R["!doctype"]&&h.ownerDocument&&h.ownerDocument.doctype&&h.ownerDocument.doctype.name&&M(yn,h.ownerDocument.doctype.name)&&($="<!DOCTYPE "+h.ownerDocument.doctype.name+`>
`+$),re&&Pe([We,qe,Ze],Z=>{$=be($,Z," ")}),y&&Ie?y.createHTML($):$},e.setConfig=function(){let d=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};tt(d),Xe=!0},e.clearConfig=function(){oe=null,Xe=!1},e.isValidAttribute=function(d,i,h){oe||tt({});const f=L(d),w=L(i);return qt(f,w,h)},e.addHook=function(d,i){typeof i=="function"&&xe(N[d],i)},e.removeHook=function(d,i){if(i!==void 0){const h=zr(N[d],i);return h===-1?void 0:vr(N[d],h,1)[0]}return rn(N[d])},e.removeHooks=function(d){N[d]=[]},e.removeAllHooks=function(){N=pn()},e}var es=Rn();const ts={name:"MarkdownRenderer",props:{content:{type:String,required:!0}},computed:{compiledMarkdown(){const t=T.parse(this.content);return es.sanitize(t)}}},ns=["innerHTML"];function rs(t,e,n,r,s,l){return Qn(),Xn("div",{class:"markdown-content",innerHTML:l.compiledMarkdown},null,8,ns)}const ls=jn(ts,[["render",rs],["__scopeId","data-v-1ae3c89c"]]);export{ls as default};
