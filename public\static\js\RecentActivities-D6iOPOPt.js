var b=(u,c,o)=>new Promise((p,m)=>{var x=s=>{try{i(o.next(s))}catch(r){m(r)}},e=s=>{try{i(o.throw(s))}catch(r){m(r)}},i=s=>s.done?p(s.value):Promise.resolve(s.value).then(x,e);i((o=o.apply(u,c)).next())});import{aN as N,q as k,r as A,o as B,G as f,a7 as M,Q as R,n as l,N as d,B as D,m as v,A as E,I as V,J as z,D as a,E as g,k as _,aB as j,P as h,M as n,aD as q,a8 as y,_ as O}from"./index-ZVLuktk4.js";const U=u=>N.request("get","/api/auth/dashboard/recent-activities",{params:u}),F={class:"flex items-center justify-between"},G={class:"text-lg font-semibold"},J={class:"activities-list"},L={class:"flex items-start space-x-3"},P={class:"flex-1 min-w-0"},Q={class:"flex items-center space-x-2"},S={class:"text-sm text-gray-900 mt-1"},T={class:"text-xs text-gray-500 mt-1"},$={key:0,class:"text-center py-8"},H={class:"text-gray-400 text-sm"},K=k({__name:"RecentActivities",setup(u){const c=A(!1),o=A([]),p=()=>b(null,null,function*(){c.value=!0;try{const{data:e}=yield U({limit:10});e!=null&&e.success&&(o.value=e.data||[])}catch(e){console.error("Error loading activities:",e)}finally{c.value=!1}}),m=e=>({"bot.created":"ri/robot-2-line","bot.updated":"ri/edit-line","chat.created":"ri/chat-3-line","message.sent":"ri/message-3-line","user.login":"ri/login-circle-line","user.registered":"ri/user-add-line"})[e]||"ri/information-line",x=e=>({"bot.created":"success","bot.updated":"warning","chat.created":"primary","message.sent":"info","user.login":"success","user.registered":"primary"})[e]||"info";return B(()=>{p()}),(e,i)=>{const s=f("el-button"),r=f("IconifyIconOffline"),w=f("el-tag"),C=f("el-card"),I=M("loading");return l(),R(C,{class:"recent-activities",shadow:"hover"},{header:d(()=>[a("div",F,[a("span",G,n(_(y)("Recent Activities")),1),g(s,{type:"text",size:"small",onClick:p,loading:c.value},{default:d(()=>[h(n(_(y)("Refresh")),1)]),_:1},8,["loading"])])]),default:d(()=>[D((l(),v("div",J,[(l(!0),v(V,null,z(o.value,t=>(l(),v("div",{key:t.id,class:"activity-item"},[a("div",L,[g(_(j),{size:32,src:t.user.avatar,alt:t.user.name},{default:d(()=>[h(n(t.user.name.charAt(0).toUpperCase()),1)]),_:2},1032,["src","alt"]),a("div",P,[a("div",Q,[g(w,{type:x(t.type),size:"small",effect:"plain"},{default:d(()=>[g(r,{icon:e.useRenderIcon(m(t.type)),class:"mr-1"},null,8,["icon"]),h(" "+n(t.type.replace("."," ").toUpperCase()),1)]),_:2},1032,["type"])]),a("p",S,[a("strong",null,n(t.user.name),1),h(" "+n(t.description),1)]),a("p",T,n(_(q)(t.createdAt).fromNow()),1)])])]))),128)),!c.value&&o.value.length===0?(l(),v("div",$,[a("div",H,n(_(y)("No recent activities")),1)])):E("",!0)])),[[I,c.value]])]),_:1})}}}),Y=O(K,[["__scopeId","data-v-4fb9da82"]]);export{Y as default};
