# Sidebar Module

## 📋 Overview

The Sidebar Module is a core component of Laravel ProCMS that manages navigation menus and sidebar structures. It provides comprehensive functionality for creating, managing, and displaying hierarchical navigation systems with role-based access control and intelligent caching.

### Key Features

- **Hierarchical Navigation**: Support for parent-child menu relationships
- **Role-Based Access**: Fine-grained permission control for menu visibility
- **Intelligent Caching**: Automatic caching with observer-based invalidation
- **Soft Delete Support**: Safe deletion with restore capabilities
- **Bulk Operations**: Efficient bulk management of sidebar items
- **Advanced Filtering**: Comprehensive search and filtering capabilities
- **API-First Design**: RESTful API with public and authenticated endpoints

## 🏗️ Architecture

### Key Components

- **Sidebar Model**: Main sidebar entity with hierarchical relationships
- **SidebarController**: Public API for sidebar retrieval
- **Auth/SidebarController**: Administrative CRUD operations and bulk management
- **SidebarService**: Business logic and cached data retrieval with 5-minute TTL
- **SidebarFilter**: Advanced filtering and search capabilities
- **SidebarObserver**: Automatic cache invalidation on model changes
- **SidebarFacade**: Convenient access to sidebar functionality

## 📁 Module Structure

```
Modules/Sidebar/
├── app/
│   ├── Facades/
│   │   └── Sidebar.php                    # Service facade
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── SidebarController.php      # Public sidebar API
│   │   │   └── Auth/
│   │   │       └── SidebarController.php  # Admin CRUD operations
│   │   ├── Filters/
│   │   │   └── SidebarFilter.php          # Advanced filtering
│   │   └── Requests/
│   │       ├── SidebarRequest.php         # Sidebar validation
│   │       └── BulkSidebarRequest.php     # Bulk operation validation
│   ├── Models/
│   │   └── Sidebar.php                    # Main sidebar model
│   ├── Observers/
│   │   └── SidebarObserver.php            # Cache invalidation
│   ├── Providers/
│   │   ├── SidebarServiceProvider.php     # Service registration
│   │   ├── EventServiceProvider.php       # Event registration
│   │   └── RouteServiceProvider.php       # Route registration
│   └── Services/
│       └── SidebarService.php             # Business logic & caching
├── database/
│   ├── migrations/
│   │   ├── 2024_03_22_000001_create_sidebars_table.php
│   │   └── 2024_12_08_000001_add_soft_deletes_to_sidebars_table.php
│   └── seeders/
│       ├── SidebarDatabaseSeeder.php      # Main seeder
│       ├── SidebarPermissionSeeder.php    # Permission seeder
│       └── SidebarSeeder.php              # Sample data seeder
├── routes/
│   ├── api.php                            # API routes
│   └── web.php                            # Web routes
├── UPGRADE_SUMMARY.md                     # Upgrade documentation
├── TESTING_GUIDE.md                       # Testing guide
└── README.md                              # This file
```

## 🚀 Installation & Setup

### 1. Run Migrations
```bash
php artisan migrate --path=Modules/Sidebar/database/migrations
```

### 2. Seed Permissions and Sample Data
```bash
php artisan db:seed --class=Modules\\Sidebar\\Database\\Seeders\\SidebarDatabaseSeeder
```

### 3. Clear Cache
```bash
php artisan cache:clear
```

## 📡 API Endpoints

### Public Endpoints (No Authentication)

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/v1/sidebars` | Get all sidebars as tree structure |
| GET | `/api/v1/sidebars/tree` | Get sidebar tree structure |
| GET | `/api/v1/sidebars/{id}` | Get single sidebar by ID |

### Authenticated Endpoints (Requires Authentication)

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/api/v1/auth/sidebars` | Get paginated sidebars with filtering | `sidebar.view` |
| GET | `/api/v1/auth/sidebars/dropdown` | Get dropdown options | `sidebar.view` |
| POST | `/api/v1/auth/sidebars` | Create new sidebar | `sidebar.create` |
| GET | `/api/v1/auth/sidebars/{id}` | Get single sidebar | `sidebar.view` |
| PUT | `/api/v1/auth/sidebars/{id}` | Update sidebar | `sidebar.edit` |
| DELETE | `/api/v1/auth/sidebars/{id}/delete` | Soft delete sidebar | `sidebar.delete` |
| PATCH | `/api/v1/auth/sidebars/{id}/restore` | Restore soft-deleted sidebar | `sidebar.delete` |
| DELETE | `/api/v1/auth/sidebars/{id}` | Permanently delete sidebar | `sidebar.destroy` |

### Bulk Operations

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| DELETE | `/api/v1/auth/sidebars/bulk/delete` | Bulk soft delete | `sidebar.delete` |
| PATCH | `/api/v1/auth/sidebars/bulk/restore` | Bulk restore | `sidebar.delete` |
| DELETE | `/api/v1/auth/sidebars/bulk/destroy` | Bulk permanent delete | `sidebar.destroy` |

## 🔧 Usage Examples

### Using the Facade

```php
use Modules\Sidebar\Facades\Sidebar;

// Get all sidebars as tree structure
$sidebars = Sidebar::getSidebars();

// Get active sidebars
$activeSidebars = Sidebar::getActiveSidebars();

// Find sidebar by ID
$sidebar = Sidebar::findById(1);

// Get dropdown options
$options = Sidebar::getDropdownOptions();

// Clear cache
Sidebar::clearCache();
```

### Using the Service Directly

```php
use Modules\Sidebar\Services\SidebarService;

$sidebarService = app(SidebarService::class);
$sidebars = $sidebarService->getSidebars();
```

### Working with the Model

```php
use Modules\Sidebar\Models\Sidebar;

// Create new sidebar
$sidebar = Sidebar::create([
    'menu_type' => 0,
    'parent_id' => 0,
    'title' => 'Dashboard',
    'name' => 'dashboard',
    'path' => '/dashboard',
    'component' => 'Dashboard',
    'rank' => 1,
    'icon' => 'dashboard',
    'show_link' => true,
    'show_parent' => true,
]);

// Get hierarchical structure
$tree = Sidebar::buildRouterTree(Sidebar::getSidebars()->toArray());

// Get parent-child relationships
$parent = $sidebar->parent;
$children = $sidebar->children;
```

## 🔍 Filtering & Search

The module supports advanced filtering through query parameters:

```bash
# Search by title, name, path, or component
GET /api/v1/auth/sidebars?search=dashboard

# Filter by menu type
GET /api/v1/auth/sidebars?menu_type=0

# Filter by parent
GET /api/v1/auth/sidebars?parent_id=1

# Filter by status
GET /api/v1/auth/sidebars?status=active
GET /api/v1/auth/sidebars?status=hidden
GET /api/v1/auth/sidebars?status=with_children

# Filter by roles
GET /api/v1/auth/sidebars?roles[]=admin&roles[]=editor

# Sorting
GET /api/v1/auth/sidebars?sort_by=title&sort_direction=asc

# Pagination
GET /api/v1/auth/sidebars?limit=20&page=2
```

## 🗄️ Caching System

The Sidebar Module implements intelligent multi-layer caching:

### Cache Strategy
- **Cache Tags**: `sidebars`
- **TTL**: 5 minutes
- **Automatic Invalidation**: Observer-based cache clearing on model changes

### Cache Keys
- `proCMS.sidebars.tree`: Complete sidebar tree structure
- `proCMS.sidebars.active.list`: Active sidebars list
- `proCMS.sidebars.{id}`: Individual sidebar cache
- `proCMS.sidebars.dropdown`: Dropdown options cache

### Cache Management
```php
// Clear sidebar cache manually
Sidebar::clearCache();

// Or using cache tags
Cache::tags(['sidebars'])->flush();
```

## 🔐 Permissions

The module uses the following permissions:

| Permission | Description |
|------------|-------------|
| `sidebar.view` | View sidebars and access read-only operations |
| `sidebar.create` | Create new sidebars |
| `sidebar.edit` | Edit existing sidebars |
| `sidebar.delete` | Soft delete and restore sidebars |
| `sidebar.destroy` | Permanently delete sidebars |

## 🧪 Testing

Comprehensive testing guide available in `TESTING_GUIDE.md`.

### Quick Test Commands
```bash
# Run all sidebar tests
php artisan test --filter=Sidebar

# Test specific functionality
php artisan test --filter=SidebarServiceTest
php artisan test --filter=SidebarApiTest
```

## 📈 Performance

### Optimizations
- **Intelligent Caching**: 5-minute TTL with automatic invalidation
- **Efficient Queries**: Optimized database queries with proper indexing
- **Pagination**: Abuse prevention with maximum 100 items per page
- **Selective Loading**: Only load required fields for API responses

### Expected Performance
- **Cached Requests**: < 50ms response time
- **Database Requests**: < 200ms response time
- **Cache Hit Ratio**: > 80% in normal usage

## 🔄 Migration from Previous Version

If upgrading from a previous version, see `UPGRADE_SUMMARY.md` for detailed migration instructions and breaking changes.

## 🤝 Contributing

When contributing to the Sidebar module:

1. Follow the established patterns from Language and ModelAI modules
2. Maintain backward compatibility where possible
3. Add comprehensive tests for new functionality
4. Update documentation for any API changes
5. Ensure caching strategies are properly implemented

## 📄 License

This module is part of Laravel ProCMS and follows the same licensing terms.
