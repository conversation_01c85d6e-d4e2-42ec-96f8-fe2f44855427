import{aN as t,az as s}from"./index-ZVLuktk4.js";const o=e=>t.request("get","/api/auth/bots",{params:e}),u=e=>t.request("post","/api/auth/bots",{data:s(e)}),n=(e,a)=>t.request("put",`/api/auth/bots/${e}`,{data:s(a)}),l=e=>t.request("delete",`/api/auth/bots/${e}/delete`),p=e=>t.request("delete","/api/auth/bots/bulk/delete",{data:s(e)}),d=e=>t.request("delete",`/api/auth/bots/${e}/force`),b=e=>t.request("delete","/api/auth/bots/bulk/force",{data:s(e)}),i=e=>t.request("put",`/api/auth/bots/${e}/restore`),c=e=>t.request("put","/api/auth/bots/bulk/restore",{data:s(e)}),h=e=>t.request("get","/api/auth/bot-general-prompt",{params:e}),g=e=>t.request("get","/api/auth/knowledge-bases/files",{params:e}),q=e=>t.request("delete","/api/auth/knowledge-bases/files/remove",{data:e});export{b as a,c as b,p as c,d,l as e,u as f,o as g,g as h,q as i,h as j,i as r,n as u};
