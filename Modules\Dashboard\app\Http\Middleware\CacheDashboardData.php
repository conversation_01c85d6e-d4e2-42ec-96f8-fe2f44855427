<?php

namespace Modules\Dashboard\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CacheDashboardData
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only cache for authenticated users
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();
        $cacheKey = "dashboard_data_user_{$user->id}";
        $cacheTtl = 300; // 5 minutes

        // Check if we have cached data
        if (Cache::has($cacheKey)) {
            $cachedData = Cache::get($cacheKey);
            
            return response()->json([
                'success' => true,
                'message' => 'Dashboard data retrieved successfully (cached)',
                'data' => $cachedData,
                'cached' => true,
                'cache_expires_at' => Cache::get($cacheKey . '_expires')
            ]);
        }

        // Process the request
        $response = $next($request);

        // Cache the response if it's successful
        if ($response->isSuccessful()) {
            $responseData = json_decode($response->getContent(), true);
            
            if (isset($responseData['success']) && $responseData['success'] && isset($responseData['data'])) {
                Cache::put($cacheKey, $responseData['data'], $cacheTtl);
                Cache::put($cacheKey . '_expires', now()->addSeconds($cacheTtl)->toISOString(), $cacheTtl);
                
                // Add cache info to response
                $responseData['cached'] = false;
                $responseData['cache_expires_at'] = now()->addSeconds($cacheTtl)->toISOString();
                
                $response->setContent(json_encode($responseData));
            }
        }

        return $response;
    }

    /**
     * Clear dashboard cache for a specific user
     */
    public static function clearUserCache(int $userId): void
    {
        $cacheKey = "dashboard_data_user_{$userId}";
        Cache::forget($cacheKey);
        Cache::forget($cacheKey . '_expires');
    }

    /**
     * Clear all dashboard caches
     */
    public static function clearAllCaches(): void
    {
        // This would require a more sophisticated cache tagging system
        // For now, we'll just clear the current user's cache
        if (Auth::check()) {
            self::clearUserCache(Auth::id());
        }
    }
}
