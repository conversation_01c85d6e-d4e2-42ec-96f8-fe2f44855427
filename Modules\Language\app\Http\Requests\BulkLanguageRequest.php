<?php

namespace Modules\Language\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class BulkLanguageRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                'exists:languages,id',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('Language IDs'),
            'ids.*' => __('Language ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Please select at least one language.'),
            'ids.array' => __('Invalid data format.'),
            'ids.min' => __('Please select at least one language.'),
            'ids.*.required' => __('Language ID is required.'),
            'ids.*.integer' => __('Language ID must be an integer.'),
            'ids.*.exists' => __('One or more selected languages must be in trash to be permanently deleted.'),
        ];
    }
}
