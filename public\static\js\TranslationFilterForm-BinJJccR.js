import{q as F,r as R,e as y,G as n,Q as w,n as M,N as l,E as e,k as a,a8 as o,P as p,M as m,D as j}from"./index-ZVLuktk4.js";const D={class:"flex justify-end gap-4"},G=F({__name:"TranslationFilterForm",props:{visible:{type:Boolean,default:!1},values:{default:()=>({})}},emits:["update:visible","update:values","submit","reset"],setup(g,{emit:k}){const f=g,d=k,C=R(),b=y({get:()=>f.visible,set:v=>d("update:visible",v)}),t=y({get:()=>f.values||{},set:v=>d("update:values",v)}),x=()=>{d("submit",t.value),d("update:visible",!1)},S=()=>{d("reset"),d("update:visible",!1)},V=()=>{d("update:visible",!1)};return(v,u)=>{const _=n("el-input"),i=n("el-form-item"),r=n("el-option"),T=n("el-select"),h=n("el-radio"),U=n("el-radio-group"),N=n("el-form"),c=n("el-button"),B=n("el-dialog");return M(),w(B,{modelValue:b.value,"onUpdate:modelValue":u[5]||(u[5]=s=>b.value=s),title:a(o)("Filter Translations"),width:"500px",onClose:V},{footer:l(()=>[j("div",D,[e(c,{onClick:V},{default:l(()=>[p(m(a(o)("Cancel")),1)]),_:1}),e(c,{onClick:S},{default:l(()=>[p(m(a(o)("Reset")),1)]),_:1}),e(c,{type:"primary",onClick:x},{default:l(()=>[p(m(a(o)("Filter")),1)]),_:1})])]),default:l(()=>[e(N,{ref_key:"formRef",ref:C,model:t.value,"label-width":"100px","label-position":"right"},{default:l(()=>[e(i,{label:a(o)("Key")},{default:l(()=>[e(_,{modelValue:t.value.key,"onUpdate:modelValue":u[0]||(u[0]=s=>t.value.key=s),placeholder:a(o)("Search by key")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(i,{label:a(o)("Value")},{default:l(()=>[e(_,{modelValue:t.value.value,"onUpdate:modelValue":u[1]||(u[1]=s=>t.value.value=s),placeholder:a(o)("Search by value")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(i,{label:a(o)("Group")},{default:l(()=>[e(_,{modelValue:t.value.group,"onUpdate:modelValue":u[2]||(u[2]=s=>t.value.group=s),placeholder:a(o)("Search by group")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(i,{label:a(o)("Locale")},{default:l(()=>[e(T,{modelValue:t.value.locale,"onUpdate:modelValue":u[3]||(u[3]=s=>t.value.locale=s),placeholder:a(o)("Select locale"),clearable:""},{default:l(()=>[e(r,{label:"All",value:""}),e(r,{label:"English",value:"en"}),e(r,{label:"Vietnamese",value:"vi"}),e(r,{label:"Chinese",value:"zh"}),e(r,{label:"Japanese",value:"ja"})]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),e(i,{label:a(o)("Trashed")},{default:l(()=>[e(U,{modelValue:t.value.isTrashed,"onUpdate:modelValue":u[4]||(u[4]=s=>t.value.isTrashed=s)},{default:l(()=>[e(h,{value:"no"},{default:l(()=>[p(m(a(o)("No")),1)]),_:1}),e(h,{value:"yes"},{default:l(()=>[p(m(a(o)("Yes")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"])]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}});export{G as default};
