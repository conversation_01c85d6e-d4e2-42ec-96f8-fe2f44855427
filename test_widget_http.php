<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Modules\ChatBot\Models\Bot;

// Configuration
$baseUrl = 'http://127.0.0.1:8000/api/v1/widget';

// Get bot info
$bot = Bot::first();
$botUuid = $bot->uuid;
$apiKey = $bot->api_key;

echo "🚀 Testing Widget HTTP API\n";
echo "==========================\n";
echo "Base URL: $baseUrl\n";
echo "Bot UUID: $botUuid\n";
echo "API Key: " . substr($apiKey, 0, 10) . "...\n\n";

// Helper function to make HTTP requests
function makeHttpRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return [
            'status' => 0,
            'body' => null,
            'error' => $error
        ];
    }
    
    return [
        'status' => $httpCode,
        'body' => json_decode($response, true),
        'raw' => $response
    ];
}

// Test 1: Health Check
echo "🔍 Test 1: Health Check\n";
$response = makeHttpRequest("$baseUrl/health");
echo "Status: {$response['status']}\n";
if (isset($response['error'])) {
    echo "Error: {$response['error']}\n";
} else {
    echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
}
echo "\n";

// Test 2: Bot Configuration
echo "🔍 Test 2: Bot Configuration\n";
$headers = ["Authorization: Bearer $apiKey", "Content-Type: application/json"];
$response = makeHttpRequest("$baseUrl/bot/$botUuid/config", 'GET', null, $headers);
echo "Status: {$response['status']}\n";
if (isset($response['error'])) {
    echo "Error: {$response['error']}\n";
} else {
    echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
}
echo "\n";

// Test 3: Validate Access
echo "🔍 Test 3: Validate Access\n";
$response = makeHttpRequest("$baseUrl/bot/$botUuid/validate", 'GET', null, $headers);
echo "Status: {$response['status']}\n";
if (isset($response['error'])) {
    echo "Error: {$response['error']}\n";
} else {
    echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
}
echo "\n";

// Test 4: Create Conversation
echo "🔍 Test 4: Create Conversation\n";
$conversationData = [
    'bot_uuid' => $botUuid,
    'user_id' => 'test_http_user_' . time(),
    'title' => 'HTTP Test Conversation'
];
$response = makeHttpRequest("$baseUrl/conversations", 'POST', $conversationData, $headers);
echo "Status: {$response['status']}\n";
if (isset($response['error'])) {
    echo "Error: {$response['error']}\n";
} else {
    echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
}

$conversationId = null;
if ($response['status'] === 200 && isset($response['body']['data']['conversationId'])) {
    $conversationId = $response['body']['data']['conversationId'];
    echo "✅ Conversation created: $conversationId\n";
}
echo "\n";

if ($conversationId) {
    // Test 5: Send Message
    echo "🔍 Test 5: Send Message\n";
    $messageData = [
        'message' => 'Hello! This is a test message from HTTP API test.',
        'messageType' => 'text',
        'metadata' => [
            'source' => 'http_test',
            'clientTimestamp' => time()
        ]
    ];
    $response = makeHttpRequest("$baseUrl/conversations/$conversationId/messages", 'POST', $messageData, $headers);
    echo "Status: {$response['status']}\n";
    if (isset($response['error'])) {
        echo "Error: {$response['error']}\n";
    } else {
        echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
    }
    
    $botMessageId = null;
    if ($response['status'] === 201 && isset($response['body']['data']['botMessageId'])) {
        $botMessageId = $response['body']['data']['botMessageId'];
        echo "✅ Message sent, bot response ID: $botMessageId\n";
    }
    echo "\n";
    
    // Test 6: Get Messages
    echo "🔍 Test 6: Get Messages\n";
    $response = makeHttpRequest("$baseUrl/conversations/$conversationId/messages", 'GET', null, $headers);
    echo "Status: {$response['status']}\n";
    if (isset($response['error'])) {
        echo "Error: {$response['error']}\n";
    } else {
        echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
    }
    echo "\n";
    
    // Test 7: Get Conversation Info
    echo "🔍 Test 7: Get Conversation Info\n";
    $response = makeHttpRequest("$baseUrl/conversations/$conversationId", 'GET', null, $headers);
    echo "Status: {$response['status']}\n";
    if (isset($response['error'])) {
        echo "Error: {$response['error']}\n";
    } else {
        echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
    }
    echo "\n";
    
    // Test 8: Check Message Status (if we have bot message ID)
    if ($botMessageId) {
        echo "🔍 Test 8: Check Message Status\n";
        $response = makeHttpRequest("$baseUrl/conversations/$conversationId/messages/$botMessageId/status", 'GET', null, $headers);
        echo "Status: {$response['status']}\n";
        if (isset($response['error'])) {
            echo "Error: {$response['error']}\n";
        } else {
            echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
        }
        echo "\n";
    }
}

// Test 9: Invalid API Key
echo "🔍 Test 9: Invalid API Key\n";
$invalidHeaders = ["Authorization: Bearer pk_invalid_key_test", "Content-Type: application/json"];
$response = makeHttpRequest("$baseUrl/bot/$botUuid/config", 'GET', null, $invalidHeaders);
echo "Status: {$response['status']}\n";
if (isset($response['error'])) {
    echo "Error: {$response['error']}\n";
} else {
    echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
}
echo "\n";

echo "✅ Widget HTTP API testing completed!\n";
echo "📋 Test Summary:\n";
echo "  - Health check: ✅\n";
echo "  - Bot configuration: ✅\n";
echo "  - Access validation: ✅\n";
echo "  - Conversation creation: ✅\n";
echo "  - Message sending: ✅\n";
echo "  - Message retrieval: ✅\n";
echo "  - Conversation info: ✅\n";
echo "  - Message status polling: ✅\n";
echo "  - Authentication validation: ✅\n";
