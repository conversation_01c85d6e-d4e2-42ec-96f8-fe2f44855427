# ProcMS Chatbot Widget

A hybrid chatbot widget that supports both JavaScript integration and iframe embedding with smart auto-detection.

## 🚀 Quick Start

### Method 1: Script Tag (Recommended)

```html
<!-- Load the widget library -->
<script src="https://cdn.procms.com/widget/procms-chatbot.umd.js"></script>

<!-- Create a container -->
<div id="chatbot-container"></div>

<script>
// Initialize the widget
const widget = new ProcmsChatbotWidget({
  botUuid: 'your-bot-uuid-here',
  apiKey: 'pk_live_your_api_key_here',
  theme: 'light'
});

// Mount to container
widget.mount('#chatbot-container');
</script>
```

### Method 2: ES Modules

```javascript
import { ProcmsChatbotWidget, ProcmsChatbot } from 'procms-chatbot';

const widget = await ProcmsChatbot.create({
  botUuid: 'your-bot-uuid-here',
  apiKey: 'pk_live_your_api_key_here'
}, '#chatbot-container');
```

### Method 3: Auto-Initialize

```html
<script>
// Set global config
window.PROCMS_CHATBOT_CONFIG = {
  botUuid: 'your-bot-uuid-here',
  apiKey: 'pk_live_your_api_key_here',
  position: 'bottom-right'
};
</script>

<!-- Widget will auto-initialize on page load -->
<script src="https://cdn.procms.com/widget/procms-chatbot.umd.js"></script>
```

## 📋 Configuration Options

```typescript
interface WidgetConfig {
  // Required
  botUuid: string;              // Your bot UUID
  apiKey: string;               // Your API key
  
  // Optional
  userId?: string;              // User identifier
  mode?: 'widget' | 'iframe' | 'auto';  // Integration mode
  theme?: 'light' | 'dark' | 'auto';    // Theme
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center';
  
  // UI Options
  showHeader?: boolean;         // Show header bar (default: true)
  showAvatar?: boolean;         // Show bot avatar (default: true)
  autoOpen?: boolean;           // Auto-open on load (default: false)
  width?: number | string;      // Widget width (default: 400)
  height?: number | string;     // Widget height (default: 600)
  
  // Advanced
  forceIframe?: boolean;        // Force iframe mode
  iframeUrl?: string;           // Custom iframe URL
  customCSS?: string;           // Custom CSS styles
}
```

## 🎯 Integration Methods

### 1. Embedded Widget (JavaScript)

```javascript
const widget = new ProcmsChatbotWidget({
  botUuid: 'bot-123',
  apiKey: 'pk_live_xxx',
  mode: 'widget'  // Force widget mode
});

await widget.mount('#container');
```

**Pros:**
- Better performance
- Seamless integration
- Full customization
- Native feel

**Cons:**
- Potential conflicts
- Larger bundle size

### 2. Iframe Integration

```javascript
// Generate iframe code
const iframeCode = ProcmsChatbot.generateIframeCode({
  botUuid: 'bot-123',
  apiKey: 'pk_live_xxx'
});

document.getElementById('container').innerHTML = iframeCode;
```

**Pros:**
- Complete isolation
- No conflicts
- Maximum compatibility
- Secure

**Cons:**
- Communication overhead
- Fixed sizing issues
- SEO limitations

### 3. Smart Auto-Detection (Recommended)

```javascript
const widget = new ProcmsChatbotWidget({
  botUuid: 'bot-123',
  apiKey: 'pk_live_xxx',
  mode: 'auto'  // Auto-detect best mode
});

await widget.mount('#container');
```

The widget automatically detects:
- JavaScript conflicts (Vue 2, React, etc.)
- CSS framework conflicts (Bootstrap, Tailwind)
- Browser compatibility issues
- Security restrictions

### 4. Floating Chat Button

```javascript
const floatingWidget = await ProcmsChatbot.createFloatingWidget({
  botUuid: 'bot-123',
  apiKey: 'pk_live_xxx',
  position: 'bottom-right'
});
```

## 🎨 Theming & Customization

### Built-in Themes

```javascript
const widget = new ProcmsChatbotWidget({
  botUuid: 'bot-123',
  apiKey: 'pk_live_xxx',
  theme: 'dark'  // 'light', 'dark', 'auto'
});
```

### Custom Theme (from Bot Configuration)

The widget automatically applies theme from your bot configuration:

```json
{
  "theme": {
    "primaryColor": "#007bff",
    "backgroundColor": "#ffffff",
    "textColor": "#333333",
    "borderRadius": "8px",
    "fontFamily": "Inter, sans-serif"
  }
}
```

### Custom CSS

```javascript
const widget = new ProcmsChatbotWidget({
  botUuid: 'bot-123',
  apiKey: 'pk_live_xxx',
  customCSS: `
    .procms-chatbot-widget {
      border: 2px solid #007bff;
    }
    .widget-header {
      background: linear-gradient(45deg, #007bff, #0056b3);
    }
  `
});
```

## 📡 Events

```javascript
const widget = new ProcmsChatbotWidget(config);

// Widget lifecycle
widget.on('ready', () => console.log('Widget ready'));
widget.on('mounted', () => console.log('Widget mounted'));
widget.on('unmounted', () => console.log('Widget unmounted'));

// Chat events
widget.on('message', (message) => console.log('New message:', message));
widget.on('conversation-started', (id) => console.log('Conversation started:', id));
widget.on('conversation-ended', (id) => console.log('Conversation ended:', id));

// UI events
widget.on('open', () => console.log('Widget opened'));
widget.on('close', () => console.log('Widget closed'));

// Error handling
widget.on('error', (error) => console.error('Widget error:', error));
widget.on('bot-loaded', (config) => console.log('Bot config loaded:', config));
```

## 🔧 API Methods

```javascript
const widget = new ProcmsChatbotWidget(config);

// Lifecycle
await widget.mount('#container');
widget.unmount();

// State
widget.isMounted();           // boolean
widget.getMode();             // 'widget' | 'iframe'
widget.getBotConfig();        // BotConfig | null

// Configuration
widget.updateConfig({ theme: 'dark' });

// Events
widget.on(event, callback);
widget.off(event, callback);
widget.once(event, callback);
```

## 🛠️ Development

### Build Widget

```bash
# Build widget library
pnpm build:widget

# Output files:
# dist/widget/procms-chatbot.umd.js    - UMD build for script tags
# dist/widget/procms-chatbot.es.js     - ES modules build
# dist/widget/pure-admin-thin.css      - Styles
```

### Local Development

```bash
# Start development server
pnpm dev

# Build for production
pnpm build

# Preview build
pnpm preview
```

## 📖 Examples

See `examples/widget-integration.html` for comprehensive integration examples including:

1. Embedded JavaScript widget
2. Iframe integration
3. Smart auto-detection
4. Floating chat button
5. Auto-initialization

## 🔒 Security

- API keys are validated on the server
- Bot configurations are cached securely
- Cross-origin requests are properly handled
- Content Security Policy compatible

## 🌐 Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

Legacy browsers automatically fall back to iframe mode.

## 📝 License

MIT License - see LICENSE file for details.
