<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Sidebar\Http\Controllers\SidebarController;
use Modules\Sidebar\Http\Controllers\Auth\SidebarController as AuthSidebarController;

// Public API routes (no authentication required)
Route::prefix('v1')->group(function () {
    Route::get('sidebars', [SidebarController::class, 'index'])->name('api.sidebar.index');
    Route::get('sidebars/tree', [SidebarController::class, 'tree'])->name('api.sidebar.tree');
    Route::get('sidebars/{id}', [SidebarController::class, 'show'])->name('api.sidebar.show');
});

// Authenticated API routes (admin/auth)
Route::middleware(['auth:api'])->prefix('v1/auth')->group(function () {
    // Sidebar dropdown
    Route::get('sidebars/dropdown', [AuthSidebarController::class, 'dropdown'])->name('auth.sidebar.dropdown');

    // Standard CRUD routes
    Route::get('sidebars', [AuthSidebarController::class, 'index'])->name('auth.sidebar.index');
    Route::post('sidebars', [AuthSidebarController::class, 'store'])->name('auth.sidebar.store');
    Route::get('sidebars/{id}', [AuthSidebarController::class, 'show'])->name('auth.sidebar.show');
    Route::put('sidebars/{id}', [AuthSidebarController::class, 'update'])->name('auth.sidebar.update');
    Route::patch('sidebars/{id}', [AuthSidebarController::class, 'update'])->name('auth.sidebar.update');

    // Bulk operations
    Route::delete('sidebars/bulk/delete', [AuthSidebarController::class, 'bulkDelete'])->name('auth.sidebar.bulk-delete');
    Route::delete('sidebars/bulk/destroy', [AuthSidebarController::class, 'bulkDestroy'])->name('auth.sidebar.bulk-destroy');
    Route::put('sidebars/bulk/restore', [AuthSidebarController::class, 'bulkRestore'])->name('auth.sidebar.bulk-restore');

    // Delete operations
    Route::delete('sidebars/{id}/delete', [AuthSidebarController::class, 'delete'])->name('auth.sidebar.delete');
    Route::delete('sidebars/{id}/destroy', [AuthSidebarController::class, 'destroy'])->name('auth.sidebar.destroy');
    Route::put('sidebars/{id}/restore', [AuthSidebarController::class, 'restore'])->name('auth.sidebar.restore');
});
