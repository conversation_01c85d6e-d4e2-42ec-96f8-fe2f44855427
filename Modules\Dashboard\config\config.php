<?php

return [
    'name' => 'Dashboard',

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Configure caching behavior for dashboard data
    |
    */
    'cache' => [
        'enabled' => env('DASHBOARD_CACHE_ENABLED', true),
        'ttl' => env('DASHBOARD_CACHE_TTL', 300), // 5 minutes
        'prefix' => 'dashboard_',
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Settings
    |--------------------------------------------------------------------------
    |
    | Default configuration for dashboard behavior
    |
    */
    'defaults' => [
        'period' => 'week',
        'timezone' => 'UTC',
        'include_charts' => true,
        'include_stats' => true,
        'currency' => 'USD',
    ],

    /*
    |--------------------------------------------------------------------------
    | Chart Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for chart colors and styling
    |
    */
    'charts' => [
        'colors' => [
            'primary' => '#3B82F6',
            'secondary' => '#10B981',
            'tertiary' => '#8B5CF6',
            'warning' => '#F59E0B',
            'danger' => '#EF4444',
        ],
        'background_opacity' => 0.1,
        'tension' => 0.4,
    ],

    /*
    |--------------------------------------------------------------------------
    | Storage Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for storage quota and limits
    |
    */
    'storage' => [
        'default_quota_mb' => env('DASHBOARD_DEFAULT_QUOTA_MB', 1000),
        'warning_threshold' => env('DASHBOARD_STORAGE_WARNING_THRESHOLD', 80), // 80%
        'critical_threshold' => env('DASHBOARD_STORAGE_CRITICAL_THRESHOLD', 95), // 95%
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Rate limiting configuration for dashboard endpoints
    |
    */
    'rate_limiting' => [
        'enabled' => env('DASHBOARD_RATE_LIMITING_ENABLED', true),
        'max_attempts' => env('DASHBOARD_RATE_LIMIT_ATTEMPTS', 60),
        'decay_minutes' => env('DASHBOARD_RATE_LIMIT_DECAY', 1),
    ],

    /*
    |--------------------------------------------------------------------------
    | Real-time Updates
    |--------------------------------------------------------------------------
    |
    | Configuration for real-time dashboard updates
    |
    */
    'realtime' => [
        'enabled' => env('DASHBOARD_REALTIME_ENABLED', false),
        'broadcast_channel' => 'dashboard.{userId}',
        'update_interval' => env('DASHBOARD_UPDATE_INTERVAL', 30), // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Export Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for dashboard data export
    |
    */
    'export' => [
        'enabled' => env('DASHBOARD_EXPORT_ENABLED', true),
        'formats' => ['json', 'csv', 'pdf'],
        'max_records' => env('DASHBOARD_EXPORT_MAX_RECORDS', 10000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Permissions
    |--------------------------------------------------------------------------
    |
    | Permission configuration for dashboard access
    |
    */
    'permissions' => [
        'view_dashboard' => 'dashboard.view',
        'export_dashboard' => 'dashboard.export',
        'admin_dashboard' => 'dashboard.admin',
    ],
];
