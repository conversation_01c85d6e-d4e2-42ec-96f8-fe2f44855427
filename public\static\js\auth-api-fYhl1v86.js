import{aN as t,az as a}from"./index-ZVLuktk4.js";const d=e=>t.request("get","/api/auth/sidebars",{params:e}),u=()=>t.request("get","/api/auth/sidebars/dropdown"),i=e=>t.request("post","/api/auth/sidebars",{data:a(e)}),n=(e,r)=>t.request("put",`/api/auth/sidebars/${e}`,{data:a(r)}),o=e=>t.request("delete",`/api/auth/sidebars/${e}/delete`),b=e=>t.request("delete","/api/auth/sidebars/bulk/delete",{data:a(e)});export{u as a,b,i as c,o as d,d as g,n as u};
