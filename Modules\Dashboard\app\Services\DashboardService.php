<?php

namespace Modules\Dashboard\Services;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Models\KnowledgeBase;

class DashboardService
{
    /**
     * Get dashboard statistics for the authenticated user
     */
    public function getDashboardStats(): array
    {
        $user = Auth::user();

        return [
            'stats' => [
                'chatbot' => $this->getChatbotStats($user),
                'conversation' => $this->getConversationStats($user),
                'token' => $this->getTokenStats($user),
                'knowledge' => $this->getKnowledgeStats($user),
                'storage' => $this->getStorageStats($user)
            ],
            'charts' => [
                'tokenTrend' => $this->getTokenTrend($user),
                'conversationTrend' => $this->getConversationTrend($user)
            ]
        ];
    }

    /**
     * Get chatbot statistics
     */
    private function getChatbotStats($user): array
    {
        $total = Bot::where('owner_id', $user->id)
            ->where('owner_type', get_class($user))
            ->count();

        $active = Bot::where('owner_id', $user->id)
            ->where('owner_type', get_class($user))
            ->where('status', 'active')
            ->count();

        $draft = Bot::where('owner_id', $user->id)
            ->where('owner_type', get_class($user))
            ->where('status', 'draft')
            ->count();

        // Get most used bot based on conversation count
        $mostUsedBot = Bot::where('owner_id', $user->id)
            ->where('owner_type', get_class($user))
            ->withCount('conversations')
            ->orderBy('conversations_count', 'desc')
            ->value('name') ?? 'N/A';

        return [
            'total' => $total,
            'active' => $active,
            'draft' => $draft,
            'mostUsedBot' => $mostUsedBot
        ];
    }

    /**
     * Get conversation statistics
     */
    private function getConversationStats($user): array
    {
        $total = Conversation::where('owner_id', $user->id)
            ->where('owner_type', get_class($user))
            ->count();

        $today = Conversation::where('owner_id', $user->id)
            ->where('owner_type', get_class($user))
            ->whereDate('created_at', Carbon::today())
            ->count();

        $week = Conversation::where('owner_id', $user->id)
            ->where('owner_type', get_class($user))
            ->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])
            ->count();

        // Calculate average conversation length (messages per conversation)
        $avgConversationLength = Conversation::where('owner_id', $user->id)
            ->where('owner_type', get_class($user))
            ->avg('message_count') ?? 0;

        return [
            'total' => $total,
            'today' => $today,
            'week' => $week,
            'avgConversationLength' => round($avgConversationLength, 1)
        ];
    }

    /**
     * Get token usage statistics
     */
    private function getTokenStats($user): array
    {
        // Get total tokens from messages in user's conversations
        $totalTokens = Message::whereHas('conversation', function($query) use ($user) {
                $query->where('owner_id', $user->id)
                      ->where('owner_type', get_class($user));
            })
            ->sum('total_tokens') ?? 0;

        // Get estimated cost from messages in user's conversations
        $estimatedCost = Message::whereHas('conversation', function($query) use ($user) {
                $query->where('owner_id', $user->id)
                      ->where('owner_type', get_class($user));
            })
            ->sum('cost') ?? 0;

        return [
            'total' => $totalTokens,
            'estimatedCost' => number_format($estimatedCost, 2)
        ];
    }

    /**
     * Get knowledge base statistics
     */
    private function getKnowledgeStats($user): array
    {
        $totalDocuments = KnowledgeBase::where('owner_id', $user->id)
            ->where('owner_type', get_class($user))
            ->count();

        // Get file types distribution based on metadata
        $fileTypesQuery = KnowledgeBase::where('owner_id', $user->id)
            ->where('owner_type', get_class($user))
            ->whereNotNull('metadata')
            ->get();

        $fileTypes = [
            'pdf' => 0,
            'docx' => 0,
            'txt' => 0,
            'other' => 0
        ];

        foreach ($fileTypesQuery as $kb) {
            $metadata = $kb->metadata ?? [];
            $mimeType = $metadata['mime_type'] ?? '';

            if (str_contains($mimeType, 'pdf')) {
                $fileTypes['pdf']++;
            } elseif (str_contains($mimeType, 'word') || str_contains($mimeType, 'docx')) {
                $fileTypes['docx']++;
            } elseif (str_contains($mimeType, 'text') || str_contains($mimeType, 'txt')) {
                $fileTypes['txt']++;
            } else {
                $fileTypes['other']++;
            }
        }

        // Get most queried document (based on usage in conversations)
        $mostQueriedDoc = KnowledgeBase::where('owner_id', $user->id)
            ->where('owner_type', get_class($user))
            ->withCount(['conversations'])
            ->orderBy('conversations_count', 'desc')
            ->value('name') ?? 'N/A';

        return [
            'totalDocuments' => $totalDocuments,
            'fileTypes' => $fileTypes,
            'mostQueriedDoc' => $mostQueriedDoc
        ];
    }

    /**
     * Get storage statistics
     */
    private function getStorageStats($user): array
    {
        // Get quota limit from config
        $quotaLimitMB = config('dashboard.storage.default_quota_mb', 1000);

        // Calculate documents size from knowledge bases
        $documentsSizeMB = 0;
        $knowledgeBases = KnowledgeBase::where('owner_id', $user->id)
            ->where('owner_type', get_class($user))
            ->whereNotNull('metadata')
            ->get();

        foreach ($knowledgeBases as $kb) {
            $metadata = $kb->metadata ?? [];
            $fileSize = $metadata['file_size'] ?? 0;
            $documentsSizeMB += $fileSize / (1024 * 1024); // Convert bytes to MB
        }

        // Calculate attachments size from messages
        $attachmentsSizeMB = 0;
        $messages = Message::whereHas('conversation', function($query) use ($user) {
                $query->where('owner_id', $user->id)
                      ->where('owner_type', get_class($user));
            })
            ->whereNotNull('attachments')
            ->get();

        foreach ($messages as $message) {
            $attachments = $message->attachments ?? [];
            foreach ($attachments as $attachment) {
                $size = $attachment['size'] ?? 0;
                $attachmentsSizeMB += $size / (1024 * 1024); // Convert bytes to MB
            }
        }

        $totalUsedMB = $documentsSizeMB + $attachmentsSizeMB;
        $remainingQuotaMB = max(0, $quotaLimitMB - $totalUsedMB);

        return [
            'totalUsedMB' => round($totalUsedMB, 1),
            'documentsSizeMB' => round($documentsSizeMB, 1),
            'attachmentsSizeMB' => round($attachmentsSizeMB, 1),
            'remainingQuotaMB' => round($remainingQuotaMB, 1),
            'quotaLimitMB' => $quotaLimitMB,
            'usagePercent' => round(($totalUsedMB / $quotaLimitMB) * 100, 1)
        ];
    }

    /**
     * Get token usage trend for the last 7 days
     */
    private function getTokenTrend($user): array
    {
        $labels = [];
        $inputTokens = [];
        $outputTokens = [];

        // Get data for last 7 days
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $labels[] = $date->format('D'); // Mon, Tue, etc.

            // Get input tokens (prompt_tokens) for this day
            $dayInputTokens = Message::whereHas('conversation', function($query) use ($user) {
                    $query->where('owner_id', $user->id)
                          ->where('owner_type', get_class($user));
                })
                ->whereDate('created_at', $date)
                ->sum('prompt_tokens') ?? 0;

            // Get output tokens (completion_tokens) for this day
            $dayOutputTokens = Message::whereHas('conversation', function($query) use ($user) {
                    $query->where('owner_id', $user->id)
                          ->where('owner_type', get_class($user));
                })
                ->whereDate('created_at', $date)
                ->sum('completion_tokens') ?? 0;

            $inputTokens[] = $dayInputTokens;
            $outputTokens[] = $dayOutputTokens;
        }

        return [
            'labels' => $labels,
            'input' => $inputTokens,
            'output' => $outputTokens
        ];
    }

    /**
     * Get conversation trend for the last 7 days
     */
    private function getConversationTrend($user): array
    {
        $labels = [];
        $values = [];

        // Get data for last 7 days
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $labels[] = $date->format('D'); // Mon, Tue, etc.

            // Get conversation count for this day
            $dayConversations = Conversation::where('owner_id', $user->id)
                ->where('owner_type', get_class($user))
                ->whereDate('created_at', $date)
                ->count();

            $values[] = $dayConversations;
        }

        return [
            'labels' => $labels,
            'values' => $values
        ];
    }

}
