const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/BotForm-Cggzpy5Z.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/js/auth-api-CublKEkJ.js","static/js/auth-api-L1fufnq1.js","static/js/BotAvatarUpload-Bh2oM-m3.js","static/css/BotAvatarUpload-BRCer00X.css","static/js/BotAdvancedSettings-CH4CNJOI.js","static/css/BotAdvancedSettings-CJ-E8AEZ.css","static/js/BotBasicInfo-C6a1odXX.js","static/css/BotBasicInfo-NnPTbaXb.css","static/js/BotAIBrain-ClmEJzm0.js","static/css/BotAIBrain-D6RVgme9.css","static/js/BotChatInterface-4yBFLBGv.js","static/css/BotChatInterface-g3R5CyXH.css","static/js/BotKnowledgeBase.vue_vue_type_script_setup_true_lang-DVBtQ41X.js","static/css/BotForm-CCYmtK6s.css","static/js/BotFilterForm-B2xrIti6.js","static/js/index-CQnBjWL2.js","static/js/_plugin-vue_export-helper-QGN-qG8u.js","static/css/BotFilterForm-CqjbYAlk.css","static/css/plus-drawer-form-DaizY_En.css"])))=>i.map(i=>d[i]);
var be=Object.defineProperty,xe=Object.defineProperties;var ve=Object.getOwnPropertyDescriptors;var de=Object.getOwnPropertySymbols;var we=Object.prototype.hasOwnProperty,Ce=Object.prototype.propertyIsEnumerable;var ue=(s,i,r)=>i in s?be(s,i,{enumerable:!0,configurable:!0,writable:!0,value:r}):s[i]=r,W=(s,i)=>{for(var r in i||(i={}))we.call(i,r)&&ue(s,r,i[r]);if(de)for(var r of de(i))Ce.call(i,r)&&ue(s,r,i[r]);return s},te=(s,i)=>xe(s,ve(i));var y=(s,i,r)=>new Promise((B,x)=>{var _=g=>{try{h(r.next(g))}catch(m){x(m)}},w=g=>{try{h(r.throw(g))}catch(m){x(m)}},h=g=>g.done?B(g.value):Promise.resolve(g.value).then(_,w);h((r=r.apply(s,i)).next())});import{r as b,g as Be,S as ge,ax as f,a8 as a,ay as E,az as ke,aA as _e,ai as P,aB as De,aC as D,aD as Re,q as ye,e as Pe,aE as Se,Q as he,n as F,aF as Te,N as u,ae,D as V,H as Ae,E as c,k as e,aG as fe,P as Q,M as S,as as Fe,aH as Ve,o as Ee,m as U,aI as He,G as M,I as Y,aJ as v,L as j,A as $e,a5 as ze,a4 as le,aK as me,aL as Ue,aM as pe}from"./index-ZVLuktk4.js";import{b as Me,r as Ie,a as Oe,d as Le,c as Ge,e as Ne,u as Ke,f as We,g as Ye}from"./auth-api-CublKEkJ.js";import{P as je}from"./index-DMMM8XNs.js";import{u as Qe,_ as qe}from"./_plugin-vue_export-helper-QGN-qG8u.js";import"./sortable.esm-7jaD_3Ar.js";import"./epTheme-DzIV6EF-.js";function Je(){const s=b(!1),i=Be({total:0,pageSize:20,currentPage:1,pageSizes:[10,20,50,100,200],background:!0,layout:"total, sizes, prev, pager, next, jumper"}),r=b([]),B=b([]),x=b({sortBy:"createdAt",sortOrder:"asc"}),_=b(!1),w=b(!1),h=b({name:null,description:null,status:"active",logoUrl:null,logo:null,knowledge:{enabled:!0,text:"",newUploads:[],libraryFiles:[],botFiles:[]}}),g=b(),m=b({isTrashed:"no"}),d=()=>y(null,null,function*(){var l,o,n;s.value=!0;try{const t=yield Ye(ke(te(W({},m.value),{order:`${x.value.sortBy}:${x.value.sortOrder}`,page:i.currentPage,limit:i.pageSize})));B.value=_e(t.data),i.total=t.total||((l=t.data)==null?void 0:l.length)||0}catch(t){console.error("Get Bot error:",t),f(((n=(o=t.response)==null?void 0:o.data)==null?void 0:n.message)||(t==null?void 0:t.message)||a("Get failed"),{type:"error"})}finally{s.value=!1}}),H=l=>{r.value=l},q=n=>y(null,[n],function*({prop:l,order:o}){x.value.sortBy=l,x.value.sortOrder=o==="ascending"?"asc":"desc",yield d()}),$=()=>y(null,null,function*(){yield d()}),J=l=>y(null,null,function*(){i.pageSize=l,i.currentPage=1,yield d()}),X=l=>y(null,null,function*(){try{yield E.confirm(a("Are you sure to delete this item?"),a("Warning"),{confirmButtonText:a("OK"),cancelButtonText:a("Cancel"),type:"warning"}),yield I(l)}catch(o){}}),I=l=>y(null,null,function*(){var o,n;try{s.value=!0;const t=yield Ne(l.toString());return t.success?(f(t.message||a("Delete successful"),{type:"success"}),yield d(),!0):(f(t.message||a("Delete failed"),{type:"error"}),!1)}catch(t){return f(((n=(o=t.response)==null?void 0:o.data)==null?void 0:n.message)||(t==null?void 0:t.message)||a("Delete failed"),{type:"error"}),!1}finally{s.value=!1}}),Z=l=>y(null,null,function*(){const o=r.value.map(n=>n.uuid);if(o.length===0){f(a("Please select items to delete"),{type:"warning"});return}try{yield E.confirm(a("Are you sure to delete selected items?"),a("Warning"),{confirmButtonText:a("OK"),cancelButtonText:a("Cancel"),type:"warning"}),(yield T(o))&&(l!=null&&l.clearSelection)&&l.clearSelection()}catch(n){}}),T=l=>y(null,null,function*(){var o,n;try{s.value=!0;const t=yield Ge({ids:l});return t.success?(f(t.message||a("Delete successful"),{type:"success"}),yield d(),!0):(f(t.message||a("Delete failed"),{type:"error"}),!1)}catch(t){return console.error("Bulk delete Bots error:",t),f(((n=(o=t.response)==null?void 0:o.data)==null?void 0:n.message)||a("Delete failed"),{type:"error"}),!1}finally{s.value=!1}}),A=(l,o,n)=>y(null,null,function*(){const t=W({},l);if(l.uuid!=null){const k=yield O(l.uuid,t);return k&&(o.value=[],n&&n(),w.value=!1),k}const p=yield z(t);return p&&(o.value=[],n&&n(),w.value=!1),p}),z=l=>y(null,null,function*(){var o,n;try{s.value=!0;const t=yield We(l);return t.success?(f(t.message||a("Create successful"),{type:"success"}),yield d(),!0):(f((t==null?void 0:t.message)||a("Create failed"),{type:"error"}),!1)}catch(t){return f(((n=(o=t.response)==null?void 0:o.data)==null?void 0:n.message)||(t==null?void 0:t.message)||a("Create failed"),{type:"error"}),!1}finally{s.value=!1}}),O=(l,o)=>y(null,null,function*(){var n,t;try{s.value=!0;const p=yield Ke(l,o);return p.success?(f(p.message||a("Update successful"),{type:"success"}),yield d(),!0):(f((p==null?void 0:p.message)||a("Update failed"),{type:"error"}),!1)}catch(p){return f(((t=(n=p.response)==null?void 0:n.data)==null?void 0:t.message)||(p==null?void 0:p.message)||a("Update failed"),{type:"error"}),!1}finally{s.value=!1}});return{loading:s,filterRef:m,pagination:i,records:B,multipleSelection:r,sort:x,filterVisible:_,drawerVisible:w,drawerValues:h,botFormRef:g,fnGetBots:d,fnHandleCreateBot:z,fnHandleUpdateBot:O,fnHandleDelete:I,fnHandleBulkDelete:T,fnHandleSelectionChange:H,fnHandleSortChange:q,fnHandlePageChange:$,fnHandleSizeChange:J,handleDelete:X,handleBulkDelete:Z,handlePermanentDelete:l=>y(null,null,function*(){try{yield E.confirm(a("Are you sure you want to permanently delete this item? This action cannot be undone."),a("Warning"),{confirmButtonText:a("Confirm"),cancelButtonText:a("Cancel"),type:"warning"}),yield Le(l.uuid.toString()),f(a("Permanently deleted successfully"),{type:"success"}),yield d()}catch(o){o!=="cancel"&&(console.error("Error permanently deleting bot:",o),f(a("Permanent delete failed"),{type:"error"}))}}),handleBulkPermanentDelete:l=>y(null,null,function*(){const o=l||r.value.map(n=>n.uuid);if(o.length===0){f(a("Please select items to permanently delete"),{type:"warning"});return}try{yield E.confirm(a("Are you sure you want to permanently delete selected items? This action cannot be undone."),a("Warning"),{confirmButtonText:a("Confirm"),cancelButtonText:a("Cancel"),type:"warning"}),yield Oe({ids:o}),f(a("Permanently deleted successfully"),{type:"success"}),yield d()}catch(n){n!=="cancel"&&(console.error("Error bulk permanently deleting bots:",n),f(a("Permanent delete failed"),{type:"error"}))}}),handleRestore:l=>y(null,null,function*(){try{yield E.confirm(a("Are you sure you want to restore this item?"),a("Confirm"),{confirmButtonText:a("Confirm"),cancelButtonText:a("Cancel"),type:"info"}),yield Ie(l.uuid.toString()),f(a("Restored successfully"),{type:"success"}),yield d()}catch(o){o!=="cancel"&&(console.error("Error restoring bot:",o),f(a("Restore failed"),{type:"error"}))}}),handleBulkRestore:l=>y(null,null,function*(){const o=l||r.value.map(n=>n.uuid);if(o.length===0){f(a("Please select items to restore"),{type:"warning"});return}try{yield E.confirm(a("Are you sure you want to restore selected items?"),a("Confirm"),{confirmButtonText:a("Confirm"),cancelButtonText:a("Cancel"),type:"info"}),yield Me({ids:o}),f(a("Restored successfully"),{type:"success"}),yield d()}catch(n){n!=="cancel"&&(console.error("Error bulk restoring bots:",n),f(a("Restore failed"),{type:"error"}))}}),handleSubmit:A,handleFilter:l=>y(null,null,function*(){m.value=l,yield d()}),handleReset:()=>{h.value={name:null,description:null,status:"active",logoUrl:null,logo:null,knowledge:{enabled:!0,text:"",newUploads:[],libraryFiles:[],botFiles:[]}},ge(()=>{var l;(l=g==null?void 0:g.value)!=null&&l.resetForm&&g.value.resetForm()})}}}const Xe=[{type:"selection",width:"30px",sortable:!1},{prop:"logo",align:"center",width:90,headerRenderer:()=>"",cellRenderer:({row:s})=>P(De,{size:50,src:s.logoUrl||null,alt:s.name})},{prop:"name",align:"left",sortable:!1,minWidth:210,headerRenderer:()=>a("Bot Name"),cellRenderer:({row:s})=>P("div",{class:"flex flex-col"},[P("div",{class:"font-medium text-gray-900 text-sm line-clamp-2"},s.name||"Untitled Bot"),P("div",{class:"text-sm text-gray-500 mt-1"},s.description)])},{prop:"aiModel",align:"left",width:180,headerRenderer:()=>a("Model AI"),cellRenderer:({row:s})=>{var i;return P("span",{class:"text-sm font-medium text-gray-900"},((i=s.aiModel)==null?void 0:i.name)||"-")}},{prop:"status",align:"left",width:130,headerRenderer:()=>a("Status"),cellRenderer:({row:s})=>{const i={draft:{type:"info",text:"Draft",class:"bg-gray-100 text-gray-800",icon:"ri:draft-line",iconClass:"text-gray-600"},review:{type:"warning",text:"Review",class:"bg-yellow-100 text-yellow-800",icon:"ri:time-line",iconClass:"text-yellow-600"},active:{type:"success",text:"Production",class:"bg-green-100 text-green-800",icon:"ri:checkbox-circle-fill",iconClass:"text-green-600"},paused:{type:"warning",text:"Development",class:"bg-orange-100 text-orange-800",icon:"ri:time-fill",iconClass:"text-orange-600"},banned:{type:"danger",text:"Inactive",class:"bg-red-100 text-red-800",icon:"ri:close-circle-fill",iconClass:"text-red-600"}},r=i[s.status]||i.draft;return P("span",{class:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${r.class}`},[P(D,{icon:r.icon,class:`w-3 h-3 mr-1.5 ${r.iconClass}`}),r.text])}},{prop:"createdAt",align:"left",width:140,headerRenderer:()=>a("Created At"),cellRenderer:({row:s})=>P("span",{class:"text-sm text-gray-600"},Re(s.createdAt).format("YYYY-MM-DD HH:mm"))},{label:"",fixed:"right",width:140,slot:"operation",sortable:!1}],Ze={class:"plus-dialog-body"};var et=ye({name:"PlusDialog",inheritAttrs:!1,__name:"index",props:{modelValue:{type:Boolean,default:!1},confirmText:{default:""},cancelText:{default:""},confirmLoading:{type:Boolean,default:!1},hasFooter:{type:Boolean,default:!0},footerAlign:{default:"right"},top:{default:"15vh"},width:{default:"460px"},title:{default:""}},emits:["update:modelValue","cancel","confirm"],setup(s,{emit:i}){const r=s,B=i,x=Pe(()=>({justifyContent:r.footerAlign==="left"?"flex-start":r.footerAlign==="center"?"center":"flex-end"})),_=b(!1),{t:w}=Qe();Se(()=>{_.value=r.modelValue});const h=()=>{B("confirm")},g=()=>{B("update:modelValue",!1),B("cancel")};return(m,d)=>(F(),he(e(Ve),Fe({modelValue:_.value,"onUpdate:modelValue":d[0]||(d[0]=H=>_.value=H),top:m.top,width:m.width,title:m.title||e(w)("plus.dialog.title"),"close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":!1,"before-close":g,class:"plus-dialog"},m.$attrs),Te({default:u(()=>[V("div",Ze,[ae(m.$slots,"default")])]),_:2},[m.$slots.header?{name:"header",fn:u(()=>[ae(m.$slots,"header")]),key:"0"}:void 0,m.hasFooter?{name:"footer",fn:u(()=>[V("div",{class:"plus-dialog-footer",style:Ae(x.value)},[ae(m.$slots,"footer",{},()=>[c(e(fe),{onClick:g},{default:u(()=>[Q(S(m.cancelText||e(w)("plus.dialog.cancelText")),1)]),_:1}),c(e(fe),{type:"primary",loading:m.confirmLoading,onClick:h},{default:u(()=>[Q(S(m.confirmText||e(w)("plus.dialog.confirmText")),1)]),_:1},8,["loading"])])],4)]),key:"1"}:void 0]),1040,["modelValue","top","width","title"]))}}),tt=qe(et,[["__file","index.vue"]]);const at=tt,lt={class:"main"},nt={class:"ml-2"},st={class:"ml-2"},ot={class:"ml-2"},rt={class:"ml-2"},gt=ye({__name:"index",setup(s){const i=me(()=>pe(()=>import("./BotForm-Cggzpy5Z.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]))),r=me(()=>pe(()=>import("./BotFilterForm-B2xrIti6.js"),__vite__mapDeps([17,1,2,18,19,20,21]))),B=b(),x=b(),_=b(),{loading:w,filterRef:h,pagination:g,records:m,multipleSelection:d,handleBulkDelete:H,handleDelete:q,fnGetBots:$,fnHandlePageChange:J,fnHandleSelectionChange:X,fnHandleSortChange:I,fnHandleSizeChange:Z,filterVisible:T,drawerVisible:A,drawerValues:z,botFormRef:O,handleSubmit:ne,handleFilter:se,handleBulkPermanentDelete:oe,handleBulkRestore:re,handlePermanentDelete:ie,handleRestore:ce,handleReset:l}=Je(),o=n=>{var t,p,k,R,L,G,C,N;z.value=te(W({},Ue(n,!0)),{logoUrl:n.logoUrl||n.logo||null,model:n.aiModel.key,knowledge:{enabled:(p=(t=n.knowledge)==null?void 0:t.enabled)!=null?p:!0,text:(R=(k=n.knowledge)==null?void 0:k.text)!=null?R:"",newUploads:[],libraryFiles:(G=(L=n.knowledge)==null?void 0:L.libraryFiles)!=null?G:[],botFiles:(N=(C=n.knowledge)==null?void 0:C.botFiles)!=null?N:[]}}),A.value=!0};return Ee(()=>{ge(()=>{$()})}),(n,t)=>{const p=M("el-button"),k=M("el-tooltip"),R=M("el-dropdown-item"),L=M("el-dropdown-menu"),G=M("el-dropdown");return F(),U("div",lt,[V("div",{ref_key:"contentRef",ref:x,class:j(["flex",e(ze)()?"flex-wrap":""])},[c(e(je),{class:"!w-full",style:{transition:"width 220ms cubic-bezier(0.4, 0, 0.2, 1)"},title:e(a)("Bot Management"),columns:e(Xe),onRefresh:e($),onFilter:t[4]||(t[4]=C=>T.value=!0)},{buttons:u(()=>[c(k,{content:e(a)("Create new"),placement:"top"},{default:u(()=>[c(p,{type:"text",class:"font-bold text-[16px]",disabled:!e(v)("bot.create"),onClick:t[0]||(t[0]=()=>{e(l)(),A.value=!0})},{default:u(()=>[c(e(D),{icon:e(v)("bot.create")?"flat-color-icons:plus":"icons8:plus",width:"18px"},null,8,["icon"])]),_:1},8,["disabled"])]),_:1},8,["content"]),e(h).isTrashed==="yes"?(F(),U(Y,{key:0},[c(k,{content:e(a)("Restore"),placement:"top"},{default:u(()=>[c(p,{type:"text",class:"font-bold text-[16px]",disabled:e(d).length===0||e(d).length>0&&!e(v)("bot.delete"),onClick:t[1]||(t[1]=()=>e(re)())},{default:u(()=>[c(e(D),{icon:"tabler:restore",width:"18px",class:j({"text-amber-800":e(d).length>0&&e(v)("bot.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"]),c(k,{content:e(a)("Bulk Destroy"),placement:"top"},{default:u(()=>[c(p,{type:"text",class:"font-bold text-[16px]",disabled:e(d).length===0||e(d).length>0&&!e(v)("bot.destroy"),onClick:t[2]||(t[2]=()=>e(oe)())},{default:u(()=>[c(e(D),{icon:"tabler:trash-x-filled",width:"18px",class:j({"text-red-800":e(d).length>0&&e(v)("bot.destroy")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])],64)):(F(),U(Y,{key:1},[e(h).isTrashed==="no"?(F(),he(k,{key:0,content:e(a)("Bulk Delete"),placement:"top"},{default:u(()=>[c(p,{type:"text",class:"font-bold text-[16px]",disabled:e(d).length==0||e(d).length>0&&!e(v)("bot.delete"),onClick:t[3]||(t[3]=()=>e(H)())},{default:u(()=>[c(e(D),{icon:"tabler:trash",width:"18px",class:j({"text-red-800":e(d).length>0&&e(v)("bot.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])):$e("",!0)],64))]),default:u(({size:C,dynamicColumns:N})=>[c(e(He),{ref_key:"tableRef",ref:B,"align-whole":"center","table-layout":"auto",loading:e(w),size:C,adaptive:"",adaptiveConfig:{offsetBottom:108},data:e(m),columns:N,pagination:e(g),"header-cell-style":{background:"var(--el-fill-color-light)",color:"var(--el-text-color-primary)"},onSortChange:e(I),onPageSizeChange:e(Z),onPageCurrentChange:e(J),onSelectionChange:e(X)},{operation:u(({row:K})=>[c(G,{"split-button":"",trigger:"click",size:"small"},{dropdown:u(()=>[c(L,{class:"min-w-[130px]"},{default:u(()=>[c(R,{disabled:""},{default:u(()=>[Q(S(e(a)("Action")),1)]),_:1}),e(h).isTrashed=="no"?(F(),U(Y,{key:0},[c(R,{disabled:!e(v)("bot.edit"),onClick:ee=>o(K)},{default:u(()=>[c(e(D),{icon:"material-symbols:edit",class:"text-blue-800"}),V("span",nt,S(e(a)("Edit")),1)]),_:2},1032,["disabled","onClick"]),c(R,{disabled:!e(v)("bot.delete"),onClick:ee=>e(q)(K)},{default:u(()=>[c(e(D),{icon:"tabler:trash",class:"text-red-800"}),V("span",st,S(e(a)("Delete")),1)]),_:2},1032,["disabled","onClick"])],64)):(F(),U(Y,{key:1},[c(R,{disabled:!e(v)("bot.delete"),onClick:ee=>e(ce)(K)},{default:u(()=>[c(e(D),{icon:"tabler:restore",class:"text-amber-800"}),V("span",ot,S(e(a)("Restore")),1)]),_:2},1032,["disabled","onClick"]),c(R,{disabled:!e(v)("bot.destroy"),onClick:ee=>e(ie)(K)},{default:u(()=>[c(e(D),{icon:"tabler:trash-x",class:"text-red-800"}),V("span",rt,S(e(a)("Destroy")),1)]),_:2},1032,["disabled","onClick"])],64))]),_:2},1024)]),default:u(()=>[Q(S(e(a)("Action"))+" ",1)]),_:2},1024)]),_:2},1032,["loading","size","data","columns","pagination","onSortChange","onPageSizeChange","onPageCurrentChange","onSelectionChange"])]),_:1},8,["title","columns","onRefresh"])],2),c(e(r),{ref:"filterFormRef",visible:e(T),"onUpdate:visible":t[5]||(t[5]=C=>le(T)?T.value=C:null),values:e(h),"onUpdate:values":t[6]||(t[6]=C=>le(h)?h.value=C:null),onSubmit:e(se),onReset:t[7]||(t[7]=()=>{h.value={isTrashed:"no"},e($)()})},null,8,["visible","values","onSubmit"]),c(e(at),{ref_key:"dialogFormRef",ref:_,modelValue:e(A),"onUpdate:modelValue":t[8]||(t[8]=C=>le(A)?A.value=C:null),width:"90%",top:"10px",title:e(a)("Bot Management"),"has-footer":!1,closeOnClickModal:!0,closeOnPressEscape:!0,draggable:!1,destroyOnClose:!0,onClose:e(l)},{default:u(()=>[c(e(i),{ref_key:"botFormRef",ref:O,useBot:{drawerValues:e(z),handleSubmit:e(ne),handleReset:e(l)}},null,8,["useBot"])]),_:1},8,["modelValue","title","onClose"])])}}});export{gt as default};
