var $=(V,i,d)=>new Promise((g,P)=>{var M=b=>{try{k(d.next(b))}catch(U){P(U)}},Z=b=>{try{k(d.throw(b))}catch(U){P(U)}},k=b=>b.done?g(b.value):Promise.resolve(b.value).then(M,Z);k((d=d.apply(V,i)).next())});import{q as Q,B as se,ai as oe,a7 as de,g as q,a8 as r,r as C,ad as ne,e as O,o as ie,m as ue,n as T,D as c,E as a,k as e,G as _,N as s,M as x,Q as ce,A as pe,P as E,aC as D,fI as me,ax as w,fJ as fe,i as ge,fK as he,_ as we}from"./index-ZVLuktk4.js";import{c as ye,a as ve,b as _e,d as be}from"./validation-Db2MkLBC.js";import{u as xe}from"./useLayout-B65ZqU-V.js";import{u as y}from"./hooks-CuzZ-_om.js";import{d as z}from"./user-3-fill-xIOYikcN.js";import{d as R}from"./lock-fill-BZPnsoM7.js";const h=Q({name:"Motion",props:{delay:{type:Number,default:50}},render(){const{delay:V}=this,i=de("motion");return se(oe("div",{},{default:()=>{var d,g;return[(g=(d=this.$slots).default)==null?void 0:g.call(d)]}}),[[i,{initial:{opacity:0,y:100},enter:{opacity:1,y:0,transition:{delay:V}}}]])}}),J=(V,i,d)=>{if(!i||i.trim()===""){d(new Error(r("This field is required")));return}if(i.length<2){d(new Error(r("Name must be at least 2 characters")));return}if(i.length>50){d(new Error(r("Name must not exceed 50 characters")));return}if(!/^[a-zA-ZÀ-ÿ\s\-']+$/.test(i)){d(new Error(r("Name can only contain letters, spaces, hyphens, and apostrophes")));return}d()},Ve=(V,i,d)=>{if(i&&i.length>255){d(new Error(r("Address must not exceed 255 characters")));return}d()},Pe=(V,i,d)=>{if(i){const g=new Date(i),P=new Date,M=P.getFullYear()-g.getFullYear();if(g>P){d(new Error(r("Birthday cannot be in the future")));return}if(M>150){d(new Error(r("Please enter a valid birthday")));return}}d()},Ne=(V,i,d)=>{if(!i||i.trim()===""){d(new Error(r("Current password is required")));return}d()},K=q({firstName:[{validator:J,trigger:"blur"}],lastName:[{validator:J,trigger:"blur"}],email:[{validator:be(),trigger:"blur"}],phone:[{validator:_e({required:!1}),trigger:"blur"}],address:[{validator:Ve,trigger:"blur"}],birthday:[{validator:Pe,trigger:"blur"}],gender:[{required:!1,message:r("Please select gender"),trigger:"change"}],currentPassword:[{validator:Ne,trigger:"blur"}],newPassword:[{validator:ve({minLength:8,requireNumbers:!0,requireSymbols:!1,requireUppercase:!1,requireLowercase:!1,useSettings:!0}),trigger:"blur"}],confirmPassword:[{validator:ye("newPassword"),trigger:"blur"}]}),Ue={width:24,height:24,body:'<path fill="currentColor" d="M3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Zm9.06 8.683L5.648 6.238L4.353 7.762l7.72 6.555l7.581-6.56l-1.308-1.513l-6.285 5.439Z"/>'},Ce={width:24,height:24,body:'<path fill="currentColor" d="M21 16.42v3.536a1 1 0 0 1-.93.998c-.437.03-.794.046-1.07.046c-8.837 0-16-7.163-16-16c0-.276.015-.633.046-1.07A1 1 0 0 1 4.044 3H7.58a.5.5 0 0 1 .498.45c.023.23.044.413.064.552A13.901 13.901 0 0 0 9.35 8.003c.095.2.033.439-.147.567l-2.158 1.542a13.047 13.047 0 0 0 6.844 6.844l1.54-2.154a.462.462 0 0 1 .573-.149a13.897 13.897 0 0 0 4 1.205c.139.02.322.041.55.064a.5.5 0 0 1 .449.498Z"/>'},Me={width:24,height:24,body:'<path fill="currentColor" d="M18.364 17.364L12 23.728l-6.364-6.364a9 9 0 1 1 12.728 0ZM12 15a4 4 0 1 0 0-8a4 4 0 0 0 0 8Zm0-2a2 2 0 1 1 0-4a2 2 0 0 1 0 4Z"/>'},Ee={width:24,height:24,body:'<path fill="currentColor" d="M2 11h20v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-9Zm15-8h4a1 1 0 0 1 1 1v5H2V4a1 1 0 0 1 1-1h4V1h2v2h6V1h2v2Z"/>'},ke={width:24,height:24,body:'<path fill="currentColor" d="M9 3h6l2 2h4a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1h4l2-2Zm3 16a6 6 0 1 0 0-12a6 6 0 0 0 0 12Zm0-2a4 4 0 1 1 0-8a4 4 0 0 1 0 8Z"/>'},Fe={width:24,height:24,body:'<path fill="currentColor" d="M18 21v-8H6v8H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h13l4 4v13a1 1 0 0 1-1 1h-2Zm-2 0H8v-6h8v6Z"/>'},Ze={class:"profile-container"},$e={class:"p-6"},De={class:"w-full max-w-6xl mx-auto"},Ae={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},ze={class:"flex items-center space-x-6"},Re={class:"relative"},qe={class:"flex-1"},Ie={class:"text-3xl font-bold text-gray-900 dark:text-white mb-2"},Le={class:"text-gray-600 dark:text-gray-300 mb-1"},Ye={class:"text-gray-500 dark:text-gray-400"},He={class:"flex items-center mt-3 space-x-4"},Be={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Se={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6"},Ge={class:"text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center"},Oe={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Te={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Je={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6"},Ke={class:"text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center"},Qe=Q({name:"Profile",__name:"index",setup(V){ge();const i=C(!1),d=C(!1),g=C(!1),P=C(!1),M=C(),Z=C(),k=C(),{initStorage:b}=xe();b();const U=ne(),u=O(()=>U.userInfo),W=O(()=>{var m,t,p,l;return(m=u.value)!=null&&m.fullName?u.value.fullName:(t=u.value)!=null&&t.firstName&&((p=u.value)!=null&&p.lastName)?`${u.value.firstName} ${u.value.lastName}`:((l=u.value)==null?void 0:l.username)||r("User Profile")}),o=q({username:"",firstName:"",lastName:"",email:"",phone:"",address:"",birthday:"",gender:"male"}),f=q({currentPassword:"",newPassword:"",confirmPassword:""}),j=()=>{u.value&&(o.username=u.value.username||"",o.firstName=u.value.firstName||"",o.lastName=u.value.lastName||"",o.email=u.value.email||"",o.phone=u.value.phone||"",o.address=u.value.address||"",o.birthday=u.value.birthday||"",o.gender=u.value.gender||"male")},X=m=>$(null,null,function*(){var t,p;if(m)try{yield m.validate(),i.value=!0;const l=yield me({firstName:o.firstName,lastName:o.lastName,phone:o.phone,address:o.address,birthday:o.birthday,gender:o.gender});if(!l.success){w(l.message,{type:"error"});return}yield U.getUserInfo(),w(l.message||r("Profile updated successfully"),{type:"success"})}catch(l){w(((p=(t=l==null?void 0:l.response)==null?void 0:t.data)==null?void 0:p.message)||(l==null?void 0:l.message)||r("Failed to update profile"),{type:"error"})}finally{i.value=!1}}),ee=m=>$(null,null,function*(){var t,p;if(m)try{yield m.validate(),d.value=!0;const l=yield fe({currentPassword:f.currentPassword,newPassword:f.newPassword,passwordConfirmation:f.confirmPassword});if(!l.success){w(l.message,{type:"error"});return}f.currentPassword="",f.newPassword="",f.confirmPassword="",w(l.message||r("Password updated successfully"),{type:"success"})}catch(l){w(((p=(t=l==null?void 0:l.response)==null?void 0:t.data)==null?void 0:p.message)||(l==null?void 0:l.message)||r("Failed to update password"),{type:"error"})}finally{d.value=!1}}),ae=m=>$(null,null,function*(){var t,p;try{g.value=!0;const l=new FormData;l.append("avatar",m.raw);const F=yield he(l);return F.success?(yield U.getUserInfo(),w(F.message||r("Avatar updated successfully"),{type:"success"}),!0):(w(F.message,{type:"error"}),!1)}catch(l){return w(((p=(t=l==null?void 0:l.response)==null?void 0:t.data)==null?void 0:p.message)||(l==null?void 0:l.message)||r("Failed to update avatar"),{type:"error"}),!1}finally{g.value=!1}}),re=m=>{const t=m.type.startsWith("image/"),p=m.size/1024/1024<2;return t?p?!0:(w(r("Avatar size must be less than 2MB"),{type:"error"}),!1):(w(r("Avatar must be an image"),{type:"error"}),!1)};return ie(()=>{j()}),(m,t)=>{var Y,H,B,S,G;const p=_("el-avatar"),l=_("el-button"),F=_("el-upload"),I=_("el-tag"),N=_("el-input"),v=_("el-form-item"),te=_("el-date-picker"),A=_("el-option"),le=_("el-select"),L=_("el-form");return T(),ue("div",Ze,[c("div",$e,[c("div",De,[c("div",Ae,[c("div",ze,[c("div",Re,[a(p,{size:120,src:(Y=u.value)==null?void 0:Y.avatar,icon:e(y)(e(z)),class:"border-4 border-white shadow-lg"},null,8,["src","icon"]),a(F,{ref_key:"avatarUploadRef",ref:k,"show-file-list":!1,"before-upload":re,"http-request":ae,accept:"image/*",class:"absolute bottom-0 right-0"},{default:s(()=>[a(l,{icon:e(y)(e(ke)),circle:"",size:"small",type:"primary",loading:g.value,class:"shadow-lg"},null,8,["icon","loading"])]),_:1},512)]),c("div",qe,[c("h1",Ie,x(W.value),1),c("p",Le," @"+x((H=u.value)==null?void 0:H.username),1),c("p",Ye,x((B=u.value)==null?void 0:B.email),1),c("div",He,[(S=u.value)!=null&&S.isVerified?(T(),ce(I,{key:0,type:"success",size:"small"},{default:s(()=>[E(x(e(r)("Verified")),1)]),_:1})):pe("",!0),a(I,{type:((G=u.value)==null?void 0:G.status)==="active"?"success":"warning",size:"small"},{default:s(()=>{var n;return[E(x(e(r)(((n=u.value)==null?void 0:n.status)||"Unknown")),1)]}),_:1},8,["type"])])])])]),c("div",Be,[c("div",Se,[c("h2",Ge,[a(e(D),{icon:"ri:user-settings-line",class:"mr-2"}),E(" "+x(e(r)("Profile Information")),1)]),a(L,{ref_key:"profileFormRef",ref:M,model:o,rules:e(K),size:"large","label-position":"top"},{default:s(()=>[c("div",Oe,[a(e(h),{delay:100},{default:s(()=>[a(v,{label:e(r)("First Name"),prop:"firstName"},{default:s(()=>[a(N,{modelValue:o.firstName,"onUpdate:modelValue":t[0]||(t[0]=n=>o.firstName=n),placeholder:e(r)("Enter first name"),"prefix-icon":e(y)(e(z)),clearable:""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1},8,["label"])]),_:1}),a(e(h),{delay:150},{default:s(()=>[a(v,{label:e(r)("Last Name"),prop:"lastName"},{default:s(()=>[a(N,{modelValue:o.lastName,"onUpdate:modelValue":t[1]||(t[1]=n=>o.lastName=n),placeholder:e(r)("Enter last name"),"prefix-icon":e(y)(e(z)),clearable:""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1},8,["label"])]),_:1})]),a(e(h),{delay:200},{default:s(()=>[a(v,{label:e(r)("Email"),prop:"email"},{default:s(()=>[a(N,{modelValue:o.email,"onUpdate:modelValue":t[2]||(t[2]=n=>o.email=n),placeholder:e(r)("Enter email"),"prefix-icon":e(y)(e(Ue)),disabled:"",readonly:""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1},8,["label"])]),_:1}),a(e(h),{delay:250},{default:s(()=>[a(v,{label:e(r)("Phone"),prop:"phone"},{default:s(()=>[a(N,{modelValue:o.phone,"onUpdate:modelValue":t[3]||(t[3]=n=>o.phone=n),placeholder:e(r)("Enter phone number"),"prefix-icon":e(y)(e(Ce)),clearable:""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1},8,["label"])]),_:1}),a(e(h),{delay:300},{default:s(()=>[a(v,{label:e(r)("Address"),prop:"address"},{default:s(()=>[a(N,{modelValue:o.address,"onUpdate:modelValue":t[4]||(t[4]=n=>o.address=n),placeholder:e(r)("Enter address"),"prefix-icon":e(y)(e(Me)),type:"textarea",rows:3},null,8,["modelValue","placeholder","prefix-icon"])]),_:1},8,["label"])]),_:1}),c("div",Te,[a(e(h),{delay:350},{default:s(()=>[a(v,{label:e(r)("Birthday"),prop:"birthday"},{default:s(()=>[a(te,{modelValue:o.birthday,"onUpdate:modelValue":t[5]||(t[5]=n=>o.birthday=n),type:"date",placeholder:e(r)("Select birthday"),"prefix-icon":e(y)(e(Ee)),class:"w-full",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue","placeholder","prefix-icon"])]),_:1},8,["label"])]),_:1}),a(e(h),{delay:400},{default:s(()=>[a(v,{label:e(r)("Gender"),prop:"gender"},{default:s(()=>[a(le,{modelValue:o.gender,"onUpdate:modelValue":t[6]||(t[6]=n=>o.gender=n),placeholder:e(r)("Select gender"),class:"w-full"},{default:s(()=>[a(A,{label:e(r)("Male"),value:"male"},null,8,["label"]),a(A,{label:e(r)("Female"),value:"female"},null,8,["label"]),a(A,{label:e(r)("Other"),value:"other"},null,8,["label"])]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1})]),a(e(h),{delay:450},{default:s(()=>[a(l,{type:"primary",size:"large",loading:i.value,disabled:P.value,class:"w-full",onClick:t[7]||(t[7]=n=>X(M.value))},{default:s(()=>[a(e(D),{icon:e(Fe),class:"mr-2"},null,8,["icon"]),E(" "+x(e(r)("Update Profile")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["model","rules"])]),c("div",Je,[c("h2",Ke,[a(e(D),{icon:"ri:lock-password-line",class:"mr-2"}),E(" "+x(e(r)("Change Password")),1)]),a(L,{ref_key:"passwordFormRef",ref:Z,model:f,rules:e(K),size:"large","label-position":"top"},{default:s(()=>[a(e(h),{delay:100},{default:s(()=>[a(v,{label:e(r)("Current Password"),prop:"currentPassword"},{default:s(()=>[a(N,{modelValue:f.currentPassword,"onUpdate:modelValue":t[8]||(t[8]=n=>f.currentPassword=n),type:"password",placeholder:e(r)("Enter current password"),"prefix-icon":e(y)(e(R)),"show-password":"",clearable:""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1},8,["label"])]),_:1}),a(e(h),{delay:150},{default:s(()=>[a(v,{label:e(r)("New Password"),prop:"newPassword"},{default:s(()=>[a(N,{modelValue:f.newPassword,"onUpdate:modelValue":t[9]||(t[9]=n=>f.newPassword=n),type:"password",placeholder:e(r)("Enter new password"),"prefix-icon":e(y)(e(R)),"show-password":"",clearable:""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1},8,["label"])]),_:1}),a(e(h),{delay:200},{default:s(()=>[a(v,{label:e(r)("Confirm New Password"),prop:"confirmPassword"},{default:s(()=>[a(N,{modelValue:f.confirmPassword,"onUpdate:modelValue":t[10]||(t[10]=n=>f.confirmPassword=n),type:"password",placeholder:e(r)("Confirm new password"),"prefix-icon":e(y)(e(R)),"show-password":"",clearable:""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1},8,["label"])]),_:1}),a(e(h),{delay:250},{default:s(()=>[a(l,{type:"danger",size:"large",loading:d.value,disabled:P.value,class:"w-full",onClick:t[11]||(t[11]=n=>ee(Z.value))},{default:s(()=>[a(e(D),{icon:"ri:key-2-line",class:"mr-2"}),E(" "+x(e(r)("Update Password")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["model","rules"])])])])])])}}}),la=we(Qe,[["__scopeId","data-v-2aace585"]]);export{la as default};
