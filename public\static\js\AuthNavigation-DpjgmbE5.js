import{q as y,m as f,n as o,D as d,Q as s,A as l,N as a,P as n,M as r,k as u,a8 as c,G as m,I as w,J as B,i as C,_ as v}from"./index-ZVLuktk4.js";const L={class:"auth-navigation"},T={class:"flex justify-between items-center"},N={class:"flex gap-2"},F={class:"flex gap-2"},P=y({__name:"AuthNavigation",props:{showLogin:{type:Boolean,default:!0},showRegister:{type:Boolean,default:!0},showForgotPassword:{type:Boolean,default:!1},showBackToLogin:{type:Boolean,default:!1},customLinks:{default:()=>[]}},setup(x){const i=C(),g=()=>{i.push("/login")},h=()=>{i.push("/register")},_=()=>{i.push("/forget")},k=e=>{i.push({path:e.path,query:e.query})};return(e,R)=>{const t=m("el-button");return o(),f("div",L,[d("div",T,[d("div",N,[e.showBackToLogin?(o(),s(t,{key:0,link:"",type:"primary",onClick:g},{default:a(()=>[n(r(u(c)("Back to Login")),1)]),_:1})):l("",!0),e.showForgotPassword?(o(),s(t,{key:1,link:"",type:"primary",onClick:_},{default:a(()=>[n(r(u(c)("Forgot Password?")),1)]),_:1})):l("",!0)]),d("div",F,[e.showLogin?(o(),s(t,{key:0,link:"",type:"primary",onClick:g},{default:a(()=>[n(r(u(c)("Sign In")),1)]),_:1})):l("",!0),e.showRegister?(o(),s(t,{key:1,link:"",type:"primary",onClick:h},{default:a(()=>[n(r(u(c)("Sign Up")),1)]),_:1})):l("",!0),(o(!0),f(w,null,B(e.customLinks,p=>(o(),s(t,{key:p.path,link:"",type:"primary",onClick:b=>k(p)},{default:a(()=>[n(r(p.text),1)]),_:2},1032,["onClick"]))),128))])])])}}}),A=v(P,[["__scopeId","data-v-8fac1818"]]);export{A as default};
