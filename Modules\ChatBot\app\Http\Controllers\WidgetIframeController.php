<?php

namespace Modules\ChatBot\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\BotShareLink;

class WidgetIframeController extends Controller
{
    /**
     * Display the widget iframe page.
     */
    public function index(Request $request): Response
    {
        try {
            // Validate required parameters
            $botUuid = $request->query('bot_uuid');
            $apiKey = $request->query('api_key');
            
            if (!$botUuid) {
                return $this->errorPage('Bot UUID is required');
            }

            // Validate bot access
            $bot = $this->validateBotAccess($request, $botUuid, $apiKey);
            if (!$bot) {
                return $this->errorPage('Bot not found or access denied');
            }

            // Get widget configuration
            $widgetConfig = $this->getWidgetConfig($request, $bot);

            // Return iframe view
            return response()->view('chatbot::widget.iframe', [
                'bot' => $bot,
                'config' => $widgetConfig,
                'apiBaseUrl' => url('/api/v1/widget'),
            ])->header('X-Frame-Options', 'ALLOWALL')
              ->header('Content-Security-Policy', "frame-ancestors *;");

        } catch (\Exception $e) {
            return $this->errorPage('Failed to load widget: ' . $e->getMessage());
        }
    }

    /**
     * Validate bot access for iframe.
     */
    private function validateBotAccess(Request $request, string $botUuid, ?string $apiKey): ?Bot
    {
        // Try API key access
        if ($apiKey && str_starts_with($apiKey, 'pk_')) {
            $bot = Bot::where('uuid', $botUuid)
                     ->where('api_key', $apiKey)
                     ->active()
                     ->first();
            
            if ($bot) {
                return $bot;
            }
        }

        // Try share token access (botUuid might be share token)
        $shareLink = BotShareLink::with(['bot'])
                                ->where('token', $botUuid)
                                ->active()
                                ->first();

        if ($shareLink && $shareLink->bot->canBeShared()) {
            return $shareLink->bot;
        }

        return null;
    }

    /**
     * Get widget configuration from request parameters.
     */
    private function getWidgetConfig(Request $request, Bot $bot): array
    {
        return [
            'botUuid' => $bot->uuid,
            'apiKey' => $request->query('api_key'),
            'userId' => $request->query('user_id'),
            'theme' => $request->query('theme', 'light'),
            'position' => $request->query('position', 'bottom-right'),
            'showHeader' => $request->query('show_header', '1') === '1',
            'showAvatar' => $request->query('show_avatar', '1') === '1',
            'autoOpen' => $request->query('auto_open', '0') === '1',
            'width' => $request->query('width', '400'),
            'height' => $request->query('height', '600'),
            'domain' => $request->query('domain'),
            'referrer' => $request->query('referrer'),
        ];
    }

    /**
     * Return error page for iframe.
     */
    private function errorPage(string $message): Response
    {
        return response()->view('chatbot::widget.error', [
            'message' => $message,
        ])->header('X-Frame-Options', 'ALLOWALL');
    }

    /**
     * Widget health check for iframe.
     */
    public function health(): Response
    {
        return response()->json([
            'status' => 'healthy',
            'timestamp' => now()->toISOString(),
            'version' => '1.0.0',
            'mode' => 'iframe',
        ])->header('X-Frame-Options', 'ALLOWALL');
    }

    /**
     * Get widget configuration as JSON (for iframe initialization).
     */
    public function config(Request $request): Response
    {
        try {
            $botUuid = $request->query('bot_uuid');
            $apiKey = $request->query('api_key');
            
            if (!$botUuid) {
                return response()->json(['error' => 'Bot UUID is required'], 400);
            }

            $bot = $this->validateBotAccess($request, $botUuid, $apiKey);
            if (!$bot) {
                return response()->json(['error' => 'Bot not found or access denied'], 404);
            }

            $config = [
                'bot' => [
                    'uuid' => $bot->uuid,
                    'name' => $bot->name,
                    'description' => $bot->description,
                    'logoUrl' => $bot->logo_url,
                    'greetingMessage' => $bot->greeting_message ?: "Xin chào! Tôi là {$bot->name}. Tôi có thể giúp gì cho bạn?",
                    'starterMessages' => $bot->starter_messages ?: [],
                    'status' => $bot->status->value,
                    'theme' => [
                        'primaryColor' => $bot->theme['primary_color'] ?? '#007bff',
                        'backgroundColor' => $bot->theme['background_color'] ?? '#ffffff',
                        'textColor' => $bot->theme['text_color'] ?? '#333333',
                        'borderRadius' => $bot->theme['border_radius'] ?? '8px',
                        'fontFamily' => $bot->theme['font_family'] ?? 'system-ui',
                    ],
                ],
                'widget' => $this->getWidgetConfig($request, $bot),
                'api' => [
                    'baseUrl' => url('/api/v1/widget'),
                    'endpoints' => [
                        'health' => '/health',
                        'botConfig' => "/bot/{$bot->uuid}/config",
                        'conversations' => '/conversations',
                        'messages' => '/conversations/{conversationId}/messages',
                    ]
                ]
            ];

            return response()->json($config)
                           ->header('X-Frame-Options', 'ALLOWALL')
                           ->header('Access-Control-Allow-Origin', '*');

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to load configuration'], 500);
        }
    }
}
