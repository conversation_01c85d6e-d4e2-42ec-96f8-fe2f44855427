const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/LanguageDrawerForm-DwalCJ5c.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/js/hooks-CuzZ-_om.js","static/js/index-CQnBjWL2.js","static/js/_plugin-vue_export-helper-QGN-qG8u.js","static/css/plus-drawer-form-DaizY_En.css","static/js/LanguageFilterForm-DzynWIy9.js","static/css/LanguageFilterForm-DgsRcTzy.css"])))=>i.map(i=>d[i]);
var fe=Object.defineProperty,me=Object.defineProperties;var ye=Object.getOwnPropertyDescriptors;var le=Object.getOwnPropertySymbols;var he=Object.prototype.hasOwnProperty,ve=Object.prototype.propertyIsEnumerable;var oe=(n,d,u)=>d in n?fe(n,d,{enumerable:!0,configurable:!0,writable:!0,value:u}):n[d]=u,ne=(n,d)=>{for(var u in d||(d={}))he.call(d,u)&&oe(n,u,d[u]);if(le)for(var u of le(d))ve.call(d,u)&&oe(n,u,d[u]);return n},ie=(n,d)=>me(n,ye(d));var g=(n,d,u)=>new Promise((x,B)=>{var k=b=>{try{_(u.next(b))}catch(f){B(f)}},y=b=>{try{_(u.throw(b))}catch(f){B(f)}},_=b=>b.done?x(b.value):Promise.resolve(b.value).then(k,y);_((u=u.apply(n,d)).next())});import{aN as C,az as F,r as w,g as we,aL as pe,aA as ue,ax as o,a8 as t,ay as H,aD as be,ai as Ce,fF as xe,fG as ke,q as _e,o as De,S as Be,m as V,n as z,D as $,E as i,N as p,k as e,aI as Se,G as q,P as ce,M as A,I as G,aJ as v,aC as D,L as I,Q as Le,A as Pe,a5 as Re,a4 as W,aK as de,aM as ge,_ as Te}from"./index-ZVLuktk4.js";import{P as He}from"./index-DMMM8XNs.js";import"./sortable.esm-7jaD_3Ar.js";import"./epTheme-DzIV6EF-.js";const ze=n=>C.request("get","/api/auth/languages",{params:n}),Ae=()=>C.request("get","/api/auth/languages/dropdown"),Fe=n=>C.request("post","/api/auth/languages",{data:F(n)}),Ee=(n,d)=>C.request("put",`/api/auth/languages/${n}`,{data:F(d)}),Ne=n=>C.request("delete",`/api/auth/languages/${n}/delete`),Oe=n=>C.request("delete","/api/auth/languages/bulk/delete",{data:F(n)}),Ve=n=>C.request("delete",`/api/auth/languages/${n}/force`),$e=n=>C.request("delete","/api/auth/languages/bulk/force",{data:F(n)}),qe=n=>C.request("put",`/api/auth/languages/${n}/restore`),Ue=n=>C.request("put","/api/auth/languages/bulk/restore",{data:F(n)});function Ke(){const n=w(!1),d=w({isTrashed:"no"}),u=we({total:0,pageSize:20,currentPage:1,pageSizes:[10,20,50,100,200],background:!0,layout:"total, sizes, prev, pager, next, jumper"}),x=w([]),B=w([]),k=w({sortBy:"createdAt",sortOrder:"asc"}),y=w([]),_=w(),b=w(!1),f=w(!1),E=w({status:"active"}),m=()=>g(null,null,function*(){var s,r;n.value=!0;try{const l=yield ze(F(ie(ne({},d.value),{order:`${k.value.sortBy}:${k.value.sortOrder}`,page:u.currentPage,limit:u.pageSize})));B.value=ue(l.data),u.total=l.total}catch(l){console.error("Get Languages error:",l),o(((r=(s=l.response)==null?void 0:s.data)==null?void 0:r.message)||(l==null?void 0:l.message)||t("Get failed"),{type:"error"})}finally{n.value=!1}}),N=()=>g(null,null,function*(){try{const s=yield Ae();y.value=ue(s.data)}catch(s){console.error("Get Languages dropdown error:",s)}}),M=s=>g(null,null,function*(){var r,l;try{n.value=!0;const a=yield Fe(s);return a.success?(o(a.message||t("Create successful"),{type:"success"}),yield m(),!0):(o(a.message||t("Create failed"),{type:"error"}),!1)}catch(a){return o(((l=(r=a.response)==null?void 0:r.data)==null?void 0:l.message)||(a==null?void 0:a.message)||t("Create failed"),{type:"error"}),!1}finally{n.value=!1}}),Y=(s,r)=>g(null,null,function*(){var l,a;try{n.value=!0;const h=yield Ee(s,r);return h.success?(o(h.message||t("Update successful"),{type:"success"}),yield m(),!0):(o(h.message||t("Update failed"),{type:"error"}),!1)}catch(h){return o(((a=(l=h.response)==null?void 0:l.data)==null?void 0:a.message)||(h==null?void 0:h.message)||t("Update failed"),{type:"error"}),!1}finally{n.value=!1}}),j=s=>{x.value=s},Q=l=>g(null,[l],function*({prop:s,order:r}){k.value.sortBy=s,k.value.sortOrder=r==="ascending"?"asc":"desc",yield m()}),P=s=>g(null,null,function*(){u.currentPage=s,yield m()}),S=s=>g(null,null,function*(){u.pageSize=s,u.currentPage=1,yield m()}),L=s=>g(null,null,function*(){try{yield H.confirm(t("Are you sure to delete this item?"),t("Warning"),{confirmButtonText:t("OK"),cancelButtonText:t("Cancel"),type:"warning"}),yield U(s.id)}catch(r){}}),U=s=>g(null,null,function*(){var r,l;try{n.value=!0;const a=yield Ne(s);return a.success?(o(a.message||t("Delete successful"),{type:"success"}),yield m(),!0):(o(a.message||t("Delete failed"),{type:"error"}),!1)}catch(a){return o(((l=(r=a.response)==null?void 0:r.data)==null?void 0:l.message)||(a==null?void 0:a.message)||t("Delete failed"),{type:"error"}),!1}finally{n.value=!1}}),J=s=>g(null,null,function*(){const r=x.value.map(l=>l.id);if(r.length===0){o(t("Please select items to delete"),{type:"warning"});return}try{yield H.confirm(t("Are you sure to delete selected items?"),t("Warning"),{confirmButtonText:t("OK"),cancelButtonText:t("Cancel"),type:"warning"}),(yield X(r))&&(s!=null&&s.clearSelection)&&s.clearSelection()}catch(l){}}),X=s=>g(null,null,function*(){var r,l;try{n.value=!0;const a=yield Oe({ids:s});return a.success?(o(a.message||t("Delete successful"),{type:"success"}),yield m(),!0):(o(a.message||t("Delete failed"),{type:"error"}),!1)}catch(a){return console.error("Bulk delete Languages error:",a),o(((l=(r=a.response)==null?void 0:r.data)==null?void 0:l.message)||t("Delete failed"),{type:"error"}),!1}finally{n.value=!1}}),Z=s=>g(null,null,function*(){try{yield H.confirm(t("Are you sure to permanently delete this item?"),t("Warning"),{confirmButtonText:t("OK"),cancelButtonText:t("Cancel"),type:"warning"}),yield ee(s.id)}catch(r){}}),ee=s=>g(null,null,function*(){var r,l;try{n.value=!0;const a=yield Ve(s);return a.success?(o(a.message||t("Delete successful"),{type:"success"}),yield m(),!0):(o(a.message||t("Delete failed"),{type:"error"}),!1)}catch(a){return o(((l=(r=a.response)==null?void 0:r.data)==null?void 0:l.message)||(a==null?void 0:a.message)||t("Delete failed"),{type:"error"}),!1}finally{n.value=!1}}),te=s=>g(null,null,function*(){const r=x.value.map(l=>l.id);if(r.length===0){o(t("Please select items to delete"),{type:"warning"});return}try{yield H.confirm(t("Are you sure to permanently delete selected items?"),t("Warning"),{confirmButtonText:t("OK"),cancelButtonText:t("Cancel"),type:"warning"}),(yield ae(r))&&(s!=null&&s.clearSelection)&&s.clearSelection()}catch(l){}}),ae=s=>g(null,null,function*(){var r,l;try{n.value=!0;const a=yield $e({ids:s});return a.success?(o(a.message||t("Delete successful"),{type:"success"}),yield m(),!0):(o(a.message||t("Delete failed"),{type:"error"}),!1)}catch(a){return console.error("Bulk permanent delete Languages error:",a),o(((l=(r=a.response)==null?void 0:r.data)==null?void 0:l.message)||t("Delete failed"),{type:"error"}),!1}finally{n.value=!1}}),se=s=>g(null,null,function*(){try{yield H.confirm(t("Are you sure to restore this item?"),t("Warning"),{confirmButtonText:t("OK"),cancelButtonText:t("Cancel"),type:"warning"}),yield K(s.id)}catch(r){}}),K=s=>g(null,null,function*(){var r,l;try{n.value=!0;const a=yield qe(s);return a.success?(o(a.message||t("Restore successful"),{type:"success"}),yield m(),!0):(o(a.message||t("Restore failed"),{type:"error"}),!1)}catch(a){return o(((l=(r=a.response)==null?void 0:r.data)==null?void 0:l.message)||(a==null?void 0:a.message)||t("Restore failed"),{type:"error"}),!1}finally{n.value=!1}}),c=s=>g(null,null,function*(){const r=x.value.map(l=>l.id);if(r.length===0){o(t("Please select items to restore"),{type:"warning"});return}try{yield H.confirm(t("Are you sure to restore selected items?"),t("Warning"),{confirmButtonText:t("OK"),cancelButtonText:t("Cancel"),type:"warning"}),(yield R(r))&&(s!=null&&s.clearSelection)&&s.clearSelection()}catch(l){}}),R=s=>g(null,null,function*(){var r,l;try{n.value=!0;const a=yield Ue({ids:s});return a.success?(o(a.message||t("Restore successful"),{type:"success"}),yield m(),!0):(o(a.message||t("Restore failed"),{type:"error"}),!1)}catch(a){return console.error("Bulk restore Languages error:",a),o(((l=(r=a.response)==null?void 0:r.data)==null?void 0:l.message)||t("Restore failed"),{type:"error"}),!1}finally{n.value=!1}});return{loading:n,filterRef:d,pagination:u,records:B,multipleSelection:x,sort:k,languagesDropdown:y,handleBulkDelete:J,handleDelete:L,handlePermanentDelete:Z,handleBulkPermanentDelete:te,handleRestore:se,handleBulkRestore:c,fnGetLanguages:m,fnGetLanguagesDropdown:N,fnHandlePageChange:P,fnHandleSelectionChange:j,fnHandleSortChange:Q,fnHandleSizeChange:S,filterVisible:b,drawerVisible:f,drawerValues:E,languageFormRef:_,handleSubmit:s=>g(null,null,function*(){var l;let r=!1;s.id!=null?r=yield Y(Number(s.id),s):(r=yield M(s),r&&(E.value={status:"active"},(l=_.value)==null||l.resetForm()))}),handleFilter:s=>g(null,null,function*(){d.value=s,yield m()}),handleEdit:s=>{E.value=ne({},pe(s,!0)),f.value=!0}}}const Ge=[{type:"selection",width:"30px",sortable:!1},{type:"index",width:40,headerRenderer:()=>t("No.")},{prop:"code",align:"center",sortable:!0,width:90,headerRenderer:()=>t("code")},{prop:"name",align:"left",sortable:!0,minWidth:160,headerRenderer:()=>t("Language Name")},{prop:"nativeName",align:"left",sortable:!0,minWidth:160,headerRenderer:()=>t("Language Native Name")},{prop:"direction",align:"center",sortable:!0,width:160,headerRenderer:()=>t("Direction"),cellRenderer:({row:n})=>n.direction.toUpperCase()},{prop:"status",align:"center",sortable:!0,width:100,headerRenderer:()=>t("Status"),cellRenderer:({row:n})=>Ce(ke,{type:n.status==="active"?"success":"danger",size:"small"},()=>t(xe(n.status)).toUpperCase())},{prop:"createdAt",width:160,sortable:!0,headerRenderer:()=>t("Created at"),formatter:({createdAt:n})=>n?be(n).format("YYYY-MM-DD HH:mm"):"-"},{label:"",fixed:"right",width:140,slot:"operation",sortable:!1}],Ie={class:"main"},We={class:"ml-2"},Me={class:"ml-2"},Ye={class:"ml-2"},je={class:"ml-2"},Qe=_e({__name:"index",setup(n){const d=de(()=>ge(()=>import("./LanguageDrawerForm-DwalCJ5c.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),u=de(()=>ge(()=>import("./LanguageFilterForm-DzynWIy9.js"),__vite__mapDeps([7,1,2,4,5,8,6]))),x=w(),B=w(),{loading:k,filterRef:y,pagination:_,records:b,multipleSelection:f,handleBulkDelete:E,handleDelete:m,fnGetLanguages:N,fnHandlePageChange:M,fnHandleSelectionChange:Y,fnHandleSortChange:j,fnHandleSizeChange:Q,filterVisible:P,drawerVisible:S,drawerValues:L,languageFormRef:U,handleSubmit:J,handleFilter:X,handleBulkPermanentDelete:Z,handleBulkRestore:ee,handlePermanentDelete:te,handleRestore:ae}=Ke(),se=K=>{L.value=pe(K,!0),S.value=!0};return De(()=>{Be(()=>{N()})}),(K,c)=>{const R=q("el-button"),O=q("el-tooltip"),T=q("el-dropdown-item"),re=q("el-dropdown-menu"),s=q("el-dropdown");return z(),V("div",Ie,[$("div",{ref_key:"contentRef",ref:B,class:I(["flex",e(Re)()?"flex-wrap":""])},[i(e(He),{class:"!w-full",style:{transition:"width 220ms cubic-bezier(0.4, 0, 0.2, 1)"},title:e(t)("Language Management"),columns:e(Ge),onRefresh:e(N),onFilter:c[4]||(c[4]=r=>P.value=!0)},{buttons:p(()=>[i(O,{content:e(t)("Create new"),placement:"top"},{default:p(()=>[i(R,{type:"text",class:"font-bold text-[16px]",disabled:!e(v)("language.create"),onClick:c[0]||(c[0]=()=>{S.value=!0})},{default:p(()=>[i(e(D),{icon:e(v)("language.create")?"flat-color-icons:plus":"icons8:plus",width:"18px"},null,8,["icon"])]),_:1},8,["disabled"])]),_:1},8,["content"]),e(y).isTrashed==="yes"?(z(),V(G,{key:0},[i(O,{content:e(t)("Restore"),placement:"top"},{default:p(()=>[i(R,{type:"text",class:"font-bold text-[16px]",disabled:e(f).length===0||e(f).length>0&&!e(v)("language.delete"),onClick:c[1]||(c[1]=()=>e(ee)())},{default:p(()=>[i(e(D),{icon:"tabler:restore",width:"18px",class:I({"text-amber-800":e(f).length>0&&e(v)("language.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"]),i(O,{content:e(t)("Bulk Destroy"),placement:"top"},{default:p(()=>[i(R,{type:"text",class:"font-bold text-[16px]",disabled:e(f).length===0||e(f).length>0&&!e(v)("language.destroy"),onClick:c[2]||(c[2]=()=>e(Z)())},{default:p(()=>[i(e(D),{icon:"tabler:trash-x-filled",width:"18px",class:I({"text-red-800":e(f).length>0&&e(v)("language.destroy")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])],64)):(z(),V(G,{key:1},[e(y).isTrashed==="no"?(z(),Le(O,{key:0,content:e(t)("Bulk Delete"),placement:"top"},{default:p(()=>[i(R,{type:"text",class:"font-bold text-[16px]",disabled:e(f).length==0||e(f).length>0&&!e(v)("language.delete"),onClick:c[3]||(c[3]=()=>e(E)())},{default:p(()=>[i(e(D),{icon:"tabler:trash",width:"18px",class:I({"text-red-800":e(f).length>0&&e(v)("language.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])):Pe("",!0)],64))]),default:p(({size:r,dynamicColumns:l})=>[i(e(Se),{ref_key:"tableRef",ref:x,"align-whole":"center","table-layout":"auto",loading:e(k),size:r,adaptive:"",border:"",adaptiveConfig:{offsetBottom:108},data:e(b),columns:l,pagination:e(_),"header-cell-style":{background:"var(--el-fill-color-light)",color:"var(--el-text-color-primary)"},onSortChange:e(j),onPageSizeChange:e(Q),onPageCurrentChange:e(M),onSelectionChange:e(Y)},{operation:p(({row:a})=>[i(s,{"split-button":"",trigger:"click",size:"small"},{dropdown:p(()=>[i(re,{class:"min-w-[130px]"},{default:p(()=>[i(T,{disabled:""},{default:p(()=>[ce(A(e(t)("Action")),1)]),_:1}),e(y).isTrashed=="no"?(z(),V(G,{key:0},[i(T,{disabled:!e(v)("language.edit"),onClick:h=>se(a)},{default:p(()=>[i(e(D),{icon:"material-symbols:edit",class:"text-blue-800"}),$("span",We,A(e(t)("Edit")),1)]),_:2},1032,["disabled","onClick"]),i(T,{disabled:!e(v)("language.delete"),onClick:h=>e(m)(a)},{default:p(()=>[i(e(D),{icon:"tabler:trash",class:"text-red-800"}),$("span",Me,A(e(t)("Delete")),1)]),_:2},1032,["disabled","onClick"])],64)):(z(),V(G,{key:1},[i(T,{disabled:!e(v)("language.delete"),onClick:h=>e(ae)(a)},{default:p(()=>[i(e(D),{icon:"tabler:restore",class:"text-red-800"}),$("span",Ye,A(e(t)("Restore")),1)]),_:2},1032,["disabled","onClick"]),i(T,{disabled:!e(v)("language.destroy"),onClick:h=>e(te)(a)},{default:p(()=>[i(e(D),{icon:"tabler:trash-x",class:"text-red-800"}),$("span",je,A(e(t)("Destroy")),1)]),_:2},1032,["disabled","onClick"])],64))]),_:2},1024)]),default:p(()=>[ce(A(e(t)("Action"))+" ",1)]),_:2},1024)]),_:2},1032,["loading","size","data","columns","pagination","onSortChange","onPageSizeChange","onPageCurrentChange","onSelectionChange"])]),_:1},8,["title","columns","onRefresh"])],2),i(e(d),{ref_key:"languageFormRef",ref:U,visible:e(S),"onUpdate:visible":c[5]||(c[5]=r=>W(S)?S.value=r:null),values:e(L),"onUpdate:values":c[6]||(c[6]=r=>W(L)?L.value=r:null),onSubmit:e(J),onClose:c[7]||(c[7]=()=>{var r;(r=e(U))==null||r.resetForm(),L.value={status:"active"}})},null,8,["visible","values","onSubmit"]),i(e(u),{ref:"filterFormRef",visible:e(P),"onUpdate:visible":c[8]||(c[8]=r=>W(P)?P.value=r:null),values:e(y),"onUpdate:values":c[9]||(c[9]=r=>W(y)?y.value=r:null),onSubmit:e(X),onReset:c[10]||(c[10]=()=>{y.value={isTrashed:"no"},e(N)()})},null,8,["visible","values","onSubmit"])])}}}),at=Te(Qe,[["__scopeId","data-v-0dcc2f1f"]]);export{at as default};
