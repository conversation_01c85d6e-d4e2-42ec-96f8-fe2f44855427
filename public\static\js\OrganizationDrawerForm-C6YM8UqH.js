var h=(b,g,n)=>new Promise((s,t)=>{var u=a=>{try{d(n.next(a))}catch(p){t(p)}},i=a=>{try{d(n.throw(a))}catch(p){t(p)}},d=a=>a.done?s(a.value):Promise.resolve(a.value).then(u,i);d((n=n.apply(b,g)).next())});/* empty css                         */import{q as U,r as T,o as _,e as r,a8 as e,ai as k,G as x,Q as I,n as S,N as f,D as P,E as w,P as C,M as y,k as v}from"./index-ZVLuktk4.js";import{u as q}from"./hooks-CuzZ-_om.js";import F from"./OrganizationLogoUpload-DfB3BT2D.js";import{P as O}from"./index-CQnBjWL2.js";import"./auth-api-Buobw8mr.js";import"./_plugin-vue_export-helper-QGN-qG8u.js";const z={class:"custom-group-header"},N={class:"font-semibold"},V={class:"custom-footer"},W=U({__name:"OrganizationDrawerForm",props:{visible:{type:Boolean},values:{},isEdit:{type:Boolean}},emits:["update:visible","update:values","submit"],setup(b,{expose:g,emit:n}){const s=b,t=n,u=T(!1),i=T();_(()=>{});const d=[{label:r(()=>e("Logo")),prop:"logo",valueType:"",renderField:()=>k(F,{logoUrl:String(s.values.logoUrl||""),autoUpload:!0,"onUpdate:logoUrl":o=>{s.values&&(s.values.logoUrl=o)},onLogoFileChange:o=>{s.values&&(s.values.logo=o)},onUploadSuccess:o=>{},onUploadError:o=>{console.error("Logo upload failed:",o)}}),colProps:{span:24}},{label:r(()=>e("Registration Type")),prop:"type",valueType:"select",options:[{label:e("Team"),value:"team"},{label:e("Company"),value:"company"}],fieldProps:{placeholder:"",clearable:!1,style:{width:"100%"}},colProps:{span:24}},{label:r(()=>e("Organization Name")),prop:"name",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input organization name"),trigger:["blur"]},{min:2,max:100,message:e("Length must be between 2 and 100 characters"),trigger:["blur"]}],fieldProps:{placeholder:""},colProps:{span:24}},{label:r(()=>e("Description")),prop:"description",valueType:"textarea",fieldProps:{placeholder:"",rows:3},colProps:{span:24}},{label:r(()=>e("Email")),prop:"email",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input email"),trigger:["blur"]},{type:"email",message:e("Please input valid email"),trigger:["blur"]}],fieldProps:{placeholder:""},colProps:{span:24}},{label:r(()=>e("Status")),prop:"status",valueType:"select",required:!0,options:[{label:e("Active"),value:"active"},{label:e("Inactive"),value:"inactive"},{label:e("Suspended"),value:"suspended"}],fieldProps:{placeholder:"",clearable:!1},colProps:{span:9}},{label:r(()=>e("Website")),prop:"website",valueType:"input",fieldProps:{placeholder:""},colProps:{span:24}},{label:r(()=>e("Is Verified")),prop:"isVerified",valueType:"switch",fieldProps:{activeText:e("Yes"),inactiveText:e("No")},colProps:{span:9}}],a=o=>h(null,null,function*(){var m;if(!(!((m=i.value)!=null&&m.formInstance)||!(yield i.value.formInstance.validate())))try{u.value=!0,t("submit",o)}finally{setTimeout(()=>{u.value=!1},3e3)}}),p=()=>{var o;(o=i.value)!=null&&o.formInstance&&i.value.formInstance.resetFields()};return g({resetForm:p}),(o,l)=>{const m=x("el-button");return S(),I(v(O),{ref_key:"formRef",ref:i,visible:o.visible,"model-value":o.values,size:"40%",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0,destroyOnClose:!0,form:{columns:d,footerAlign:"center",hasFooter:!1,labelSuffix:"",labelPosition:"top",labelWidth:"auto",labelAlign:"left",requireAsteriskPosition:"right",rowProps:{gutter:10}},"onUpdate:visible":l[2]||(l[2]=c=>t("update:visible",c)),"onUpdate:modelValue":l[3]||(l[3]=c=>t("update:values",c)),onClose:p},{"drawer-header":f(()=>[P("div",z,[P("span",N,y(v(e)("Organization Information")),1)])]),"drawer-footer":f(()=>[P("div",V,[w(m,{plain:"",onClick:l[0]||(l[0]=c=>t("update:visible",!1))},{default:f(()=>[C(y(v(e)("Cancel")),1)]),_:1}),w(m,{plain:"",type:"primary",loading:u.value,icon:v(q)("ri:save-2-line"),onClick:l[1]||(l[1]=c=>a(s.values))},{default:f(()=>[C(y(v(e)("Save")),1)]),_:1},8,["loading","icon"])])]),_:1},8,["visible","model-value","form"])}}});export{W as default};
