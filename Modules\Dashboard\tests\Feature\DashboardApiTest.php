<?php

namespace Modules\Dashboard\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Modules\User\Models\User;
use Laravel\Sanctum\Sanctum;

class DashboardApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create();
    }

    /** @test */
    public function it_requires_authentication_to_access_dashboard_data()
    {
        $response = $this->getJson('/api/v1/auth/dashboard/data');

        $response->assertStatus(401);
    }

    /** @test */
    public function it_can_get_complete_dashboard_data()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/auth/dashboard/data');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'stats' => [
                        'chatbot' => [
                            'total',
                            'active',
                            'draft',
                            'mostUsedBot'
                        ],
                        'conversation' => [
                            'total',
                            'today',
                            'week',
                            'avgConversationLength'
                        ],
                        'token' => [
                            'total',
                            'estimatedCost'
                        ],
                        'knowledge' => [
                            'totalDocuments',
                            'fileTypes',
                            'mostQueriedDoc'
                        ],
                        'storage' => [
                            'totalUsedMB',
                            'documentsSizeMB',
                            'attachmentsSizeMB',
                            'remainingQuotaMB',
                            'quotaLimitMB',
                            'usagePercent'
                        ]
                    ],
                    'charts' => [
                        'tokenTrend',
                        'conversationTrend'
                    ],
                    'metadata' => [
                        'lastUpdated',
                        'timezone',
                        'currency'
                    ]
                ]
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals('Dashboard data retrieved successfully', $response->json('message'));
    }

    /** @test */
    public function it_can_get_dashboard_stats_only()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/auth/dashboard/stats');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'chatbot',
                    'conversation',
                    'token',
                    'knowledge',
                    'storage'
                ]
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertArrayNotHasKey('charts', $response->json('data'));
    }

    /** @test */
    public function it_can_get_dashboard_charts_only()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/auth/dashboard/charts');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'tokenTrend',
                    'conversationTrend'
                ]
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertArrayNotHasKey('stats', $response->json('data'));
    }

    /** @test */
    public function it_can_get_dashboard_summary()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/auth/dashboard/summary');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'totalBots',
                    'totalConversations',
                    'totalTokens',
                    'totalDocuments',
                    'storageUsedPercent'
                ]
            ]);

        $this->assertTrue($response->json('success'));
    }

    /** @test */
    public function it_validates_dashboard_request_parameters()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/auth/dashboard/data?period=invalid');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['period']);
    }

    /** @test */
    public function it_accepts_valid_dashboard_request_parameters()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/auth/dashboard/data?period=week&timezone=UTC&include_charts=true');

        $response->assertStatus(200);
        $this->assertTrue($response->json('success'));
    }

    /** @test */
    public function it_handles_service_exceptions_gracefully()
    {
        Sanctum::actingAs($this->user);

        // Mock the service to throw an exception
        $this->mock(\Modules\Dashboard\Services\DashboardService::class)
            ->shouldReceive('getDashboardStats')
            ->andThrow(new \Exception('Service error'));

        $response = $this->getJson('/api/v1/auth/dashboard/data');

        $response->assertStatus(500)
            ->assertJson([
                'success' => false,
                'message' => 'Failed to retrieve dashboard data: Service error'
            ]);
    }

    /** @test */
    public function it_returns_consistent_data_structure()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/auth/dashboard/data');

        $data = $response->json('data');

        // Check that all required fields are present
        $this->assertArrayHasKey('stats', $data);
        $this->assertArrayHasKey('charts', $data);
        $this->assertArrayHasKey('metadata', $data);

        // Check stats structure
        $stats = $data['stats'];
        $this->assertArrayHasKey('chatbot', $stats);
        $this->assertArrayHasKey('conversation', $stats);
        $this->assertArrayHasKey('token', $stats);
        $this->assertArrayHasKey('knowledge', $stats);
        $this->assertArrayHasKey('storage', $stats);

        // Check charts structure
        $charts = $data['charts'];
        $this->assertArrayHasKey('tokenTrend', $charts);
        $this->assertArrayHasKey('conversationTrend', $charts);

        // Check that chart data has proper structure for Chart.js
        $this->assertArrayHasKey('labels', $charts['tokenTrend']);
        $this->assertArrayHasKey('datasets', $charts['tokenTrend']);
        $this->assertIsArray($charts['tokenTrend']['datasets']);
    }

    /** @test */
    public function it_includes_cache_information_in_response()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/auth/dashboard/data');

        $response->assertStatus(200);
        
        // First request should not be cached
        $this->assertArrayHasKey('cached', $response->json());
        $this->assertArrayHasKey('cache_expires_at', $response->json());
    }
}
