<?php

namespace Modules\Sidebar\Facades;

use Illuminate\Support\Facades\Facade;
use Modules\Sidebar\Services\SidebarService;

/**
 * @method static array getSidebars()
 * @method static array getSidebarTree()
 * @method static \Illuminate\Support\Collection getActiveSidebars()
 * @method static \Modules\Sidebar\Models\Sidebar|null findById(int $id)
 * @method static \Illuminate\Support\Collection getDropdownOptions()
 * @method static void clearCache()
 *
 * @see SidebarService
 */
class Sidebar extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'sidebar.service';
    }
}
