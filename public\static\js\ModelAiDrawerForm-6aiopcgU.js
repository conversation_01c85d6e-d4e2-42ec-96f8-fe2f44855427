var f=(P,y,i)=>new Promise((v,r)=>{var n=o=>{try{p(i.next(o))}catch(u){r(u)}},t=o=>{try{p(i.throw(o))}catch(u){r(u)}},p=o=>o.done?v(o.value):Promise.resolve(o.value).then(n,t);p((i=i.apply(P,y)).next())});/* empty css                         */import{q as N,r as b,o as M,a8 as e,e as a,y as S,G as _,Q as x,n as F,N as g,D as h,E as w,P as k,M as T,k as m,aA as I}from"./index-ZVLuktk4.js";import{u as B}from"./hooks-CuzZ-_om.js";import{g as E}from"./auth-api-C3NnK5ai.js";import{g as O}from"./auth-api-BCod4ziT.js";import{P as V}from"./index-CQnBjWL2.js";import"./_plugin-vue_export-helper-QGN-qG8u.js";const Y={class:"custom-group-header"},$={class:"font-semibold"},R={class:"custom-footer"},H=N({__name:"ModelAiDrawerForm",props:{visible:{type:Boolean},values:{},isEdit:{type:Boolean}},emits:["update:visible","update:values","submit"],setup(P,{expose:y,emit:i}){const v=P,r=i,n=b(!1),t=b(),p=b([]),o=b([]);M(()=>{});const u=[{label:a(()=>e("Model key")),prop:"key",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input model key"),trigger:["blur"]}],fieldProps:{placeholder:""},colProps:{span:17}},{label:a(()=>e("Status")),prop:"status",valueType:"select",fieldProps:{placeholder:"",clearable:!1},options:[{label:e("Active"),value:"active"},{label:e("Inactive"),value:"inactive"},{label:e("Draft"),value:"draft"}],colProps:{span:7}},{label:a(()=>e("Model name")),prop:"name",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input model name"),trigger:["blur","change"]},{min:2,max:100,message:e("Length should be between 2 and 100 characters"),trigger:["blur","change"]}],fieldProps:{placeholder:""},colProps:{span:24}},{label:a(()=>e("Model Provider")),prop:"modelProviderId",valueType:"select",required:!0,fieldProps:{placeholder:"",clearable:!1},options:a(()=>[...p.value.map(l=>({label:l.name,value:l.id}))]),colProps:{span:12}},{label:a(()=>e("Model Category")),prop:"categories",valueType:"select",required:!0,fieldProps:{placeholder:"",clearable:!1,multiple:!0,collapseTags:!0,collapseTagsTooltip:!0,maxCollapseTags:3},options:a(()=>[...o.value.map(l=>({label:l.name,value:Number(l.id)}))]),colProps:{span:12}},{label:a(()=>e("API Endpoint")),prop:"apiEndpoint",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input API endpoint"),trigger:["blur"]},{pattern:/^\/.*$/,message:e("API endpoint must start with /"),trigger:"blur"}],fieldProps:{placeholder:""},colProps:{span:24}},{label:a(()=>e("Streaming")),prop:"streaming",valueType:"select",fieldProps:{placeholder:"",clearable:!1},options:[{label:e("Yes"),value:!0},{label:e("No"),value:!1}],colProps:{span:6}},{label:a(()=>e("Vision")),prop:"vision",valueType:"select",fieldProps:{placeholder:"",clearable:!1},options:[{label:e("Yes"),value:!0},{label:e("No"),value:!1}],colProps:{span:6}},{label:a(()=>e("Function Calling (Tools)")),prop:"functionCalling",valueType:"select",fieldProps:{placeholder:"",clearable:!1},options:[{label:e("Yes"),value:!0},{label:e("No"),value:!1}],colProps:{span:6}},{label:a(()=>e("Set Default")),prop:"isDefault",valueType:"select",fieldProps:{placeholder:"",clearable:!1},options:[{label:e("Yes"),value:!0},{label:e("No"),value:!1}],colProps:{span:6}},{label:a(()=>e("Sort Order")),prop:"sortOrder",valueType:"input-number",fieldProps:{min:1,max:999,placeholder:"1"},colProps:{span:24}},{label:a(()=>e("Description")),prop:"description",valueType:"textarea",fieldProps:{placeholder:"",rows:4},colProps:{span:24}}],q=l=>f(null,null,function*(){var d;if(!(!((d=t.value)!=null&&d.formInstance)||!(yield t.value.formInstance.validate())))try{n.value=!0,r("submit",l)}finally{setTimeout(()=>{n.value=!1},3e3)}}),C=()=>{var l;(l=t.value)!=null&&l.formInstance&&t.value.formInstance.resetFields()},D=()=>f(null,null,function*(){yield O().then(({data:l})=>{p.value=I(l)}).catch()}),A=()=>f(null,null,function*(){yield E().then(({data:l})=>{o.value=I(l)}).catch()});return S(()=>v.visible,()=>{v.visible&&(A(),D())}),y({resetForm:C}),(l,s)=>{const d=_("el-button");return F(),x(m(V),{ref_key:"formRef",ref:t,visible:l.visible,"model-value":l.values,size:"50%",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0,destroyOnClose:!0,form:{columns:u,footerAlign:"center",hasFooter:!1,labelSuffix:"",labelPosition:"top",labelWidth:"auto",labelAlign:"left",requireAsteriskPosition:"right",rowProps:{gutter:10}},"onUpdate:visible":s[2]||(s[2]=c=>r("update:visible",c)),"onUpdate:modelValue":s[3]||(s[3]=c=>r("update:values",c)),onClose:C},{"drawer-header":g(()=>[h("div",Y,[h("span",$,T(m(e)("Information Form")),1)])]),"drawer-footer":g(()=>[h("div",R,[w(d,{plain:"",onClick:s[0]||(s[0]=c=>r("update:visible",!1))},{default:g(()=>[k(T(m(e)("Cancel")),1)]),_:1}),w(d,{plain:"",type:"primary",loading:n.value,icon:m(B)("ri:save-2-line"),onClick:s[1]||(s[1]=c=>q(l.values))},{default:g(()=>[k(T(m(e)("Save")),1)]),_:1},8,["loading","icon"])])]),_:1},8,["visible","model-value","form"])}}});export{H as default};
