var V=(p,n,s)=>new Promise((f,m)=>{var b=o=>{try{g(s.next(o))}catch(i){m(i)}},_=o=>{try{g(s.throw(o))}catch(i){m(i)}},g=o=>o.done?f(o.value):Promise.resolve(o.value).then(b,_);g((s=s.apply(p,n)).next())});import{q as R,B as F,ai as S,a7 as q,g as I,a8 as d,r as w,fN as $,m as M,n as E,D as l,k as e,E as a,a4 as O,G as v,Q as U,X as z,a6 as L,N as u,M as N,ap as j,P as A,aC as G,ad as H,ax as C,i as Q,_ as W}from"./index-ZVLuktk4.js";import{a as X,u as J,b as K,d as Y}from"./dark-C3BJic5q.js";import{u as Z}from"./useLayout-B65ZqU-V.js";import{b as ee,i as se,a as ae,T as te}from"./index-BnPdKjjT.js";import{u as oe}from"./hooks-CuzZ-_om.js";import ne from"./AuthNavigation-DpjgmbE5.js";import"./epTheme-DzIV6EF-.js";const x=R({name:"Motion",props:{delay:{type:Number,default:50}},render(){const{delay:p}=this,n=q("motion");return F(S("div",{},{default:()=>[this.$slots.default()]}),[[n,{initial:{opacity:0,y:100},enter:{opacity:1,y:0,transition:{delay:p}}}]])}}),ie=(p,n,s)=>{if(!n||n.trim()===""){s(new Error(d("Email is required")));return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(n)){s(new Error(d("Invalid email format")));return}s()},re=I({email:[{validator:ie,trigger:"blur"}]}),le={class:"select-none"},de=["src"],ce={class:"flex-c absolute right-5 top-3"},ue={class:"login-container"},me={class:"img"},pe={class:"login-box"},fe={class:"login-form"},_e={class:"outline-none"},ge={class:"text-center mb-4 text-gray-500"},ve={class:"flex justify-between items-center"},ye=R({name:"ForgetPassword",__name:"index",setup(p){const n=Q(),s=w(!1),f=w(!1),m=w();w("");const{initStorage:b}=Z();b();const{dataTheme:_,overallStyle:g,dataThemeChange:o}=X();o(g.value),J();const i=I({email:""}),P=c=>V(null,null,function*(){var r,y;if(c)try{yield c.validate(),s.value=!0;const t=yield H().forgotPassword({email:i.email});if(!t.success){C(t.message,{type:"error"});return}C(t.message,{type:"success"}),yield n.push({path:"/reset-password",query:{email:i.email}})}catch(t){C(((y=(r=t==null?void 0:t.response)==null?void 0:r.data)==null?void 0:y.message)||(t==null?void 0:t.message)||d("Some information is incorrect. Please review and try again."),{type:"error"})}finally{s.value=!1}}),T=j(c=>P(c),1e3,!0);return $(document,"keydown",({code:c})=>{["Enter","NumpadEnter"].includes(c)&&!f.value&&!s.value&&T(m.value)}),(c,r)=>{const y=v("el-switch"),t=v("el-input"),k=v("el-form-item"),D=v("el-button"),B=v("el-form");return E(),M("div",le,[l("img",{src:e(ee),class:"wave",alt:"Bg"},null,8,de),l("div",ce,[a(y,{modelValue:e(_),"onUpdate:modelValue":r[0]||(r[0]=h=>O(_)?_.value=h:null),"inline-prompt":"","active-icon":e(Y),"inactive-icon":e(K),onChange:e(o)},null,8,["modelValue","active-icon","inactive-icon","onChange"])]),l("div",ue,[l("div",me,[(E(),U(z(L(e(se)))))]),l("div",pe,[l("div",fe,[a(e(ae),{class:"avatar"}),a(e(x),null,{default:u(()=>[l("h2",_e,[a(e(te),{options:{strings:[e(d)("Forgot Password")],cursor:!1,speed:100}},null,8,["options"])])]),_:1}),l("div",ge,N(e(d)("Enter your email address and we'll send you an OTP code to reset your password")),1),a(B,{ref_key:"ruleFormRef",ref:m,model:i,rules:e(re),size:"large"},{default:u(()=>[a(e(x),{delay:100},{default:u(()=>[a(k,{rules:[{required:!0,message:e(d)("Please enter email"),trigger:"blur"}],prop:"email"},{default:u(()=>[a(t,{modelValue:i.email,"onUpdate:modelValue":r[1]||(r[1]=h=>i.email=h),clearable:"",placeholder:e(d)("Email"),"prefix-icon":e(oe)("ri:mail-line")},null,8,["modelValue","placeholder","prefix-icon"])]),_:1},8,["rules"])]),_:1}),a(e(x),{delay:250},{default:u(()=>[l("div",ve,[a(D,{class:"w-full !uppercase",size:"large",type:"danger",round:"",loading:s.value,disabled:f.value,onClick:r[2]||(r[2]=h=>P(m.value))},{default:u(()=>[a(e(G),{icon:"ri:login-box-line",width:"20",class:"mr-2"}),A(" "+N(e(d)("Send OTP Code")),1)]),_:1},8,["loading","disabled"])])]),_:1}),a(e(x),{delay:300},{default:u(()=>[a(ne,{"show-login":!0,"show-register":!0,"show-forgot-password":!1})]),_:1})]),_:1},8,["model","rules"])])])])])}}}),Ne=W(ye,[["__scopeId","data-v-1dcbd416"]]);export{Ne as default};
