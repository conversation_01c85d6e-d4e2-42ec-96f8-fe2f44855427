import{aN as r,az as t}from"./index-ZVLuktk4.js";const o=e=>r.request("get","/api/auth/providers",{params:e}),u=()=>r.request("get","/api/auth/providers/dropdown"),d=e=>r.request("post","/api/auth/providers",{data:t(e)}),i=(e,s)=>r.request("put",`/api/auth/providers/${e}`,{data:t(s)}),p=e=>r.request("delete",`/api/auth/providers/${e}/delete`),n=e=>r.request("delete","/api/auth/providers/bulk/delete",{data:t(e)}),v=e=>r.request("delete",`/api/auth/providers/${e}/force`),l=e=>r.request("delete","/api/auth/providers/bulk/force",{data:t(e)}),c=e=>r.request("put",`/api/auth/providers/${e}/restore`),h=e=>r.request("put","/api/auth/providers/bulk/restore",{data:t(e)});export{o as a,h as b,l as c,v as d,p as e,n as f,u as g,d as h,c as r,i as u};
