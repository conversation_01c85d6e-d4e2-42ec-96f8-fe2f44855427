<?php

namespace Modules\Sidebar\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Core\Traits\ResponseTrait;
use Modules\Sidebar\Facades\Sidebar;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SidebarController extends Controller
{
    use ResponseTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        return $this->successResponse(
            Sidebar::getSidebars(),
            __('Sidebars retrieved successfully.')
        );
    }

    /**
     * Display the sidebar tree structure.
     */
    public function tree(): JsonResponse
    {
        return $this->successResponse(
            Sidebar::getSidebarTree(),
            __('Sidebar tree retrieved successfully.')
        );
    }

    /**
     * Get sidebar by ID.
     */
    public function show(int $id): JsonResponse
    {
        $sidebar = Sidebar::findById($id);

        if (!$sidebar) {
            return $this->errorResponse(__('Sidebar not found.'), 404);
        }

        return $this->successResponse($sidebar);
    }
}
