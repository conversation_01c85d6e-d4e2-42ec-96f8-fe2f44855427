import{q as E,r as m,G as h,m as p,n,D as i,M as f,k as t,a8 as e,E as u,ga as y,N as _,Q as P,gb as L,gc as M,ax as o,_ as N}from"./index-ZVLuktk4.js";const j={class:"card"},z={class:"section-title !block text-center"},J={class:"flex justify-center"},R={key:0,class:"avatar-uploader-loading"},S={class:"loading-text"},q=["src"],C={class:"text-xs text-center text-gray-500 !mt-4"},T=E({__name:"BotAvatarUpload",props:{logoUrl:{},logo:{}},emits:["upload-success","upload-error"],setup(U,{emit:x}){const v=U,l=x,r=m(!1),c=m([]),A=a=>{const s=["image/jpeg","image/jpg","image/png"].includes(a.type),d=a.size/1024/1024<5;return s?d?(l("change",{raw:a,logoUrl:URL.createObjectURL(a)}),r.value=!0,!0):(o(e("Avatar size must be less than 5MB!"),{type:"error"}),!1):(o(e("Avatar must be JPG, PNG or JPEG format!"),{type:"error"}),!1)},k=(a,s)=>{r.value=!1,a.success&&a.data?(o(e("Avatar uploaded successfully!"),{type:"success"}),l("upload-success",a.data),c.value=[]):(o(a.message||e("Avatar upload failed!"),{type:"error"}),l("upload-error",a))},B=a=>{r.value=!1,console.error("Avatar upload error:",a),o(e("Avatar upload failed!"),{type:"error"}),l("upload-error",a),c.value=[]};return(a,s)=>{var g;const d=h("el-icon"),b=h("el-upload");return n(),p("div",j,[i("h2",z,f(t(e)("Avatar")),1),i("div",J,[u(b,{"file-list":c.value,"onUpdate:fileList":s[0]||(s[0]=G=>c.value=G),class:"avatar-uploader",action:"/api/auth/bots/upload-avatar","show-file-list":!1,"auto-upload":!0,headers:{Authorization:`Bearer ${(g=t(y)().accessToken)!=null?g:t(y)()}`,"X-Requested-With":"XMLHttpRequest"},"before-upload":A,onSuccess:k,onError:B},{default:_(()=>[r.value?(n(),p("div",R,[u(d,{class:"is-loading"},{default:_(()=>[u(t(L))]),_:1}),i("div",S,f(t(e)("Uploading...")),1)])):v.logoUrl?(n(),p("img",{key:1,src:v.logoUrl,class:"avatar",alt:"avatar"},null,8,q)):(n(),P(d,{key:2,class:"avatar-uploader-icon"},{default:_(()=>[u(t(M))]),_:1}))]),_:1},8,["file-list","headers"])]),i("p",C,f(t(e)("Upload JPG, PNG, JPEG images. Size under 5MB.")),1)])}}}),D=N(T,[["__scopeId","data-v-ef670f49"]]);export{D as default};
