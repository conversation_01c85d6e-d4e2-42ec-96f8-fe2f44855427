<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('widget_users', function (Blueprint $table) {
            $table->id();
            $table->string('widget_id', 100)->unique()->comment('Unique widget user identifier');
            $table->string('session_id', 100)->nullable()->comment('Session ID');
            $table->string('ip_address', 45)->nullable()->comment('IP address');
            $table->text('user_agent')->nullable()->comment('User agent string');
            $table->json('metadata')->nullable()->comment('Additional metadata');
            $table->timestamps();

            $table->index(['widget_id', 'created_at']);
            $table->index('session_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('widget_users');
    }
};
