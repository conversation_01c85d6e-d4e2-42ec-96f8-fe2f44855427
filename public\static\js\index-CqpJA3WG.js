const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/RoleDrawerForm-BT8PJajG.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/js/hooks-CuzZ-_om.js","static/js/auth-api-CR4e_tAC.js","static/js/index-CQnBjWL2.js","static/js/_plugin-vue_export-helper-QGN-qG8u.js","static/css/plus-drawer-form-DaizY_En.css","static/js/RoleFilterForm-DyWn63cx.js"])))=>i.map(i=>d[i]);
var fe=Object.defineProperty,pe=Object.defineProperties;var me=Object.getOwnPropertyDescriptors;var le=Object.getOwnPropertySymbols;var ye=Object.prototype.hasOwnProperty,ge=Object.prototype.propertyIsEnumerable;var ae=(i,o,a)=>o in i?fe(i,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):i[o]=a,H=(i,o)=>{for(var a in o||(o={}))ye.call(o,a)&&ae(i,a,o[a]);if(le)for(var a of le(o))ge.call(o,a)&&ae(i,a,o[a]);return i},q=(i,o)=>pe(i,me(o));var g=(i,o,a)=>new Promise((C,k)=>{var R=v=>{try{_(a.next(v))}catch(d){k(d)}},h=v=>{try{_(a.throw(v))}catch(d){k(d)}},_=v=>v.done?C(v.value):Promise.resolve(v.value).then(R,h);_((a=a.apply(i,o)).next())});import{r as x,g as he,aA as se,az as be,ax as c,a8 as t,ay as P,ai as N,aC as w,q as xe,o as ve,S as we,m as I,n as T,D as V,E as r,N as u,k as e,aI as Ce,G as $,P as oe,M as E,I as U,aJ as b,L as O,Q as Re,A as _e,a5 as ke,a4 as M,aK as re,aL as Se,aM as ie,_ as Be}from"./index-ZVLuktk4.js";import{g as De,a as Pe,b as Te,r as Ee,c as Ae,d as Fe,e as ze,f as Ie,u as Ve,h as $e}from"./auth-api-CR4e_tAC.js";import{P as He}from"./index-DMMM8XNs.js";import"./sortable.esm-7jaD_3Ar.js";import"./epTheme-DzIV6EF-.js";function Ne(){const i=x(!1),o=x({isTrashed:"no"}),a=he({total:0,pageSize:20,currentPage:1,pageSizes:[10,20,50,100,200],background:!0,layout:"total, sizes, prev, pager, next, jumper"}),C=x([]),k=x([]),R=x({sortBy:"createdAt",sortOrder:"asc"}),h=x([]),_=x(),v=x(!1),d=x(!1),A=x({status:"active"}),m=()=>g(null,null,function*(){try{i.value=!0;const l=yield Pe(be(q(H({},o.value),{order:`${R.value.sortBy}:${R.value.sortOrder}`,page:a.currentPage,limit:a.pageSize})));k.value=se(l.data),a.total=l.total}catch(l){console.error("Error fetching roles:",l),c(t("Failed to fetch roles"),{type:"error"})}finally{i.value=!1}}),F=()=>g(null,null,function*(){try{const l=yield De();h.value=se(l.data)}catch(l){console.error("Error fetching roles dropdown:",l)}}),W=l=>{C.value=l},G=()=>{m()},L=l=>{a.pageSize=l,a.currentPage=1,m()},K=l=>g(null,null,function*(){R.value={sortBy:l.prop,sortOrder:l.order=="ascending"?"asc":"desc"},yield m()}),B=l=>g(null,null,function*(){var n,s;try{i.value=!0;const p=yield $e(l);return p.success?(c(p.message||t("Create successful"),{type:"success"}),yield m(),d.value=!1,!0):(c(p.message||t("Create failed"),{type:"error"}),!1)}catch(p){return c(((s=(n=p.response)==null?void 0:n.data)==null?void 0:s.message)||(p==null?void 0:p.message)||t("Create failed"),{type:"error"}),!1}finally{i.value=!1}}),S=(l,n)=>g(null,null,function*(){var s,p;try{i.value=!0;const y=yield Ve(l,n);return y.success?(c(y.message||t("Update successful"),{type:"success"}),yield m(),!0):(c(y.message||t("Update failed"),{type:"error"}),!1)}catch(y){return c(((p=(s=y.response)==null?void 0:s.data)==null?void 0:p.message)||(y==null?void 0:y.message)||t("Update failed"),{type:"error"}),!1}finally{i.value=!1}});return{loading:i,filterRef:o,pagination:a,records:k,multipleSelection:C,sort:R,rolesDropdown:h,handleBulkDelete:l=>g(null,null,function*(){const n=l||C.value.map(s=>s.id);if(n.length===0){c(t("Please select items to delete"),{type:"warning"});return}try{yield P.confirm(t("Are you sure you want to delete selected items?"),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Ie({ids:n}),c(t("Deleted successfully"),{type:"success"}),m()}catch(s){s!=="cancel"&&(console.error("Error bulk deleting roles:",s),c(t("Delete failed"),{type:"error"}))}}),handleDelete:l=>g(null,null,function*(){try{yield P.confirm(t("Are you sure you want to delete this item?"),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield ze(l.id),c(t("Deleted successfully"),{type:"success"}),m()}catch(n){n!=="cancel"&&(console.error("Error deleting role:",n),c(t("Delete failed"),{type:"error"}))}}),handlePermanentDelete:l=>g(null,null,function*(){try{yield P.confirm(t("Are you sure you want to permanently delete this item? This action cannot be undone."),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Fe(l.id),c(t("Permanently deleted successfully"),{type:"success"}),m()}catch(n){n!=="cancel"&&(console.error("Error permanently deleting role:",n),c(t("Permanent delete failed"),{type:"error"}))}}),handleBulkPermanentDelete:l=>g(null,null,function*(){const n=l||C.value.map(s=>s.id);if(n.length===0){c(t("Please select items to delete"),{type:"warning"});return}try{yield P.confirm(t("Are you sure you want to permanently delete selected items? This action cannot be undone."),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Ae({ids:n}),c(t("Permanently deleted successfully"),{type:"success"}),m()}catch(s){s!=="cancel"&&(console.error("Error bulk permanently deleting roles:",s),c(t("Permanent delete failed"),{type:"error"}))}}),handleRestore:l=>g(null,null,function*(){try{yield P.confirm(t("Are you sure you want to restore this item?"),t("Confirm"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"info"}),yield Ee(l.id),c(t("Restored successfully"),{type:"success"}),m()}catch(n){n!=="cancel"&&(console.error("Error restoring role:",n),c(t("Restore failed"),{type:"error"}))}}),handleBulkRestore:l=>g(null,null,function*(){const n=l||C.value.map(s=>s.id);if(n.length===0){c(t("Please select items to restore"),{type:"warning"});return}try{yield P.confirm(t("Are you sure you want to restore selected items?"),t("Confirm"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"info"}),yield Te({ids:n}),c(t("Restored successfully"),{type:"success"}),yield m()}catch(s){s!=="cancel"&&(console.error("Error bulk restoring roles:",s),c(t("Restore failed"),{type:"error"}))}}),fnGetRoles:m,fnGetRolesDropdown:F,fnHandlePageChange:G,fnHandleSelectionChange:W,fnHandleSortChange:K,fnHandleSizeChange:L,filterVisible:v,drawerVisible:d,drawerValues:A,roleFormRef:_,handleSubmit:l=>g(null,null,function*(){var s;let n=!1;l.id!=null?n=yield S(Number(l.id),l):(n=yield B(l),n&&(A.value={status:"active"},(s=_.value)==null||s.resetForm()))}),handleFilter:l=>g(null,null,function*(){o.value=l,yield m()}),handleEdit:l=>{A.value=H({},l),d.value=!0}}}const Ue=[{type:"selection",width:"30px",sortable:!1},{type:"index",width:40,headerRenderer:()=>t("No.")},{prop:"name",align:"left",sortable:"custom",width:190,headerRenderer:()=>t("Name")},{prop:"displayName",sortable:!1,align:"left",width:200,headerRenderer:()=>t("Display name")},{prop:"description",align:"left",minWidth:150,headerRenderer:()=>t("Description")},{prop:"isSystem",sortable:"custom",align:"center",width:140,headerRenderer:()=>t("System Role"),cellRenderer:({row:i})=>{const o=i.isSystem,a={icon:o?"ri:shield-check-fill":"ri:user-line",iconClass:o?"text-orange-600":"text-blue-600",class:o?"bg-orange-100":"bg-blue-100"};return N("span",{class:`inline-flex items-center justify-center w-8 h-8 rounded-full ${a.class}`},N(w,{icon:a.icon,class:`w-4 h-4 ${a.iconClass}`}))}},{prop:"status",sortable:!1,align:"center",width:100,headerRenderer:()=>t("Status"),cellRenderer:({row:i})=>{const o={active:{type:"success",text:t("Active"),class:"bg-green-100 text-green-800",icon:"ri:checkbox-circle-fill",iconClass:"text-green-600"},inactive:{type:"danger",text:t("Inactive"),class:"bg-red-100 text-red-800",icon:"ri:close-circle-fill",iconClass:"text-red-600"}},a=o[i.status]||o.active;return N("span",{class:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${a.class}`},[N(w,{icon:a.icon,class:`w-3 h-3 mr-1.5 ${a.iconClass}`}),a.text])}},{label:"",fixed:"right",width:140,slot:"operation",sortable:!1}],Oe={class:"main"},Me={class:"ml-2"},We={class:"ml-2"},Ge={class:"ml-2"},Le={class:"ml-2"},Ke=xe({__name:"index",setup(i){const o=re(()=>ie(()=>import("./RoleDrawerForm-BT8PJajG.js"),__vite__mapDeps([0,1,2,3,4,5,6,7]))),a=re(()=>ie(()=>import("./RoleFilterForm-DyWn63cx.js"),__vite__mapDeps([8,1,2,3,5,6,7]))),C=x(),k=x(),{loading:R,filterRef:h,pagination:_,records:v,multipleSelection:d,handleBulkDelete:A,handleDelete:m,fnGetRoles:F,fnHandlePageChange:W,fnHandleSelectionChange:G,fnHandleSortChange:L,fnHandleSizeChange:K,filterVisible:B,drawerVisible:S,drawerValues:D,roleFormRef:j,handleSubmit:J,handleFilter:X,handleBulkPermanentDelete:Y,handleBulkRestore:Z,handlePermanentDelete:ee,handleRestore:te}=Ne(),ne=l=>{const n=l.permissions.map(s=>Number(s.id));D.value=q(H({},Se(l,!0)),{permissions:n}),S.value=!0};return ve(()=>{we(()=>{F()})}),(l,n)=>{const s=$("el-button"),p=$("el-tooltip"),y=$("el-dropdown-item"),ce=$("el-dropdown-menu"),de=$("el-dropdown");return T(),I("div",Oe,[V("div",{ref_key:"contentRef",ref:k,class:O(["flex",e(ke)()?"flex-wrap":""])},[r(e(He),{class:"!w-full",style:{transition:"width 220ms cubic-bezier(0.4, 0, 0.2, 1)"},title:e(t)("Role Management"),columns:e(Ue),onRefresh:e(F),onFilter:n[4]||(n[4]=f=>B.value=!0)},{buttons:u(()=>[r(p,{content:e(t)("Create new"),placement:"top"},{default:u(()=>[r(s,{type:"text",class:"font-bold text-[16px]",disabled:!e(b)("role.create"),onClick:n[0]||(n[0]=()=>{S.value=!0})},{default:u(()=>[r(e(w),{icon:e(b)("role.create")?"flat-color-icons:plus":"icons8:plus",width:"18px"},null,8,["icon"])]),_:1},8,["disabled"])]),_:1},8,["content"]),e(h).isTrashed==="yes"?(T(),I(U,{key:0},[r(p,{content:e(t)("Restore"),placement:"top"},{default:u(()=>[r(s,{type:"text",class:"font-bold text-[16px]",disabled:e(d).length===0||e(d).length>0&&!e(b)("role.delete"),onClick:n[1]||(n[1]=()=>e(Z)())},{default:u(()=>[r(e(w),{icon:"tabler:restore",width:"18px",class:O({"text-amber-600":e(d).length>0&&e(b)("role.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"]),r(p,{content:e(t)("Bulk Destroy"),placement:"top"},{default:u(()=>[r(s,{type:"text",class:"font-bold text-[16px]",disabled:e(d).length===0||e(d).length>0&&!e(b)("role.destroy"),onClick:n[2]||(n[2]=()=>e(Y)())},{default:u(()=>[r(e(w),{icon:"tabler:trash-x-filled",width:"18px",class:O({"text-red-700":e(d).length>0&&e(b)("role.destroy")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])],64)):(T(),I(U,{key:1},[e(h).isTrashed==="no"?(T(),Re(p,{key:0,content:e(t)("Bulk Delete"),placement:"top"},{default:u(()=>[r(s,{type:"text",class:"font-bold text-[16px]",disabled:e(d).length==0||e(d).length>0&&!e(b)("role.delete")||e(d).some(f=>f.isSystem),onClick:n[3]||(n[3]=()=>e(A)())},{default:u(()=>[r(e(w),{icon:"tabler:trash",width:"18px",class:O({"text-red-700":e(d).length>0&&e(b)("role.delete")&&!e(d).some(f=>f.isSystem)})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])):_e("",!0)],64))]),default:u(({size:f,dynamicColumns:ue})=>[r(e(Ce),{ref_key:"tableRef",ref:C,"align-whole":"center","table-layout":"auto",loading:e(R),size:f,adaptive:"",border:"",adaptiveConfig:{offsetBottom:108},data:e(v),columns:ue,pagination:e(_),"header-cell-style":{background:"var(--el-fill-color-light)",color:"var(--el-text-color-primary)"},onSortChange:e(L),onPageSizeChange:e(K),onPageCurrentChange:e(W),onSelectionChange:e(G)},{operation:u(({row:z})=>[r(de,{"split-button":"",trigger:"click",size:"small"},{dropdown:u(()=>[r(ce,{class:"min-w-[130px]"},{default:u(()=>[r(y,{disabled:""},{default:u(()=>[oe(E(e(t)("Action")),1)]),_:1}),e(h).isTrashed=="no"?(T(),I(U,{key:0},[r(y,{disabled:!e(b)("role.edit"),onClick:Q=>ne(z)},{default:u(()=>[r(e(w),{icon:"material-symbols:edit",class:"text-blue-800"}),V("span",Me,E(e(t)("Edit")),1)]),_:2},1032,["disabled","onClick"]),r(y,{disabled:!e(b)("role.delete")||z.isSystem,onClick:Q=>e(m)(z)},{default:u(()=>[r(e(w),{icon:"tabler:trash",class:"text-red-800"}),V("span",We,E(e(t)("Delete")),1)]),_:2},1032,["disabled","onClick"])],64)):(T(),I(U,{key:1},[r(y,{disabled:!e(b)("role.delete"),onClick:Q=>e(te)(z)},{default:u(()=>[r(e(w),{icon:"tabler:restore",class:"text-amber-800"}),V("span",Ge,E(e(t)("Restore")),1)]),_:2},1032,["disabled","onClick"]),r(y,{disabled:!e(b)("role.destroy"),onClick:Q=>e(ee)(z)},{default:u(()=>[r(e(w),{icon:"tabler:trash-x",class:"text-red-800"}),V("span",Le,E(e(t)("Destroy")),1)]),_:2},1032,["disabled","onClick"])],64))]),_:2},1024)]),default:u(()=>[oe(E(e(t)("Action"))+" ",1)]),_:2},1024)]),_:2},1032,["loading","size","data","columns","pagination","onSortChange","onPageSizeChange","onPageCurrentChange","onSelectionChange"])]),_:1},8,["title","columns","onRefresh"])],2),r(e(o),{ref_key:"roleFormRef",ref:j,visible:e(S),"onUpdate:visible":n[5]||(n[5]=f=>M(S)?S.value=f:null),values:e(D),"onUpdate:values":n[6]||(n[6]=f=>M(D)?D.value=f:null),onSubmit:e(J),onClose:n[7]||(n[7]=()=>{var f;(f=e(j))==null||f.resetForm(),D.value={status:"active"}})},null,8,["visible","values","onSubmit"]),r(e(a),{ref:"filterFormRef",visible:e(B),"onUpdate:visible":n[8]||(n[8]=f=>M(B)?B.value=f:null),values:e(h),"onUpdate:values":n[9]||(n[9]=f=>M(h)?h.value=f:null),onSubmit:e(X),onReset:n[10]||(n[10]=()=>{h.value={isTrashed:"no"},e(F)()})},null,8,["visible","values","onSubmit"])])}}}),Ze=Be(Ke,[["__scopeId","data-v-ce6655ea"]]);export{Ze as default};
