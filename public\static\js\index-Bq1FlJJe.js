const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/OrganizationDrawerForm-C6YM8UqH.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/js/hooks-CuzZ-_om.js","static/js/OrganizationLogoUpload-DfB3BT2D.js","static/js/auth-api-Buobw8mr.js","static/css/OrganizationLogoUpload-B0cFI9HP.css","static/js/index-CQnBjWL2.js","static/js/_plugin-vue_export-helper-QGN-qG8u.js","static/css/plus-drawer-form-DaizY_En.css","static/js/OrganizationFilterForm-DQvRqGKQ.js","static/css/OrganizationFilterForm-CFmkMSuZ.css"])))=>i.map(i=>d[i]);
var ge=Object.defineProperty,fe=Object.defineProperties;var pe=Object.getOwnPropertyDescriptors;var ne=Object.getOwnPropertySymbols;var me=Object.prototype.hasOwnProperty,ye=Object.prototype.propertyIsEnumerable;var re=(r,c,o)=>c in r?ge(r,c,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[c]=o,le=(r,c)=>{for(var o in c||(c={}))me.call(c,o)&&re(r,o,c[o]);if(ne)for(var o of ne(c))ye.call(c,o)&&re(r,o,c[o]);return r},oe=(r,c)=>fe(r,pe(c));var g=(r,c,o)=>new Promise((C,D)=>{var b=h=>{try{m(o.next(h))}catch(k){D(k)}},H=h=>{try{m(o.throw(h))}catch(k){D(k)}},m=h=>h.done?C(h.value):Promise.resolve(h.value).then(b,H);m((o=o.apply(r,c)).next())});import{r as v,g as he,ax as i,a8 as e,ay as T,az as xe,aA as ve,ai as z,aB as Ce,aC as w,aD as we,q as be,o as ze,S as _e,m as F,n as R,D as I,E as d,N as p,k as t,aI as ke,G as $,P as ie,M as A,I as M,aJ as _,L as W,Q as Be,A as De,a5 as Se,a4 as N,aK as ce,aL as Oe,aM as de,_ as Te}from"./index-ZVLuktk4.js";import{b as Re,d as Ae,u as He,c as Pe,g as Ve,a as Fe,r as Ie,e as $e,f as Ue}from"./auth-api-Buobw8mr.js";import{P as Ee}from"./index-DMMM8XNs.js";import"./sortable.esm-7jaD_3Ar.js";import"./epTheme-DzIV6EF-.js";function Ke(){const r=v(!1),c=v({isTrashed:"no"}),o=he({total:0,pageSize:20,currentPage:1,pageSizes:[10,20,50,100,200],background:!0,layout:"total, sizes, prev, pager, next, jumper"}),C=v([]),D=v([]),b=v({sortBy:"createdAt",sortOrder:"asc"}),H=v(!1),m=v(!1),h=v({status:"active",organizationType:"company",isVerified:!1,employeeCount:0,settings:{},metadata:{}}),k=v(),u=()=>g(null,null,function*(){var s,l;r.value=!0;try{const n=yield Ve(xe(oe(le({},c.value),{order:`${b.value.sortBy}:${b.value.sortOrder}`,page:o.currentPage,limit:o.pageSize})));D.value=ve(n.data),o.total=n.total}catch(n){console.error("Get Organizations error:",n),i(((l=(s=n.response)==null?void 0:s.data)==null?void 0:l.message)||(n==null?void 0:n.message)||e("Get failed"),{type:"error"})}finally{r.value=!1}}),G=s=>{C.value=s},L=n=>g(null,[n],function*({prop:s,order:l}){b.value.sortBy=s,b.value.sortOrder=l==="ascending"?"asc":"desc",yield u()}),P=()=>g(null,null,function*(){yield u()}),Y=s=>g(null,null,function*(){o.pageSize=s,o.currentPage=1,yield u()}),j=s=>g(null,null,function*(){try{yield T.confirm(e("Are you sure to delete this item?"),e("Warning"),{confirmButtonText:e("OK"),cancelButtonText:e("Cancel"),type:"warning"}),yield U(s)}catch(l){}}),U=s=>g(null,null,function*(){var l,n;try{r.value=!0;const a=yield Ae(s);return a.success?(i(a.message||e("Delete successful"),{type:"success"}),yield u(),!0):(i(a.message||e("Delete failed"),{type:"error"}),!1)}catch(a){return i(((n=(l=a.response)==null?void 0:l.data)==null?void 0:n.message)||(a==null?void 0:a.message)||e("Delete failed"),{type:"error"}),!1}finally{r.value=!1}}),Q=s=>g(null,null,function*(){const l=C.value.map(n=>n.uuid);if(l.length===0){i(e("Please select items to delete"),{type:"warning"});return}try{yield T.confirm(e("Are you sure to delete selected items?"),e("Warning"),{confirmButtonText:e("OK"),cancelButtonText:e("Cancel"),type:"warning"}),(yield S(l))&&(s!=null&&s.clearSelection)&&s.clearSelection()}catch(n){}}),S=s=>g(null,null,function*(){var l,n;try{r.value=!0;const a=yield Re({ids:s});return a.success?(i(a.message||e("Delete successful"),{type:"success"}),yield u(),!0):(i(a.message||e("Delete failed"),{type:"error"}),!1)}catch(a){return console.error("Bulk delete Organizations error:",a),i(((n=(l=a.response)==null?void 0:l.data)==null?void 0:n.message)||e("Delete failed"),{type:"error"}),!1}finally{r.value=!1}}),O=s=>g(null,null,function*(){try{yield T.confirm(e("Are you sure to delete this item?"),e("Warning"),{confirmButtonText:e("OK"),cancelButtonText:e("Cancel"),type:"warning"}),yield B(s)}catch(l){}}),B=s=>g(null,null,function*(){var l,n;try{r.value=!0;const a=yield Ue(s);return a.success?(i(a.message||e("Delete successful"),{type:"success"}),yield u(),!0):(i(a.message||e("Delete failed"),{type:"error"}),!1)}catch(a){return i(((n=(l=a.response)==null?void 0:l.data)==null?void 0:n.message)||(a==null?void 0:a.message)||e("Delete failed"),{type:"error"}),!1}finally{r.value=!1}}),E=s=>g(null,null,function*(){const l=C.value.map(n=>n.uuid);if(l.length===0){i(e("Please select items to delete"),{type:"warning"});return}try{yield T.confirm(e("Are you sure to delete selected items?"),e("Warning"),{confirmButtonText:e("OK"),cancelButtonText:e("Cancel"),type:"warning"}),(yield q(l))&&(s!=null&&s.clearSelection)&&s.clearSelection()}catch(n){}}),q=s=>g(null,null,function*(){var l,n;try{r.value=!0;const a=yield $e({ids:s});return a.success?(i(a.message||e("Delete successful"),{type:"success"}),yield u(),!0):(i(a.message||e("Delete failed"),{type:"error"}),!1)}catch(a){return console.error("Bulk delete Organizations error:",a),i(((n=(l=a.response)==null?void 0:l.data)==null?void 0:n.message)||e("Delete failed"),{type:"error"}),!1}finally{r.value=!1}}),J=s=>g(null,null,function*(){try{yield T.confirm(e("Are you sure to restore this item?"),e("Warning"),{confirmButtonText:e("OK"),cancelButtonText:e("Cancel"),type:"warning"}),yield X(s)}catch(l){}}),X=s=>g(null,null,function*(){var l,n;try{r.value=!0;const a=yield Ie(s);return a.success?(i(a.message||e("Restore successful"),{type:"success"}),yield u(),!0):(i(a.message||e("Restore failed"),{type:"error"}),!1)}catch(a){return i(((n=(l=a.response)==null?void 0:l.data)==null?void 0:n.message)||(a==null?void 0:a.message)||e("Restores failed"),{type:"error"}),!1}finally{r.value=!1}}),Z=s=>g(null,null,function*(){const l=C.value.map(n=>n.uuid);if(l.length===0){i(e("Please select items to restore"),{type:"warning"});return}try{yield T.confirm(e("Are you sure to restore selected items?"),e("Warning"),{confirmButtonText:e("OK"),cancelButtonText:e("Cancel"),type:"warning"}),(yield ee(l))&&(s!=null&&s.clearSelection)&&s.clearSelection()}catch(n){}}),ee=s=>g(null,null,function*(){var l,n;try{r.value=!0;const a=yield Fe({ids:s});return a.success?(i(a.message||e("Restore successful"),{type:"success"}),yield u(),!0):(i(a.message||e("Restore failed"),{type:"error"}),!1)}catch(a){return console.error("Bulk restore Organizations error:",a),i(((n=(l=a.response)==null?void 0:l.data)==null?void 0:n.message)||e("Restore failed"),{type:"error"}),!1}finally{r.value=!1}}),te=s=>g(null,null,function*(){var n;let l=!1;s.id!=null?l=yield f(String(s.id),s):(l=yield V(s),l&&(h.value={status:"active",organizationType:"company",isVerified:!1,employeeCount:0,settings:{},metadata:{}},(n=k.value)==null||n.resetForm()))}),ae=s=>g(null,null,function*(){c.value=s,yield u()}),V=s=>g(null,null,function*(){var l,n;try{r.value=!0;const a=yield Pe(s);return a.success?(i(a.message||e("Create successful"),{type:"success"}),yield u(),!0):(i((a==null?void 0:a.message)||e("Create failed"),{type:"error"}),!1)}catch(a){return i(((n=(l=a.response)==null?void 0:l.data)==null?void 0:n.message)||(a==null?void 0:a.message)||e("Create failed"),{type:"error"}),!1}finally{r.value=!1}}),f=(s,l)=>g(null,null,function*(){var n,a;try{r.value=!0;const x=yield He(s,l);return x.success?(i(x.message||e("Update successful"),{type:"success"}),yield u(),!0):(i((x==null?void 0:x.message)||e("Update failed"),{type:"error"}),!1)}catch(x){return i(((a=(n=x.response)==null?void 0:n.data)==null?void 0:a.message)||(x==null?void 0:x.message)||e("Update failed"),{type:"error"}),!1}finally{r.value=!1}});return{loading:r,filterRef:c,pagination:o,records:D,multipleSelection:C,sort:b,filterVisible:H,drawerVisible:m,drawerValues:h,organizationFormRef:k,fnGetOrganizations:u,fnHandleCreateOrganization:V,fnHandleUpdateOrganization:f,fnHandleDelete:U,fnHandleBulkDelete:S,fnHandleSelectionChange:G,fnHandleSortChange:L,fnHandlePageChange:P,fnHandleSizeChange:Y,handleDelete:j,handleBulkDelete:Q,handleDestroy:O,handleBulkDestroy:E,handleRestore:J,handleBulkRestore:Z,handleSubmit:te,handleFilter:ae}}const Me=[{type:"selection",width:"30px",sortable:!1},{prop:"logo",align:"center",width:90,headerRenderer:()=>e("Logo"),cellRenderer:({row:r})=>z(Ce,{size:50,src:r.logoUrl,alt:r.name})},{prop:"name",align:"left",sortable:!1,minWidth:210,headerRenderer:()=>e("Organization Name"),cellRenderer:({row:r})=>z("div",{class:"flex flex-col"},[z("div",{class:"font-medium text-gray-900 text-sm line-clamp-2"},r.name||e("Untitled Organization")),z("div",{class:"text-sm text-gray-500 mt-1"},r.description||r.email)])},{prop:"organizationType",align:"left",width:130,headerRenderer:()=>e("Type"),cellRenderer:({row:r})=>{const c={company:{class:"bg-blue-100 text-blue-800",icon:"ri:building-line",iconClass:"text-blue-600"},nonprofit:{class:"bg-green-100 text-green-800",icon:"ri:heart-line",iconClass:"text-green-600"},government:{class:"bg-purple-100 text-purple-800",icon:"ri:government-line",iconClass:"text-purple-600"},educational:{class:"bg-orange-100 text-orange-800",icon:"ri:school-line",iconClass:"text-orange-600"}},o=c[r.organizationType]||c.company,C=r.organizationType?r.organizationType.charAt(0).toUpperCase()+r.organizationType.slice(1):e("Company");return z("span",{class:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${o.class}`},[z(w,{icon:o.icon,class:`w-3 h-3 mr-1.5 ${o.iconClass}`}),C])}},{prop:"status",align:"left",width:130,headerRenderer:()=>e("Status"),cellRenderer:({row:r})=>{const c={active:{class:"bg-green-100 text-green-800",icon:"ri:checkbox-circle-fill",iconClass:"text-green-600",text:e("Active")},inactive:{class:"bg-gray-100 text-gray-800",icon:"ri:pause-circle-line",iconClass:"text-gray-600",text:e("Inactive")},suspended:{class:"bg-red-100 text-red-800",icon:"ri:close-circle-fill",iconClass:"text-red-600",text:e("Suspended")}},o=c[r.status]||c.active;return z("span",{class:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${o.class}`},[z(w,{icon:o.icon,class:`w-3 h-3 mr-1.5 ${o.iconClass}`}),o.text])}},{prop:"createdAt",align:"left",width:140,headerRenderer:()=>e("Created At"),cellRenderer:({row:r})=>z("span",{class:"text-sm text-gray-600"},we(r.createdAt).format("YYYY-MM-DD HH:mm"))},{label:"",fixed:"right",width:140,slot:"operation",sortable:!1}],We={class:"main"},Ne={class:"ml-2"},Ge={class:"ml-2"},Le={class:"ml-2"},Ye={class:"ml-2"},je=be({__name:"index",setup(r){const c=ce(()=>de(()=>import("./OrganizationDrawerForm-C6YM8UqH.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]))),o=ce(()=>de(()=>import("./OrganizationFilterForm-DQvRqGKQ.js"),__vite__mapDeps([10,1,2,7,8,11,9]))),C=v(),D=v(),b=v(),{loading:H,filterRef:m,pagination:h,records:k,multipleSelection:u,handleBulkDelete:G,handleDelete:L,fnGetOrganizations:P,fnHandlePageChange:Y,fnHandleSelectionChange:j,fnHandleSortChange:U,fnHandleSizeChange:Q,filterVisible:S,drawerVisible:O,drawerValues:B,organizationFormRef:E,handleSubmit:q,handleFilter:J,handleBulkDestroy:X,handleBulkRestore:Z,handleDestroy:ee,handleRestore:te}=Ke(),ae=V=>{B.value=Oe(V,!0),O.value=!0};return ze(()=>{_e(()=>{P()})}),(V,f)=>{const s=$("el-button"),l=$("el-tooltip"),n=$("el-dropdown-item"),a=$("el-dropdown-menu"),x=$("el-dropdown");return R(),F("div",We,[I("div",{ref_key:"contentRef",ref:D,class:W(["flex",t(Se)()?"flex-wrap":""])},[d(t(Ee),{class:"!w-full",style:{transition:"width 220ms cubic-bezier(0.4, 0, 0.2, 1)"},title:t(e)("Organization Management"),columns:t(Me),onRefresh:t(P),onFilter:f[1]||(f[1]=y=>S.value=!0)},{buttons:p(()=>[d(l,{content:t(e)("Create new"),placement:"top"},{default:p(()=>[d(s,{type:"text",class:"font-bold text-[16px]",disabled:!t(_)("organization.create"),onClick:f[0]||(f[0]=()=>{O.value=!0})},{default:p(()=>[d(t(w),{icon:t(_)("organization.create")?"flat-color-icons:plus":"icons8:plus",width:"18px"},null,8,["icon"])]),_:1},8,["disabled"])]),_:1},8,["content"]),t(m).isTrashed==="yes"?(R(),F(M,{key:0},[d(l,{content:t(e)("Restore"),placement:"top"},{default:p(()=>[d(s,{type:"text",class:"font-bold text-[16px]",disabled:t(u).length===0||t(u).length>0&&!t(_)("organization.restore"),onClick:t(Z)},{default:p(()=>[d(t(w),{icon:"tabler:restore",width:"18px",class:W({"text-blue-600":t(u).length>0})},null,8,["class"])]),_:1},8,["disabled","onClick"])]),_:1},8,["content"]),d(l,{content:t(e)("Bulk Delete"),placement:"top"},{default:p(()=>[d(s,{type:"text",class:"font-bold text-[16px]",disabled:t(u).length===0||t(u).length>0&&!t(_)("organization.force-delete"),onClick:t(G)},{default:p(()=>[d(t(w),{icon:"tabler:trash-x-filled",width:"18px",class:W({"text-red-700":t(u).length>0})},null,8,["class"])]),_:1},8,["disabled","onClick"])]),_:1},8,["content"])],64)):(R(),F(M,{key:1},[t(m).isTrashed==="no"?(R(),Be(l,{key:0,content:t(e)("Bulk Destroy"),placement:"top"},{default:p(()=>[d(s,{type:"text",class:"font-bold text-[16px]",disabled:t(u).length==0||t(u).length>0&&!t(_)("organization.destroy"),onClick:t(X)},{default:p(()=>[d(t(w),{icon:"tabler:trash",width:"18px",class:W({"text-red-700":t(u).length>0})},null,8,["class"])]),_:1},8,["disabled","onClick"])]),_:1},8,["content"])):De("",!0)],64))]),default:p(({size:y,dynamicColumns:ue})=>[d(t(ke),{ref_key:"tableRef",ref:C,"align-whole":"center","table-layout":"auto",loading:t(H),size:y,adaptive:"",border:"",adaptiveConfig:{offsetBottom:108},data:t(k),columns:ue,pagination:t(h),"header-cell-style":{background:"var(--el-fill-color-light)",color:"var(--el-text-color-primary)"},onSortChange:t(U),onPageSizeChange:t(Q),onPageCurrentChange:t(Y),onSelectionChange:t(j)},{operation:p(({row:K})=>[d(x,{"split-button":"",trigger:"click",size:"small"},{dropdown:p(()=>[d(a,{class:"min-w-[130px]"},{default:p(()=>[d(n,{disabled:""},{default:p(()=>[ie(A(t(e)("Action")),1)]),_:1}),t(m).isTrashed==="no"?(R(),F(M,{key:0},[d(n,{disabled:!t(_)("organization.update"),onClick:se=>ae(K)},{default:p(()=>[d(t(w),{icon:"material-symbols:edit",class:"text-blue-600"}),I("span",Ne,A(t(e)("Edit")),1)]),_:2},1032,["disabled","onClick"]),d(n,{disabled:!t(_)("organization.destroy"),onClick:se=>t(ee)(K.id)},{default:p(()=>[d(t(w),{icon:"tabler:trash",class:"text-red-800"}),I("span",Ge,A(t(e)("Destroy")),1)]),_:2},1032,["disabled","onClick"])],64)):(R(),F(M,{key:1},[d(n,{disabled:!t(_)("organization.restore"),onClick:se=>t(te)(K.id)},{default:p(()=>[d(t(w),{icon:"tabler:restore",class:"text-red-800"}),I("span",Le,A(t(e)("Restore")),1)]),_:2},1032,["disabled","onClick"]),d(n,{disabled:!t(_)("organization.force-delete"),onClick:se=>t(L)(K.id)},{default:p(()=>[d(t(w),{icon:"tabler:trash-x",class:"text-red-800"}),I("span",Ye,A(t(e)("Delete")),1)]),_:2},1032,["disabled","onClick"])],64))]),_:2},1024)]),default:p(()=>[ie(A(t(e)("Action"))+" ",1)]),_:2},1024)]),_:2},1032,["loading","size","data","columns","pagination","onSortChange","onPageSizeChange","onPageCurrentChange","onSelectionChange"])]),_:1},8,["title","columns","onRefresh"])],2),d(t(c),{ref_key:"organizationFormRef",ref:E,visible:t(O),"onUpdate:visible":f[2]||(f[2]=y=>N(O)?O.value=y:null),values:t(B),"onUpdate:values":f[3]||(f[3]=y=>N(B)?B.value=y:null),"is-edit":!!t(B).id,onSubmit:t(q),onClose:f[4]||(f[4]=()=>{var y;(y=t(E))==null||y.resetForm(),B.value={status:"active",organizationType:"company",isVerified:!1,employeeCount:0,settings:{},metadata:{}}})},null,8,["visible","values","is-edit","onSubmit"]),d(t(o),{ref_key:"filterFormRef",ref:b,visible:t(S),"onUpdate:visible":f[5]||(f[5]=y=>N(S)?S.value=y:null),values:t(m),"onUpdate:values":f[6]||(f[6]=y=>N(m)?m.value=y:null),onSubmit:t(J),onReset:f[7]||(f[7]=()=>{m.value={isTrashed:"no"},t(P)()})},null,8,["visible","values","onSubmit"])])}}}),tt=Te(je,[["__scopeId","data-v-4e3ec332"]]);export{tt as default};
