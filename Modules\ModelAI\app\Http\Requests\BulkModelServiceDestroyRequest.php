<?php

namespace Modules\ModelAI\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class BulkModelServiceDestroyRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                Rule::exists('model_services', 'id')->whereNotNull('deleted_at'), // Only soft deleted services
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('Service IDs'),
            'ids.*' => __('Service ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Please select at least one service.'),
            'ids.array' => __('Invalid data format.'),
            'ids.min' => __('Please select at least one service.'),
            'ids.*.required' => __('Service ID is required.'),
            'ids.*.integer' => __('Service ID must be an integer.'),
            'ids.*.exists' => __('One or more selected services must be in trash to be permanently deleted.'),
        ];
    }
}
