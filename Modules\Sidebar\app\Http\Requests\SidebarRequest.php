<?php

namespace Modules\Sidebar\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class SidebarRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'menu_type' => ['required', 'integer', 'min:0'],
            'parent_id' => ['nullable', 'integer', 'exists:sidebars,id'],
            'title' => ['required', 'string', 'max:255'],
            'name' => ['required', 'string', 'max:255', 'unique:sidebars,name' . ($this->route('sidebar') ? ',' . $this->route('sidebar') : '')],
            'path' => ['required', 'string', 'max:255', 'unique:sidebars,path' . ($this->route('sidebar') ? ',' . $this->route('sidebar') : '')],
            'component' => ['nullable', 'string', 'max:255'],
            'rank' => ['nullable', 'integer', 'min:0'],
            'redirect' => ['nullable', 'string', 'max:255'],
            'icon' => ['nullable', 'string', 'max:255'],
            'extra_icon' => ['nullable', 'string', 'max:255'],
            'enter_transition' => ['nullable', 'string', 'max:255'],
            'leave_transition' => ['nullable', 'string', 'max:255'],
            'active_path' => ['nullable', 'string', 'max:255'],
            'roles' => ['nullable', 'array'],
            'roles.*' => ['exists:roles,name'],
            'auths' => ['nullable', 'array'],
            'auths.*' => ['string', 'max:255'],
            'frame_src' => ['nullable', 'string', 'max:255'],
            'frame_loading' => ['boolean'],
            'keep_alive' => ['boolean'],
            'hidden_tag' => ['boolean'],
            'fixed_tag' => ['boolean'],
            'show_link' => ['boolean'],
            'show_parent' => ['boolean'],
        ];

        // Prevent self-referencing parent
        if ($this->route('sidebar')) {
            $rules['parent_id'][] = 'not_in:' . $this->route('sidebar');
        }

        return $rules;
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'menu_type' => __('sidebar.attributes.menu_type'),
            'parent_id' => __('sidebar.attributes.parent_id'),
            'title' => __('sidebar.attributes.title'),
            'name' => __('sidebar.attributes.name'),
            'path' => __('sidebar.attributes.path'),
            'component' => __('sidebar.attributes.component'),
            'rank' => __('sidebar.attributes.rank'),
            'redirect' => __('sidebar.attributes.redirect'),
            'icon' => __('sidebar.attributes.icon'),
            'extra_icon' => __('sidebar.attributes.extra_icon'),
            'enter_transition' => __('sidebar.attributes.enter_transition'),
            'leave_transition' => __('sidebar.attributes.leave_transition'),
            'active_path' => __('sidebar.attributes.active_path'),
            'roles' => __('sidebar.attributes.roles'),
            'roles.*' => __('sidebar.attributes.role'),
            'auths' => __('sidebar.attributes.auths'),
            'auths.*' => __('sidebar.attributes.auth'),
            'frame_src' => __('sidebar.attributes.frame_src'),
            'frame_loading' => __('sidebar.attributes.frame_loading'),
            'keep_alive' => __('sidebar.attributes.keep_alive'),
            'hidden_tag' => __('sidebar.attributes.hidden_tag'),
            'fixed_tag' => __('sidebar.attributes.fixed_tag'),
            'show_link' => __('sidebar.attributes.show_link'),
            'show_parent' => __('sidebar.attributes.show_parent'),
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'roles.*.exists' => __('sidebar.validation.role_not_exists'),
            'parent_id.exists' => __('sidebar.validation.parent_not_exists'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'frame_loading' => $this->boolean('frame_loading'),
            'keep_alive' => $this->boolean('keep_alive'),
            'hidden_tag' => $this->boolean('hidden_tag'),
            'fixed_tag' => $this->boolean('fixed_tag'),
            'show_link' => $this->boolean('show_link'),
            'show_parent' => $this->boolean('show_parent'),
        ]);
    }
}
