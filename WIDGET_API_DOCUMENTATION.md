# ProcMS Chatbot Widget API Documentation

## 🚀 Overview

The ProcMS Chatbot Widget API provides a comprehensive set of endpoints for integrating chatbot functionality into web applications. The API supports both API key authentication and share token access, making it suitable for both private and public bot deployments.

## 🔐 Authentication

The Widget API supports two authentication methods:

### 1. API Key Authentication
```http
Authorization: Bearer pk_your_api_key_here
```

### 2. Share Token Authentication
Use the share token as the bot identifier in the URL path.

## 📋 Base URL

```
https://your-domain.com/api/v1/widget
```

## 🛠️ API Endpoints

### Health Check

**GET** `/health`

Check the health status of the Widget API.

**Response:**
```json
{
  "success": true,
  "message": "Widget API is healthy",
  "data": {
    "status": "healthy",
    "timestamp": "2025-07-18T00:06:56.051385Z",
    "version": "1.0.0"
  }
}
```

### Bot Configuration

**GET** `/bot/{identifier}/config`

Get bot configuration for widget initialization.

**Parameters:**
- `identifier`: Bot UUID or share token

**Headers:**
- `Authorization: Bearer {api_key}` (required for private bots)

**Response:**
```json
{
  "success": true,
  "message": "Bot configuration retrieved successfully",
  "data": {
    "uuid": "bot-uuid",
    "name": "Customer Support Assistant",
    "description": "A helpful AI assistant...",
    "logoUrl": "https://example.com/logo.png",
    "greetingMessage": "Hello! How can I help you?",
    "starterMessages": ["Help with products", "Contact support"],
    "closingMessage": "Thank you for using our service!",
    "status": "active",
    "visibility": "private",
    "theme": {
      "primaryColor": "#007bff",
      "backgroundColor": "#ffffff",
      "textColor": "#333333",
      "borderRadius": "8px",
      "fontFamily": "system-ui"
    },
    "aiModel": {
      "name": "GPT-4",
      "provider": "OpenAI"
    },
    "toolCallingMode": "auto",
    "parameters": {
      "temperature": 0.7,
      "max_tokens": 1000
    },
    "accessType": "api_key",
    "metadata": {
      "createdAt": "2025-07-17T11:36:25.000000Z",
      "updatedAt": "2025-07-17T17:37:45.000000Z"
    }
  }
}
```

### Validate Access

**GET** `/bot/{identifier}/validate`

Validate access to a bot.

**Response:**
```json
{
  "success": true,
  "message": "Access granted",
  "data": {
    "hasAccess": true,
    "accessType": "api_key",
    "bot": {
      "uuid": "bot-uuid",
      "name": "Customer Support Assistant",
      "status": "active"
    }
  }
}
```

### Create Conversation

**POST** `/conversations`

Create a new conversation with the bot.

**Request Body:**
```json
{
  "bot_uuid": "bot-uuid",
  "user_id": "optional-user-id",
  "title": "Optional conversation title"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Conversation ready",
  "data": {
    "conversationId": "conversation-uuid",
    "botUuid": "bot-uuid",
    "userId": "user-id",
    "widgetUserId": 123,
    "title": "Conversation Title",
    "status": "active",
    "createdAt": "2025-07-18T00:06:56.000000Z",
    "lastMessageAt": "2025-07-18T00:06:56.000000Z",
    "hasGreeting": true,
    "bot": {
      "uuid": "bot-uuid",
      "name": "Bot Name",
      "logoUrl": "https://example.com/logo.png",
      "greetingMessage": "Hello!"
    }
  }
}
```

### Send Message

**POST** `/conversations/{uuid}/messages`

Send a message to the bot and get a response.

**Request Body:**
```json
{
  "message": "Hello, I need help!",
  "messageType": "text",
  "attachments": [],
  "metadata": {
    "source": "widget",
    "clientTimestamp": 1642781234
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Message sent and response is being processed",
  "data": {
    "response": "Processing your message...",
    "messageId": "user-message-uuid",
    "botMessageId": "bot-message-uuid",
    "tokens": null,
    "model": "gpt-4-turbo",
    "responseTime": null,
    "status": "pending",
    "isProcessing": true,
    "suggestions": [],
    "conversation": {
      "uuid": "conversation-uuid",
      "lastMessageAt": "2025-07-18T00:06:56.000000Z"
    },
    "messages": [
      {
        "id": "user-message-uuid",
        "conversationId": "conversation-uuid",
        "type": "user",
        "content": "Hello, I need help!",
        "timestamp": "2025-07-18T00:06:56.000000Z"
      },
      {
        "id": "bot-message-uuid",
        "conversationId": "conversation-uuid",
        "type": "bot",
        "content": "Processing your message...",
        "timestamp": "2025-07-18T00:06:56.000000Z",
        "status": "pending",
        "metadata": {
          "tokens": null,
          "model": "gpt-4-turbo",
          "responseTime": null
        }
      }
    ]
  }
}
```

### Get Messages

**GET** `/conversations/{uuid}/messages`

Retrieve messages from a conversation.

**Response:**
```json
{
  "success": true,
  "message": "Messages retrieved successfully",
  "data": {
    "messages": [
      {
        "id": "message-uuid",
        "conversationId": "conversation-uuid",
        "type": "bot",
        "content": "Hello! How can I help you?",
        "timestamp": "2025-07-18T00:06:56.000000Z",
        "metadata": {
          "tokens": 15,
          "model": "gpt-4-turbo",
          "responseTime": 1.2
        }
      }
    ],
    "conversation": {
      "uuid": "conversation-uuid",
      "title": "Conversation Title"
    },
    "pagination": {
      "total": 10,
      "limit": 100
    }
  }
}
```

### Get Conversation Info

**GET** `/conversations/{uuid}`

Get information about a specific conversation.

**Response:**
```json
{
  "success": true,
  "message": "Conversation retrieved successfully",
  "data": {
    "uuid": "conversation-uuid",
    "title": "Conversation Title",
    "status": "active",
    "bot": {
      "uuid": "bot-uuid",
      "name": "Bot Name"
    },
    "lastMessageAt": "2025-07-18T00:06:56.000000Z",
    "createdAt": "2025-07-18T00:06:56.000000Z"
  }
}
```

### Check Message Status

**GET** `/conversations/{uuid}/messages/{messageUuid}/status`

Check the processing status of a specific message (useful for polling).

**Response:**
```json
{
  "success": true,
  "message": "Message status retrieved successfully",
  "data": {
    "id": "message-uuid",
    "conversationId": "conversation-uuid",
    "type": "bot",
    "content": "Here's my response!",
    "timestamp": "2025-07-18T00:06:56.000000Z",
    "status": "completed",
    "isCompleted": true,
    "isFailed": false,
    "errorMessage": null,
    "metadata": {
      "tokens": 25,
      "model": "gpt-4-turbo",
      "responseTime": 2.1
    }
  }
}
```

## 🔄 Message Processing Flow

1. **Send Message**: POST to `/conversations/{uuid}/messages`
2. **Immediate Response**: Receive user message confirmation and pending bot message
3. **Poll Status**: GET `/conversations/{uuid}/messages/{messageUuid}/status` to check processing
4. **Get Updated Messages**: GET `/conversations/{uuid}/messages` to retrieve completed responses

## 📊 Message Status Values

- `pending`: Message is being processed
- `completed`: Message processing completed successfully
- `failed`: Message processing failed

## 🚨 Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error_code": "ERROR_CODE"
}
```

Common error codes:
- `WIDGET_AUTH_FAILED`: Authentication failed
- `BOT_NOT_FOUND`: Bot not found or access denied
- `CONVERSATION_NOT_FOUND`: Conversation not found
- `MESSAGE_NOT_FOUND`: Message not found

## 🔧 Integration Example

```javascript
// Initialize widget
const apiKey = 'pk_your_api_key_here';
const botUuid = 'your-bot-uuid';
const baseUrl = 'https://your-domain.com/api/v1/widget';

// Get bot configuration
const botConfig = await fetch(`${baseUrl}/bot/${botUuid}/config`, {
  headers: { 'Authorization': `Bearer ${apiKey}` }
}).then(r => r.json());

// Create conversation
const conversation = await fetch(`${baseUrl}/conversations`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    bot_uuid: botUuid,
    user_id: 'user-123'
  })
}).then(r => r.json());

// Send message
const response = await fetch(`${baseUrl}/conversations/${conversation.data.conversationId}/messages`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    message: 'Hello!',
    messageType: 'text'
  })
}).then(r => r.json());
```

## 🎯 Best Practices

1. **Cache bot configuration** to reduce API calls
2. **Use message status polling** for real-time updates
3. **Handle authentication errors** gracefully
4. **Implement retry logic** for failed requests
5. **Store conversation IDs** for session persistence

## 📝 Notes

- All timestamps are in ISO 8601 format (UTC)
- Message processing is asynchronous - use polling for real-time updates
- Widget users are automatically created and managed
- Conversations support both authenticated and anonymous users
