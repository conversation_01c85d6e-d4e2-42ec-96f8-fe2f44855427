var c=(h,P,d)=>new Promise((x,y)=>{var m=g=>{try{n(d.next(g))}catch(p){y(p)}},b=g=>{try{n(d.throw(g))}catch(p){y(p)}},n=g=>g.done?x(g.value):Promise.resolve(g.value).then(m,b);n((d=d.apply(h,P)).next())});import{a8 as s,q as L,r as v,g as T,e as V,o as $,z as j,G as k,m as z,n as O,D as w,E as u,k as o,N as M,gP as J,P as H,M as K,ay as A,ax as f,gN as Q}from"./index-ZVLuktk4.js";import{h as F,i as W,j as X}from"./auth-api-CublKEkJ.js";import{g as Y}from"./auth-api-L1fufnq1.js";import Z from"./BotAvatarUpload-Bh2oM-m3.js";import ee from"./BotAdvancedSettings-CH4CNJOI.js";import re from"./BotBasicInfo-C6a1odXX.js";import te from"./BotAIBrain-ClmEJzm0.js";import se from"./BotChatInterface-4yBFLBGv.js";import{_ as ae}from"./BotKnowledgeBase.vue_vue_type_script_setup_true_lang-DVBtQ41X.js";const oe={name:[{required:!0,message:s("Please input bot name"),trigger:["blur","change"]},{min:2,max:100,message:s("Length should be between 2 and 100 characters"),trigger:["blur","change"]}],description:[{max:500,message:s("Description cannot exceed 500 characters"),trigger:["blur","change"]}],model:[{required:!0,message:s("Please select AI model"),trigger:["blur","change"]}],systemPrompt:[{required:!0,message:s("Please input system prompt"),trigger:["blur","change"]},{max:8e3,message:s("System prompt cannot exceed 8000 characters"),trigger:["blur","change"]}],greetingMessage:[{max:500,message:s("Greeting message cannot exceed 500 characters"),trigger:["blur","change"]}],closingMessage:[{max:500,message:s("Closing message cannot exceed 500 characters"),trigger:["blur","change"]}],status:[{required:!0,message:s("Please select status"),trigger:["blur","change"]},{required:!0,message:s("Please select status"),trigger:["blur","change"]}],visibility:[{required:!0,message:s("Please select visibility"),trigger:["blur","change"]}],botType:[{required:!0,message:s("Please select bot type"),trigger:["blur","change"]}],toolCallingMode:[{required:!0,message:s("Please select tool calling mode"),trigger:["blur","change"]}]},ne={class:"w-full h-full"},le={class:"main-grid"},ie={class:"left-column space-y-6"},ge={class:"right-column space-y-6"},ce={class:"flex justify-end gap-2"},xe=L({__name:"BotForm",props:{useBot:{}},emits:["close"],setup(h,{expose:P,emit:d}){const x=h,y=d,m=v(),b=v([]),n=T({prompt:!1,greeting:!1,starters:!1,submit:!1,library:!1}),g=v([]),p=v([]),{drawerValues:t,handleSubmit:S,handleReset:E}=x.useBot,G=(r,e,l)=>c(null,null,function*(){if(!m.value)return!1;try{if(!(yield m.value.validate()))return!1}catch(i){return console.error("Form validation failed:",i),!1}try{n.submit=!0;const i=yield S(r,e,l);return i?setTimeout(()=>{y("close"),n.submit=!1},1e3):n.submit=!1,i}catch(i){return console.error("Submit error:",i),n.submit=!1,!1}}),U=r=>c(null,null,function*(){try{const{data:e,success:l}=yield X(r);return l?e:null}catch(e){return null}}),q=r=>{!t||!r||(t.logoUrl=r.logoUrl,t.logo=r.logo)},C=()=>c(null,null,function*(){var r;try{const{value:e}=yield A.prompt(s("Briefly describe the role of the Agent:"),s("✨ Prompt Generator Assistant"),{confirmButtonText:s("Generate"),cancelButtonText:s("Cancel"),inputPlaceholder:s("Example: Vietnamese literature lesson planning assistant")});if(!e)return;n.prompt=!0;const l=yield U({type:"system_prompt",role:e});l&&(t.systemPrompt=l.trim(),f(s("Prompt generated successfully!"),{type:"success"}),yield(r=m.value)==null?void 0:r.validateField("systemPrompt"))}catch(e){e!=="cancel"&&console.error("Error generating prompt:",e)}finally{n.prompt=!1}}),R=()=>c(null,null,function*(){if(!t.name){f(s("Please enter AI Assistant Name first."),{type:"warning"});return}n.greeting=!0;try{const r=yield U({type:"greeting_message",name:t.name});r&&(t.greetingMessage=r.replace(/"/g,"").trim())}catch(r){console.error("Error generating greeting:",r)}finally{n.greeting=!1}}),_=()=>c(null,null,function*(){var r;if(!t.systemPrompt){f(s("Please create System Prompt for best suggestions."),{type:"warning"});return}n.starters=!0;try{const e=yield U({type:"starting_message",system_prompt:t.systemPrompt});if(e)try{const l=((r=e.match(new RegExp("\\[.*\\]","s")))==null?void 0:r[0])||e,i=JSON.parse(l);Array.isArray(i)&&i.every(a=>typeof a=="string")?t.starterMessages=i.slice(0,4):f(s("AI returned data not in string array format."),{type:"error"})}catch(l){console.error("JSON parsing error:",l,"Raw result:",e),f(s("Cannot parse suggestions from AI."),{type:"error"})}}catch(e){console.error("Error generating starters:",e)}finally{n.starters=!1}}),I=()=>c(null,null,function*(){try{const{data:r,success:e}=yield Y();e&&(b.value=r.map(l=>({description:l.description,value:l.key,label:`${l.name} (${l.key})`})))}catch(r){console.error("Error loading AI models:",r)}});P({resetForm:()=>{m.value&&m.value.resetFields()}});const N=V(()=>g.value),B=()=>c(null,null,function*(){try{n.library=!0;const r=yield F();r.success&&(g.value=r.data)}catch(r){console.error("Error refreshing file library:",r)}finally{n.library=!1}}),D=r=>c(null,null,function*(){try{yield A.confirm(s("Are you sure to delete this file?")),(yield W({id:r})).success&&(Q.success(s("File deleted successfully")),yield B())}catch(e){console.error("Error deleting file:",e)}});return $(()=>c(null,null,function*(){yield I();try{const r=yield F();r.success&&(g.value=r.data)}catch(r){console.error("Error loading file library:",r)}})),j(()=>{E()}),(r,e)=>{const l=k("el-button"),i=k("el-form");return O(),z("div",ne,[w("div",le,[w("div",ie,[u(Z,{"logo-url":o(t).logoUrl,onUploadSuccess:q},null,8,["logo-url"]),u(ee,{"knowledge-enabled":o(t).knowledge.enabled,"onUpdate:knowledgeEnabled":e[0]||(e[0]=a=>o(t).knowledge.enabled=a)},null,8,["knowledge-enabled"])]),w("div",ge,[u(i,{ref_key:"formRef",ref:m,model:o(t),rules:o(oe),"label-position":"top","require-asterisk-position":"right",size:"default",class:"flex flex-col gap-2"},{default:M(()=>[u(re,{values:o(t),"ai-models":b.value,"onUpdate:values":e[1]||(e[1]=a=>Object.assign(o(t),a))},null,8,["values","ai-models"]),u(te,{"system-prompt":o(t).systemPrompt,loading:n.prompt,"onUpdate:systemPrompt":e[2]||(e[2]=a=>o(t).systemPrompt=a),onGeneratePrompt:C},null,8,["system-prompt","loading"]),u(se,{"greeting-message":o(t).greetingMessage,"starter-messages":o(t).starterMessages,name:o(t).name,"logo-url":o(t).logoUrl,"loading-greeting":n.greeting,"loading-starters":n.starters,"onUpdate:greetingMessage":e[3]||(e[3]=a=>o(t).greetingMessage=a),"onUpdate:starterMessages":e[4]||(e[4]=a=>o(t).starterMessages=a),onGenerateGreeting:R,onGenerateStarters:_},null,8,["greeting-message","starter-messages","name","logo-url","loading-greeting","loading-starters"]),u(ae,{"drawer-values":o(t),loading:n.library,"file-library":N.value,"selected-files":p.value,"onUpdate:fileLibrary":e[5]||(e[5]=a=>g.value=a),"onUpdate:selectedFiles":e[6]||(e[6]=a=>p.value=a),"onUpdate:knowledgeText":e[7]||(e[7]=a=>o(t).knowledge.text=a),"onUpdate:newUploads":e[8]||(e[8]=a=>o(t).knowledge.newUploads=a),"onUpdate:libraryFiles":e[9]||(e[9]=a=>o(t).knowledge.libraryFiles=a),"onUpdate:botFiles":e[10]||(e[10]=a=>o(t).knowledge.botFiles=a),onRefreshLibrary:B,onDeleteFile:D},null,8,["drawer-values","loading","file-library","selected-files"]),w("div",ce,[u(l,{round:"",type:"danger",icon:o(J),size:"large",loading:n.submit,onClick:e[11]||(e[11]=a=>G(o(t),[],()=>{}))},{default:M(()=>[H(K(o(s)("Save")),1)]),_:1},8,["icon","loading"])])]),_:1},8,["model","rules"])])])])}}});export{xe as default};
