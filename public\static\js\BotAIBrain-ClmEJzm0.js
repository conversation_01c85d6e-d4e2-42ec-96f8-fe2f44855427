import{q as P,G as o,m as g,n as y,D as l,E as s,M as m,k as e,a8 as t,N as p,P as B,_ as x}from"./index-ZVLuktk4.js";const b={class:"card"},v={class:"flex flex-row items-center justify-between"},k={class:"section-title"},w=P({__name:"BotAIBrain",props:{systemPrompt:{},loading:{type:Boolean}},emits:["update:systemPrompt","generatePrompt"],setup(c,{emit:i}){const a=c,n=i,d=r=>{n("update:systemPrompt",r)},_=()=>{n("generatePrompt")};return(r,A)=>{const u=o("el-button"),f=o("el-input"),h=o("el-form-item");return y(),g("div",b,[l("div",v,[l("h2",k,m(e(t)("AI Brain")),1),s(u,{class:"gemini-button",loading:a.loading,round:"",onClick:_},{default:p(()=>[B(m(e(t)("✨ Prompt Generator Assistant")),1)]),_:1},8,["loading"])]),s(h,{label:e(t)("Suggestion - Prompt"),prop:"systemPrompt"},{default:p(()=>[s(f,{"model-value":a.systemPrompt,type:"textarea",rows:8,placeholder:e(t)("This is the most important part..."),"onUpdate:modelValue":d},null,8,["model-value","placeholder"])]),_:1},8,["label"])])}}}),I=x(w,[["__scopeId","data-v-8af1b54c"]]);export{I as default};
