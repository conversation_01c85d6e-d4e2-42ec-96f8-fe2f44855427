# Vue Widget API Client Update Guide

## 🔄 Cập nhật để sử dụng Backend API mới

### 1. **Cập nhật Base URL và Endpoints**

```typescript
// Trong bot-api.ts
export class BotApiClient {
  constructor(config: ApiClientConfig) {
    this.apiKey = config.apiKey;
    // Cập nhật base URL để sử dụng widget endpoints
    this.baseUrl = config.baseUrl || 'https://api.procms.com';
    if (!this.baseUrl.includes('/api/v1/widget')) {
      this.baseUrl = this.baseUrl.replace(/\/+$/, '') + '/api/v1/widget';
    }
    // ... other config
  }

  /**
   * Get bot configuration (cập nhật endpoint)
   */
  async getBotConfig(botUuid: string, forceRefresh = false): Promise<ApiResponse<BotConfig>> {
    if (forceRefresh) {
      this.clearCache(`/bot/${botUuid}/config`);
    }
    // Endpoint mới: /bot/{uuid}/config thay vì /api/public/bots/{uuid}/config
    return this.request<BotConfig>(`/bot/${botUuid}/config`);
  }

  /**
   * Validate API key and bot access (cập nhật endpoint)
   */
  async validateAccess(botUuid: string): Promise<ApiResponse<any>> {
    // Endpoint mới: /bot/{uuid}/validate
    return this.request(`/bot/${botUuid}/validate`, {}, false);
  }

  /**
   * Health check (cập nhật endpoint)
   */
  async healthCheck(): Promise<ApiResponse<any>> {
    // Endpoint mới: /health thay vì /api/health
    return this.request('/health', {}, false);
  }
}
```

### 2. **Cập nhật Conversation Management**

```typescript
export class BotApiClient {
  /**
   * Start conversation (cập nhật để sử dụng endpoint mới)
   */
  async startConversation(
    botUuid: string, 
    userId?: string,
    metadata?: Record<string, any>
  ): Promise<ApiResponse<{
    conversationId: string;
    userId: string;
    title: string;
    hasGreeting: boolean;
    bot: {
      name: string;
      logoUrl?: string;
      greetingMessage?: string;
    };
  }>> {
    const payload = {
      bot_uuid: botUuid,
      user_id: userId,
      title: metadata?.title,
      metadata
    };

    // Endpoint mới: /conversations
    return this.request('/conversations', {
      method: 'POST',
      body: JSON.stringify(payload)
    }, false);
  }

  /**
   * Get conversation info (endpoint mới)
   */
  async getConversation(conversationId: string): Promise<ApiResponse<any>> {
    return this.request(`/conversations/${conversationId}`);
  }

  /**
   * Get conversation messages (cập nhật endpoint)
   */
  async getConversationHistory(conversationId: string): Promise<ApiResponse<{
    messages: Array<{
      id: string;
      type: 'user' | 'bot';
      content: string;
      timestamp: string;
      status?: string;
    }>;
    total: number;
  }>> {
    // Endpoint mới: /conversations/{id}/messages
    return this.request(`/conversations/${conversationId}/messages`);
  }
}
```

### 3. **Cập nhật Message Sending**

```typescript
export class BotApiClient {
  /**
   * Send message (cập nhật để sử dụng endpoint và response format mới)
   */
  async sendMessage(
    botUuid: string,
    conversationId: string,
    message: string,
    options?: {
      messageType?: 'text' | 'quick_reply' | 'postback';
      attachments?: Array<{ type: string; url: string; name?: string }>;
      metadata?: Record<string, any>;
    }
  ): Promise<ApiResponse<{
    response: string;
    messageId: string;
    botMessageId: string;
    status: string;
    isProcessing: boolean;
    messages: Array<{
      id: string;
      type: 'user' | 'bot';
      content: string;
      timestamp: string;
      status?: string;
    }>;
  }>> {
    const payload = {
      message,
      messageType: options?.messageType || 'text',
      attachments: options?.attachments || [],
      metadata: {
        timestamp: new Date().toISOString(),
        clientTimestamp: Date.now(),
        ...options?.metadata
      }
    };

    // Endpoint mới: /conversations/{id}/messages
    return this.request(`/conversations/${conversationId}/messages`, {
      method: 'POST',
      body: JSON.stringify(payload)
    }, false);
  }

  /**
   * Check message status (endpoint mới)
   */
  async getMessageStatus(
    conversationId: string, 
    messageId: string
  ): Promise<ApiResponse<{
    id: string;
    type: 'user' | 'bot';
    content: string;
    timestamp: string;
    status: string;
    isCompleted: boolean;
    isFailed: boolean;
  }>> {
    return this.request(`/conversations/${conversationId}/messages/${messageId}/status`);
  }
}
```

### 4. **Cập nhật Response Handling**

```typescript
// Cập nhật BotConfig interface để match với response mới
export interface BotConfig {
  uuid: string;
  name: string;
  description?: string;
  logoUrl?: string;
  greetingMessage: string;
  starterMessages?: string[];
  status: string;
  theme?: {
    primaryColor?: string;
    backgroundColor?: string;
    textColor?: string;
    borderRadius?: string;
    fontFamily?: string;
  };
  // Removed: aiModel, toolCallingMode, parameters, accessType, metadata
}

// Cập nhật ChatMessage interface
export interface ChatMessage {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: string;
  status?: string;
  // Removed: conversationId, metadata
}
```

### 5. **Cập nhật Widget Configuration**

```typescript
// Trong widget initialization
const widget = new ProcmsChatbotWidget({
  botUuid: 'your-bot-uuid',
  apiKey: 'pk_your_api_key',
  // Cập nhật base URL để point đến widget API
  baseUrl: 'https://your-domain.com', // Sẽ tự động append /api/v1/widget
  
  // Các config khác giữ nguyên
  theme: 'light',
  position: 'bottom-right',
  // ...
});
```

### 6. **Migration Steps**

1. **Backup current implementation**
2. **Update bot-api.ts với endpoints mới**
3. **Update interfaces để match response format mới**
4. **Test bot configuration loading**
5. **Test conversation creation**
6. **Test message sending/receiving**
7. **Test message status polling**
8. **Update error handling cho response format mới**

### 7. **Breaking Changes Summary**

| Aspect | Old | New |
|--------|-----|-----|
| **Base URL** | `/api/public/bots/` | `/api/v1/widget/bot/` |
| **Health Check** | `/api/health` | `/health` |
| **Bot Config** | `/api/public/bots/{uuid}/config` | `/bot/{uuid}/config` |
| **Conversations** | Custom endpoints | `/conversations` |
| **Messages** | Custom endpoints | `/conversations/{id}/messages` |
| **Response** | Full data with metadata | Minimal data only |
| **Authentication** | Various methods | Consistent widget auth |

### 8. **Benefits của việc migration**

- ✅ **Security**: Không còn expose sensitive data
- ✅ **Performance**: Response nhỏ hơn, load nhanh hơn
- ✅ **Consistency**: Unified API structure
- ✅ **Features**: Message status polling, better error handling
- ✅ **Scalability**: Dedicated widget infrastructure

### 9. **Testing Checklist**

- [ ] Bot configuration loads correctly
- [ ] Theme applies properly
- [ ] Greeting message displays
- [ ] Starter messages work
- [ ] Conversation creation works
- [ ] Message sending works
- [ ] Message receiving works
- [ ] Message status polling works
- [ ] Error handling works
- [ ] Authentication works with API key
- [ ] Authentication works with share token
