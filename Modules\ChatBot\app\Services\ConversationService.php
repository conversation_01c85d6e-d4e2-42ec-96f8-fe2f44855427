<?php

namespace Modules\ChatBot\Services;

use Illuminate\Support\Collection;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Enums\ConversationStatus;
use Modules\ChatBot\Enums\MessageRole;
use Mo<PERSON>les\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Enums\ContentType;

class ConversationService
{
    /**
     * Create a new conversation with user permission validation.
     */
    public function createOrUpdateConversation(array $data): array
    {
        // Convert bot_uuid to bot_id if provided
        $bot = Bot::where('uuid', $data['bot'])->firstOrFail();

        if (!$bot) {
            return __('Bot not found.');
        }

        if (!$bot->isActive()) {
            return __('Bot is not active and cannot be used for conversations.');
        }

        // Set owner_id if not provided
        if (!isset($data['owner_id'])) {
            $data['owner_id'] = auth()->id();
        }

        // Check if user can access this bot
        if (!$bot->canBeAccessedBy($data['owner_id'])) {
            return __('You do not have permission to use this bot.');
        }

        // Generate title if not provided
        if (empty($data['title'])) {
            $data['title'] = $this->generateConversationTitle($bot);
        }

        $conversation = Conversation::updateOrCreate([
            'uuid' => $data['conversation_uuid'] ?? null,
            'bot_id' => $bot->id,
            'owner_id' => auth()->id(),
            'owner_type' => get_class(auth()->user()),
        ], [
            'title' => $data['title'],
            'status' => ConversationStatus::ACTIVE,
        ]);

        // Create greeting message if bot has one
        if (empty($data['conversation_uuid']) && $bot->hasGreetingMessage()) {
            $this->createGreetingMessage($conversation, $bot);
        }

        // Load fresh data với relationship
        $conversation = $conversation->fresh(['bot:id,uuid,name']);

        // Trả về đúng format bạn cần
        return $conversation->only([
            'uuid',
            'title',
            'message_count',
            'last_message_at',
            'updated_at',
            'bot'
        ]);
    }

    public function updateConversation(string $uuid, array $data): Conversation
    {
        $conversation = Conversation::query()->where([
            'uuid' => $uuid,
            'owner_id' => auth()->id(),
            'owner_type' => get_class(auth()->user()),
        ])->firstOrFail();

        $conversation->update([
            'title' => $data['title']
        ]);
        return $conversation;
    }

    public function getByUuuid($uuid)
    {
        return Conversation::where([
            'uuid' => $uuid,
            'owner_id' => auth()->id(),
            'owner_type' => get_class(auth()->user()),
        ])->firstOrFail();
    }


    /**
     * Get conversation statistics.
     */
    public function getConversationStats(Conversation $conversation): array
    {
        $messages = $conversation->messages();

        return [
            'total_messages' => $messages->count(),
            'user_messages' => $messages->fromUser()->count(),
            'assistant_messages' => $messages->fromAssistant()->count(),
            'total_tokens' => $messages->sum('total_tokens'),
            'total_cost' => $messages->sum('cost'),
            'average_response_time' => $messages->fromAssistant()->avg('response_time_ms'),
            'first_message_at' => $messages->oldest()->first()?->created_at,
            'last_message_at' => $messages->latest()->first()?->created_at,
        ];
    }

    /**
     * Generate a conversation title based on the bot.
     */
    private function generateConversationTitle(Bot $bot): string
    {
        $timestamp = now()->format('Y-m-d H:i');
        return "Cuộc trò chuyện với {$bot->name} - {$timestamp}";
    }

    /**
     * Get active conversations count for a user.
     */
    public function getActiveConversationsCount(int $userId, string $userType): int
    {
        return Conversation::forOwner($userId, $userType)
            ->active()
            ->count();
    }

    /**
     * Get conversations for a user.
     */
    public function getUserConversations(array $data = []): Collection
    {
        return Conversation::query()
            ->with([
                'bot:id,uuid,name'
            ])
            ->whereHas('bot', function ($botQuery) {
                $botQuery->active();
            })
            ->forOwner(auth()->id(), get_class(auth()->user()))
            ->active()
            ->orderBy('last_message_at', 'desc')
            ->get()
            ->map(function ($conversation) {
                $conversation->bot->append('logo_url');
                return $conversation->makeHidden(['id', 'bot_id', 'owner_id', 'owner_type', 'status', 'created_at']);
            });
    }

    /**
     * Get recent conversations for a user.
     */
    public function getRecentConversations(
        int    $userId,
        string $userType,
        int    $limit = 5
    ): Collection
    {
        return Conversation::query()
            ->with(['bot', 'latestMessage'])
            ->forOwner($userId, $userType)
            ->active()
            ->orderBy('last_message_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function createGreetingMessage(Conversation $conversation, Bot $bot): void
    {
        try {
            $messageService = app(\Modules\ChatBot\Services\MessageService::class);

            $messageService->createAssistantMessage([
                'conversation_id' => $conversation->id,
                'role' => MessageRole::ASSISTANT,
                'content' => $bot->getGreetingMessage(),
                'content_type' => ContentType::TEXT,
                'status' => MessageStatus::COMPLETED,
            ]);
        } catch (\Exception $e) {
            logger()->error('Failed to create greeting message: ' . $e->getMessage());
        }
    }

    public function deleteByUuid(string $uuid)
    {
        return Conversation::where([
            'uuid' => $uuid,
            'owner_id' => auth()->id(),
            'owner_type' => get_class(auth()->user()),
        ])->delete();
    }
}
