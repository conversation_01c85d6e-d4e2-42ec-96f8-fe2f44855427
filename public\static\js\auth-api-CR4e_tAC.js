import{aN as t,az as r}from"./index-ZVLuktk4.js";const o=e=>t.request("get","/api/auth/roles",{params:e}),u=e=>t.request("get","/api/auth/permissions",{params:e}),l=()=>t.request("get","/api/auth/roles/dropdown"),n=e=>t.request("post","/api/auth/roles",{data:r(e)}),p=(e,s)=>t.request("put",`/api/auth/roles/${e}`,{data:r(s)}),d=e=>t.request("delete",`/api/auth/roles/${e}/delete`),i=e=>t.request("delete","/api/auth/roles/bulk/delete",{data:r(e)}),c=e=>t.request("delete",`/api/auth/roles/${e}/force`),h=e=>t.request("delete","/api/auth/roles/bulk/force",{data:r(e)}),q=e=>t.request("put",`/api/auth/roles/${e}/restore`),R=e=>t.request("put","/api/auth/roles/bulk/restore",{data:r(e)});export{o as a,R as b,h as c,c as d,d as e,i as f,l as g,n as h,u as i,q as r,p as u};
