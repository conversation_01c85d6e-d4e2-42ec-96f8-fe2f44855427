<?php

namespace Modules\Sidebar\Observers;

use Illuminate\Support\Facades\Cache;
use Modules\Sidebar\Models\Sidebar;

class SidebarObserver
{
    /**
     * Cache tag for sidebars
     */
    private const CACHE_TAG = 'sidebars';

    /**
     * Handle the Sidebar "created" event.
     */
    public function created(Sidebar $sidebar): void
    {
        $this->clearSidebarCache($sidebar);
    }

    /**
     * Handle the Sidebar "updated" event.
     */
    public function updated(Sidebar $sidebar): void
    {
        $this->clearSidebarCache($sidebar);
    }

    /**
     * Handle the Sidebar "deleted" event.
     */
    public function deleted(Sidebar $sidebar): void
    {
        $this->clearSidebarCache($sidebar);
    }

    /**
     * Handle the Sidebar "restored" event.
     */
    public function restored(Sidebar $sidebar): void
    {
        $this->clearSidebarCache($sidebar);
    }

    /**
     * Handle the Sidebar "force deleted" event.
     */
    public function forceDeleted(Sidebar $sidebar): void
    {
        $this->clearSidebarCache($sidebar);
    }

    /**
     * Clear cache for sidebars.
     */
    private function clearSidebarCache(Sidebar $sidebar): void
    {
        if (enabledCache()) {
            Cache::tags([self::CACHE_TAG])->flush();
        }
    }
}
