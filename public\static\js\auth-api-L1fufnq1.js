var q=Object.defineProperty,M=Object.defineProperties;var A=Object.getOwnPropertyDescriptors;var d=Object.getOwnPropertySymbols;var i=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable;var l=(e,t,a)=>t in e?q(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,p=(e,t)=>{for(var a in t||(t={}))i.call(t,a)&&l(e,a,t[a]);if(d)for(var a of d(t))n.call(t,a)&&l(e,a,t[a]);return e},c=(e,t)=>M(e,A(t));var m=(e,t)=>{var a={};for(var r in e)i.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(e!=null&&d)for(var r of d(e))t.indexOf(r)<0&&n.call(e,r)&&(a[r]=e[r]);return a};import{aN as o,az as s}from"./index-ZVLuktk4.js";const f=e=>o.request("get","/api/auth/model-ai",{params:e}),g=()=>o.request("get","/api/auth/model-ai/dropdown"),D=e=>o.request("post","/api/auth/model-ai",{data:s(e)}),v=(e,t)=>o.request("put",`/api/auth/model-ai/${e}`,{data:s(t)}),w=e=>o.request("delete",`/api/auth/model-ai/${e}/delete`),P=e=>o.request("delete","/api/auth/model-ai/bulk/delete",{data:s(e)}),$=e=>o.request("delete",`/api/auth/model-ai/${e}/force`),y=e=>o.request("delete","/api/auth/model-ai/bulk/force",{data:s(e)}),C=e=>o.request("put",`/api/auth/model-ai/${e}/restore`),S=e=>o.request("put","/api/auth/model-ai/bulk/restore",{data:s(e)}),_=e=>{const u=e,{allowedParameters:t,defaultParameters:a}=u,r=m(u,["allowedParameters","defaultParameters"]),h=c(p({},s(r)),{allowed_parameters:t,default_parameters:a});return o.request("post","/api/auth/model-ai/service",{data:h})};export{f as a,S as b,y as c,$ as d,w as e,P as f,g,v as h,D as i,C as r,_ as u};
