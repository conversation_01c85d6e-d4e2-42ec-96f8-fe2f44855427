var P=(b,g,p)=>new Promise((c,t)=>{var d=s=>{try{u(p.next(s))}catch(n){t(n)}},i=s=>{try{u(p.throw(s))}catch(n){t(n)}},u=s=>s.done?c(s.value):Promise.resolve(s.value).then(d,i);u((p=p.apply(b,g)).next())});/* empty css                         */import{q as k,r as y,a8 as e,e as a,y as F,G as N,Q as Y,n as _,N as v,D as h,E as q,P as C,M as w,k as f,aA as M}from"./index-ZVLuktk4.js";import{g as A}from"./auth-api-CR4e_tAC.js";import{P as B}from"./index-CQnBjWL2.js";import"./_plugin-vue_export-helper-QGN-qG8u.js";const I={class:"custom-group-header"},S={class:"font-semibold"},x={class:"custom-footer"},G=k({__name:"UserDrawerForm",props:{visible:{type:Boolean},values:{}},emits:["update:visible","update:values","submit"],setup(b,{expose:g,emit:p}){const c=b,t=p,d=y(!1),i=y(),u=y([]),s=()=>P(null,null,function*(){var l;try{const r=yield A();u.value=(l=M(r.data))==null?void 0:l.map(o=>({label:o.name,value:o.id}))}catch(r){console.error("Error fetching roles:",r)}}),n=[{label:a(()=>e("Username")),prop:"username",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input username"),trigger:["blur"]}],fieldProps:{placeholder:""},colProps:{span:12}},{label:a(()=>e("Password")),prop:"password",valueType:"input",rules:[{required:!0,message:e("Please input password"),trigger:["blur"]},{min:6,message:e("Password must be at least 6 characters"),trigger:["blur"]}],fieldProps:{placeholder:"",type:"password",showPassword:!0},colProps:{span:12}},{label:a(()=>e("First Name")),prop:"firstName",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input first name"),trigger:["blur"]}],fieldProps:{placeholder:""},colProps:{span:12}},{label:a(()=>e("Last Name")),prop:"lastName",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input last name"),trigger:["blur"]}],fieldProps:{placeholder:""},colProps:{span:12}},{label:a(()=>e("Birthday")),prop:"birthday",valueType:"date-picker",fieldProps:{placeholder:"",type:"date",format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD"},colProps:{span:12}},{label:a(()=>e("Gender")),prop:"gender",valueType:"select",options:[{label:e("Male"),value:"male"},{label:e("Female"),value:"female"},{label:e("Other"),value:"other"}],fieldProps:{placeholder:"",clearable:!1},colProps:{span:12}},{label:a(()=>e("Email")),prop:"email",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input email"),trigger:["blur"]},{type:"email",message:e("Please input valid email"),trigger:["blur"]}],fieldProps:{placeholder:""},colProps:{span:12}},{label:a(()=>e("Phone")),prop:"phone",valueType:"input",fieldProps:{placeholder:""},colProps:{span:12}},{label:a(()=>e("Address")),prop:"address",valueType:"textarea",fieldProps:{placeholder:"",rows:3},colProps:{span:24}},{label:a(()=>e("Roles")),prop:"roles",required:!0,rules:[{required:!0,message:e("Please select at least one role"),trigger:["change"]}],valueType:"select",fieldProps:{placeholder:"",multiple:!0,clearable:!0,filterable:!0,maxCollapseTags:3,collapseTagsTooltip:!0},options:a(()=>[...u.value]),colProps:{span:12}},{label:a(()=>e("Status")),prop:"status",valueType:"select",required:!0,options:[{label:e("Active"),value:"active"},{label:e("Inactive"),value:"inactive"},{label:e("Suspended"),value:"suspended"},{label:e("Banned"),value:"banned"},{label:e("Pending"),value:"pending"}],fieldProps:{placeholder:"",clearable:!1},colProps:{span:12}}];F(()=>c.visible,l=>{l&&(s(),n[1].rules[0].required=!c.values.id)},{immediate:!0});const D=l=>P(null,null,function*(){var o;if(!(!((o=i.value)!=null&&o.formInstance)||!(yield i.value.formInstance.validate())))try{d.value=!0,t("submit",l)}finally{setTimeout(()=>{d.value=!1},3e3)}}),T=()=>{var l;(l=i.value)!=null&&l.formInstance&&i.value.formInstance.resetFields()};return g({resetForm:T}),(l,r)=>{const o=N("el-button");return _(),Y(f(B),{ref_key:"formRef",ref:i,visible:l.visible,"model-value":l.values,size:"40%",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0,destroyOnClose:!0,form:{columns:n,footerAlign:"center",hasFooter:!1,labelSuffix:"",labelPosition:"top",labelWidth:"auto",labelAlign:"left",requireAsteriskPosition:"right",rowProps:{gutter:10}},"onUpdate:visible":r[2]||(r[2]=m=>t("update:visible",m)),"onUpdate:modelValue":r[3]||(r[3]=m=>t("update:values",m)),onClose:T},{"drawer-header":v(()=>[h("div",I,[h("span",S,w(f(e)("Information Form")),1)])]),"drawer-footer":v(()=>[h("div",x,[q(o,{plain:"",onClick:r[0]||(r[0]=m=>t("update:visible",!1))},{default:v(()=>[C(w(f(e)("Cancel")),1)]),_:1}),q(o,{type:"primary",loading:d.value,onClick:r[1]||(r[1]=m=>D(l.values))},{default:v(()=>[C(w(f(e)("Submit")),1)]),_:1},8,["loading"])])]),_:1},8,["visible","model-value","form"])}}});export{G as default};
