import{a8 as n,u as E}from"./index-ZVLuktk4.js";const P=/^[^\s@]+@[^\s@]+\.[^\s@]+$/,p=(e,i={})=>{const{minLength:t=6,requireNumbers:o=!1,requireSymbols:r=!1,requireUppercase:a=!1,requireLowercase:d=!1}=i;let u=0;const f=[];e.length>=t?u+=1:f.push(n("Password must be at least :length characters",{length:t})),/\d/.test(e)?u+=1:o&&f.push(n("Password must contain at least one number")),/[!@#$%^&*(),.?":{}|<>]/.test(e)?u+=1:r&&f.push(n("Password must contain at least one symbol")),/[A-Z]/.test(e)?u+=1:a&&f.push(n("Password must contain at least one uppercase letter")),/[a-z]/.test(e)?u+=1:d&&f.push(n("Password must contain at least one lowercase letter")),e.length>=12&&(u+=1),/[!@#$%^&*(),.?":{}|<>]/.test(e)&&/\d/.test(e)&&/[A-Z]/.test(e)&&/[a-z]/.test(e)&&(u+=1);const m=f.length===0;return{score:Math.min(u,4),feedback:f,isValid:m}},L=()=>(e,i,t)=>{if(!i){t(new Error(n("Email is required")));return}if(!P.test(i)){t(new Error(n("Please enter a valid email address")));return}t()},S=(e={})=>(i,t,o)=>{var d,u,f,m,l,h,q,w,c,g;if(!t){o(new Error(n("Password is required")));return}let r=e;if(e.useSettings!==!1){const s=(u=(d=E())==null?void 0:d.settings)==null?void 0:u.security;r={minLength:(s==null?void 0:s.passwordMinLength)||e.minLength||6,requireNumbers:(m=(f=s==null?void 0:s.passwordRequireNumbers)!=null?f:e.requireNumbers)!=null?m:!1,requireSymbols:(h=(l=s==null?void 0:s.passwordRequireSymbols)!=null?l:e.requireSymbols)!=null?h:!1,requireUppercase:(w=(q=s==null?void 0:s.passwordRequireUppercase)!=null?q:e.requireUppercase)!=null?w:!1,requireLowercase:(g=(c=s==null?void 0:s.passwordRequireLowercases)!=null?c:e.requireLowercase)!=null?g:!1}}const a=p(t,r);if(!a.isValid){o(new Error(a.feedback[0]));return}o()},b=e=>(i,t,o)=>{if(!t){o(new Error(n("Please confirm your password")));return}const r=i.form||i.model;if(r&&r[e]!==t){o(new Error(n("Passwords do not match")));return}o()},R=(e={})=>{const{length:i=6,required:t=!0}=e;return(o,r,a)=>{if(!r){t?a(new Error(n("Verification code is required"))):a();return}if(r.length!==i){a(new Error(n("Verification code must be :length digits",{length:i})));return}if(!/^\d+$/.test(r)){a(new Error(n("Verification code must contain only numbers")));return}a()}},_=(e={})=>{const{required:i=!1}=e;return(t,o,r)=>{if(!o){i?r(new Error(n("Phone number is required"))):r();return}const a=o.replace(/\D/g,"");if(a.length<10||a.length>15){r(new Error(n("Please enter a valid phone number")));return}r()}};export{S as a,_ as b,b as c,L as d,R as e};
