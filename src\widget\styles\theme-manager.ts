/**
 * Theme Manager for ProcMS Widget
 * Handles theme switching, custom themes, and CSS variable management
 */

export interface ThemeConfig {
  // Primary colors
  primaryColor?: string;
  primaryHover?: string;
  primaryActive?: string;
  primaryLight?: string;
  
  // Background colors
  backgroundColor?: string;
  surfaceColor?: string;
  borderColor?: string;
  
  // Text colors
  textPrimary?: string;
  textSecondary?: string;
  textMuted?: string;
  textInverse?: string;
  
  // Message colors
  userMessageBg?: string;
  userMessageText?: string;
  botMessageBg?: string;
  botMessageText?: string;
  botMessageBorder?: string;
  
  // Typography
  fontFamily?: string;
  fontSize?: string;
  fontWeight?: string;
  lineHeight?: string;
  
  // Spacing and layout
  borderRadius?: string;
  spacing?: string;
  
  // Shadows
  shadow?: string;
  shadowLg?: string;
  
  // Custom CSS variables
  customVariables?: Record<string, string>;
}

export type ThemeMode = 'light' | 'dark' | 'auto';

export class ThemeManager {
  private container: HTMLElement;
  private currentTheme: ThemeMode = 'light';
  private customTheme: ThemeConfig | null = null;
  private mediaQuery: MediaQueryList;
  private observers: Array<(theme: ThemeMode) => void> = [];

  constructor(container: HTMLElement) {
    this.container = container;
    this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    this.setupMediaQueryListener();
    this.initializeTheme();
  }

  /**
   * Set theme mode
   */
  setTheme(theme: ThemeMode): void {
    this.currentTheme = theme;
    this.applyTheme();
    this.notifyObservers();
    this.saveThemePreference();
  }

  /**
   * Get current theme mode
   */
  getTheme(): ThemeMode {
    return this.currentTheme;
  }

  /**
   * Get effective theme (resolves 'auto' to actual theme)
   */
  getEffectiveTheme(): 'light' | 'dark' {
    if (this.currentTheme === 'auto') {
      return this.mediaQuery.matches ? 'dark' : 'light';
    }
    return this.currentTheme;
  }

  /**
   * Apply custom theme configuration
   */
  setCustomTheme(theme: ThemeConfig): void {
    this.customTheme = theme;
    this.applyCustomTheme();
  }

  /**
   * Get current custom theme
   */
  getCustomTheme(): ThemeConfig | null {
    return this.customTheme;
  }

  /**
   * Reset to default theme
   */
  resetTheme(): void {
    this.customTheme = null;
    this.removeCustomVariables();
    this.applyTheme();
  }

  /**
   * Add theme change observer
   */
  onThemeChange(callback: (theme: ThemeMode) => void): () => void {
    this.observers.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.observers.indexOf(callback);
      if (index > -1) {
        this.observers.splice(index, 1);
      }
    };
  }

  /**
   * Generate CSS variables from theme config
   */
  generateCSSVariables(theme: ThemeConfig): Record<string, string> {
    const variables: Record<string, string> = {};

    // Map theme config to CSS variables
    const mapping: Record<keyof ThemeConfig, string> = {
      primaryColor: '--procms-primary',
      primaryHover: '--procms-primary-hover',
      primaryActive: '--procms-primary-active',
      primaryLight: '--procms-primary-light',
      backgroundColor: '--procms-widget-bg',
      surfaceColor: '--procms-widget-surface',
      borderColor: '--procms-widget-border',
      textPrimary: '--procms-text-primary',
      textSecondary: '--procms-text-secondary',
      textMuted: '--procms-text-muted',
      textInverse: '--procms-text-inverse',
      userMessageBg: '--procms-user-message-bg',
      userMessageText: '--procms-user-message-text',
      botMessageBg: '--procms-bot-message-bg',
      botMessageText: '--procms-bot-message-text',
      botMessageBorder: '--procms-bot-message-border',
      fontFamily: '--procms-font-family',
      fontSize: '--procms-font-size-base',
      fontWeight: '--procms-font-weight-normal',
      lineHeight: '--procms-line-height-normal',
      borderRadius: '--procms-widget-border-radius',
      spacing: '--procms-space-4',
      shadow: '--procms-widget-shadow',
      shadowLg: '--procms-widget-shadow-lg'
    };

    // Apply mapped variables
    Object.entries(mapping).forEach(([key, cssVar]) => {
      const value = theme[key as keyof ThemeConfig];
      if (value) {
        variables[cssVar] = value;
      }
    });

    // Apply custom variables
    if (theme.customVariables) {
      Object.entries(theme.customVariables).forEach(([key, value]) => {
        variables[key.startsWith('--') ? key : `--${key}`] = value;
      });
    }

    return variables;
  }

  /**
   * Apply CSS variables to container
   */
  applyCSSVariables(variables: Record<string, string>): void {
    Object.entries(variables).forEach(([property, value]) => {
      this.container.style.setProperty(property, value);
    });
  }

  /**
   * Remove custom CSS variables
   */
  removeCustomVariables(): void {
    if (!this.customTheme) return;

    const variables = this.generateCSSVariables(this.customTheme);
    Object.keys(variables).forEach(property => {
      this.container.style.removeProperty(property);
    });
  }

  /**
   * Get theme from bot configuration
   */
  applyBotTheme(botTheme: any): void {
    if (!botTheme) return;

    const themeConfig: ThemeConfig = {
      primaryColor: botTheme.primaryColor,
      backgroundColor: botTheme.backgroundColor,
      textPrimary: botTheme.textColor,
      fontFamily: botTheme.fontFamily,
      borderRadius: botTheme.borderRadius,
      customVariables: botTheme.customVariables
    };

    this.setCustomTheme(themeConfig);
  }

  /**
   * Export current theme as CSS
   */
  exportThemeCSS(): string {
    const variables = this.customTheme 
      ? this.generateCSSVariables(this.customTheme)
      : {};

    const cssRules = Object.entries(variables)
      .map(([property, value]) => `  ${property}: ${value};`)
      .join('\n');

    return `.procms-chatbot-widget {\n${cssRules}\n}`;
  }

  /**
   * Validate color format
   */
  private isValidColor(color: string): boolean {
    const style = new Option().style;
    style.color = color;
    return style.color !== '';
  }

  /**
   * Convert hex to RGB
   */
  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  /**
   * Generate color variations
   */
  generateColorVariations(baseColor: string): Partial<ThemeConfig> {
    if (!this.isValidColor(baseColor)) {
      console.warn('Invalid color provided:', baseColor);
      return {};
    }

    const rgb = this.hexToRgb(baseColor);
    if (!rgb) return {};

    // Generate hover and active states
    const hoverColor = `rgb(${Math.max(0, rgb.r - 20)}, ${Math.max(0, rgb.g - 20)}, ${Math.max(0, rgb.b - 20)})`;
    const activeColor = `rgb(${Math.max(0, rgb.r - 40)}, ${Math.max(0, rgb.g - 40)}, ${Math.max(0, rgb.b - 40)})`;
    const lightColor = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`;

    return {
      primaryColor: baseColor,
      primaryHover: hoverColor,
      primaryActive: activeColor,
      primaryLight: lightColor
    };
  }

  /**
   * Initialize theme from saved preference or system
   */
  private initializeTheme(): void {
    const savedTheme = this.getSavedThemePreference();
    if (savedTheme) {
      this.currentTheme = savedTheme;
    } else {
      this.currentTheme = 'auto';
    }
    this.applyTheme();
  }

  /**
   * Apply current theme to container
   */
  private applyTheme(): void {
    // Remove existing theme classes
    this.container.classList.remove(
      'procms-theme-light',
      'procms-theme-dark',
      'procms-theme-auto'
    );

    // Add current theme class
    this.container.classList.add(`procms-theme-${this.currentTheme}`);

    // Set data attribute for CSS targeting
    this.container.setAttribute('data-procms-theme', this.currentTheme);

    // Apply custom theme if exists
    if (this.customTheme) {
      this.applyCustomTheme();
    }
  }

  /**
   * Apply custom theme variables
   */
  private applyCustomTheme(): void {
    if (!this.customTheme) return;

    const variables = this.generateCSSVariables(this.customTheme);
    this.applyCSSVariables(variables);
  }

  /**
   * Setup media query listener for auto theme
   */
  private setupMediaQueryListener(): void {
    this.mediaQuery.addEventListener('change', () => {
      if (this.currentTheme === 'auto') {
        this.notifyObservers();
      }
    });
  }

  /**
   * Notify theme change observers
   */
  private notifyObservers(): void {
    this.observers.forEach(callback => {
      try {
        callback(this.currentTheme);
      } catch (error) {
        console.error('Error in theme change observer:', error);
      }
    });
  }

  /**
   * Save theme preference to localStorage
   */
  private saveThemePreference(): void {
    try {
      localStorage.setItem('procms-widget-theme', this.currentTheme);
    } catch (error) {
      // Ignore localStorage errors
    }
  }

  /**
   * Get saved theme preference
   */
  private getSavedThemePreference(): ThemeMode | null {
    try {
      const saved = localStorage.getItem('procms-widget-theme');
      if (saved && ['light', 'dark', 'auto'].includes(saved)) {
        return saved as ThemeMode;
      }
    } catch (error) {
      // Ignore localStorage errors
    }
    return null;
  }

  /**
   * Cleanup
   */
  destroy(): void {
    this.observers.length = 0;
    this.removeCustomVariables();
    this.mediaQuery.removeEventListener('change', this.setupMediaQueryListener);
  }
}

// Utility functions
export const themeUtils = {
  /**
   * Create theme manager instance
   */
  createThemeManager(container: HTMLElement): ThemeManager {
    return new ThemeManager(container);
  },

  /**
   * Validate theme configuration
   */
  validateThemeConfig(theme: ThemeConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate colors
    const colorFields = ['primaryColor', 'backgroundColor', 'textPrimary'];
    colorFields.forEach(field => {
      const value = theme[field as keyof ThemeConfig];
      if (value && typeof value === 'string') {
        const style = new Option().style;
        style.color = value;
        if (!style.color) {
          errors.push(`Invalid color format for ${field}: ${value}`);
        }
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Merge theme configurations
   */
  mergeThemes(base: ThemeConfig, override: ThemeConfig): ThemeConfig {
    return {
      ...base,
      ...override,
      customVariables: {
        ...base.customVariables,
        ...override.customVariables
      }
    };
  }
};
