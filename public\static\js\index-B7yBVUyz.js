var U=(d,t,o)=>new Promise((m,r)=>{var e=c=>{try{g(o.next(c))}catch(_){r(_)}},i=c=>{try{g(o.throw(c))}catch(_){r(_)}},g=c=>c.done?m(c.value):Promise.resolve(c.value).then(e,i);g((o=o.apply(d,t)).next())});import{q as z,B as ae,ai as ie,a7 as le,g as Z,a8 as l,ad as M,u as J,r as S,o as de,y as B,m as D,n as V,k as s,fL as ue,e as A,fM as ce,fN as me,D as v,E as n,a4 as fe,G as C,N as u,Q as G,X as pe,a6 as ge,i as _e,ap as ye,I as we,J as ve,L as he,H as xe,P as I,M as E,aC as Ce,ax as R,fO as be,s as Se,_ as Re}from"./index-ZVLuktk4.js";import{a as Ve,u as Le,b as Ie,d as Ee}from"./dark-C3BJic5q.js";import{u as Pe}from"./useLayout-B65ZqU-V.js";import{b as $e,i as qe,a as Ne,T as Te}from"./index-BnPdKjjT.js";import{u as k}from"./hooks-CuzZ-_om.js";import{u as Ue,g as ke}from"./useTranslation-Bgn2GAKO.js";import{d as Me}from"./lock-fill-BZPnsoM7.js";import{d as Be}from"./user-3-fill-xIOYikcN.js";import De from"./SocialLogin-ejeq2OH_.js";import"./epTheme-DzIV6EF-.js";const b=z({name:"Motion",props:{delay:{type:Number,default:50}},render(){const{delay:d}=this,t=le("motion");return ae(ie("div",{},{default:()=>[this.$slots.default()]}),[[t,{initial:{opacity:0,y:100},enter:{opacity:1,y:0,transition:{delay:d}}}]])}}),ze=(d,t,o)=>{if(!t||t.trim()===""){o(new Error(l("Username/email required")));return}if(/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)){o();return}const r=3,e=20;if(t.length<r){o(new Error(l("Username too short (min {count})",{count:r})));return}if(t.length>e){o(new Error(l("Username too long (max {count})",{count:e})));return}if(!/^[a-zA-Z0-9_-]+$/.test(t)){o(new Error(l("Invalid username format")));return}o()},Fe=(d,t,o)=>{var h;if(!t||t.trim()===""){o(new Error(l("Password is required")));return}const r=((h=J().settings)==null?void 0:h.security)||{},e=r.passwordMinLength||6,i=r.passwordRequireNumbers||!1,g=r.passwordRequireSymbols||!1,c=r.passwordRequireUppercase||!1,_=r.passwordRequireLowercase||!1;if(t.length<e){o(new Error(l("Password too short (min {count})",{count:e})));return}if(i&&!/\d/.test(t)){o(new Error(l("Password needs numbers")));return}if(g&&!/[!@#$%^&*(),.?":{}|<>]/.test(t)){o(new Error(l("Password needs symbols")));return}if(c&&!/[A-Z]/.test(t)){o(new Error(l("Password needs uppercase")));return}if(_&&!/[a-z]/.test(t)){o(new Error(l("Password needs lowercase")));return}o()},He=Z({login:[{validator:ze,trigger:"blur"}],password:[{validator:Fe,trigger:"blur"}],verifyCode:[{required:!0,message:l("Verification code required"),trigger:"blur"},{validator:(d,t,o)=>{t===""?o(new Error(l("Verification code required"))):M().verifyCode!==t?o(new Error(l("Verification code incorrect"))):o()},trigger:"blur"}]}),je=(d=120,t=40)=>{const o=S(),m=S("");function r(i){m.value=i}function e(){o.value&&(m.value=Oe(o.value,d,t))}return de(()=>{e()}),{domRef:o,imgCode:m,setImgCode:r,getImgCode:e}};function w(d,t){return Math.floor(Math.random()*(t-d)+d)}function P(d,t){const o=w(d,t),m=w(d,t),r=w(d,t);return`rgb(${o},${m},${r})`}function Oe(d,t,o){let m="";const r="0123456789",e=d.getContext("2d");if(!e)return m;e.fillStyle=P(180,230),e.fillRect(0,0,t,o);for(let i=0;i<4;i+=1){const g=r[w(0,r.length)];m+=g;const c=w(18,41),_=w(-30,30);e.font=`${c}px Simhei`,e.textBaseline="top",e.fillStyle=P(80,150),e.save(),e.translate(30*i+15,15),e.rotate(_*Math.PI/180),e.fillText(g,-10,-15),e.restore()}for(let i=0;i<5;i+=1)e.beginPath(),e.moveTo(w(0,t),w(0,o)),e.lineTo(w(0,t),w(0,o)),e.strokeStyle=P(180,230),e.closePath(),e.stroke();for(let i=0;i<41;i+=1)e.beginPath(),e.arc(w(0,t),w(0,o),1,0,2*Math.PI),e.closePath(),e.fillStyle=P(150,200),e.fill();return m}const Ae=z({name:"ReImageVerify",__name:"index",props:{code:{default:""}},emits:["update:code"],setup(d,{expose:t,emit:o}){const m=d,r=o,{domRef:e,imgCode:i,setImgCode:g,getImgCode:c}=je();return B(()=>m.code,_=>{g(_)}),B(i,_=>{r("update:code",_)}),t({getImgCode:c}),(_,h)=>(V(),D("canvas",{ref_key:"domRef",ref:e,width:"120",height:"40",class:"cursor-pointer",onClick:h[0]||(h[0]=(...$)=>s(c)&&s(c)(...$))},null,512))}}),Ge={class:"select-none"},Ze=["src"],Je={class:"flex-c absolute right-5 top-3"},Qe={class:"login-container"},We={class:"img"},Xe={class:"login-box"},Ke={class:"login-form"},Ye={class:"outline-none"},eo={class:"flex justify-between w-full items-center mb-3"},oo={class:"w-full text-left"},to={class:"w-full text-end"},so={class:"flex justify-between mt-3 items-center"},no=z({name:"Login",__name:"index",setup(d){const t=ue(),o=J(),m=Ue();Object.keys(o.settings).length===0&&o.fetchPublicSettings();const r=_e(),e=S(!1),i=S(!1),g=S(),c=S(""),{initStorage:_}=Pe();_();const{dataTheme:h,overallStyle:$,dataThemeChange:F}=Ve();F($.value);const{title:Q,getDropdownItemStyle:W,getDropdownItemClass:X}=Le(),x=Z({login:null,password:null,verifyCode:null}),K=A(()=>{var f;return(f=t.languages)==null?void 0:f.map(a=>({locale:a.code,native:a.nativeName}))}),H=A(()=>t.locale||ce.global.locale.value),j=f=>U(null,null,function*(){var a,L;if(f)try{yield f.validate(),e.value=!0;const y=yield M().loginByCredential(x);if(!y.success){R(y.message,{type:"error"});return}i.value=!0;try{yield be(),yield r.push(Se(!0).path),R(y.message,{type:"success"})}catch(O){R(l("Router Error"),{type:"error"})}finally{i.value=!1}}catch(y){R(((L=(a=y==null?void 0:y.response)==null?void 0:a.data)==null?void 0:L.message)||(y==null?void 0:y.message)||l("Some information is incorrect. Please review and try again."),{type:"error"})}finally{e.value=!1}}),Y=ye(f=>j(f),1e3,!0),ee=f=>{m.switchLanguage(f)},oe=f=>U(null,null,function*(){try{e.value=!0;const a="https://cloudai.com.vn";window.location.href=`${a}/auth/social/${f}`}catch(a){R((a==null?void 0:a.message)||l("Social login failed"),{type:"error"})}finally{e.value=!1}}),te=()=>{r.push("/register")},se=()=>{r.push("/forget")};return B(c,f=>{M().setVerifyCode(f)}),me(document,"keydown",({code:f})=>{["Enter","NumpadEnter"].includes(f)&&!i.value&&!e.value&&Y(g.value)}),(f,a)=>{const L=C("el-switch"),y=C("el-dropdown-item"),O=C("el-dropdown-menu"),ne=C("el-dropdown"),q=C("el-input"),N=C("el-form-item"),T=C("el-button"),re=C("el-form");return V(),D("div",Ge,[v("img",{src:s($e),class:"wave",alt:"Bg"},null,8,Ze),v("div",Je,[n(L,{modelValue:s(h),"onUpdate:modelValue":a[0]||(a[0]=p=>fe(h)?h.value=p:null),"inline-prompt":"","active-icon":s(Ee),"inactive-icon":s(Ie),onChange:s(F)},null,8,["modelValue","active-icon","inactive-icon","onChange"]),n(ne,{trigger:"click"},{dropdown:u(()=>[n(O,{class:"translation"},{default:u(()=>[(V(!0),D(we,null,ve(K.value,p=>(V(),G(y,{key:p.locale,style:xe(s(W)(H.value,p.locale)),class:he(["dark:!text-white",s(X)(H.value,p.locale)]),onClick:ro=>ee(p.locale)},{default:u(()=>[I(E(p.native),1)]),_:2},1032,["style","class","onClick"]))),128))]),_:1})]),default:u(()=>[n(s(ke),{class:"hover:text-primary hover:!bg-[transparent] w-[20px] h-[20px] ml-1.5 cursor-pointer outline-none duration-300"})]),_:1})]),v("div",Qe,[v("div",We,[(V(),G(pe(ge(s(qe)))))]),v("div",Xe,[v("div",Ke,[n(s(Ne),{class:"avatar"}),n(s(b),null,{default:u(()=>[n(s(b),null,{default:u(()=>[v("h2",Ye,[n(s(Te),{options:{strings:[s(Q)],cursor:!1,speed:100}},null,8,["options"])])]),_:1})]),_:1}),n(re,{ref_key:"ruleFormRef",ref:g,model:x,rules:s(He),size:"large"},{default:u(()=>[n(s(b),{delay:100},{default:u(()=>[n(N,{prop:"login"},{default:u(()=>[n(q,{modelValue:x.login,"onUpdate:modelValue":a[1]||(a[1]=p=>x.login=p),clearable:"",placeholder:s(l)("Username/Email"),"prefix-icon":s(k)(s(Be))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),n(s(b),{delay:150},{default:u(()=>[n(N,{prop:"password"},{default:u(()=>[n(q,{modelValue:x.password,"onUpdate:modelValue":a[2]||(a[2]=p=>x.password=p),clearable:"","show-password":"",placeholder:s(l)("Password"),"prefix-icon":s(k)(s(Me))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),n(s(b),{delay:200},{default:u(()=>[n(N,{prop:"verifyCode"},{default:u(()=>[n(q,{modelValue:x.verifyCode,"onUpdate:modelValue":a[4]||(a[4]=p=>x.verifyCode=p),clearable:"",placeholder:s(l)("VerifyCode"),"prefix-icon":s(k)("ri:shield-keyhole-line")},{append:u(()=>[n(Ae,{code:c.value,"onUpdate:code":a[3]||(a[3]=p=>c.value=p)},null,8,["code"])]),_:1},8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),n(s(b),{delay:230},{default:u(()=>[v("div",eo,[v("div",oo,[n(T,{link:"",type:"primary",onClick:te},{default:u(()=>[I(E(s(l)("Register")),1)]),_:1})]),v("div",to,[n(T,{link:"",type:"primary",onClick:se},{default:u(()=>[I(E(s(l)("Forgot password?")),1)]),_:1})])])]),_:1}),n(s(b),{delay:250},{default:u(()=>[v("div",so,[n(T,{class:"w-full !uppercase",size:"large",type:"danger",round:"",loading:e.value,disabled:i.value,onClick:a[5]||(a[5]=p=>j(g.value))},{default:u(()=>[n(s(Ce),{icon:"ri:login-box-line",width:"20",class:"mr-2"}),I(" "+E(s(l)("Login")),1)]),_:1},8,["loading","disabled"])])]),_:1}),n(s(b),{delay:350},{default:u(()=>[n(De,{loading:e.value,disabled:i.value,onSocialLogin:oe},null,8,["loading","disabled"])]),_:1})]),_:1},8,["model","rules"])])])])])}}}),wo=Re(no,[["__scopeId","data-v-e5990b49"]]);export{wo as default};
