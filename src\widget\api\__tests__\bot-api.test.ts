/**
 * Bot API Client Tests
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { BotApiClient, ApiError, getBotApiClient } from '../bot-api';

// Mock fetch
global.fetch = vi.fn();

describe('BotApiClient', () => {
  let apiClient: BotApiClient;
  const mockApiKey = 'pk_test_12345678901234567890123456789012';
  const mockBotUuid = 'bot-uuid-123';

  beforeEach(() => {
    apiClient = new BotApiClient({
      apiKey: mockApiKey,
      baseUrl: 'https://api.test.com',
      timeout: 5000,
      retryAttempts: 2,
      enableCache: true
    });
    
    // Clear all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    apiClient.destroy();
  });

  describe('Constructor', () => {
    it('should initialize with correct configuration', () => {
      const config = apiClient.getConfig();
      expect(config.apiKey).toBe(mockApiKey);
      expect(config.baseUrl).toBe('https://api.test.com');
      expect(config.timeout).toBe(5000);
      expect(config.retryAttempts).toBe(2);
      expect(config.enableCache).toBe(true);
    });

    it('should use default configuration when not provided', () => {
      const defaultClient = new BotApiClient({ apiKey: mockApiKey });
      const config = defaultClient.getConfig();
      
      expect(config.timeout).toBe(10000);
      expect(config.retryAttempts).toBe(3);
      expect(config.enableCache).toBe(true);
      
      defaultClient.destroy();
    });
  });

  describe('Request Interceptors', () => {
    it('should apply request interceptors', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({ success: true, data: {} })
      };
      (fetch as any).mockResolvedValue(mockResponse);

      let interceptorCalled = false;
      apiClient.addRequestInterceptor((config) => {
        interceptorCalled = true;
        return { ...config, headers: { ...config.headers, 'X-Custom': 'test' } };
      });

      await apiClient.getBotConfig(mockBotUuid);

      expect(interceptorCalled).toBe(true);
      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'X-Custom': 'test'
          })
        })
      );
    });
  });

  describe('Response Interceptors', () => {
    it('should apply response interceptors', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({ success: true, data: {} })
      };
      (fetch as any).mockResolvedValue(mockResponse);

      let interceptorCalled = false;
      apiClient.addResponseInterceptor((response) => {
        interceptorCalled = true;
        return response;
      });

      await apiClient.getBotConfig(mockBotUuid);
      expect(interceptorCalled).toBe(true);
    });
  });

  describe('Caching', () => {
    it('should cache GET requests', async () => {
      const mockData = { success: true, data: { uuid: mockBotUuid, name: 'Test Bot' } };
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve(mockData)
      };
      (fetch as any).mockResolvedValue(mockResponse);

      // First request
      const result1 = await apiClient.getBotConfig(mockBotUuid);
      expect(fetch).toHaveBeenCalledTimes(1);

      // Second request should use cache
      const result2 = await apiClient.getBotConfig(mockBotUuid);
      expect(fetch).toHaveBeenCalledTimes(1); // Still only 1 call
      expect(result1).toEqual(result2);
    });

    it('should bypass cache when forceRefresh is true', async () => {
      const mockData = { success: true, data: { uuid: mockBotUuid, name: 'Test Bot' } };
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve(mockData)
      };
      (fetch as any).mockResolvedValue(mockResponse);

      // First request
      await apiClient.getBotConfig(mockBotUuid);
      expect(fetch).toHaveBeenCalledTimes(1);

      // Force refresh should make new request
      await apiClient.getBotConfig(mockBotUuid, true);
      expect(fetch).toHaveBeenCalledTimes(2);
    });

    it('should not cache POST requests', async () => {
      const mockData = { success: true, data: { conversationId: 'conv-123' } };
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve(mockData)
      };
      (fetch as any).mockResolvedValue(mockResponse);

      // Make two identical POST requests
      await apiClient.startConversation(mockBotUuid, 'user-123');
      await apiClient.startConversation(mockBotUuid, 'user-123');

      expect(fetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Error Handling', () => {
    it('should throw ApiError for HTTP errors', async () => {
      const mockResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found',
        json: () => Promise.resolve({ message: 'Bot not found' })
      };
      (fetch as any).mockResolvedValue(mockResponse);

      await expect(apiClient.getBotConfig(mockBotUuid)).rejects.toThrow(ApiError);
    });

    it('should retry on network errors', async () => {
      // First two calls fail, third succeeds
      (fetch as any)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: {} })
        });

      const result = await apiClient.getBotConfig(mockBotUuid);
      expect(fetch).toHaveBeenCalledTimes(3);
      expect(result.success).toBe(true);
    });

    it('should not retry on 4xx errors', async () => {
      const mockResponse = {
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        json: () => Promise.resolve({ message: 'Invalid API key' })
      };
      (fetch as any).mockResolvedValue(mockResponse);

      await expect(apiClient.getBotConfig(mockBotUuid)).rejects.toThrow(ApiError);
      expect(fetch).toHaveBeenCalledTimes(1); // No retries
    });
  });

  describe('Bot Configuration', () => {
    it('should get bot configuration', async () => {
      const mockBotConfig = {
        uuid: mockBotUuid,
        name: 'Test Bot',
        greetingMessage: 'Hello!',
        status: 'active'
      };
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockBotConfig })
      };
      (fetch as any).mockResolvedValue(mockResponse);

      const result = await apiClient.getBotConfig(mockBotUuid);
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockBotConfig);
      expect(fetch).toHaveBeenCalledWith(
        'https://api.test.com/api/public/bots/bot-uuid-123/config',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': `Bearer ${mockApiKey}`
          })
        })
      );
    });
  });

  describe('Conversation Management', () => {
    it('should start conversation', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({ 
          success: true, 
          data: { conversationId: 'conv-123' } 
        })
      };
      (fetch as any).mockResolvedValue(mockResponse);

      const result = await apiClient.startConversation(mockBotUuid, 'user-123');
      
      expect(result.success).toBe(true);
      expect(result.data?.conversationId).toBe('conv-123');
    });

    it('should send message', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({ 
          success: true, 
          data: { 
            response: 'Bot response',
            messageId: 'msg-123',
            botMessageId: 'bot-msg-123'
          } 
        })
      };
      (fetch as any).mockResolvedValue(mockResponse);

      const result = await apiClient.sendMessage(
        mockBotUuid, 
        'conv-123', 
        'Hello bot'
      );
      
      expect(result.success).toBe(true);
      expect(result.data?.response).toBe('Bot response');
    });
  });

  describe('Utility Methods', () => {
    it('should clear cache', () => {
      apiClient.setCache('test-key', { data: 'test' });
      expect(apiClient.getCacheStats().size).toBe(1);
      
      apiClient.clearCache();
      expect(apiClient.getCacheStats().size).toBe(0);
    });

    it('should update configuration', () => {
      apiClient.updateConfig({ timeout: 15000 });
      expect(apiClient.getConfig().timeout).toBe(15000);
    });
  });
});

describe('Singleton Pattern', () => {
  it('should return same instance for same API key', () => {
    const client1 = getBotApiClient('test-key');
    const client2 = getBotApiClient('test-key');
    
    expect(client1).toBe(client2);
  });

  it('should return different instances for different API keys', () => {
    const client1 = getBotApiClient('test-key-1');
    const client2 = getBotApiClient('test-key-2');
    
    expect(client1).not.toBe(client2);
  });
});

describe('ApiError', () => {
  it('should create error with correct properties', () => {
    const error = new ApiError(404, 'Not Found', 'Resource not found', { code: 'NOT_FOUND' });
    
    expect(error.status).toBe(404);
    expect(error.statusText).toBe('Not Found');
    expect(error.message).toBe('Resource not found');
    expect(error.data).toEqual({ code: 'NOT_FOUND' });
    expect(error.name).toBe('ApiError');
  });
});
