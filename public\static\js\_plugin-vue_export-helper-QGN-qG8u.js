import{g6 as u,r as o,g7 as v,e as l,g8 as g,k as c,a4 as i,g9 as _}from"./index-ZVLuktk4.js";const f=e=>(a,t)=>d(a,t,c(e)),d=(e,a,t)=>_(t,e,e).replace(/\{(\w+)\}/g,(s,r)=>{var n;return`${(n=a==null?void 0:a[r])!=null?n:`{${r}}`}`}),p=e=>{const a=l(()=>c(e).name),t=i(e)?e:o(e);return{lang:a,locale:t,t:f(e)}},x=e=>{const a=u(v,o());return p(l(()=>{var t;return(t=a.value)!=null&&t.plus?a.value:g}))};var b=(e,a)=>{const t=e.__vccOpts||e;for(const[s,r]of a)t[s]=r;return t};export{b as _,x as u};
