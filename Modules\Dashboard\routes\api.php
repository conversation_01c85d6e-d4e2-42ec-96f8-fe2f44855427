<?php

use Illuminate\Support\Facades\Route;
use Modules\Dashboard\Http\Controllers\DashboardController;
use Modules\Dashboard\Http\Middleware\CacheDashboardData;

Route::middleware(['auth:api'])->prefix('v1/auth')->group(function () {
    // Dashboard data endpoints with caching
    Route::middleware([CacheDashboardData::class])->group(function () {
        Route::get('dashboard/data', [DashboardController::class, 'getDashboardData'])->name('dashboard.data');
        Route::get('dashboard/stats', [DashboardController::class, 'getStats'])->name('dashboard.stats');
        Route::get('dashboard/charts', [DashboardController::class, 'getCharts'])->name('dashboard.charts');
    });

    // Non-cached endpoints
    Route::get('dashboard/summary', [DashboardController::class, 'getSummary'])->name('dashboard.summary');

    // Standard CRUD routes
    Route::apiResource('dashboards', DashboardController::class)->names('dashboard');
});
