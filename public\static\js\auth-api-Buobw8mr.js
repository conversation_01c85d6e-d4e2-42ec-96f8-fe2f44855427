import{aN as a,az as r}from"./index-ZVLuktk4.js";const n=t=>a.request("get","/api/auth/organizations",{params:t}),s=t=>a.request("post","/api/auth/organizations",{data:r(t)}),i=(t,e)=>a.request("put",`/api/auth/organizations/${t}`,{data:r(e)}),u=t=>a.request("delete",`/api/auth/organizations/${t}`),g=t=>a.request("delete","/api/auth/organizations/bulk-delete",{data:t}),p=t=>a.request("delete",`/api/auth/organizations/${t}/force`),d=t=>a.request("delete","/api/auth/organizations/bulk/force",{data:t}),z=t=>a.request("put",`/api/auth/organizations/${t}/restore`),l=t=>a.request("put","/api/auth/organizations/bulk-restore",{data:t}),c=t=>{const e=new FormData;return e.append("logo",t),a.request("post","/api/auth/organizations/upload-logo",{data:e,headers:{"Content-Type":"multipart/form-data"}})};export{l as a,g as b,s as c,u as d,d as e,p as f,n as g,c as h,z as r,i as u};
