const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/ModelAiDrawerForm-6aiopcgU.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/js/hooks-CuzZ-_om.js","static/js/auth-api-C3NnK5ai.js","static/js/auth-api-BCod4ziT.js","static/js/index-CQnBjWL2.js","static/js/_plugin-vue_export-helper-QGN-qG8u.js","static/css/plus-drawer-form-DaizY_En.css","static/js/ModelAiFilterForm-CccdNPpo.js","static/css/ModelAiFilterForm-Db39-sCU.css","static/js/ModelServiceDrawerForm-XmPh7ehM.js","static/js/ParameterManager-Bc4qYJ8Y.js"])))=>i.map(i=>d[i]);
var we=Object.defineProperty,Ce=Object.defineProperties;var xe=Object.getOwnPropertyDescriptors;var me=Object.getOwnPropertySymbols;var _e=Object.prototype.hasOwnProperty,ke=Object.prototype.propertyIsEnumerable;var fe=(o,c,r)=>c in o?we(o,c,{enumerable:!0,configurable:!0,writable:!0,value:r}):o[c]=r,G=(o,c)=>{for(var r in c||(c={}))_e.call(c,r)&&fe(o,r,c[r]);if(me)for(var r of me(c))ke.call(c,r)&&fe(o,r,c[r]);return o},Z=(o,c)=>Ce(o,xe(c));var f=(o,c,r)=>new Promise((x,S)=>{var _=w=>{try{g(r.next(w))}catch(k){S(k)}},P=w=>{try{g(r.throw(w))}catch(k){S(k)}},g=w=>w.done?x(w.value):Promise.resolve(w.value).then(_,P);g((r=r.apply(o,c)).next())});import{r as y,g as Ae,ax as d,a8 as t,aA as pe,az as Se,ay as E,aD as Re,ai as ye,aC as C,q as De,o as Be,S as Pe,m as $,n as F,D as V,E as s,N as u,k as e,aI as Me,G as N,P as ee,M as A,aJ as b,I as Y,L as K,Q as Te,A as Ee,a5 as Fe,a4 as z,aK as te,aL as ge,aM as ae,_ as Ve}from"./index-ZVLuktk4.js";import{u as ze,g as He,a as Ie,b as Ue,r as $e,c as Ne,d as Oe,e as We,f as Le,h as Ge,i as Ye}from"./auth-api-L1fufnq1.js";import{P as Ke}from"./index-DMMM8XNs.js";import"./sortable.esm-7jaD_3Ar.js";import"./epTheme-DzIV6EF-.js";function je(){const o=y(!1),c=y({isTrashed:"no"}),r=Ae({total:0,pageSize:20,currentPage:1,pageSizes:[10,20,50,100,200],background:!0,layout:"total, sizes, prev, pager, next, jumper"}),x=y([]),S=y([]),_=y({sortBy:"createdAt",sortOrder:"asc"}),P=y([]),g=y(),w=y(!1),k=y(!1),v=y({streaming:!1,functionCalling:!1,vision:!1,isDefault:!1,status:"active"}),j=y(!1),O=y({}),M=y(),p=()=>f(null,null,function*(){try{o.value=!0;const a=yield Ie(Se(Z(G({},c.value),{order:`${_.value.sortBy}:${_.value.sortOrder}`,page:r.currentPage,limit:r.pageSize})));S.value=pe(a.data),r.total=a.total}catch(a){console.error("Error fetching modelAi:",a),d(t("Failed to fetch modelAi"),{type:"error"})}finally{o.value=!1}}),Q=()=>f(null,null,function*(){try{const a=yield He();P.value=pe(a.data)}catch(a){console.error("Error fetching modelAi dropdown:",a)}}),q=a=>{x.value=a},J=()=>f(null,null,function*(){yield p()}),T=a=>f(null,null,function*(){r.pageSize=a,r.currentPage=1,yield p()}),R=a=>f(null,null,function*(){_.value={sortBy:a.prop,sortOrder:a.order=="ascending"?"asc":"desc"},yield p()}),D=a=>f(null,null,function*(){var n,i;try{o.value=!0;const l=yield Ye(a);return l.success?(d(l.message||t("Create successful"),{type:"success"}),yield p(),!0):(d(l.message||t("Create failed"),{type:"error"}),!1)}catch(l){return d(((i=(n=l.response)==null?void 0:n.data)==null?void 0:i.message)||(l==null?void 0:l.message)||t("Create failed"),{type:"error"}),!1}finally{o.value=!1}}),W=(a,n)=>f(null,null,function*(){var i,l;try{o.value=!0;const h=yield Ge(a,n);return h.success?(d(h.message||t("Update successful"),{type:"success"}),yield p(),!0):(d(h.message||t("Update failed"),{type:"error"}),!1)}catch(h){return d(((l=(i=h.response)==null?void 0:i.data)==null?void 0:l.message)||(h==null?void 0:h.message)||t("Update failed"),{type:"error"}),!1}finally{o.value=!1}});return{loading:o,filterRef:c,pagination:r,records:S,multipleSelection:x,sort:_,modelAiDropdown:P,handleBulkDelete:a=>f(null,null,function*(){const n=a||x.value.map(i=>i.id);if(n.length===0){d(t("Please select items to delete"),{type:"warning"});return}try{yield E.confirm(t("Are you sure you want to delete selected items?"),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Le({ids:n}),d(t("Deleted successfully"),{type:"success"}),yield p()}catch(i){i!=="cancel"&&(console.error("Error bulk deleting modelAi:",i),d(t("Delete failed"),{type:"error"}))}}),handleDelete:a=>f(null,null,function*(){try{yield E.confirm(t("Are you sure you want to delete this item?"),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield We(a.id),d(t("Deleted successfully"),{type:"success"}),yield p()}catch(n){n!=="cancel"&&(console.error("Error deleting modelAi:",n),d(t("Delete failed"),{type:"error"}))}}),handlePermanentDelete:a=>f(null,null,function*(){try{yield E.confirm(t("Are you sure you want to permanently delete this item? This action cannot be undone."),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Oe(a.id),d(t("Permanently deleted successfully"),{type:"success"}),yield p()}catch(n){n!=="cancel"&&(console.error("Error permanently deleting modelAi:",n),d(t("Permanent delete failed"),{type:"error"}))}}),handleBulkPermanentDelete:a=>f(null,null,function*(){const n=a||x.value.map(i=>i.id);if(n.length===0){d(t("Please select items to delete"),{type:"warning"});return}try{yield E.confirm(t("Are you sure you want to permanently delete selected items? This action cannot be undone."),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Ne({ids:n}),d(t("Permanently deleted successfully"),{type:"success"}),yield p()}catch(i){i!=="cancel"&&(console.error("Error bulk permanently deleting modelAi:",i),d(t("Permanent delete failed"),{type:"error"}))}}),handleRestore:a=>f(null,null,function*(){try{yield E.confirm(t("Are you sure you want to restore this item?"),t("Confirm"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"info"}),yield $e(a.id),d(t("Restored successfully"),{type:"success"}),yield p()}catch(n){n!=="cancel"&&(console.error("Error restoring modelAi:",n),d(t("Restore failed"),{type:"error"}))}}),handleBulkRestore:a=>f(null,null,function*(){const n=a||x.value.map(i=>i.id);if(n.length===0){d(t("Please select items to restore"),{type:"warning"});return}try{yield E.confirm(t("Are you sure you want to restore selected items?"),t("Confirm"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"info"}),yield Ue({ids:n}),d(t("Restored successfully"),{type:"success"}),yield p()}catch(i){i!=="cancel"&&(console.error("Error bulk restoring modelAi:",i),d(t("Restore failed"),{type:"error"}))}}),fnGetModelAi:p,fnGetModelAiDropdown:Q,fnHandlePageChange:J,fnHandleSelectionChange:q,fnHandleSortChange:R,fnHandleSizeChange:T,filterVisible:w,drawerVisible:k,drawerValues:v,modelAiFormRef:g,handleSubmit:a=>f(null,null,function*(){var i;let n=!1;a.id!=null?n=yield W(Number(a.id),a):(n=yield D(a),n&&(v.value={status:"active"},(i=g.value)==null||i.resetForm()))}),handleFilter:a=>f(null,null,function*(){c.value=a,yield p()}),handleEdit:a=>{v.value=G({},a),k.value=!0},serviceVisible:j,serviceValues:O,serviceFormRef:M,handleServiceSubmit:a=>f(null,null,function*(){try{const{success:n}=yield ze(a);n&&(d(t("Service saved successfully"),{type:"success"}),yield p())}catch(n){console.error("Error saving service:",n),d(t("Service save failed"),{type:"error"})}}),handleServiceClose:()=>{var a;(a=M.value)==null||a.resetForm(),O.value={}}}}const Qe=[{type:"selection",width:"30px",sortable:!1},{type:"index",width:40,headerRenderer:()=>t("No.")},{prop:"key",align:"left",sortable:!0,minWidth:160,headerRenderer:()=>t("Model key")},{prop:"name",align:"left",sortable:!0,minWidth:160,headerRenderer:()=>t("Model Name")},{prop:"provider",align:"center",sortable:!0,width:140,headerRenderer:()=>t("Provider"),formatter:({provider:o})=>(o==null?void 0:o.name)||"-"},{prop:"status",sortable:!1,align:"left",width:160,headerRenderer:()=>t("Status"),cellRenderer:({row:o})=>{const c={active:{type:"success",text:t("Active"),class:"bg-green-100 text-green-800",icon:"ri:checkbox-circle-fill",iconClass:"text-green-600"},inactive:{type:"danger",text:t("Inactive"),class:"bg-red-100 text-red-800",icon:"ri:close-circle-fill",iconClass:"text-red-600"}},r=c[o.status]||c.active;return ye("span",{class:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${r.class}`},[ye(C,{icon:r.icon,class:`w-3 h-3 mr-1.5 ${r.iconClass}`}),r.text])}},{prop:"createdAt",width:160,sortable:!0,headerRenderer:()=>t("Created at"),formatter:({createdAt:o})=>o?Re(o).format("YYYY-MM-DD HH:mm"):"-"},{label:"",fixed:"right",width:140,slot:"operation",sortable:!1}],qe={class:"main"},Je={class:"ml-2"},Xe={class:"ml-2"},Ze={class:"ml-2"},et={class:"ml-2"},tt={class:"ml-2"},at=De({__name:"index",setup(o){const c=te(()=>ae(()=>import("./ModelAiDrawerForm-6aiopcgU.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8]))),r=te(()=>ae(()=>import("./ModelAiFilterForm-CccdNPpo.js"),__vite__mapDeps([9,1,2,5,4,3,6,7,10,8]))),x=te(()=>ae(()=>import("./ModelServiceDrawerForm-XmPh7ehM.js"),__vite__mapDeps([11,1,2,3,12,6,7,8]))),S=y(),_=y(),{loading:P,filterRef:g,pagination:w,records:k,multipleSelection:v,handleBulkDelete:j,handleDelete:O,fnGetModelAi:M,fnHandlePageChange:p,fnHandleSelectionChange:Q,fnHandleSortChange:q,fnHandleSizeChange:J,filterVisible:T,drawerVisible:R,drawerValues:D,modelAiFormRef:W,handleSubmit:le,handleFilter:ne,handleBulkPermanentDelete:se,handleBulkRestore:oe,handlePermanentDelete:re,handleRestore:ie,serviceVisible:H,serviceValues:I,serviceFormRef:de,handleServiceSubmit:ce,handleServiceClose:ue}=je(),a=i=>{D.value=Z(G({},ge(i,!0)),{categories:i.categories.map(l=>l.id)}),R.value=!0},n=i=>{I.value=ge(i,!0),H.value=!0};return Be(()=>{Pe(()=>{M()})}),(i,l)=>{const h=N("el-button"),L=N("el-tooltip"),B=N("el-dropdown-item"),ve=N("el-dropdown-menu"),he=N("el-dropdown");return F(),$("div",qe,[V("div",{ref_key:"contentRef",ref:_,class:K(["flex",e(Fe)()?"flex-wrap":""])},[s(e(Ke),{class:"!w-full",style:{transition:"width 220ms cubic-bezier(0.4, 0, 0.2, 1)"},title:e(t)("ModelAi Management"),columns:e(Qe),onRefresh:e(M),onFilter:l[4]||(l[4]=m=>T.value=!0)},{buttons:u(()=>[s(L,{content:e(t)("Create new"),placement:"top"},{default:u(()=>[s(h,{type:"text",class:"font-bold text-[16px]",disabled:!e(b)("model-ai.create"),onClick:l[0]||(l[0]=()=>{R.value=!0})},{default:u(()=>[s(e(C),{icon:e(b)("model-ai.create")?"flat-color-icons:plus":"icons8:plus",width:"18px"},null,8,["icon"])]),_:1},8,["disabled"])]),_:1},8,["content"]),e(g).isTrashed==="yes"?(F(),$(Y,{key:0},[s(L,{content:e(t)("Restore"),placement:"top"},{default:u(()=>[s(h,{type:"text",class:"font-bold text-[16px]",disabled:e(v).length===0||e(v).length>0&&!e(b)("model-ai.delete"),onClick:l[1]||(l[1]=()=>e(oe)())},{default:u(()=>[s(e(C),{icon:"tabler:restore",width:"18px",class:K({"text-amber-800":e(v).length>0&&e(b)("model-ai.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"]),s(L,{content:e(t)("Bulk Destroy"),placement:"top"},{default:u(()=>[s(h,{type:"text",class:"font-bold text-[16px]",disabled:e(v).length===0||e(v).length>0&&!e(b)("model-ai.destroy"),onClick:l[2]||(l[2]=()=>e(se)())},{default:u(()=>[s(e(C),{icon:"tabler:trash-x-filled",width:"18px",class:K({"text-red-800":e(v).length>0&&e(b)("model-ai.destroy")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])],64)):(F(),$(Y,{key:1},[e(g).isTrashed==="no"?(F(),Te(L,{key:0,content:e(t)("Bulk Delete"),placement:"top"},{default:u(()=>[s(h,{type:"text",class:"font-bold text-[16px]",disabled:e(v).length==0||e(v).length>0&&!e(b)("model-ai.delete"),onClick:l[3]||(l[3]=()=>e(j)())},{default:u(()=>[s(e(C),{icon:"tabler:trash",width:"18px",class:K({"text-red-800":e(v).length>0&&e(b)("model-ai.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])):Ee("",!0)],64))]),default:u(({size:m,dynamicColumns:be})=>[s(e(Me),{ref_key:"tableRef",ref:S,"align-whole":"center","table-layout":"auto",loading:e(P),size:m,adaptive:"",border:"",adaptiveConfig:{offsetBottom:108},data:e(k),columns:be,pagination:e(w),"header-cell-style":{background:"var(--el-fill-color-light)",color:"var(--el-text-color-primary)"},onSortChange:e(q),onPageSizeChange:e(J),onPageCurrentChange:e(p),onSelectionChange:e(Q)},{operation:u(({row:U})=>[s(he,{"split-button":"",trigger:"click",size:"small"},{dropdown:u(()=>[s(ve,{class:"min-w-[130px]"},{default:u(()=>[s(B,{disabled:""},{default:u(()=>[ee(A(e(t)("Utilities")),1)]),_:1}),s(B,{divided:"",disabled:e(b)("model-service.write"),onClick:()=>n(U)},{default:u(()=>[s(e(C),{icon:"mdi:cogs",class:"text-green-700"}),V("span",Je,A(e(t)("Service")),1)]),_:2},1032,["disabled","onClick"]),s(B,{disabled:""},{default:u(()=>[ee(A(e(t)("Action")),1)]),_:1}),e(g).isTrashed=="no"?(F(),$(Y,{key:0},[s(B,{disabled:!e(b)("model-ai.edit"),onClick:X=>a(U)},{default:u(()=>[s(e(C),{icon:"material-symbols:edit",class:"text-blue-800"}),V("span",Xe,A(e(t)("Edit")),1)]),_:2},1032,["disabled","onClick"]),s(B,{disabled:!e(b)("model-ai.delete"),onClick:X=>e(O)(U)},{default:u(()=>[s(e(C),{icon:"tabler:trash",class:"text-red-800"}),V("span",Ze,A(e(t)("Delete")),1)]),_:2},1032,["disabled","onClick"])],64)):(F(),$(Y,{key:1},[s(B,{disabled:!e(b)("model-ai.delete"),onClick:X=>e(ie)(U)},{default:u(()=>[s(e(C),{icon:"tabler:restore",class:"text-amber-800"}),V("span",et,A(e(t)("Restore")),1)]),_:2},1032,["disabled","onClick"]),s(B,{disabled:!e(b)("model-ai.destroy"),onClick:X=>e(re)(U)},{default:u(()=>[s(e(C),{icon:"tabler:trash-x",class:"text-red-800"}),V("span",tt,A(e(t)("Destroy")),1)]),_:2},1032,["disabled","onClick"])],64))]),_:2},1024)]),default:u(()=>[ee(A(e(t)("Action"))+" ",1)]),_:2},1024)]),_:2},1032,["loading","size","data","columns","pagination","onSortChange","onPageSizeChange","onPageCurrentChange","onSelectionChange"])]),_:1},8,["title","columns","onRefresh"])],2),s(e(c),{ref_key:"modelAiFormRef",ref:W,visible:e(R),"onUpdate:visible":l[5]||(l[5]=m=>z(R)?R.value=m:null),values:e(D),"onUpdate:values":l[6]||(l[6]=m=>z(D)?D.value=m:null),onSubmit:e(le),onClose:l[7]||(l[7]=()=>{var m;(m=e(W))==null||m.resetForm(),D.value={status:"active"}})},null,8,["visible","values","onSubmit"]),s(e(r),{ref:"filterFormRef",visible:e(T),"onUpdate:visible":l[8]||(l[8]=m=>z(T)?T.value=m:null),values:e(g),"onUpdate:values":l[9]||(l[9]=m=>z(g)?g.value=m:null),onSubmit:e(ne),onReset:l[10]||(l[10]=()=>{g.value={isTrashed:"no"},e(M)()})},null,8,["visible","values","onSubmit"]),s(e(x),{ref_key:"serviceFormRef",ref:de,visible:e(H),"onUpdate:visible":l[11]||(l[11]=m=>z(H)?H.value=m:null),values:e(I),"onUpdate:values":l[12]||(l[12]=m=>z(I)?I.value=m:null),onSubmit:e(ce),onClose:e(ue)},null,8,["visible","values","onSubmit","onClose"])])}}}),dt=Ve(at,[["__scopeId","data-v-3aa34fed"]]);export{dt as default};
