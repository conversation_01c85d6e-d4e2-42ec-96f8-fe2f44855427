var b=(v,f,t)=>new Promise((g,r)=>{var n=l=>{try{i(t.next(l))}catch(u){r(u)}},p=l=>{try{i(t.throw(l))}catch(u){r(u)}},i=l=>l.done?g(l.value):Promise.resolve(l.value).then(n,p);i((t=t.apply(v,f)).next())});/* empty css                         */import{q as k,r as y,o as q,aA as F,a8 as e,e as a,G as A,Q as D,n as N,N as d,D as P,E as C,P as _,M as h,k as m,_ as V}from"./index-ZVLuktk4.js";import{g as O}from"./auth-api-C3NnK5ai.js";import{P as B}from"./index-CQnBjWL2.js";import"./_plugin-vue_export-helper-QGN-qG8u.js";const I={class:"custom-group-header"},M={class:"font-semibold"},E={class:"custom-footer"},R=k({__name:"ModelToolDrawerForm",props:{visible:{type:Boolean},values:{},isEdit:{type:Boolean}},emits:["update:visible","update:values","submit"],setup(v,{expose:f,emit:t}){const g=v,r=t,n=y(!1),p=y(),i=y([]);q(()=>b(null,null,function*(){try{const{data:o}=yield O();i.value=F(o)}catch(o){console.error("Failed to load categories:",o)}}));const l=[{label:e("Function"),value:"function"},{label:e("Plugin"),value:"plugin"},{label:e("Integration"),value:"integration"},{label:e("Custom"),value:"custom"}],u=[{label:e("Active"),value:"active"},{label:e("Inactive"),value:"inactive"}],x=[{label:a(()=>e("Tool Name")),prop:"name",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input tool name"),trigger:["blur","change"]},{min:2,max:100,message:e("Length should be between 2 and 100 characters"),trigger:["blur","change"]}],fieldProps:{placeholder:e("Enter tool name")},colProps:{span:12}},{label:a(()=>e("Slug")),prop:"slug",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input tool slug"),trigger:["blur"]},{pattern:/^[a-z0-9-_]+$/,message:e("Slug can only contain lowercase letters, numbers, hyphens and underscores"),trigger:"blur"}],fieldProps:{placeholder:e("e.g., web-search, calculator")},colProps:{span:12}},{label:a(()=>e("Tool Type")),prop:"toolType",valueType:"select",required:!0,rules:[{required:!0,message:e("Please select tool type"),trigger:["blur","change"]}],options:l,fieldProps:{placeholder:e("Select tool type")},colProps:{span:12}},{label:a(()=>e("Version")),prop:"version",valueType:"input",fieldProps:{placeholder:e("e.g., 1.0.0, v2.1")},colProps:{span:12}},{label:a(()=>e("Categories")),prop:"categories",valueType:"checkbox",options:a(()=>i.value.map(o=>({label:o.name,value:o.id}))),colProps:{span:24}},{label:a(()=>e("Status")),prop:"status",valueType:"select",required:!0,options:u,fieldProps:{placeholder:e("Select status")},colProps:{span:12}},{label:a(()=>e("Is Active")),prop:"isActive",valueType:"switch",colProps:{span:12}},{label:a(()=>e("Parameters")),prop:"parameters",valueType:"textarea",fieldProps:{placeholder:e("JSON parameters configuration (optional)"),rows:4},colProps:{span:24}},{label:a(()=>e("Configuration")),prop:"configuration",valueType:"textarea",fieldProps:{placeholder:e("JSON configuration (optional)"),rows:4},colProps:{span:24}},{label:a(()=>e("Description")),prop:"description",valueType:"textarea",fieldProps:{placeholder:e("Tool description"),showWordLimit:!0,autosize:{minRows:3,maxRows:6}},colProps:{span:24}}],T=()=>{var o;(o=p.value)==null||o.resetFields()},S=()=>b(null,null,function*(){var o;try{n.value=!0,(yield(o=p.value)==null?void 0:o.validate())&&(r("submit",g.values),r("update:visible",!1))}catch(s){console.error("Form validation failed:",s)}finally{n.value=!1}});return f({resetForm:T}),(o,s)=>{const w=A("el-button");return N(),D(m(B),{ref_key:"formRef",ref:p,visible:o.visible,"model-value":o.values,size:"60%",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0,destroyOnClose:!0,form:{columns:x,footerAlign:"center",hasFooter:!1,labelSuffix:"",labelPosition:"top",labelWidth:"auto",labelAlign:"left",requireAsteriskPosition:"right",rowProps:{gutter:10}},"onUpdate:visible":s[1]||(s[1]=c=>r("update:visible",c)),"onUpdate:modelValue":s[2]||(s[2]=c=>r("update:values",c)),onClose:T},{"drawer-header":d(()=>[P("div",I,[P("span",M,h(m(e)("Tool Information")),1)])]),"drawer-footer":d(()=>[P("div",E,[C(w,{onClick:s[0]||(s[0]=c=>r("update:visible",!1))},{default:d(()=>[_(h(m(e)("Cancel")),1)]),_:1}),C(w,{type:"primary",loading:n.value,onClick:S},{default:d(()=>[_(h(m(e)("Save")),1)]),_:1},8,["loading"])])]),_:1},8,["visible","model-value","form"])}}}),G=V(R,[["__scopeId","data-v-97a95d7e"]]);export{G as default};
