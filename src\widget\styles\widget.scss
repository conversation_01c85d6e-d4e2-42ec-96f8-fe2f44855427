/**
 * Main Widget Stylesheet
 * Combines all widget styles with proper isolation
 */

// Import variables first
@import './variables.scss';

// Import base styles
@import './base.scss';

// Import component styles
@import './components.scss';

// Import utility classes
@import './utilities.scss';

/* === Widget Layout === */
.procms-chatbot-widget {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: var(--procms-font-family);
  font-size: var(--procms-font-size-base);
  line-height: var(--procms-line-height-normal);
  color: var(--procms-text-primary);
  background: var(--procms-widget-bg);
  border-radius: var(--procms-widget-border-radius);
  overflow: hidden;
  box-shadow: var(--procms-widget-shadow);
  
  /* Ensure proper isolation */
  isolation: isolate;
  contain: layout style paint;
  
  /* Prevent text selection on UI elements */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  
  /* Allow text selection in messages */
  .procms-message__text {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
  }
  
  /* Prevent context menu on most elements */
  * {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* Allow text selection where needed */
  input,
  textarea,
  .procms-message__text,
  .procms-selectable {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
}

/* === Animation Classes === */
.procms-fade-enter-active,
.procms-fade-leave-active {
  transition: opacity var(--procms-transition-base);
}

.procms-fade-enter-from,
.procms-fade-leave-to {
  opacity: 0;
}

.procms-slide-up-enter-active,
.procms-slide-up-leave-active {
  transition: all var(--procms-transition-base);
}

.procms-slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.procms-slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.procms-bounce-enter-active {
  animation: procms-bounce-in var(--procms-transition-slow);
}

.procms-bounce-leave-active {
  animation: procms-bounce-out var(--procms-transition-base);
}

@keyframes procms-bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes procms-bounce-out {
  0% {
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.3);
  }
}

/* === Utility Classes === */
.procms-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.procms-not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.procms-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.procms-break-words {
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

.procms-break-all {
  word-break: break-all;
}

.procms-no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

/* === Focus Management === */
.procms-chatbot-widget {
  &:focus-within {
    outline: 2px solid var(--procms-primary);
    outline-offset: 2px;
  }
  
  /* Focus trap for modal-like behavior */
  &.procms-focus-trap {
    position: relative;
    
    &::before,
    &::after {
      content: '';
      position: absolute;
      width: 1px;
      height: 1px;
      opacity: 0;
      pointer-events: none;
    }
    
    &::before {
      top: 0;
      left: 0;
    }
    
    &::after {
      bottom: 0;
      right: 0;
    }
  }
}

/* === High Contrast Mode === */
@media (prefers-contrast: high) {
  .procms-chatbot-widget {
    --procms-widget-border: currentColor;
    --procms-primary: ButtonText;
    --procms-widget-bg: ButtonFace;
    --procms-widget-surface: ButtonFace;
    --procms-text-primary: ButtonText;
    --procms-text-secondary: ButtonText;
    
    border: 2px solid currentColor;
    
    .procms-message {
      &--user .procms-message__content {
        background: Highlight;
        color: HighlightText;
        border: 1px solid currentColor;
      }
      
      &--bot .procms-message__content {
        background: ButtonFace;
        color: ButtonText;
        border: 1px solid currentColor;
      }
    }
    
    .procms-input-area__input {
      border: 1px solid currentColor;
      background: Field;
      color: FieldText;
    }
    
    .procms-input-area__send {
      background: ButtonFace;
      color: ButtonText;
      border: 1px solid currentColor;
    }
  }
}

/* === Reduced Motion === */
@media (prefers-reduced-motion: reduce) {
  .procms-chatbot-widget {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
    
    .procms-typing-indicator__dots span {
      animation: none;
    }
    
    .procms-spinner {
      animation: none;
      border-top-color: transparent;
    }
  }
}

/* === Print Styles === */
@media print {
  .procms-chatbot-widget {
    display: none !important;
  }
}

/* === Mobile Optimizations === */
@media (max-width: 768px) {
  .procms-chatbot-widget {
    /* Adjust font sizes for mobile */
    --procms-font-size-base: 16px; /* Prevent zoom on iOS */
    --procms-input-height: 48px; /* Larger touch targets */
    --procms-button-height: 48px;
    --procms-avatar-size: 36px;
    
    /* Adjust spacing */
    --procms-space-4: 1.25rem;
    --procms-message-spacing: 1.25rem;
    
    .procms-widget-header {
      height: 56px; /* Larger header on mobile */
      padding: 0 var(--procms-space-4);
    }
    
    .procms-messages-container {
      padding: var(--procms-space-3);
    }
    
    .procms-widget-footer {
      padding: var(--procms-space-3);
    }
    
    .procms-message {
      &__content {
        max-width: 85%; /* More space for messages */
        font-size: var(--procms-font-size-base);
      }
    }
    
    .procms-input-area {
      &__input {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: var(--procms-space-3) var(--procms-space-4);
      }
    }
  }
}

@media (max-width: 480px) {
  .procms-chatbot-widget {
    /* Even more mobile-friendly adjustments */
    .procms-message {
      &__content {
        max-width: 90%;
        padding: var(--procms-space-3);
      }
    }
    
    .procms-starter-messages {
      &__button {
        padding: var(--procms-space-3);
        font-size: var(--procms-font-size-sm);
      }
    }
  }
}

/* === Dark Mode Specific Adjustments === */
[data-procms-theme="dark"] .procms-chatbot-widget,
[data-procms-theme="auto"] .procms-chatbot-widget {
  @media (prefers-color-scheme: dark) {
    /* Enhanced shadows for dark mode */
    box-shadow: var(--procms-widget-shadow-lg);
    
    /* Adjust scrollbar for dark mode */
    ::-webkit-scrollbar-track {
      background: var(--procms-gray-800);
    }
    
    ::-webkit-scrollbar-thumb {
      background: var(--procms-gray-600);
      
      &:hover {
        background: var(--procms-gray-500);
      }
    }
    
    /* Adjust selection colors */
    ::selection {
      background: var(--procms-primary);
      color: var(--procms-text-inverse);
    }
    
    ::-moz-selection {
      background: var(--procms-primary);
      color: var(--procms-text-inverse);
    }
  }
}

/* === Performance Optimizations === */
.procms-chatbot-widget {
  /* Enable hardware acceleration */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  
  /* Optimize repaints */
  .procms-message,
  .procms-input-area,
  .procms-widget-header {
    will-change: auto;
  }
  
  /* Optimize scrolling */
  .procms-messages-container {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
}

/* === Browser-specific Fixes === */
/* Safari */
@supports (-webkit-appearance: none) {
  .procms-chatbot-widget {
    .procms-input-area__input {
      -webkit-appearance: none;
      border-radius: var(--procms-input-radius);
    }
    
    .procms-input-area__send {
      -webkit-appearance: none;
    }
  }
}

/* Firefox */
@-moz-document url-prefix() {
  .procms-chatbot-widget {
    .procms-input-area__input {
      -moz-appearance: none;
    }
  }
}

/* Edge */
@supports (-ms-ime-align: auto) {
  .procms-chatbot-widget {
    .procms-input-area__input {
      -ms-ime-align: auto;
    }
  }
}
