import{u as p}from"./hooks-CuzZ-_om.js";import{q as x,e as a,G as n,Q as r,n as t,N as v,D as o,m,A as h,M as l,L as c,E as w,P as k,k as u,_ as C}from"./index-ZVLuktk4.js";const I={class:"flex items-center justify-between"},B={class:"flex-1"},N={class:"text-sm text-gray-600 mb-1"},M={class:"text-2xl font-bold text-gray-900 mb-2"},V={key:1},S=x({__name:"StatsCard",props:{title:{},value:{},icon:{},color:{default:"primary"},trend:{},loading:{type:Boolean,default:!1}},setup(y){const s=y,g=a(()=>{const e={primary:"border-blue-200 bg-blue-50",success:"border-green-200 bg-green-50",warning:"border-yellow-200 bg-yellow-50",danger:"border-red-200 bg-red-50",info:"border-gray-200 bg-gray-50"};return e[s.color]||e.primary}),f=a(()=>{const e={primary:"text-blue-600",success:"text-green-600",warning:"text-yellow-600",danger:"text-red-600",info:"text-gray-600"};return e[s.color]||e.primary}),_=a(()=>s.trend?s.trend.isUp?"text-green-600":"text-red-600":"");return(e,D)=>{const i=n("el-skeleton-item"),d=n("IconifyIconOffline"),b=n("el-card");return t(),r(b,{class:c(["stats-card",g.value]),shadow:"hover","body-style":{padding:"20px"}},{default:v(()=>[o("div",I,[o("div",B,[o("div",N,l(e.title),1),o("div",M,[e.loading?(t(),r(i,{key:0,variant:"text",style:{width:"60px",height:"32px"}})):(t(),m("span",V,l(e.value),1))]),e.trend&&!e.loading?(t(),m("div",{key:0,class:c(["text-sm flex items-center",_.value])},[w(d,{icon:u(p)(e.trend.isUp?"ep:arrow-up":"ep:arrow-down"),class:"mr-1"},null,8,["icon"]),k(" "+l(Math.abs(e.trend.value))+"% ",1)],2)):h("",!0)]),o("div",{class:c(["text-3xl",f.value])},[e.loading?(t(),r(i,{key:0,variant:"circle",style:{width:"48px",height:"48px"}})):(t(),r(d,{key:1,icon:u(p)(e.icon)},null,8,["icon"]))],2)])]),_:1},8,["class"])}}}),U=C(S,[["__scopeId","data-v-c5157b15"]]);export{U as default};
