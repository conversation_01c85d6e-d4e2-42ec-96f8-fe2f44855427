import{q as g,G as s,m as u,n as w,D as a,E as n,M as l,k as e,a8 as t,N as f,_ as h}from"./index-ZVLuktk4.js";const v={class:"card"},k={class:"section-title"},b={class:"text-xs text-gray-500"},x=g({__name:"BotAdvancedSettings",props:{knowledgeEnabled:{type:Boolean}},emits:["update:knowledgeEnabled"],setup(d,{emit:c}){const i=d,r=c,_=o=>{r("update:knowledgeEnabled",o)};return(o,B)=>{const p=s("el-switch"),m=s("el-form-item");return w(),u("div",v,[a("h2",k,l(e(t)("Advanced Settings")),1),n(m,{label:e(t)("Use Knowledge Base")},{default:f(()=>[n(p,{"model-value":i.knowledgeEnabled,size:"large","onUpdate:modelValue":_},null,8,["model-value"])]),_:1},8,["label"]),a("p",b,l(e(t)("Enable this feature to allow Agent access to your private knowledge sources.")),1)])}}}),y=h(x,[["__scopeId","data-v-d8d43653"]]);export{y as default};
