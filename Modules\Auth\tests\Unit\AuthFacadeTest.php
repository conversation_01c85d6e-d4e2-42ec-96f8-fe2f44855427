<?php

namespace Modules\Auth\Tests\Unit;

use Modules\Auth\Tests\TestCase;
use Modules\Auth\Facades\AuthFacade;
use Modules\Auth\Services\AuthService;
use Modules\User\Models\User;
use Modules\User\Enums\UserStatus;
use Modules\User\Enums\UserGender;
use Illuminate\Support\Facades\Hash;
use PHPUnit\Framework\Attributes\Test;

class AuthFacadeTest extends TestCase
{
    #[Test]
    public function it_has_correct_facade_accessor()
    {
        try {
            $service = AuthFacade::getFacadeRoot();
            $this->assertInstanceOf(AuthService::class, $service);

        } catch (\Exception $e) {
            $this->logError('Facade accessor test failed', $e);
            $this->fail('Facade accessor test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_can_register_user_via_facade()
    {
        try {
            $userData = [
                'username' => 'facadeuser',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'first_name' => 'Facade',
                'last_name' => 'User',
                'gender' => UserGender::Male->value,
            ];

            $result = AuthFacade::register($userData);

            $this->assertTrue($result['success']);
            $this->assertArrayHasKey('user', $result);
            $this->assertArrayHasKey('message', $result);
            $this->assertArrayHasKey('email_verification_required', $result);

            $user = $result['user'];
            $this->assertInstanceOf(User::class, $user);
            $this->assertEquals('facadeuser', $user->username);
            $this->assertEquals('<EMAIL>', $user->email);

        } catch (\Exception $e) {
            $this->logError('Facade register test failed', $e);
            $this->fail('Facade register test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_can_login_user_via_facade()
    {
        try {
            $user = User::factory()->create([
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'status' => UserStatus::Active,
                'is_verified' => true,
                'email_verified_at' => now(),
            ]);

            $credentials = [
                'email' => '<EMAIL>',
                'password' => 'password123',
            ];

            $result = AuthFacade::login($credentials);

            $this->assertTrue($result['success']);
            $this->assertArrayHasKey('data', $result);
            $this->assertArrayHasKey('user', $result['data']);
            $this->assertArrayHasKey('token', $result['data']);

        } catch (\Exception $e) {
            $this->logError('Facade login test failed', $e);
            $this->fail('Facade login test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_can_send_password_reset_via_facade()
    {
        try {
            $user = User::factory()->create([
                'email' => '<EMAIL>',
            ]);

            $result = AuthFacade::sendPasswordResetCode('<EMAIL>');

            $this->assertTrue($result['success']);
            $this->assertArrayHasKey('message', $result);

        } catch (\Exception $e) {
            $this->logError('Facade password reset test failed', $e);
            $this->fail('Facade password reset test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_can_verify_email_via_facade()
    {
        try {
            $user = User::factory()->create([
                'email' => '<EMAIL>',
                'status' => UserStatus::Inactive,
                'is_verified' => false,
            ]);

            // Create verification code manually for testing
            \Modules\ActiveCode\Models\ActiveCode::create([
                'code' => '123456',
                'type' => 'email_verification',
                'identifier' => '<EMAIL>',
                'expires_at' => now()->addMinutes(15),
            ]);

            $result = AuthFacade::verifyEmail('<EMAIL>', '123456');

            $this->assertTrue($result['success']);
            $this->assertArrayHasKey('message', $result);

            $user->refresh();
            $this->assertTrue($user->is_verified);
            $this->assertEquals(UserStatus::Active, $user->status);

        } catch (\Exception $e) {
            $this->logError('Facade email verification test failed', $e);
            $this->fail('Facade email verification test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_can_resend_verification_code_via_facade()
    {
        try {
            $user = User::factory()->create([
                'email' => '<EMAIL>',
                'status' => UserStatus::Inactive,
                'is_verified' => false,
            ]);

            $result = AuthFacade::resendVerificationCode('<EMAIL>');

            $this->assertTrue($result['success']);
            $this->assertArrayHasKey('message', $result);

            // Check that new verification code was created
            $this->assertDatabaseHas('active_codes', [
                'identifier' => '<EMAIL>',
                'type' => 'email_verification',
            ]);

        } catch (\Exception $e) {
            $this->logError('Facade resend verification test failed', $e);
            $this->fail('Facade resend verification test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_can_reset_password_via_facade()
    {
        try {
            $user = User::factory()->create([
                'email' => '<EMAIL>',
                'password' => Hash::make('oldpassword'),
            ]);

            // Create reset code manually for testing
            \Modules\ActiveCode\Models\ActiveCode::create([
                'code' => '654321',
                'type' => 'password_reset',
                'identifier' => '<EMAIL>',
                'expires_at' => now()->addMinutes(15),
            ]);

            $result = AuthFacade::resetPassword(
                '<EMAIL>',
                '654321',
                'newpassword123'
            );

            $this->assertTrue($result['success']);
            $this->assertArrayHasKey('message', $result);

            $user->refresh();
            $this->assertTrue(Hash::check('newpassword123', $user->password));

        } catch (\Exception $e) {
            $this->logError('Facade password reset test failed', $e);
            $this->fail('Facade password reset test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_can_logout_via_facade()
    {
        try {
            $user = User::factory()->create([
                'status' => UserStatus::Active,
                'is_verified' => true,
            ]);

            // Login to get token
            $token = auth('api')->login($user);
            auth('api')->setToken($token);

            // Logout should not throw exception
            AuthFacade::logout();

            // Token should be invalidated
            $this->expectException(\Tymon\JWTAuth\Exceptions\TokenInvalidException::class);
            auth('api')->user();

        } catch (\Tymon\JWTAuth\Exceptions\TokenInvalidException $e) {
            // This is expected after logout
            $this->assertTrue(true);
        } catch (\Exception $e) {
            $this->logError('Facade logout test failed', $e);
            $this->fail('Facade logout test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_can_refresh_token_via_facade()
    {
        try {
            $user = User::factory()->create([
                'status' => UserStatus::Active,
                'is_verified' => true,
            ]);

            // Login to get initial token
            $token = auth('api')->login($user);
            auth('api')->setToken($token);

            $result = AuthFacade::refresh();

            $this->assertArrayHasKey('token', $result);
            $this->assertArrayHasKey('token_type', $result);
            $this->assertArrayHasKey('expires_in', $result);
            $this->assertEquals('bearer', $result['token_type']);
            $this->assertNotEmpty($result['token']);

        } catch (\Exception $e) {
            $this->logError('Facade token refresh test failed', $e);
            $this->fail('Facade token refresh test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_can_be_used_statically()
    {
        try {
            // Test that facade can be called statically
            $userData = [
                'username' => 'staticuser',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'first_name' => 'Static',
                'last_name' => 'User',
                'gender' => UserGender::Female->value,
            ];

            $result = AuthFacade::register($userData);

            $this->assertTrue($result['success']);
            $this->assertInstanceOf(User::class, $result['user']);

        } catch (\Exception $e) {
            $this->logError('Facade static usage test failed', $e);
            $this->fail('Facade static usage test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_handles_errors_gracefully()
    {
        try {
            // Test with invalid data to ensure facade handles errors
            $result = AuthFacade::login([
                'email' => '<EMAIL>',
                'password' => 'wrongpassword',
            ]);

            $this->assertFalse($result['success']);
            $this->assertArrayHasKey('message', $result);

        } catch (\Exception $e) {
            $this->logError('Facade error handling test failed', $e);
            $this->fail('Facade error handling test failed: ' . $e->getMessage());
        }
    }
}
