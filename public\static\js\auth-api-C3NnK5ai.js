import{aN as t,az as a}from"./index-ZVLuktk4.js";const s=e=>t.request("get","/api/auth/model-categories",{params:e}),u=()=>t.request("get","/api/auth/model-categories/dropdown"),d=e=>t.request("post","/api/auth/model-categories",{data:a(e)}),n=(e,r)=>t.request("put",`/api/auth/model-categories/${e}`,{data:a(r)}),i=e=>t.request("delete",`/api/auth/model-categories/${e}/delete`),l=e=>t.request("delete","/api/auth/model-categories/bulk/delete",{data:a(e)}),g=e=>t.request("delete",`/api/auth/model-categories/${e}/force`),c=e=>t.request("delete","/api/auth/model-categories/bulk/force",{data:a(e)}),p=e=>t.request("put",`/api/auth/model-categories/${e}/restore`),m=e=>t.request("put","/api/auth/model-categories/bulk/restore",{data:a(e)});export{s as a,m as b,c,g as d,i as e,l as f,u as g,d as h,p as r,n as u};
