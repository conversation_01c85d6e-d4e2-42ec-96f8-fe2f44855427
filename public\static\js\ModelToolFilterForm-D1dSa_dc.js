var y=(v,b,s)=>new Promise((a,r)=>{var m=o=>{try{i(s.next(o))}catch(p){r(p)}},n=o=>{try{i(s.throw(o))}catch(p){r(p)}},i=o=>o.done?a(o.value):Promise.resolve(o.value).then(m,n);i((s=s.apply(v,b)).next())});/* empty css                         */import{q as A,r as g,o as k,aA as w,a8 as e,e as t,G as I,Q as M,n as N,N as c,D as h,E as _,P as C,M as P,k as d,_ as x}from"./index-ZVLuktk4.js";import{g as B}from"./auth-api-C3NnK5ai.js";import{P as D}from"./index-CQnBjWL2.js";import"./_plugin-vue_export-helper-QGN-qG8u.js";const O={class:"custom-group-header"},R={class:"font-semibold"},V={class:"custom-footer"},q=A({__name:"ModelToolFilterForm",props:{visible:{type:Boolean},values:{}},emits:["update:visible","update:values","submit","reset"],setup(v,{emit:b}){const s=v,a=b,r=g(!1),m=g(),n=g([]);k(()=>y(null,null,function*(){try{const{data:l}=yield B();n.value=w(l)}catch(l){console.error("Failed to load categories:",l)}}));const i=[{label:e("Function"),value:"function"},{label:e("Plugin"),value:"plugin"},{label:e("Integration"),value:"integration"},{label:e("Custom"),value:"custom"}],o=[{label:e("Active"),value:"active"},{label:e("Inactive"),value:"inactive"}],p=[{label:t(()=>e("Tool Name")),prop:"name",valueType:"input",fieldProps:{placeholder:e("Search by tool name"),clearable:!0}},{label:t(()=>e("Slug")),prop:"slug",valueType:"input",fieldProps:{placeholder:e("Search by slug"),clearable:!0}},{label:t(()=>e("Tool Type")),prop:"toolType",valueType:"select",fieldProps:{placeholder:e("Select tool type"),clearable:!0},options:i,colProps:{span:12}},{label:t(()=>e("Category")),prop:"categoryId",valueType:"select",fieldProps:{placeholder:e("Select category"),clearable:!0},options:t(()=>n.value.map(l=>({label:l.name,value:l.id}))),colProps:{span:12}},{label:t(()=>e("Status")),prop:"status",valueType:"select",fieldProps:{placeholder:e("Select status"),clearable:!0},options:o,colProps:{span:12}},{label:t(()=>e("Active Status")),prop:"isActive",valueType:"select",fieldProps:{placeholder:e("Select active status"),clearable:!0},options:[{label:e("Active"),value:!0},{label:e("Inactive"),value:!1}],colProps:{span:12}},{label:t(()=>e("Trash Status")),prop:"isTrashed",valueType:"select",fieldProps:{placeholder:e("Select trash status"),clearable:!0},options:[{label:e("Active"),value:"no"},{label:e("Trashed"),value:"yes"}],colProps:{span:12}}],S=()=>y(null,null,function*(){try{r.value=!0,a("submit",s.values),a("update:visible",!1)}catch(l){console.error("Filter submission failed:",l)}finally{r.value=!1}}),F=()=>{a("reset"),a("update:visible",!1)};return(l,u)=>{const T=I("el-button");return N(),M(d(D),{ref_key:"formRef",ref:m,visible:l.visible,"model-value":l.values,size:"30%",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0,destroyOnClose:!0,form:{columns:p,footerAlign:"center",hasFooter:!1,labelSuffix:"",labelPosition:"top",labelWidth:"auto",labelAlign:"left",requireAsteriskPosition:"right",rowProps:{gutter:10}},"onUpdate:visible":u[0]||(u[0]=f=>a("update:visible",f)),"onUpdate:modelValue":u[1]||(u[1]=f=>a("update:values",f))},{"drawer-header":c(()=>[h("div",O,[h("span",R,P(d(e)("Filter Tools")),1)])]),"drawer-footer":c(()=>[h("div",V,[_(T,{plain:"",onClick:F},{default:c(()=>[C(P(d(e)("Reset")),1)]),_:1}),_(T,{type:"primary",loading:r.value,onClick:S},{default:c(()=>[C(P(d(e)("Apply Filter")),1)]),_:1},8,["loading"])])]),_:1},8,["visible","model-value","form"])}}}),W=x(q,[["__scopeId","data-v-8734871e"]]);export{W as default};
