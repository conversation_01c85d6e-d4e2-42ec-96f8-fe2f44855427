<?php

namespace Modules\Auth\Tests\Unit;

use Modules\Auth\Tests\TestCase;
use Modules\Auth\Services\AuthService;
use Modules\User\Models\User;
use Modules\User\Enums\UserStatus;
use Modules\User\Enums\UserGender;
use Illuminate\Support\Facades\Hash;
use PHPUnit\Framework\Attributes\Test;

class AuthServiceTest extends TestCase
{
    protected AuthService $authService;

    protected function setUp(): void
    {
        parent::setUp();

        try {
            $this->authService = app(AuthService::class);
        } catch (\Exception $e) {
            $this->logError('Failed to resolve AuthService', $e);
            $this->fail('Could not resolve AuthService from container');
        }
    }

    #[Test]
    public function it_can_register_user_successfully()
    {
        try {
            $userData = [
                'username' => 'testuser',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'first_name' => 'Test',
                'last_name' => 'User',
                'gender' => UserGender::Male->value,
            ];

            $result = $this->authService->register($userData);

            $this->assertTrue($result['success']);
            $this->assertArrayHasKey('user', $result);
            $this->assertArrayHasKey('message', $result);
            $this->assertArrayHasKey('email_verification_required', $result);

            $user = $result['user'];
            $this->assertInstanceOf(User::class, $user);
            $this->assertEquals('testuser', $user->username);
            $this->assertEquals('<EMAIL>', $user->email);
            $this->assertEquals('Test', $user->first_name);
            $this->assertEquals('User', $user->last_name);

            $this->assertDatabaseHas('users', [
                'username' => 'testuser',
                'email' => '<EMAIL>',
            ]);

        } catch (\Exception $e) {
            $this->logError('User registration test failed', $e);
            $this->fail('User registration test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_hashes_password_during_registration()
    {
        try {
            $userData = [
                'username' => 'testuser2',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'first_name' => 'Test',
                'last_name' => 'User',
                'gender' => UserGender::Female->value,
            ];

            $result = $this->authService->register($userData);

            $this->assertTrue($result['success']);
            $user = $result['user'];

            // Password should be hashed, not plain text
            $this->assertNotEquals('password123', $user->password);
            $this->assertTrue(Hash::check('password123', $user->password));

        } catch (\Exception $e) {
            $this->logError('Password hashing test failed', $e);
            $this->fail('Password hashing test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_sets_correct_user_status_based_on_email_verification_setting()
    {
        try {
            // Test with email verification required (default)
            $userData = [
                'username' => 'testuser3',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'first_name' => 'Test',
                'last_name' => 'User',
                'gender' => UserGender::Male->value,
            ];

            $result = $this->authService->register($userData);

            $this->assertTrue($result['success']);
            $user = $result['user'];

            // Should be inactive if email verification is required
            $this->assertEquals(UserStatus::Inactive, $user->status);
            $this->assertFalse($user->is_verified);
            $this->assertNull($user->email_verified_at);

        } catch (\Exception $e) {
            $this->logError('User status test failed', $e);
            $this->fail('User status test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_can_login_user_with_valid_credentials()
    {
        try {
            // Create a user first
            $user = User::factory()->create([
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'status' => UserStatus::Active,
                'is_verified' => true,
                'email_verified_at' => now(),
            ]);

            $credentials = [
                'email' => '<EMAIL>',
                'password' => 'password123',
            ];

            $result = $this->authService->login($credentials);

            $this->assertTrue($result['success']);
            $this->assertArrayHasKey('data', $result);
            $this->assertArrayHasKey('user', $result['data']);
            $this->assertArrayHasKey('token', $result['data']);
            $this->assertArrayHasKey('token_type', $result['data']);
            $this->assertArrayHasKey('expires_in', $result['data']);

            $this->assertEquals('bearer', $result['data']['token_type']);
            $this->assertNotEmpty($result['data']['token']);

        } catch (\Exception $e) {
            $this->logError('User login test failed', $e);
            $this->fail('User login test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_fails_login_with_invalid_credentials()
    {
        try {
            $user = User::factory()->create([
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'status' => UserStatus::Active,
                'is_verified' => true,
            ]);

            $credentials = [
                'email' => '<EMAIL>',
                'password' => 'wrongpassword',
            ];

            $result = $this->authService->login($credentials);

            $this->assertFalse($result['success']);
            $this->assertArrayHasKey('message', $result);

        } catch (\Exception $e) {
            $this->logError('Invalid login test failed', $e);
            $this->fail('Invalid login test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_fails_login_for_inactive_user()
    {
        try {
            $user = User::factory()->create([
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'status' => UserStatus::Inactive,
                'is_verified' => false,
            ]);

            $credentials = [
                'email' => '<EMAIL>',
                'password' => 'password123',
            ];

            $result = $this->authService->login($credentials);

            $this->assertFalse($result['success']);
            $this->assertArrayHasKey('message', $result);

        } catch (\Exception $e) {
            $this->logError('Inactive user login test failed', $e);
            $this->fail('Inactive user login test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_can_refresh_jwt_token()
    {
        try {
            $user = User::factory()->create([
                'status' => UserStatus::Active,
                'is_verified' => true,
            ]);

            // Login to get initial token
            $token = auth('api')->login($user);
            $this->assertNotEmpty($token);

            // Set the token for refresh
            auth('api')->setToken($token);

            $result = $this->authService->refresh();

            $this->assertArrayHasKey('token', $result);
            $this->assertArrayHasKey('token_type', $result);
            $this->assertArrayHasKey('expires_in', $result);
            $this->assertEquals('bearer', $result['token_type']);
            $this->assertNotEmpty($result['token']);

        } catch (\Exception $e) {
            $this->logError('Token refresh test failed', $e);
            $this->fail('Token refresh test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function it_can_logout_user()
    {
        try {
            $user = User::factory()->create([
                'status' => UserStatus::Active,
                'is_verified' => true,
            ]);

            // Login to get token
            $token = auth('api')->login($user);
            auth('api')->setToken($token);

            // Logout should not throw exception
            $this->authService->logout();

            // Token should be invalidated
            $this->expectException(\Tymon\JWTAuth\Exceptions\TokenInvalidException::class);
            auth('api')->user();

        } catch (\Tymon\JWTAuth\Exceptions\TokenInvalidException $e) {
            // This is expected after logout
            $this->assertTrue(true);
        } catch (\Exception $e) {
            $this->logError('User logout test failed', $e);
            $this->fail('User logout test failed: ' . $e->getMessage());
        }
    }
}
