<?php

namespace Modules\Sidebar\Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class SidebarPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            [
                'name' => 'sidebar.view',
                'display_name' => 'View Sidebars',
                'description' => 'Can view sidebars',
                'group' => 'sidebar',
                'order' => 1
            ],
            [
                'name' => 'sidebar.create',
                'display_name' => 'Create Sidebars',
                'description' => 'Can create new sidebars',
                'group' => 'sidebar',
                'order' => 2
            ],
            [
                'name' => 'sidebar.edit',
                'display_name' => 'Edit Sidebars',
                'description' => 'Can edit existing sidebars',
                'group' => 'sidebar',
                'order' => 3
            ],
            [
                'name' => 'sidebar.delete',
                'display_name' => 'Delete Sidebars',
                'description' => 'Can soft delete sidebars',
                'group' => 'sidebar',
                'order' => 4
            ],
            [
                'name' => 'sidebar.destroy',
                'display_name' => 'Destroy Sidebars',
                'description' => 'Can permanently delete sidebars',
                'group' => 'sidebar',
                'order' => 5
            ]
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }
}