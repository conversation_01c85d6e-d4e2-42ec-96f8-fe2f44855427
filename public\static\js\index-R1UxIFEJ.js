var D=(u,o,r)=>new Promise((d,m)=>{var c=g=>{try{y(r.next(g))}catch(v){m(v)}},w=g=>{try{y(r.throw(g))}catch(v){m(v)}},y=g=>g.done?d(g.value):Promise.resolve(g.value).then(c,w);y((r=r.apply(u,o)).next())});import{q as H,B as te,ai as se,a7 as oe,g as O,a8 as s,u as Z,fL as ae,r as S,e as $,fM as ne,fN as le,m as z,n as C,D as x,k as e,E as t,a4 as ie,G as _,N as a,Q as A,X as de,a6 as ue,ap as me,I as ce,J as pe,P as L,M as q,H as fe,L as ge,aC as we,fP as _e,ax as T,i as he,_ as ye}from"./index-ZVLuktk4.js";import{a as ve,u as xe,b as Ve,d as Ne}from"./dark-C3BJic5q.js";import{u as Ee}from"./useLayout-B65ZqU-V.js";import{b as be,i as Pe,a as Ce,T as Le}from"./index-BnPdKjjT.js";import{u as E}from"./hooks-CuzZ-_om.js";import{u as qe,g as Se}from"./useTranslation-Bgn2GAKO.js";import{d as B}from"./lock-fill-BZPnsoM7.js";import{d as F}from"./user-3-fill-xIOYikcN.js";import Te from"./AuthNavigation-DpjgmbE5.js";import"./epTheme-DzIV6EF-.js";const h=H({name:"Motion",props:{delay:{type:Number,default:50}},render(){const{delay:u}=this,o=oe("motion");return te(se("div",{},{default:()=>{var r,d;return[(d=(r=this.$slots).default)==null?void 0:d.call(r)]}}),[[o,{initial:{opacity:0,y:100},enter:{opacity:1,y:0,transition:{delay:u}}}]])}}),Ue=(u,o,r)=>{if(!o||o.trim()===""){r(new Error(s("Email is required")));return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o)){r(new Error(s("Please enter a valid email address")));return}r()},Ie=(u,o,r)=>{if(!o||o.trim()===""){r(new Error(s("Username is required")));return}const d=3,m=20;if(o.length<d){r(new Error(s("Username too short (min {count})",{count:d})));return}if(o.length>m){r(new Error(s("Username too long (max {count})",{count:m})));return}if(!/^[a-zA-Z0-9_-]+$/.test(o)){r(new Error(s("Invalid username format")));return}r()},Re=(u,o,r)=>{var b;if(!o||o.trim()===""){r(new Error(s("Password is required")));return}const m=((b=Z().settings)==null?void 0:b.security)||{},c=m.minPasswordLength||6,w=m.requireNumbers||!1,y=m.requireSymbols||!1,g=m.requireUppercase||!1,v=m.requireLowercase||!1;if(o.length<c){r(new Error(s("Password too short (min {count})",{count:c})));return}if(w&&!/\d/.test(o)){r(new Error(s("Password needs numbers")));return}if(y&&!/[!@#$%^&*(),.?":{}|<>]/.test(o)){r(new Error(s("Password needs symbols")));return}if(g&&!/[A-Z]/.test(o)){r(new Error(s("Password needs uppercase")));return}if(v&&!/[a-z]/.test(o)){r(new Error(s("Password needs lowercase")));return}r()},M=u=>(o,r,d)=>{if(!r||r.trim()===""){d(new Error(s("{field} is required",{field:s(u)})));return}const m=1,c=50,w=r.trim();if(w.length<m){d(new Error(s("{field} must be at least {count} characters",{field:s(u),count:m})));return}if(w.length>c){d(new Error(s("{field} must be less than {count} characters",{field:s(u),count:c})));return}if(!/^[\p{L}\s]+$/u.test(w)){d(new Error(s("{field} contains invalid characters",{field:s(u)})));return}d()},De=u=>(o,r,d)=>{if(!r||r.trim()===""){d(new Error(s("Confirm password required")));return}if(u.password&&u.password!==r){d(new Error(s("Passwords don't match")));return}d()},$e=(u,o,r)=>{if(!o){r(new Error(s("Accept terms required")));return}r()},ze=u=>O({username:[{validator:Ie,trigger:"blur"}],email:[{validator:Ue,trigger:"blur"}],password:[{validator:Re,trigger:"blur"}],confirmPassword:[{validator:De(u),trigger:"blur"}],firstName:[{validator:M("First Name"),trigger:"blur"}],lastName:[{validator:M("Last Name"),trigger:"blur"}],acceptTerms:[{validator:$e,trigger:"change"}]}),Ae={class:"select-none"},Be=["src"],Fe={class:"flex-c absolute right-5 top-3"},Me={class:"login-container"},He={class:"img"},Oe={class:"login-box"},Ze={class:"login-form"},je={class:"outline-none"},ke={class:"flex gap-4"},Ge={class:"flex items-center"},Je=H({name:"Register",__name:"index",setup(u){const o=ae(),r=Z(),d=qe();Object.keys(r.settings).length===0&&r.fetchPublicSettings();const m=he(),c=S(!1),w=S(!1),y=S(),{initStorage:g}=Ee();g();const{dataTheme:v,overallStyle:b,dataThemeChange:U}=ve();U(b.value);const{getDropdownItemStyle:j,getDropdownItemClass:k}=xe(),n=O({username:"",email:"",firstName:"",lastName:"",password:"",confirmPassword:"",acceptTerms:!1}),G=$(()=>{var p;return(p=o.languages)==null?void 0:p.map(l=>({locale:l.code,native:l.nativeName}))}),I=$(()=>o.locale||ne.global.locale.value),R=p=>D(null,null,function*(){var l,P;if(p)try{yield p.validate(),c.value=!0;const f=yield _e({username:n.username,email:n.email,firstName:n.firstName,lastName:n.lastName,password:n.password,passwordConfirmation:n.confirmPassword,acceptTerms:n.acceptTerms});if(!f.success){T(f.message,{type:"error"});return}T(f.message,{type:"success"}),yield m.push({path:"/verify-email",query:{email:n.email}})}catch(f){T(((P=(l=f==null?void 0:f.response)==null?void 0:l.data)==null?void 0:P.message)||(f==null?void 0:f.message),{type:"error"})}finally{c.value=!1}}),J=me(p=>R(p),1e3,!0),Q=p=>{d.switchLanguage(p)};return le(document,"keydown",({code:p})=>{["Enter","NumpadEnter"].includes(p)&&!w.value&&!c.value&&J(y.value)}),(p,l)=>{const P=_("el-switch"),f=_("el-dropdown-item"),W=_("el-dropdown-menu"),X=_("el-dropdown"),N=_("el-input"),V=_("el-form-item"),K=_("el-link"),Y=_("el-checkbox"),ee=_("el-button"),re=_("el-form");return C(),z("div",Ae,[x("img",{src:e(be),class:"wave",alt:"Bg"},null,8,Be),x("div",Fe,[t(P,{modelValue:e(v),"onUpdate:modelValue":l[0]||(l[0]=i=>ie(v)?v.value=i:null),"inline-prompt":"","active-icon":e(Ne),"inactive-icon":e(Ve),onChange:e(U)},null,8,["modelValue","active-icon","inactive-icon","onChange"]),t(X,{trigger:"click"},{dropdown:a(()=>[t(W,{class:"translation"},{default:a(()=>[(C(!0),z(ce,null,pe(G.value,i=>(C(),A(f,{key:i.locale,style:fe(e(j)(I.value,i.locale)),class:ge(["dark:!text-white",e(k)(I.value,i.locale)]),onClick:Qe=>Q(i.locale)},{default:a(()=>[L(q(i.native),1)]),_:2},1032,["style","class","onClick"]))),128))]),_:1})]),default:a(()=>[t(e(Se),{class:"hover:text-primary hover:!bg-[transparent] w-[20px] h-[20px] ml-1.5 cursor-pointer outline-none duration-300"})]),_:1})]),x("div",Me,[x("div",He,[(C(),A(de(ue(e(Pe)))))]),x("div",Oe,[x("div",Ze,[t(e(Ce),{class:"avatar"}),t(e(h),null,{default:a(()=>[x("h2",je,[t(e(Le),{options:{strings:[e(s)("Create Account")],cursor:!1,speed:100}},null,8,["options"])])]),_:1}),t(re,{ref_key:"ruleFormRef",ref:y,model:n,rules:e(ze)(n),size:"large"},{default:a(()=>[x("div",ke,[t(e(h),{delay:100},{default:a(()=>[t(V,{prop:"firstName",class:"flex-1"},{default:a(()=>[t(N,{modelValue:n.firstName,"onUpdate:modelValue":l[1]||(l[1]=i=>n.firstName=i),clearable:"",placeholder:e(s)("First Name"),"prefix-icon":e(E)(e(F))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),t(e(h),{delay:120},{default:a(()=>[t(V,{prop:"lastName",class:"flex-1"},{default:a(()=>[t(N,{modelValue:n.lastName,"onUpdate:modelValue":l[2]||(l[2]=i=>n.lastName=i),clearable:"",placeholder:e(s)("Last Name"),"prefix-icon":e(E)(e(F))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1})]),t(e(h),{delay:150},{default:a(()=>[t(V,{prop:"username"},{default:a(()=>[t(N,{modelValue:n.username,"onUpdate:modelValue":l[3]||(l[3]=i=>n.username=i),clearable:"",placeholder:e(s)("Username"),"prefix-icon":e(E)("ri:user-line")},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),t(e(h),{delay:180},{default:a(()=>[t(V,{prop:"email"},{default:a(()=>[t(N,{modelValue:n.email,"onUpdate:modelValue":l[4]||(l[4]=i=>n.email=i),clearable:"",placeholder:e(s)("Email"),"prefix-icon":e(E)("ri:mail-line")},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),t(e(h),{delay:200},{default:a(()=>[t(V,{prop:"password"},{default:a(()=>[t(N,{modelValue:n.password,"onUpdate:modelValue":l[5]||(l[5]=i=>n.password=i),clearable:"","show-password":"",placeholder:e(s)("Password"),"prefix-icon":e(E)(e(B))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),t(e(h),{delay:220},{default:a(()=>[t(V,{prop:"confirmPassword"},{default:a(()=>[t(N,{modelValue:n.confirmPassword,"onUpdate:modelValue":l[6]||(l[6]=i=>n.confirmPassword=i),clearable:"","show-password":"",placeholder:e(s)("Confirm Password"),"prefix-icon":e(E)(e(B))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),t(e(h),{delay:250},{default:a(()=>[t(V,{size:"small",prop:"acceptTerms"},{default:a(()=>[t(Y,{modelValue:n.acceptTerms,"onUpdate:modelValue":l[7]||(l[7]=i=>n.acceptTerms=i)},{default:a(()=>[x("div",Ge,[L(q(e(s)("I accept the"))+" ",1),t(K,{type:"primary",class:"ml-1"},{default:a(()=>[L(q(e(s)("Terms and Conditions")),1)]),_:1})])]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(e(h),{delay:280},{default:a(()=>[t(ee,{class:"w-full !mt-3 !uppercase",size:"large",type:"danger",round:"",loading:c.value,disabled:w.value,onClick:l[8]||(l[8]=i=>R(y.value))},{default:a(()=>[t(e(we),{icon:"ri:user-add-line",width:"20",class:"mr-2"}),L(" "+q(e(s)("Create Account")),1)]),_:1},8,["loading","disabled"])]),_:1}),t(e(h),{delay:350},{default:a(()=>[t(Te,{"show-login":!0,"show-register":!1,"show-forgot-password":!1})]),_:1})]),_:1},8,["model","rules"])])])])])}}}),lr=ye(Je,[["__scopeId","data-v-bcd97042"]]);export{lr as default};
