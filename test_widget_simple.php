<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Modules\ChatBot\Models\Bot;

// Test basic functionality
echo "🚀 Testing Widget API Components\n";
echo "================================\n\n";

// Test 1: Check if Bot model has API key
echo "🔍 Test 1: Check Bot API Keys\n";
try {
    $bot = Bot::first();
    if ($bot) {
        echo "✅ Bot found: {$bot->name}\n";
        echo "✅ API Key: " . ($bot->api_key ? substr($bot->api_key, 0, 10) . '...' : 'Not set') . "\n";
        echo "✅ UUID: {$bot->uuid}\n";
    } else {
        echo "❌ No bots found\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Check middleware registration
echo "🔍 Test 2: Check Middleware Registration\n";
try {
    $app = app();
    $router = $app->make('router');
    $middlewares = $router->getMiddleware();
    
    if (isset($middlewares['widget.auth'])) {
        echo "✅ Widget auth middleware registered\n";
    } else {
        echo "❌ Widget auth middleware not found\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Check if controllers exist
echo "🔍 Test 3: Check Controller Classes\n";
$controllers = [
    'Modules\ChatBot\Http\Controllers\Api\WidgetController',
    'Modules\ChatBot\Http\Controllers\Api\WidgetConversationController',
    'Modules\ChatBot\Http\Controllers\Api\WidgetMessageController',
];

foreach ($controllers as $controller) {
    if (class_exists($controller)) {
        echo "✅ {$controller} exists\n";
    } else {
        echo "❌ {$controller} not found\n";
    }
}

echo "\n✅ Widget API component testing completed!\n";
