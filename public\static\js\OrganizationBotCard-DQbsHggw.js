import{_ as g,G as n,m,n as x,E as a,N as l,D as t,M as o,P as i}from"./index-ZVLuktk4.js";const p={},u={class:"group"},f={class:"bg-gradient-to-r from-emerald-500 to-teal-500 -m-5 mb-4 p-6 text-white"},_={class:"flex items-center justify-between"},v={class:"flex items-center space-x-3"},b={class:"text-xl font-bold"},y={class:"space-y-4"},h={class:"grid grid-cols-2 gap-4"},$={class:"flex items-center space-x-2"},w={class:"text-xs text-gray-500"},z={class:"font-semibold text-gray-800"},C={class:"flex items-center space-x-2"},A={class:"text-xs text-gray-500"},k={class:"grid grid-cols-2 gap-4"},N={class:"flex items-center space-x-2"},B={class:"text-xs text-gray-500"},D={class:"flex items-center space-x-2"},O={class:"text-xs text-gray-500"},V={class:"bg-gray-50 rounded-lg p-3"},j={class:"text-xs text-gray-500 mb-1"},E={class:"text-sm text-gray-700 leading-relaxed"},G={class:"flex space-x-2"};function I(e,s){const d=n("el-tag"),r=n("el-button"),c=n("el-card");return x(),m("div",u,[a(c,{class:"transform transition-all !rounded-[12px] duration-300 hover:scale-105 hover:shadow-2xl border-0 bg-white/80 backdrop-blur-sm overflow-hidden"},{header:l(()=>[t("div",f,[t("div",_,[t("div",v,[s[1]||(s[1]=t("div",{class:"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm"},[t("i",{class:"el-icon-picture text-2xl"})],-1)),t("div",null,[t("h3",b,o(e.$t("Organization AI Assistant")),1),s[0]||(s[0]=t("p",{class:"text-emerald-100 text-sm"},"GPT-4",-1))])]),a(d,{type:"success",effect:"light",size:"small",class:"bg-green-100 text-green-800 border-green-200"},{default:l(()=>[i(o(e.$t("Active")),1)]),_:1})])])]),default:l(()=>[t("div",y,[t("div",h,[t("div",$,[s[2]||(s[2]=t("i",{class:"el-icon-user text-gray-400"},null,-1)),t("div",null,[t("p",w,o(e.$t("Creator")),1),t("p",z,o(e.$t("Organization Admin")),1)])]),t("div",C,[s[4]||(s[4]=t("i",{class:"el-icon-calendar text-gray-400"},null,-1)),t("div",null,[t("p",A,o(e.$t("Created Date")),1),s[3]||(s[3]=t("p",{class:"font-semibold text-gray-800"},"22/03/2024",-1))])])]),t("div",k,[t("div",N,[s[6]||(s[6]=t("i",{class:"el-icon-chat-line-round text-gray-400"},null,-1)),t("div",null,[t("p",B,o(e.$t("Conversations")),1),s[5]||(s[5]=t("p",{class:"font-semibold text-gray-800"},"1,234",-1))])]),t("div",D,[s[8]||(s[8]=t("i",{class:"el-icon-star text-gray-400"},null,-1)),t("div",null,[t("p",O,o(e.$t("Rating")),1),s[7]||(s[7]=t("p",{class:"font-semibold text-gray-800"},"4.8/5",-1))])])]),t("div",V,[t("p",j,o(e.$t("Description")),1),t("p",E,o(e.$t("Advanced AI assistant for organization management and automation tasks.")),1)]),t("div",G,[a(r,{type:"primary",size:"small",class:"flex-1 !bg-gradient-to-r !from-emerald-500 !to-teal-500 !border-0 hover:!from-emerald-600 hover:!to-teal-600"},{default:l(()=>[s[9]||(s[9]=t("i",{class:"el-icon-chat-line-round mr-1"},null,-1)),i(" "+o(e.$t("Chat Now")),1)]),_:1,__:[9]}),a(r,{type:"default",size:"small",class:"!border-gray-200 hover:!border-emerald-400 hover:!text-emerald-600"},{default:l(()=>[s[10]||(s[10]=t("i",{class:"el-icon-setting mr-1"},null,-1)),i(" "+o(e.$t("Settings")),1)]),_:1,__:[10]})])])]),_:1})])}const S=g(p,[["render",I]]);export{S as default};
