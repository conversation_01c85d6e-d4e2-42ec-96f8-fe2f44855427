<?php

namespace Modules\Auth\Tests\Unit;

use Mo<PERSON>les\Auth\Tests\TestCase;
use Modules\Auth\Http\Requests\RegisterRequest;
use Modules\Auth\Http\Requests\LoginRequest;
use Modules\Auth\Http\Requests\ForgotPasswordRequest;
use Modules\Auth\Http\Requests\ResetPasswordRequest;
use Modules\Auth\Http\Requests\ChangePasswordRequest;
use Modules\Auth\Http\Requests\UpdateProfileRequest;
use Modules\User\Models\User;
use Modules\User\Enums\UserGender;
use Illuminate\Support\Facades\Validator;
use PHPUnit\Framework\Attributes\Test;

class AuthRequestTest extends TestCase
{
    #[Test]
    public function register_request_has_correct_validation_rules()
    {
        try {
            $request = new RegisterRequest();
            $rules = $request->rules();

            $expectedRules = [
                'username',
                'email',
                'password',
                'password_confirmation',
                'first_name',
                'last_name',
                'gender',
            ];

            foreach ($expectedRules as $field) {
                $this->assertArrayHasKey($field, $rules, "Missing validation rule for field: {$field}");
            }

            // Test specific rule requirements
            $this->assertStringContainsString('required', $rules['username']);
            $this->assertStringContainsString('unique:users', $rules['username']);
            $this->assertStringContainsString('required', $rules['email']);
            $this->assertStringContainsString('email', $rules['email']);
            $this->assertStringContainsString('unique:users', $rules['email']);
            $this->assertStringContainsString('confirmed', $rules['password']);

        } catch (\Exception $e) {
            $this->logError('Register request validation rules test failed', $e);
            $this->fail('Register request validation rules test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function register_request_validates_valid_data()
    {
        try {
            $request = new RegisterRequest();
            $rules = $request->rules();

            $validData = [
                'username' => 'testuser',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'first_name' => 'Test',
                'last_name' => 'User',
                'gender' => UserGender::Male->value,
                'phone' => '+1234567890',
            ];

            $validator = Validator::make($validData, $rules);
            $this->assertTrue($validator->passes(), 'Valid data should pass validation');

        } catch (\Exception $e) {
            $this->logError('Register request valid data test failed', $e);
            $this->fail('Register request valid data test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function register_request_fails_with_invalid_data()
    {
        try {
            $request = new RegisterRequest();
            $rules = $request->rules();

            $invalidDataSets = [
                // Missing required fields
                [],
                // Invalid email
                [
                    'username' => 'test',
                    'email' => 'invalid-email',
                    'password' => 'password123',
                    'password_confirmation' => 'password123',
                    'first_name' => 'Test',
                    'last_name' => 'User',
                    'gender' => UserGender::Male->value,
                ],
                // Password confirmation mismatch
                [
                    'username' => 'test',
                    'email' => '<EMAIL>',
                    'password' => 'password123',
                    'password_confirmation' => 'different',
                    'first_name' => 'Test',
                    'last_name' => 'User',
                    'gender' => UserGender::Male->value,
                ],
                // Invalid gender
                [
                    'username' => 'test',
                    'email' => '<EMAIL>',
                    'password' => 'password123',
                    'password_confirmation' => 'password123',
                    'first_name' => 'Test',
                    'last_name' => 'User',
                    'gender' => 'invalid',
                ],
            ];

            foreach ($invalidDataSets as $invalidData) {
                $validator = Validator::make($invalidData, $rules);
                $this->assertTrue($validator->fails(), 'Invalid data should fail validation');
            }

        } catch (\Exception $e) {
            $this->logError('Register request invalid data test failed', $e);
            $this->fail('Register request invalid data test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function login_request_has_correct_validation_rules()
    {
        try {
            $request = new LoginRequest();
            $rules = $request->rules();

            $expectedRules = ['login', 'password'];

            foreach ($expectedRules as $field) {
                $this->assertArrayHasKey($field, $rules, "Missing validation rule for field: {$field}");
                $this->assertStringContainsString('required', $rules[$field]);
            }

        } catch (\Exception $e) {
            $this->logError('Login request validation rules test failed', $e);
            $this->fail('Login request validation rules test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function login_request_validates_valid_data()
    {
        try {
            $request = new LoginRequest();
            $rules = $request->rules();

            $validDataSets = [
                [
                    'login' => '<EMAIL>',
                    'password' => 'password123',
                ],
                [
                    'login' => 'testuser',
                    'password' => 'password123',
                ],
            ];

            foreach ($validDataSets as $validData) {
                $validator = Validator::make($validData, $rules);
                $this->assertTrue($validator->passes(), 'Valid login data should pass validation');
            }

        } catch (\Exception $e) {
            $this->logError('Login request valid data test failed', $e);
            $this->fail('Login request valid data test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function forgot_password_request_validates_email()
    {
        try {
            $request = new ForgotPasswordRequest();
            $rules = $request->rules();

            $this->assertArrayHasKey('email', $rules);
            $this->assertStringContainsString('required', $rules['email']);
            $this->assertStringContainsString('email', $rules['email']);

            // Test valid email
            $validData = ['email' => '<EMAIL>'];
            $validator = Validator::make($validData, $rules);
            $this->assertTrue($validator->passes());

            // Test invalid email
            $invalidData = ['email' => 'invalid-email'];
            $validator = Validator::make($invalidData, $rules);
            $this->assertTrue($validator->fails());

        } catch (\Exception $e) {
            $this->logError('Forgot password request test failed', $e);
            $this->fail('Forgot password request test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function reset_password_request_validates_required_fields()
    {
        try {
            $request = new ResetPasswordRequest();
            $rules = $request->rules();

            $expectedRules = ['email', 'code', 'password', 'password_confirmation'];

            foreach ($expectedRules as $field) {
                $this->assertArrayHasKey($field, $rules, "Missing validation rule for field: {$field}");
                $this->assertStringContainsString('required', $rules[$field]);
            }

            $this->assertStringContainsString('confirmed', $rules['password']);

        } catch (\Exception $e) {
            $this->logError('Reset password request test failed', $e);
            $this->fail('Reset password request test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function change_password_request_validates_current_password()
    {
        try {
            $request = new ChangePasswordRequest();
            $rules = $request->rules();

            $expectedRules = ['current_password'];

            foreach ($expectedRules as $field) {
                $this->assertArrayHasKey($field, $rules, "Missing validation rule for field: {$field}");
                $this->assertStringContainsString('required', $rules[$field]);
            }

        } catch (\Exception $e) {
            $this->logError('Change password request test failed', $e);
            $this->fail('Change password request test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function update_profile_request_validates_optional_fields()
    {
        try {
            $request = new UpdateProfileRequest();
            $rules = $request->rules();

            // Profile update should have optional validation for most fields
            $optionalFields = ['first_name', 'last_name', 'phone', 'gender'];

            foreach ($optionalFields as $field) {
                if (isset($rules[$field])) {
                    // If field exists in rules, it should not be required
                    $this->assertStringNotContainsString('required', $rules[$field], 
                        "Field {$field} should not be required in profile update");
                }
            }

        } catch (\Exception $e) {
            $this->logError('Update profile request test failed', $e);
            $this->fail('Update profile request test failed: ' . $e->getMessage());
        }
    }

    #[Test]
    public function register_request_prevents_duplicate_username_and_email()
    {
        try {
            // Create existing user
            $existingUser = User::factory()->create([
                'username' => 'existing',
                'email' => '<EMAIL>',
            ]);

            $request = new RegisterRequest();
            $rules = $request->rules();

            // Test duplicate username
            $duplicateUsernameData = [
                'username' => 'existing',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'first_name' => 'Test',
                'last_name' => 'User',
                'gender' => UserGender::Male->value,
            ];

            $validator = Validator::make($duplicateUsernameData, $rules);
            $this->assertTrue($validator->fails());
            $this->assertTrue($validator->errors()->has('username'));

            // Test duplicate email
            $duplicateEmailData = [
                'username' => 'newuser',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'first_name' => 'Test',
                'last_name' => 'User',
                'gender' => UserGender::Male->value,
            ];

            $validator = Validator::make($duplicateEmailData, $rules);
            $this->assertTrue($validator->fails());
            $this->assertTrue($validator->errors()->has('email'));

        } catch (\Exception $e) {
            $this->logError('Duplicate validation test failed', $e);
            $this->fail('Duplicate validation test failed: ' . $e->getMessage());
        }
    }
}
