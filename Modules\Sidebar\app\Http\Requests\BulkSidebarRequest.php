<?php

namespace Modules\Sidebar\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class BulkSidebarRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => ['required', 'array', 'min:1'],
            'ids.*' => ['required', 'integer', 'exists:sidebars,id'],
            'action' => ['required', 'string', 'in:delete,restore,destroy'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('sidebar.attributes.ids'),
            'ids.*' => __('sidebar.attributes.id'),
            'action' => __('sidebar.attributes.action'),
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('sidebar.validation.ids_required'),
            'ids.array' => __('sidebar.validation.ids_must_be_array'),
            'ids.min' => __('sidebar.validation.ids_min_one'),
            'ids.*.required' => __('sidebar.validation.id_required'),
            'ids.*.integer' => __('sidebar.validation.id_must_be_integer'),
            'ids.*.exists' => __('sidebar.validation.sidebar_not_exists'),
            'action.required' => __('sidebar.validation.action_required'),
            'action.in' => __('sidebar.validation.action_invalid'),
        ];
    }

    /**
     * Get the validated IDs
     */
    public function getIds(): array
    {
        return $this->validated('ids', []);
    }

    /**
     * Get the validated action
     */
    public function getAction(): string
    {
        return $this->validated('action', '');
    }
}
