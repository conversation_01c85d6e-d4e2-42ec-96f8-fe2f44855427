import{E as a,a8 as r,ai as t,I as p,fG as s,gs as n,aD as h}from"./index-ZVLuktk4.js";import{u as m}from"./hooks-CuzZ-_om.js";const l=e=>e?h(e).format("YYYY-MM-DD HH:mm"):"-",i={0:{label:"Menu",type:"primary"},1:{label:"Iframe",type:"warning"},2:{label:"External Link",type:"danger"},3:{label:"Button",type:"info"}},f=[{type:"selection",width:55,align:"center",hide:!1},{headerRenderer:()=>r("Title"),prop:"title",align:"left",minWidth:200,cellRenderer:({row:e})=>a(p,null,[e.icon&&a("span",{class:"inline-block mr-2"},[t(m(e.icon),{style:{paddingTop:"1px"}})]),a("span",null,[r(e.title)])])},{headerRenderer:()=>r("Name"),prop:"name",align:"left",minWidth:150,showOverflowTooltip:!0},{headerRenderer:()=>r("Path"),prop:"path",align:"left",minWidth:180,showOverflowTooltip:!0,formatter:({path:e})=>e||"-"},{headerRenderer:()=>r("Type"),prop:"menuType",align:"center",width:100,cellRenderer:({row:e})=>{const{label:d,type:o}=i[e.menuType]||i[0];return t(s,{type:o,size:"small",effect:"plain"},()=>r(d))}},{headerRenderer:()=>r("Parent"),prop:"parentId",align:"center",width:100,formatter:({parentId:e})=>e||"-"},{headerRenderer:()=>r("Order"),prop:"rank",align:"center",width:80,sortable:!0},{headerRenderer:()=>r("Show Link"),prop:"showLink",align:"center",width:100,cellRenderer:({row:e})=>t(n,{modelValue:e.showLink,disabled:!0,size:"small"})},{headerRenderer:()=>r("Keep Alive"),prop:"keepAlive",align:"center",width:100,cellRenderer:({row:e})=>t(n,{modelValue:e.keepAlive,disabled:!0,size:"small"})},{headerRenderer:()=>r("Created At"),prop:"createdAt",align:"center",width:160,sortable:!0,formatter:({createdAt:e})=>l(e)},{headerRenderer:()=>r("Updated At"),prop:"updatedAt",align:"center",width:160,sortable:!0,formatter:({updatedAt:e})=>l(e)},{headerRenderer:()=>r("Operation"),fixed:"right",width:160,slot:"operation",sortable:!1,align:"center"}];export{f as columns};
