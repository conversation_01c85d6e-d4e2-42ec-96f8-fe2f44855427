var _=(d,c,o)=>new Promise((s,t)=>{var m=l=>{try{r(o.next(l))}catch(a){t(a)}},v=l=>{try{r(o.throw(l))}catch(a){t(a)}},r=l=>l.done?s(l.value):Promise.resolve(l.value).then(m,v);r((o=o.apply(d,c)).next())});/* empty css                         */import{q as w,r as g,a8 as e,e as n,G as k,Q as C,n as T,N as u,D as b,E as P,P as F,M as h,k as p,_ as S}from"./index-ZVLuktk4.js";import{P as A}from"./index-CQnBjWL2.js";import"./_plugin-vue_export-helper-QGN-qG8u.js";const x={class:"custom-group-header"},B={class:"font-semibold"},N={class:"custom-footer"},R=w({__name:"KnowledgeBaseFilterForm",props:{visible:{type:Boolean},values:{}},emits:["update:visible","update:values","submit","reset"],setup(d,{emit:c}){const o=d,s=c,t=g(!1),m=g(),v=[{label:n(()=>e("Name")),prop:"name",valueType:"input",fieldProps:{placeholder:e("Search by knowledge base name")}},{label:n(()=>e("Type")),prop:"type",valueType:"select",options:[{label:e("All"),value:""},{label:e("File"),value:"file"},{label:e("Text"),value:"text"},{label:e("URL"),value:"url"},{label:e("Document"),value:"document"}],fieldProps:{placeholder:e("Select type")}},{label:n(()=>e("Status")),prop:"status",valueType:"select",options:[{label:e("All"),value:""},{label:e("Active"),value:"active"},{label:e("Inactive"),value:"inactive"},{label:e("Processing"),value:"processing"},{label:e("Failed"),value:"failed"}],fieldProps:{placeholder:e("Select status")}},{label:n(()=>e("Trashed")),prop:"isTrashed",valueType:"select",options:[{label:e("No"),value:"no"},{label:e("Yes"),value:"yes"}],fieldProps:{placeholder:e("Show trashed items"),clearable:!1}}],r=()=>_(null,null,function*(){try{t.value=!0,s("submit",o.values)}catch(a){console.error("Filter submission failed:",a)}finally{t.value=!1}}),l=()=>{s("reset")};return(a,i)=>{const y=k("el-button");return T(),C(p(A),{ref_key:"formRef",ref:m,visible:a.visible,"model-value":a.values,size:"30%",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0,destroyOnClose:!0,form:{columns:v,footerAlign:"center",hasFooter:!1,labelSuffix:"",labelPosition:"top",labelWidth:"auto",labelAlign:"left",requireAsteriskPosition:"right",rowProps:{gutter:10}},"onUpdate:visible":i[0]||(i[0]=f=>s("update:visible",f)),"onUpdate:modelValue":i[1]||(i[1]=f=>s("update:values",f))},{"drawer-header":u(()=>[b("div",x,[b("span",B,h(p(e)("Filter")),1)])]),"drawer-footer":u(()=>[b("div",N,[P(y,{plain:"",onClick:l},{default:u(()=>[F(h(p(e)("Reset")),1)]),_:1}),P(y,{plain:"",type:"primary",loading:t.value,onClick:r},{default:u(()=>[F(h(p(e)("Apply Filter")),1)]),_:1},8,["loading"])])]),_:1},8,["visible","model-value","form"])}}}),E=S(R,[["__scopeId","data-v-d12ad6f5"]]);export{E as default};
