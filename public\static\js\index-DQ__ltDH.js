const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/StatsCard-BfYS23uf.js","static/js/hooks-CuzZ-_om.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/css/StatsCard-C9BlVxn6.css","static/js/RecentActivities-D6iOPOPt.js","static/css/RecentActivities-Lm1vf1Cy.css"])))=>i.map(i=>d[i]);
var U=(x,c,g)=>new Promise((n,i)=>{var _=l=>{try{d(g.next(l))}catch(u){i(u)}},y=l=>{try{d(g.throw(l))}catch(u){i(u)}},d=l=>l.done?n(l.value):Promise.resolve(l.value).then(_,y);d((g=g.apply(x,c)).next())});import{q as A,r as w,o as I,G as h,m as k,n as $,D as a,M as o,k as e,a8 as s,E as t,aK as M,N as r,P as v,aM as B,_ as D}from"./index-ZVLuktk4.js";import{u as f}from"./hooks-CuzZ-_om.js";const T={class:"dashboard"},E={class:"mb-6"},O={class:"text-2xl font-bold text-gray-900 mb-2"},S={class:"text-gray-600"},N={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},V={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},P={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},j={class:"lg:col-span-2"},z={class:"text-lg font-semibold"},R={class:"space-y-3"},L={class:"text-lg font-semibold"},q={class:"space-y-4"},G={class:"flex items-center justify-between"},H={class:"text-sm text-gray-600"},K={class:"flex items-center justify-between"},Q={class:"text-sm text-gray-600"},W={class:"flex items-center justify-between"},F={class:"text-sm text-gray-600"},J={class:"flex items-center justify-between"},X={class:"text-sm text-gray-600"},Y=A({__name:"index",setup(x){const c=M(()=>B(()=>import("./StatsCard-BfYS23uf.js"),__vite__mapDeps([0,1,2,3,4]))),g=M(()=>B(()=>import("./RecentActivities-D6iOPOPt.js"),__vite__mapDeps([5,2,3,6]))),n=w(!1),i=w({totalBots:0,totalChats:0,totalMessages:0,totalUsers:0,activeBots:0,activeChats:0,todayMessages:0,onlineUsers:0}),_=w({bots:{value:0,isUp:!0},chats:{value:0,isUp:!0},messages:{value:0,isUp:!0},users:{value:0,isUp:!0}}),y=()=>U(null,null,function*(){n.value=!0;try{i.value={totalBots:25,totalChats:1250,totalMessages:15680,totalUsers:342,activeBots:18,activeChats:89,todayMessages:456,onlineUsers:23},_.value={bots:{value:12.5,isUp:!0},chats:{value:8.3,isUp:!0},messages:{value:15.7,isUp:!0},users:{value:5.2,isUp:!1}}}catch(d){console.error("Error loading dashboard data:",d)}finally{n.value=!1}});return I(()=>{y()}),(d,l)=>{const u=h("IconifyIconOffline"),m=h("el-button"),C=h("el-card"),p=h("el-tag");return $(),k("div",T,[a("div",E,[a("h1",O,o(e(s)("Dashboard Overview")),1),a("p",S,o(e(s)("Welcome back! Here's what's happening with your AI assistant platform.")),1)]),a("div",N,[t(e(c),{title:e(s)("Total Bots"),value:i.value.totalBots,icon:"ri/robot-2-line",color:"primary",trend:_.value.bots,loading:n.value},null,8,["title","value","trend","loading"]),t(e(c),{title:e(s)("Total Chats"),value:i.value.totalChats,icon:"ri/chat-3-line",color:"success",trend:_.value.chats,loading:n.value},null,8,["title","value","trend","loading"]),t(e(c),{title:e(s)("Total Messages"),value:i.value.totalMessages,icon:"ri/message-3-line",color:"warning",trend:_.value.messages,loading:n.value},null,8,["title","value","trend","loading"]),t(e(c),{title:e(s)("Total Users"),value:i.value.totalUsers,icon:"ri/user-line",color:"info",trend:_.value.users,loading:n.value},null,8,["title","value","trend","loading"])]),a("div",V,[t(e(c),{title:e(s)("Active Bots"),value:i.value.activeBots,icon:"ri/robot-line",color:"success",loading:n.value},null,8,["title","value","loading"]),t(e(c),{title:e(s)("Active Chats"),value:i.value.activeChats,icon:"ri/chat-1-line",color:"primary",loading:n.value},null,8,["title","value","loading"]),t(e(c),{title:e(s)("Today Messages"),value:i.value.todayMessages,icon:"ri/message-2-line",color:"warning",loading:n.value},null,8,["title","value","loading"]),t(e(c),{title:e(s)("Online Users"),value:i.value.onlineUsers,icon:"ri/user-3-line",color:"success",loading:n.value},null,8,["title","value","loading"])]),a("div",P,[a("div",j,[t(e(g))]),a("div",null,[t(C,{shadow:"hover"},{header:r(()=>[a("span",z,o(e(s)("Quick Actions")),1)]),default:r(()=>[a("div",R,[t(m,{type:"primary",class:"w-full",onClick:l[0]||(l[0]=b=>d.$router.push("/bots"))},{default:r(()=>[t(u,{icon:e(f)("ep:plus"),class:"mr-2"},null,8,["icon"]),v(" "+o(e(s)("Create New Bot")),1)]),_:1}),t(m,{type:"success",class:"w-full",onClick:l[1]||(l[1]=b=>d.$router.push("/chat/management"))},{default:r(()=>[t(u,{icon:e(f)("ri/chat-new-line"),class:"mr-2"},null,8,["icon"]),v(" "+o(e(s)("Start New Chat")),1)]),_:1}),t(m,{type:"warning",class:"w-full",onClick:l[2]||(l[2]=b=>d.$router.push("/user/management"))},{default:r(()=>[t(u,{icon:e(f)("ri/user-add-line"),class:"mr-2"},null,8,["icon"]),v(" "+o(e(s)("Manage Users")),1)]),_:1}),t(m,{type:"info",class:"w-full",onClick:l[3]||(l[3]=b=>d.$router.push("/message/management"))},{default:r(()=>[t(u,{icon:e(f)("ri/message-line"),class:"mr-2"},null,8,["icon"]),v(" "+o(e(s)("View Messages")),1)]),_:1})])]),_:1}),t(C,{shadow:"hover",class:"mt-6"},{header:r(()=>[a("span",L,o(e(s)("System Status")),1)]),default:r(()=>[a("div",q,[a("div",G,[a("span",H,o(e(s)("API Status")),1),t(p,{type:"success",size:"small"},{default:r(()=>[v(o(e(s)("Online")),1)]),_:1})]),a("div",K,[a("span",Q,o(e(s)("Database")),1),t(p,{type:"success",size:"small"},{default:r(()=>[v(o(e(s)("Connected")),1)]),_:1})]),a("div",W,[a("span",F,o(e(s)("AI Models")),1),t(p,{type:"success",size:"small"},{default:r(()=>[v(o(e(s)("Available")),1)]),_:1})]),a("div",J,[a("span",X,o(e(s)("Storage")),1),t(p,{type:"warning",size:"small"},{default:r(()=>[v(o(e(s)("75% Used")),1)]),_:1})])])]),_:1})])])])}}}),te=D(Y,[["__scopeId","data-v-9461233d"]]);export{te as default};
