const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/ChatSidebar-XGQYyhez.js","static/js/hooks-CuzZ-_om.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/js/ChatMain-Cpq7fB-O.js","static/js/MarkdownRenderer-DWQX5-G2.js","static/css/MarkdownRenderer-YttA4swI.css","static/css/ChatMain-uIMynX68.css"])))=>i.map(i=>d[i]);
var l=(a,v,g)=>new Promise((C,m)=>{var c=u=>{try{h(g.next(u))}catch(f){m(f)}},n=u=>{try{h(g.throw(u))}catch(f){m(f)}},h=u=>u.done?C(u.value):Promise.resolve(u.value).then(c,n);h((g=g.apply(a,v)).next())});import{aN as _,az as A,aO as Y,aA as V,aP as Z,r as p,g as I,e as T,y as M,ao as ee,aQ as te,aL as se,ay as q,a8 as r,ax as y,S as ae,q as ne,an as oe,m as re,n as ce,D as ie,E as k,k as S,aK as N,aM as P}from"./index-ZVLuktk4.js";const ue=()=>_.request("get","/api/auth/chat/bots"),le=a=>_.request("get","/api/auth/conversations",{params:a}),de=a=>_.request("post","/api/auth/conversations",{data:A(a)}),ve=a=>_.request("delete",`/api/auth/conversations/${a}`),fe=(a,v)=>_.request("put",`/api/auth/conversations/${a}`,{data:A(v)}),pe=(a,v)=>_.request("get",`/api/auth/conversations/${a}/messages`,{params:v}),he=a=>_.request("post",`/api/auth/conversations/${a.conversationUuid}/messages`,{data:A(a)}),ge=Y("chat-management",{state:()=>({loading:!1,bots:[],filters:{searchQuery:""},selectedBot:null,selectedConversation:{}}),actions:{getChatBots(){return l(this,null,function*(){this.loading=!0;try{const{data:a,success:v}=yield ue();v&&(this.bots=V(a||[]))}catch(a){console.info(a)}finally{this.loading=!1}})},setConversation(a){this.selectedConversation=a}}});function R(){return ge()}function me(){const a=R(),{subscribe:v,unsubscribe:g}=Z(),C=p(!1),m=p(!1),c=I({}),n=p(null),h=p(""),u=p(!1),f=p([]),B=p([]),H=p(!1),b=p(null),x=p(null),E=T(()=>a.selectedConversation),D=T(()=>[...a.bots]),K=T(()=>n.value?c[n.value]||[]:[]),U=(e,s=1)=>l(null,null,function*(){try{const t=yield pe(e,{page:s});if(t.success&&t.data){const i=t.data.map(d=>({id:d.id,role:d.role,content:d.content}));return c[e]=i,i}return[]}catch(t){return console.error("Error loading conversation messages:",t.messages),[]}finally{w()}}),L=e=>{f.value.splice(e,1)};M(E,e=>{e&&(e!=null&&e.uuid)?n.value!==e.uuid&&(n.value=e.uuid,U(e.uuid)):n.value=null},{immediate:!0,deep:!0});const w=()=>{ae(()=>{b.value&&b.value.scrollTo({top:b.value.scrollHeight,behavior:"smooth"})})};M(n,(e,s)=>{var t;if(!((t=n.value)!=null&&t.includes("temp-"))){if(s){const i=`private-conversation.${s}`;g(i)}if(e){if(!e)return;const i=`private-conversation.${e}`;v(i,"message.received",d=>{c[e]?c[e].push(d):c[e]=[d],u.value=!1,w()})}}},{immediate:!0}),M(x,e=>{const s=D.value.find(t=>t.uuid==e);s&&(n.value=`temp-${s.uuid}`,c[n.value]=[{id:s.uuid,role:"assistant",content:s.greetingMessage}])}),ee(()=>{if(n.value){const e=`private-conversation.${n.value}`;g(e)}});const O=()=>l(null,null,function*(){try{const e=yield le();return e.success&&e.data?(B.value=V(e.data),e.data):[]}catch(e){return console.error("Error fetching conversations for bot:",e),[]}}),Q=(e,s)=>l(null,null,function*(){try{yield q.confirm(r("Are you sure you want to delete this item?"),r("Warning"),{confirmButtonText:r("Confirm"),cancelButtonText:r("Cancel"),type:"warning"}),yield z(e,s.uuid)}catch(t){t!=="cancel"&&(console.error("Error deleting conversation:",t),y(r("Delete failed"),{type:"error"}))}}),z=(e,s)=>l(null,null,function*(){var t,i,d;try{C.value=!0;const o=yield ve(s.toString());return o.success?(y(o.message||r("Delete successful"),{type:"success"}),(t=B.value)==null||t.splice(e,1),!0):(y(o.message||r("Delete failed"),{type:"error"}),!1)}catch(o){return y(((d=(i=o.response)==null?void 0:i.data)==null?void 0:d.message)||(o==null?void 0:o.message)||r("Delete failed"),{type:"error"}),!1}finally{C.value=!1}}),F=(e,s)=>l(null,null,function*(){try{const{value:t}=yield q.prompt(r("Please enter the new title for the conversation."),r("Update Conversation Title"),{confirmButtonText:r("Confirm"),cancelButtonText:r("Cancel"),inputValue:s.title});t&&(yield W(e,s.uuid,t))}catch(t){t!=="cancel"&&(console.error("Error updating conversation:",t),y(r("Update failed"),{type:"error"}))}}),W=(e,s,t)=>l(null,null,function*(){var i,d;try{C.value=!0;const o=yield fe(s,{title:t});return o.success?(y(o.message||r("Update successful"),{type:"success"}),B.value[e].title=o.data.title,!0):(y(o.message||r("Update failed"),{type:"error"}),!1)}catch(o){return y(((d=(i=o.response)==null?void 0:i.data)==null?void 0:d.message)||(o==null?void 0:o.message)||r("Update failed"),{type:"error"}),!1}finally{C.value=!1}}),j=e=>{a.setConversation(e)},G=()=>{a.setConversation({})},$=()=>l(null,null,function*(){var s;const e=h.value.trim();if(!(!e&&f.value.length===0)){if(h.value="",f.value=[],c[n.value].push({id:te(),role:"user",content:e}),(s=n.value)!=null&&s.includes("temp-")){const t=yield de({bot:x.value});t.success&&(c[t.data.uuid]=se(c[n.value]),delete c[n.value],B.value.unshift(t.data),n.value=t.data.uuid)}yield J(e)}}),J=e=>l(null,null,function*(){u.value||!n.value||(w(),yield X(e))}),X=e=>l(null,null,function*(){u.value=!0,yield he({conversationUuid:n.value,content:e}).catch(s=>{var t,i;y(((i=(t=s==null?void 0:s.response)==null?void 0:t.data)==null?void 0:i.message)||(s==null?void 0:s.message)||r("Send failed"),{type:"error"})})});return{messages:c,conversations:B,currentConversationId:n,newMessage:h,isTyping:u,attachedFiles:f,chatMessagesContainer:b,showCurrentAgentHistory:H,loadingMessages:m,selectedAgent:x,agents:D,currentMessages:K,selectedConversation:E,fetchConversationMessages:U,fetchAllConversations:O,removeAttachment:L,scrollToBottom:w,confirmDeleteConversation:Q,promptUpdateConversation:F,setConversation:j,setNewConversation:G,sendMessage:$,sendStarterPrompt:e=>l(null,null,function*(){e.trim()&&(h.value=e,yield $())})}}const ye={class:"main flex flex-col",style:{height:"calc(100% - 25px)"}},Ce={ref:"contentRef",class:"flex h-full"},we=ne({__name:"index",setup(a){const v=N(()=>P(()=>import("./ChatSidebar-XGQYyhez.js"),__vite__mapDeps([0,1,2,3]))),g=N(()=>P(()=>import("./ChatMain-Cpq7fB-O.js"),__vite__mapDeps([4,2,3,5,6,7]))),C=R(),m=me(),c=p("chat"),n=f=>{m.setConversation(f)},h=()=>m.setNewConversation(),u=()=>l(null,null,function*(){return yield C.getChatBots()});return oe(()=>l(null,null,function*(){yield u()})),(f,B)=>(ce(),re("div",ye,[ie("div",Ce,[k(S(v),{"chat-bot":S(m),onShowConversation:n,onNewConversation:h},null,8,["chat-bot"]),k(S(g),{"main-view":c.value,"chat-bot":S(m)},null,8,["main-view","chat-bot"])],512)]))}});export{we as default};
