var A=Object.defineProperty,D=Object.defineProperties;var N=Object.getOwnPropertyDescriptors;var F=Object.getOwnPropertySymbols;var S=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable;var x=(o,l,a)=>l in o?A(o,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):o[l]=a,h=(o,l)=>{for(var a in l||(l={}))S.call(l,a)&&x(o,a,l[a]);if(F)for(var a of F(l))R.call(l,a)&&x(o,a,l[a]);return o},_=(o,l)=>D(o,N(l));var k=(o,l,a)=>new Promise((r,u)=>{var c=n=>{try{v(a.next(n))}catch(g){u(g)}},p=n=>{try{v(a.throw(n))}catch(g){u(g)}},v=n=>n.done?r(n.value):Promise.resolve(n.value).then(c,p);v((a=a.apply(o,l)).next())});/* empty css                         */import{q as V,r as q,a8 as t,e as f,ai as m,ga as E,gZ as K,gD as M,gX as $,gN as O,G as X,Q as z,n as L,N as y,D as w,E as I,P as T,M as P,k as b}from"./index-ZVLuktk4.js";import{u as W}from"./hooks-CuzZ-_om.js";import{P as G}from"./index-CQnBjWL2.js";import"./_plugin-vue_export-helper-QGN-qG8u.js";const H={class:"custom-group-header"},Q={class:"font-semibold"},Y={class:"custom-footer"},te=V({__name:"KnowledgeBaseDrawerForm",props:{visible:{type:Boolean},values:{}},emits:["update:visible","update:values","submit"],setup(o,{expose:l,emit:a}){const r=o,u=a,c=q(!1),p=q(),v=[{label:f(()=>t("KnowledgeBase Name")),prop:"name",valueType:"input",required:!0,rules:[{required:!0,message:t("Please input Knowledge-base name"),trigger:["blur"]},{min:2,max:100,message:t("Length must be between 2 and 100 characters"),trigger:["blur"]}],fieldProps:{placeholder:""}},{label:f(()=>t("Type")),prop:"type",valueType:"select",required:!0,options:[{label:t("File"),value:"file"},{label:t("Text"),value:"text"}],fieldProps:{placeholder:"",clearable:!1,disabled:f(()=>r.values.uuid)},onChange:e=>{e==="text"&&u("update:values",_(h({},r.values),{files:[],storagePath:null}))}},{hideInForm:f(()=>r.values.type=="file"||r.values.uuid!=null),label:f(()=>t("Content")),prop:"content",valueType:"textarea",required:!0,rules:[{required:!0,message:t("Please input Knowledge-base content"),trigger:["blur"]}],fieldProps:{placeholder:"",rows:6}},{hideInForm:f(()=>r.values.type!="file"||r.values.uuid!=null),label:f(()=>t("Upload Files")),prop:"files",required:!0,rules:[{validator:(e,s,d)=>{r.values.type==="file"&&!r.values.storagePath&&(!Array.isArray(r.values.files)||r.values.files.length===0)?d(new Error(t("Please upload at least one file"))):d()},trigger:"change"}],renderField:e=>{var s;return m(K,{class:"upload-demo !w-full",drag:!0,action:"/api/auth/knowledge-bases/files/upload",multiple:!1,limit:1,"auto-upload":!0,headers:{Authorization:`Bearer ${(s=E().accessToken)!=null?s:E()}`,"X-Requested-With":"XMLHttpRequest"},onExceed:n,onSuccess:g,onError:U,"on-change":(d,i)=>{}},{default:()=>[m(M,{class:"el-icon--upload"},{default:()=>m($)}),m("div",{class:"el-upload__text"},[t("Drop file here or "),m("em",t("click to upload"))])],tip:()=>m("div",{class:"el-upload__tip"},[])})}}],n=()=>{O.warning(t("You can only upload one file. Please remove the existing file first."))},g=(e,s)=>{e.data&&e.data.storage_path&&u("update:values",_(h({},r.values),{storagePath:e.data.storage_path,files:[e.data]}))},U=e=>{console.error("Upload error:",e)},B=e=>k(null,null,function*(){var d;if(!(!((d=p.value)!=null&&d.formInstance)||!(yield p.value.formInstance.validate())))try{c.value=!0;const i=h({},e);i.type==="file"&&i.storagePath&&delete i.files,u("submit",i)}finally{setTimeout(()=>{c.value=!1},3e3)}}),C=()=>{var e;(e=p.value)!=null&&e.formInstance&&p.value.formInstance.resetFields()};return l({resetForm:C}),(e,s)=>{const d=X("el-button");return L(),z(b(G),{ref_key:"formRef",ref:p,visible:e.visible,"model-value":e.values,size:"40%",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0,destroyOnClose:!0,form:{columns:v,footerAlign:"center",hasFooter:!1,labelSuffix:"",labelPosition:"top",labelWidth:"auto",labelAlign:"left",requireAsteriskPosition:"right",rowProps:{gutter:10}},"onUpdate:visible":s[2]||(s[2]=i=>u("update:visible",i)),"onUpdate:modelValue":s[3]||(s[3]=i=>u("update:values",i)),onClose:C},{"drawer-header":y(()=>[w("div",H,[w("span",Q,P(b(t)("Information Form")),1)])]),"drawer-footer":y(()=>[w("div",Y,[I(d,{plain:"",onClick:s[0]||(s[0]=i=>u("update:visible",!1))},{default:y(()=>[T(P(b(t)("Cancel")),1)]),_:1}),I(d,{plain:"",type:"primary",loading:c.value,icon:b(W)("ri:save-2-line"),onClick:s[1]||(s[1]=i=>B(e.values))},{default:y(()=>[T(P(b(t)("Save")),1)]),_:1},8,["loading","icon"])])]),_:1},8,["visible","model-value","form"])}}});export{te as default};
