<?php

namespace Modules\ModelAI\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class BulkModelToolDestroyRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                Rule::exists('model_tools', 'id')->whereNotNull('deleted_at'), // Only soft deleted tools
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('Tool IDs'),
            'ids.*' => __('Tool ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Please select at least one tool.'),
            'ids.array' => __('Invalid data format.'),
            'ids.min' => __('Please select at least one tool.'),
            'ids.*.required' => __('Tool ID is required.'),
            'ids.*.integer' => __('Tool ID must be an integer.'),
            'ids.*.exists' => __('One or more selected tools must be in trash to be permanently deleted.'),
        ];
    }
}
