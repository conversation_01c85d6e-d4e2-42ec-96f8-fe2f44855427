var z=(C,_,d)=>new Promise((r,o)=>{var h=i=>{try{f(d.next(i))}catch(p){o(p)}},x=i=>{try{f(d.throw(i))}catch(p){o(p)}},f=i=>i.done?r(i.value):Promise.resolve(i.value).then(h,x);f((d=d.apply(C,_)).next())});import{q as T,j as F,r as V,g as w,a8 as e,o as U,G as u,m as B,n as k,D as m,E as t,M as c,k as l,N as a,I as q,J as A,Q as D,P as y,h8 as I,gN as O,_ as S}from"./index-ZVLuktk4.js";const j={class:"organization-bot-form"},H={class:"p-6"},R={class:"text-2xl font-bold text-gray-800 mb-6"},$={class:"w-full"},J={class:"flex justify-between items-center mb-2"},L={class:"text-sm text-gray-600"},Q={class:"w-full"},Y={class:"flex justify-between items-center mb-2"},K={class:"text-sm text-gray-600"},W=T({__name:"OrganizationBotForm",setup(C){F();const _=V([]);V("files");const d=V(),r=w({prompt:!1,greeting:!1,starters:!1,submit:!1}),o=w({name:"",description:"",aiModel:"",prompt:"",greeting:"",starters:[]}),h=w({name:[{required:!0,message:e("Please enter bot name"),trigger:"blur"}],description:[{required:!0,message:e("Please enter description"),trigger:"blur"}],aiModel:[{required:!0,message:e("Please select AI model"),trigger:"change"}]}),x=()=>z(null,null,function*(){if(d.value)try{yield d.value.validate(),r.submit=!0,O.success(e("Organization bot created successfully"))}catch(p){console.error("Form validation failed:",p)}finally{r.submit=!1}}),f=()=>{r.prompt=!0,setTimeout(()=>{o.prompt=e("You are an AI assistant for organization management. Help users with administrative tasks, scheduling, and information retrieval."),r.prompt=!1},1e3)},i=()=>{r.greeting=!0,setTimeout(()=>{o.greeting=e("Hello! I'm your organization assistant. How can I help you today?"),r.greeting=!1},1e3)};return U(()=>{_.value=[{label:"GPT-4",value:"gpt-4"},{label:"GPT-3.5 Turbo",value:"gpt-3.5-turbo"},{label:"Claude-3",value:"claude-3"}]}),(p,n)=>{const b=u("el-input"),g=u("el-form-item"),M=u("el-col"),P=u("el-option"),E=u("el-select"),G=u("el-row"),v=u("el-button"),N=u("el-form");return k(),B("div",j,[m("div",H,[m("h1",R,c(l(e)("Create Organization Bot")),1),t(N,{ref_key:"formRef",ref:d,model:o,rules:h,"label-width":"120px",class:"max-w-4xl"},{default:a(()=>[t(G,{gutter:20},{default:a(()=>[t(M,{span:12},{default:a(()=>[t(g,{label:l(e)("Bot Name"),prop:"name"},{default:a(()=>[t(b,{modelValue:o.name,"onUpdate:modelValue":n[0]||(n[0]=s=>o.name=s),placeholder:l(e)("Enter bot name")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),t(M,{span:12},{default:a(()=>[t(g,{label:l(e)("AI Model"),prop:"aiModel"},{default:a(()=>[t(E,{modelValue:o.aiModel,"onUpdate:modelValue":n[1]||(n[1]=s=>o.aiModel=s),placeholder:l(e)("Select AI model"),class:"w-full"},{default:a(()=>[(k(!0),B(q,null,A(_.value,s=>(k(),D(P,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1})]),_:1}),t(g,{label:l(e)("Description"),prop:"description"},{default:a(()=>[t(b,{modelValue:o.description,"onUpdate:modelValue":n[2]||(n[2]=s=>o.description=s),type:"textarea",rows:3,placeholder:l(e)("Enter bot description")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),t(g,{label:l(e)("System Prompt")},{default:a(()=>[m("div",$,[m("div",J,[m("span",L,c(l(e)("Define bot behavior")),1),t(v,{size:"small",loading:r.prompt,onClick:f},{icon:a(()=>[t(l(I))]),default:a(()=>[y(" "+c(l(e)("Generate")),1)]),_:1},8,["loading"])]),t(b,{modelValue:o.prompt,"onUpdate:modelValue":n[3]||(n[3]=s=>o.prompt=s),type:"textarea",rows:4,placeholder:l(e)("Enter system prompt")},null,8,["modelValue","placeholder"])])]),_:1},8,["label"]),t(g,{label:l(e)("Greeting Message")},{default:a(()=>[m("div",Q,[m("div",Y,[m("span",K,c(l(e)("First message to users")),1),t(v,{size:"small",loading:r.greeting,onClick:i},{icon:a(()=>[t(l(I))]),default:a(()=>[y(" "+c(l(e)("Generate")),1)]),_:1},8,["loading"])]),t(b,{modelValue:o.greeting,"onUpdate:modelValue":n[4]||(n[4]=s=>o.greeting=s),type:"textarea",rows:2,placeholder:l(e)("Enter greeting message")},null,8,["modelValue","placeholder"])])]),_:1},8,["label"]),t(g,null,{default:a(()=>[t(v,{type:"primary",loading:r.submit,onClick:x},{default:a(()=>[y(c(l(e)("Create Bot")),1)]),_:1},8,["loading"]),t(v,{onClick:n[5]||(n[5]=s=>p.$router.back())},{default:a(()=>[y(c(l(e)("Cancel")),1)]),_:1})]),_:1})]),_:1},8,["model","rules"])])])}}}),ee=S(W,[["__scopeId","data-v-34fd7abe"]]);export{ee as default};
