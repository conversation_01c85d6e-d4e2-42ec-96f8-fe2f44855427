/**
 * CSS Variables for ProcMS Widget Theme System
 * Isolated namespace to prevent conflicts with host website
 */

:root {
  /* === Color Palette === */
  --procms-primary-50: #eff6ff;
  --procms-primary-100: #dbeafe;
  --procms-primary-200: #bfdbfe;
  --procms-primary-300: #93c5fd;
  --procms-primary-400: #60a5fa;
  --procms-primary-500: #3b82f6;
  --procms-primary-600: #2563eb;
  --procms-primary-700: #1d4ed8;
  --procms-primary-800: #1e40af;
  --procms-primary-900: #1e3a8a;

  --procms-gray-50: #f9fafb;
  --procms-gray-100: #f3f4f6;
  --procms-gray-200: #e5e7eb;
  --procms-gray-300: #d1d5db;
  --procms-gray-400: #9ca3af;
  --procms-gray-500: #6b7280;
  --procms-gray-600: #4b5563;
  --procms-gray-700: #374151;
  --procms-gray-800: #1f2937;
  --procms-gray-900: #111827;

  --procms-success-50: #ecfdf5;
  --procms-success-500: #10b981;
  --procms-success-600: #059669;

  --procms-warning-50: #fffbeb;
  --procms-warning-500: #f59e0b;
  --procms-warning-600: #d97706;

  --procms-error-50: #fef2f2;
  --procms-error-500: #ef4444;
  --procms-error-600: #dc2626;

  /* === Theme Variables (Light) === */
  --procms-widget-bg: var(--procms-gray-50);
  --procms-widget-surface: #ffffff;
  --procms-widget-border: var(--procms-gray-200);
  --procms-widget-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --procms-widget-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Text Colors */
  --procms-text-primary: var(--procms-gray-900);
  --procms-text-secondary: var(--procms-gray-600);
  --procms-text-muted: var(--procms-gray-500);
  --procms-text-inverse: #ffffff;

  /* Interactive Colors */
  --procms-primary: var(--procms-primary-600);
  --procms-primary-hover: var(--procms-primary-700);
  --procms-primary-active: var(--procms-primary-800);
  --procms-primary-light: var(--procms-primary-50);

  /* Status Colors */
  --procms-success: var(--procms-success-500);
  --procms-warning: var(--procms-warning-500);
  --procms-error: var(--procms-error-500);

  /* === Typography === */
  --procms-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --procms-font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;

  --procms-font-size-xs: 0.75rem;    /* 12px */
  --procms-font-size-sm: 0.875rem;   /* 14px */
  --procms-font-size-base: 1rem;     /* 16px */
  --procms-font-size-lg: 1.125rem;   /* 18px */
  --procms-font-size-xl: 1.25rem;    /* 20px */
  --procms-font-size-2xl: 1.5rem;    /* 24px */

  --procms-font-weight-normal: 400;
  --procms-font-weight-medium: 500;
  --procms-font-weight-semibold: 600;
  --procms-font-weight-bold: 700;

  --procms-line-height-tight: 1.25;
  --procms-line-height-normal: 1.5;
  --procms-line-height-relaxed: 1.75;

  /* === Spacing === */
  --procms-space-1: 0.25rem;   /* 4px */
  --procms-space-2: 0.5rem;    /* 8px */
  --procms-space-3: 0.75rem;   /* 12px */
  --procms-space-4: 1rem;      /* 16px */
  --procms-space-5: 1.25rem;   /* 20px */
  --procms-space-6: 1.5rem;    /* 24px */
  --procms-space-8: 2rem;      /* 32px */
  --procms-space-10: 2.5rem;   /* 40px */
  --procms-space-12: 3rem;     /* 48px */
  --procms-space-16: 4rem;     /* 64px */

  /* === Border Radius === */
  --procms-radius-none: 0;
  --procms-radius-sm: 0.125rem;   /* 2px */
  --procms-radius-base: 0.25rem;  /* 4px */
  --procms-radius-md: 0.375rem;   /* 6px */
  --procms-radius-lg: 0.5rem;     /* 8px */
  --procms-radius-xl: 0.75rem;    /* 12px */
  --procms-radius-2xl: 1rem;      /* 16px */
  --procms-radius-full: 9999px;

  /* === Transitions === */
  --procms-transition-fast: 150ms ease-in-out;
  --procms-transition-base: 200ms ease-in-out;
  --procms-transition-slow: 300ms ease-in-out;

  /* === Z-Index === */
  --procms-z-dropdown: 1000;
  --procms-z-sticky: 1020;
  --procms-z-fixed: 1030;
  --procms-z-modal-backdrop: 1040;
  --procms-z-modal: 1050;
  --procms-z-popover: 1060;
  --procms-z-tooltip: 1070;
  --procms-z-toast: 1080;

  /* === Widget Specific === */
  --procms-widget-width: 400px;
  --procms-widget-height: 600px;
  --procms-widget-max-width: 90vw;
  --procms-widget-max-height: 90vh;
  --procms-widget-border-radius: var(--procms-radius-lg);

  /* Header */
  --procms-header-height: 60px;
  --procms-header-bg: var(--procms-primary);
  --procms-header-text: var(--procms-text-inverse);

  /* Messages */
  --procms-message-spacing: var(--procms-space-4);
  --procms-message-radius: var(--procms-radius-xl);
  --procms-message-max-width: 80%;

  /* User Message */
  --procms-user-message-bg: var(--procms-primary);
  --procms-user-message-text: var(--procms-text-inverse);

  /* Bot Message */
  --procms-bot-message-bg: var(--procms-widget-surface);
  --procms-bot-message-text: var(--procms-text-primary);
  --procms-bot-message-border: var(--procms-widget-border);

  /* Input */
  --procms-input-height: 44px;
  --procms-input-bg: var(--procms-widget-surface);
  --procms-input-border: var(--procms-widget-border);
  --procms-input-focus-border: var(--procms-primary);
  --procms-input-radius: var(--procms-radius-2xl);

  /* Buttons */
  --procms-button-height: 40px;
  --procms-button-radius: var(--procms-radius-full);
  --procms-button-primary-bg: var(--procms-primary);
  --procms-button-primary-hover: var(--procms-primary-hover);
  --procms-button-primary-text: var(--procms-text-inverse);

  /* Avatar */
  --procms-avatar-size: 32px;
  --procms-avatar-radius: var(--procms-radius-full);

  /* Scrollbar */
  --procms-scrollbar-width: 6px;
  --procms-scrollbar-track: var(--procms-gray-100);
  --procms-scrollbar-thumb: var(--procms-gray-300);
  --procms-scrollbar-thumb-hover: var(--procms-gray-400);
}

/* === Dark Theme === */
[data-procms-theme="dark"] {
  --procms-widget-bg: var(--procms-gray-900);
  --procms-widget-surface: var(--procms-gray-800);
  --procms-widget-border: var(--procms-gray-700);
  --procms-widget-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --procms-widget-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);

  --procms-text-primary: var(--procms-gray-100);
  --procms-text-secondary: var(--procms-gray-300);
  --procms-text-muted: var(--procms-gray-400);

  --procms-bot-message-bg: var(--procms-gray-700);
  --procms-bot-message-text: var(--procms-text-primary);
  --procms-bot-message-border: var(--procms-gray-600);

  --procms-input-bg: var(--procms-gray-700);
  --procms-input-border: var(--procms-gray-600);

  --procms-scrollbar-track: var(--procms-gray-800);
  --procms-scrollbar-thumb: var(--procms-gray-600);
  --procms-scrollbar-thumb-hover: var(--procms-gray-500);
}

/* === Auto Theme (System Preference) === */
@media (prefers-color-scheme: dark) {
  [data-procms-theme="auto"] {
    --procms-widget-bg: var(--procms-gray-900);
    --procms-widget-surface: var(--procms-gray-800);
    --procms-widget-border: var(--procms-gray-700);
    --procms-widget-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --procms-widget-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);

    --procms-text-primary: var(--procms-gray-100);
    --procms-text-secondary: var(--procms-gray-300);
    --procms-text-muted: var(--procms-gray-400);

    --procms-bot-message-bg: var(--procms-gray-700);
    --procms-bot-message-text: var(--procms-text-primary);
    --procms-bot-message-border: var(--procms-gray-600);

    --procms-input-bg: var(--procms-gray-700);
    --procms-input-border: var(--procms-gray-600);

    --procms-scrollbar-track: var(--procms-gray-800);
    --procms-scrollbar-thumb: var(--procms-gray-600);
    --procms-scrollbar-thumb-hover: var(--procms-gray-500);
  }
}
