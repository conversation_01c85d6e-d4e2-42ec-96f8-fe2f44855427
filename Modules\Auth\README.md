# Auth Module

## Overview

The Auth Module is a comprehensive authentication component of Laravel ProCMS that handles user registration, login, password management, and OAuth integration. It uses OTP-based verification instead of traditional URL-based activation for enhanced security.

### Key Components

- **AuthController**: Public authentication API endpoints (register, login, verify, OAuth)
- **OAuthController**: OAuth account management endpoints (link, unlink, list accounts)
- **ProfileController**: User profile and password management endpoints
- **AuthService**: Core authentication business logic and OAuth handling
- **AuthRateLimitService**: Rate limiting and security protection
- **UserSocialAccount Model**: OAuth provider account linking and management
- **AuthFacade**: Convenient access to authentication functionality
- **Request Classes**: Comprehensive validation for all authentication requests

## Features

### ✅ Core Authentication (Tested & Working)
- **User Registration**: Account creation with automatic username generation
  - Uses `account_verification` type for OTP codes
  - Account starts with `status = Pending` until email verification
  - Automatic unique username generation from email
- **Email Verification**: 6-digit OTP codes via ActiveCode module
  - Activates account and sets `status = Active` after verification
  - Resend verification codes with rate limiting
- **Multi-field Login**: Single `login` field accepts email or username
- **JWT Authentication**: Secure token-based authentication with refresh capability
- **Password Reset**: OTP-based password reset with email delivery
- **Password Change**: OTP-confirmed password changes for authenticated users
- **Profile Management**: Update user profile information
- **Token Management**: Refresh and logout with proper token invalidation

### ✅ OAuth Integration (Complete System)
- **Multiple Provider Support**: Google, Facebook, Twitter, Apple, Telegram, Zalo
- **Complete Account Linking**: Link/unlink multiple OAuth providers per user
- **Safe Conflict Resolution**: Prevents account hijacking via proper provider ID tracking
- **Provider Management API**: Full CRUD operations for OAuth account management
- **Database Configuration**: OAuth settings stored in database, not environment files
- **Automatic Account Creation**: Creates new users from OAuth data when needed
- **Linking Detection**: Supports linking OAuth accounts to existing authenticated users
- **Security Checks**: Prevents unlinking the only authentication method
- **Provider Information Storage**: Stores provider ID, email, name, avatar, and metadata

### Security Features
- **OTP Verification**: All verification processes use ActiveCode module
- **Rate Limiting**: Built-in rate limiting for OTP requests
- **JWT Security**: Secure token-based authentication
- **Account Status Validation**: Checks user status before login
- **Email Verification Required**: Enforces email verification for login

## Installation

The Auth Module is included by default in Laravel ProCMS. To ensure it's properly set up:

1. **Run Migrations** (if any):
   ```bash
   php artisan module:migrate Auth
   ```

2. **Seed Settings**:
   ```bash
   php artisan db:seed --class="Modules\Auth\Database\Seeders\AuthDatabaseSeeder"
   ```

3. **Configure OAuth** (optional):
   Add OAuth credentials to your `.env` file:
   ```env
   FACEBOOK_CLIENT_ID=your_facebook_client_id
   FACEBOOK_CLIENT_SECRET=your_facebook_client_secret
   
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   
   TWITTER_CLIENT_ID=your_twitter_client_id
   TWITTER_CLIENT_SECRET=your_twitter_client_secret
   ```

## API Endpoints

### Public Endpoints (No Authentication Required)

#### Registration
```http
POST /api/v1/auth/register
```
**Request Body:**
```json
{
    "username": "johndoe",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "first_name": "John",
    "last_name": "Doe",
    "gender": "male",
    "phone": "+1234567890"
}
```

#### Email Verification
```http
POST /api/v1/auth/verify-email
```
**Request Body:**
```json
{
    "email": "<EMAIL>",
    "code": "123456"
}
```

#### Resend Verification
```http
POST /api/v1/auth/resend-verification
```
**Request Body:**
```json
{
    "email": "<EMAIL>"
}
```

#### Login
```http
POST /api/v1/auth/login
```
**Request Body:**
```json
{
    "login": "<EMAIL>",
    "password": "password123"
}
```

#### Password Reset
```http
POST /api/v1/auth/password/forgot
```
**Request Body:**
```json
{
    "email": "<EMAIL>"
}
```

```http
POST /api/v1/auth/password/reset
```
**Request Body:**
```json
{
    "email": "<EMAIL>",
    "code": "123456",
    "password": "newpassword123",
    "password_confirmation": "newpassword123"
}
```

#### OAuth Providers
```http
GET /api/v1/auth/oauth/providers
```
Get list of enabled OAuth providers.

#### OAuth Authentication
```http
GET /oauth/{provider}
GET /oauth/{provider}/callback
```
OAuth redirect and callback URLs (web routes).
Supported providers: `google`, `facebook`, `twitter`, `apple`, `telegram`, `zalo`

### Protected Endpoints (JWT Authentication Required)

#### User Profile
```http
GET /api/v1/auth/me
PUT /api/v1/auth/profile
```

#### Token Management
```http
POST /api/v1/auth/logout
POST /api/v1/auth/refresh
```

#### Password Change
```http
POST /api/v1/auth/password/change
POST /api/v1/auth/password/confirm-change
```

#### OAuth Account Management
```http
GET /api/v1/auth/oauth/accounts
GET /api/v1/auth/oauth/available
POST /api/v1/auth/oauth/link/{provider}
DELETE /api/v1/auth/oauth/unlink/{provider}
```

## Usage Examples

### Using the Auth Service

```php
use Modules\Auth\Services\AuthService;

// Resolve service from container
$authService = app(AuthService::class);

// Register user
$result = $authService->register([
    'username' => 'johndoe',
    'email' => '<EMAIL>',
    'password' => 'password123',
    'first_name' => 'John',
    'last_name' => 'Doe',
    'gender' => 'male',
]);

// Login user
$result = $authService->login([
    'login' => '<EMAIL>',
    'password' => 'password123',
]);

// Verify email
$result = $authService->verifyEmail('<EMAIL>', '123456');
```

### Using the Auth Facade

```php
use Modules\Auth\Facades\AuthFacade;

// Register user
$result = AuthFacade::register($userData);

// Login user
$result = AuthFacade::login($credentials);

// Send password reset
$result = AuthFacade::sendPasswordResetCode('<EMAIL>');

// OAuth redirect
return AuthFacade::redirectToProvider('facebook');
```

## Integration with Other Modules

### ActiveCode Module
The Auth Module heavily integrates with the ActiveCode module for OTP functionality:

- **Account Verification**: Uses `account_verification` type for email verification
- **Password Reset**: Uses `password_reset` type for forgot password flow
- **Password Change**: Uses `password_change` type for authenticated password changes

### User Module
- Uses the existing User model with JWT implementation
- Leverages User enums (UserStatus, UserGender)
- Integrates with user roles and permissions

### Notification Module
- Sends email notifications for verification codes
- Handles password reset notifications
- Manages OAuth login notifications

## Configuration

Auth settings are managed through the Setting module:

- `auth_registration_enabled`: Enable/disable user registration
- `auth_email_verification_required`: Require email verification for account activation
  - `true`: User account starts inactive, requires OTP verification to activate
  - `false`: User account starts active, OTP sent for email verification only
- `auth_oauth_facebook_enabled`: Enable Facebook OAuth
- `auth_oauth_google_enabled`: Enable Google OAuth
- `auth_oauth_twitter_enabled`: Enable Twitter OAuth
- `auth_password_min_length`: Minimum password length
- `auth_jwt_ttl`: JWT token lifetime

### Registration Flow Based on Settings

#### When `auth_email_verification_required` = `true`:
1. User registers → Account created with `status = Inactive` and `is_verified = false`
2. OTP sent to email for account activation
3. User enters OTP → Account becomes `status = Active` and `is_verified = true`
4. User can now login

#### When `auth_email_verification_required` = `false`:
1. User registers → Account created with `status = Active` and `is_verified = true`
2. OTP sent to email for email verification (optional)
3. User can login immediately without OTP verification
4. User can optionally verify email later with OTP

## OAuth System

### Database Structure

The OAuth system uses the `user_social_accounts` table to store provider linking information:

```sql
- id: Primary key
- user_id: Foreign key to users table
- provider: OAuth provider name (google, facebook, etc.)
- provider_id: User ID from OAuth provider
- provider_email: Email from OAuth provider
- provider_name: Display name from OAuth provider
- provider_nickname: Username/handle from OAuth provider
- provider_avatar: Avatar URL from OAuth provider
- access_token: OAuth access token (optional)
- refresh_token: OAuth refresh token (optional)
- token_expires_at: Token expiration (optional)
- provider_data: Additional JSON data from provider
- last_used_at: Last time this OAuth account was used
- created_at, updated_at: Timestamps
```

### OAuth Configuration

OAuth providers are configured in the database under the `oauth` settings group:

```php
// Enable Google OAuth
setting('oauth.google.enabled', true);
setting('oauth.google.client_id', 'your-google-client-id');
setting('oauth.google.client_secret', 'your-google-client-secret');
setting('oauth.google.redirect', url('/oauth/google/callback'));
```

### OAuth Flow Examples

#### 1. OAuth Login
```bash
# Get enabled providers
curl -X GET "http://localhost:8000/api/v1/auth/oauth/providers"

# Redirect to OAuth provider
# User visits: /oauth/google
# After callback, user receives JWT token
```

#### 2. Link OAuth to Existing Account
```bash
# User must be authenticated
curl -X POST "http://localhost:8000/api/v1/auth/oauth/link/google" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Returns redirect URL for OAuth linking
```

#### 3. Manage Linked Accounts
```bash
# Get linked accounts
curl -X GET "http://localhost:8000/api/v1/auth/oauth/accounts" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Unlink provider
curl -X DELETE "http://localhost:8000/api/v1/auth/oauth/unlink/facebook" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Testing

Run the Auth module tests:

```bash
php artisan test Modules/Auth/tests
```

The test suite covers:
- User registration and verification
- Login and logout functionality
- Password reset and change
- OAuth authentication
- Profile management
- JWT token handling

## Security Considerations

- All verification processes use OTP instead of URL links
- JWT tokens are properly invalidated on logout
- Rate limiting prevents OTP abuse
- OAuth accounts are automatically verified
- Password requirements are configurable
- Account status is validated before login

## Dependencies

### External Packages
- **tymon/jwt-auth**: JWT authentication and token management
- **laravel/socialite**: Base OAuth integration framework
- **socialiteproviders/facebook**: Facebook OAuth provider
- **socialiteproviders/google**: Google OAuth provider
- **socialiteproviders/twitter**: Twitter OAuth provider
- **socialiteproviders/apple**: Apple Sign In provider
- **socialiteproviders/telegram**: Telegram OAuth provider

### Internal Modules
- **ActiveCode Module**: OTP functionality for verification codes
- **User Module**: User management and authentication
- **Notification Module**: Email notifications for auth events
- **Setting Module**: Configuration management for auth settings
- **Core Module**: Base traits and utilities
