import{q as oe,r as Q,e as le,gY as ne,o as ae,S as re,ao as ie,y as ce,G as m,m as n,n as o,D as e,k as t,M as a,a8 as p,P as h,Q as _,N as c,I as f,J as x,E as w,A as k,a4 as Y,L as C,p as de,H as ue,gl as me,K as pe,gP as he}from"./index-ZVLuktk4.js";import _e from"./MarkdownRenderer-DWQX5-G2.js";const fe={class:"chat-area w-full h-full relative bg-white"},ge={class:"chat-header flex items-center justify-between"},xe={class:"flex items-center"},ye=["src"],ve={class:"text-lg font-semibold text-gray-900"},be={key:0,class:"text-sm text-gray-500"},we={class:"font-medium text-gray-700"},ke={key:1,class:"flex"},Ce={key:0,class:"text-sm text-gray-500"},je={class:"font-medium text-gray-700"},He={class:"p-4"},Me={class:"flex items-center mb-4"},Ve=["src"],Te={class:"overflow-hidden"},Ee={class:"font-bold text-lg text-gray-800 truncate"},Se={class:"text-sm text-gray-600 mb-4 text-justify"},ze=["innerHTML"],Be={class:"bg-emerald-200 p-3 rounded-lg border border-slate-200 text-xs space-y-2"},Ne={class:"flex justify-between"},Ue={class:"font-medium text-gray-700"},Le={class:"flex justify-between"},Re={class:"font-medium text-gray-700"},Ke={key:0,class:"mt-3"},Ae={class:"flex flex-wrap gap-2"},Pe={key:0,class:"flex flex-col h-full justify-center items-center"},$e={class:"text-5xl font-extrabold text-center"},qe={class:"bg-gradient-to-r from-blue-500 to-violet-500 text-transparent bg-clip-text"},Fe=["src"],Ie={key:1,class:"text-sm whitespace-pre-wrap"},De={key:0,class:"flex justify-start"},Ge={class:"flex items-end max-w-lg"},Je=["src"],Qe={key:1,class:"flex flex-col items-center justify-center pt-4"},Ye={class:"flex flex-wrap justify-center gap-2 max-w-lg"},Oe={class:"chat-footer !absolute bottom-0 left-0 right-0"},We={key:0,class:"mb-2 flex flex-wrap gap-2"},Xe={class:"composer-wrapper"},Ze={class:"flex justify-between items-center mt-2"},st=oe({__name:"ChatMain",props:{mainView:{},chatBot:{}},setup(O){const W=O,{chatMessagesContainer:j,newMessage:y,isTyping:H,attachedFiles:M,currentMessages:B,sendMessage:N,sendStarterPrompt:X,removeAttachment:Z,selectedConversation:i,agents:U,selectedAgent:d,currentConversationId:V}=W.chatBot,T=Q(400),E=Q(),ee=le(()=>ne().userInfo),te=u=>{u.shiftKey||N()},S=()=>{if(j.value){const u=document.querySelector(".chat-header"),l=document.querySelector(".chat-footer"),g=u?u.getBoundingClientRect().height:81,z=l?l.getBoundingClientRect().height:125,v=j.value.parentElement.clientHeight-g-z;T.value=v>0?v:400}};return ae(()=>{re(()=>{setTimeout(()=>{S()},1e3)}),window.addEventListener("resize",S)}),ie(()=>{window.removeEventListener("resize",S)}),ce(d,u=>{E.value=U.value.find(l=>l.uuid==u)}),(u,l)=>{var K,A,P,$,q,F;const g=m("el-tag"),z=m("el-popover"),L=m("el-option"),v=m("el-select"),R=m("el-button"),se=m("el-input");return o(),n("div",fe,[e("header",ge,[e("div",xe,[e("img",{src:((K=t(i).bot)==null?void 0:K.logoUrl)||"/bots/default.png",alt:"logo",class:"w-10 h-10 rounded-full mr-4 object-cover"},null,8,ye),e("div",null,[e("h2",ve,a(t(i).title||t(p)("New Conversation")),1),(A=t(i))!=null&&A.uuid?(o(),n("div",be,[h(a(t(p)("Currently using"))+": ",1),e("span",we,a((P=t(i).bot)==null?void 0:P.name),1)])):(o(),n("div",ke,[t(V)&&!t(V).includes("temp-")?(o(),n("div",Ce,[h(a(t(p)("Currently using"))+": ",1),e("span",je,a(E.value.name),1)])):(o(),_(v,{key:1,modelValue:t(d),"onUpdate:modelValue":l[0]||(l[0]=s=>Y(d)?d.value=s:null),size:"small",placeholder:t(p)("Select an agent"),style:{width:"360px"}},{default:c(()=>[(o(!0),n(f,null,x(t(U),s=>(o(),_(L,{key:s.uuid,label:s.name,value:s.uuid},{default:c(()=>[w(z,{placement:"right-start",width:350,trigger:"hover","show-after":500,"popper-class":"!p-0"},{reference:c(()=>[e("div",null,a(s.name),1)]),default:c(()=>{var r,b,I,D,G;return[e("div",He,[e("div",Me,[e("img",{src:s.logo,class:"w-12 h-12 rounded-full mr-4 object-cover flex-shrink-0",alt:"logo"},null,8,Ve),e("div",Te,[e("h4",Ee,a(s.name),1),w(g,{size:"small",type:s.botType==="personal"?"info":"success"},{default:c(()=>[h(a(s.botType==="personal"?"Cá nhân":"Team"),1)]),_:2},1032,["type"])])]),e("div",Se,[e("div",{innerHTML:s.description},null,8,ze)]),e("div",Be,[e("div",Ne,[l[2]||(l[2]=e("span",{class:"text-gray-500"},"Tác giả:",-1)),e("span",Ue,a(((r=s.owner)==null?void 0:r.name)||((b=s.owner)==null?void 0:b.fullName)),1)]),e("div",Le,[l[3]||(l[3]=e("span",{class:"text-gray-500"}," Phiên bản: ",-1)),e("span",Re,a(s.version||"1.0.0"),1)])]),(I=s.metadata)!=null&&I.tags&&((G=(D=s.metadata)==null?void 0:D.tags)==null?void 0:G.length)>0?(o(),n("div",Ke,[l[4]||(l[4]=e("h5",{class:"text-xs font-semibold text-gray-500 mb-2"}," Thẻ: ",-1)),e("div",Ae,[(o(!0),n(f,null,x(s.metadata.tags,J=>(o(),_(g,{key:J,size:"small",effect:"plain"},{default:c(()=>[h(a(J),1)]),_:2},1024))),128))])])):k("",!0)])]}),_:2},1024)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"]))]))])])]),e("div",{ref_key:"chatMessagesContainer",ref:j,class:"chat-messages p-6 overflow-y-auto space-y-6",style:ue({height:T.value+"px",maxHeight:T.value+"px"})},[t(V)?(o(),n(f,{key:1},[(o(!0),n(f,null,x(t(B),s=>{var r;return o(),n("div",{key:s.id,class:C(["message flex",s.role==="user"?"justify-end":"justify-start"])},[e("div",{class:C(["flex items-end max-w-lg",{"flex-row-reverse":s.role==="user"}])},[e("img",{src:s.role==="user"?"https://placehold.co/40x40/E2E8F0/4A5568?text=U":(r=t(i).bot)==null?void 0:r.logoUrl,alt:"logo",class:C(["w-8 h-8 rounded-full object-cover",s.role==="user"?"ml-3":"mr-3"])},null,10,Fe),e("div",{class:C(["p-3 rounded-2xl shadow-sm",{"bg-blue-500 text-white rounded-br-none":s.role==="user"}])},[s.role==="assistant"?(o(),_(_e,{key:0,class:"bg-white text-gray-800 rounded-bl-none",content:s.content},null,8,["content"])):(o(),n("p",Ie,a(s.content),1))],2)],2)],2)}),128)),t(H)?(o(),n("div",De,[e("div",Ge,[e("img",{src:($=t(i).bot)==null?void 0:$.logoUrl,class:"w-8 h-8 rounded-full object-cover mr-3",alt:"logo"},null,8,Je),l[5]||(l[5]=de('<div class="p-3 rounded-2xl shadow-sm bg-white text-gray-800 rounded-bl-none"><div class="flex items-center space-x-1 typing-indicator"><span class="h-2 w-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay:-0.3s;"></span><span class="h-2 w-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay:-0.15s;"></span><span class="h-2 w-2 bg-gray-400 rounded-full animate-bounce"></span></div></div>',1))])])):k("",!0),t(d)&&t(B).length<=1?(o(),n("div",Qe,[e("div",Ye,[(o(!0),n(f,null,x(E.value.starterMessages,(s,r)=>(o(),_(R,{key:`prompt-${r}`,round:"",onClick:b=>t(X)(s)},{default:c(()=>[h(a(s),1)]),_:2},1032,["onClick"]))),128))])])):k("",!0)],64)):(o(),n("div",Pe,[e("div",$e,[e("span",qe,a(t(p)("Hello"))+" "+a(ee.value.nickname)+"! ",1)])]))],4),e("footer",Oe,[t(M).length>0?(o(),n("div",We,[(o(!0),n(f,null,x(t(M),(s,r)=>(o(),_(g,{key:s.uid,closable:"",onClose:b=>t(Z)(r)},{default:c(()=>[h(a(s.name),1)]),_:2},1032,["onClose"]))),128))])):k("",!0),e("div",Xe,[w(se,{modelValue:t(y),"onUpdate:modelValue":l[1]||(l[1]=s=>Y(y)?y.value=s:null),type:"textarea",autosize:{minRows:1,maxRows:6},placeholder:t(p)("Enter message..."),class:"composer-textarea bg-transparent",disabled:t(H)||!t(d)&&!((q=t(i))!=null&&q.uuid),onKeyup:me(pe(te,["prevent"]),["enter"])},null,8,["modelValue","placeholder","disabled","onKeyup"]),e("div",Ze,[l[6]||(l[6]=e("div",{class:"flex items-center gap-2"},null,-1)),w(R,{type:"primary",icon:t(he),circle:"",disabled:!t(y).trim()&&t(M).length===0||t(H)||!t(d)&&!((F=t(i))!=null&&F.uuid),onClick:t(N)},null,8,["icon","disabled","onClick"])])])])])}}});export{st as default};
