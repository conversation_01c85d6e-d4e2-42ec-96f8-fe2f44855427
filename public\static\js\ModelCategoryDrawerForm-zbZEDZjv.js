var h=(b,g,i)=>new Promise((a,t)=>{var p=s=>{try{d(i.next(s))}catch(u){t(u)}},n=s=>{try{d(i.throw(s))}catch(u){t(u)}},d=s=>s.done?a(s.value):Promise.resolve(s.value).then(p,n);d((i=i.apply(b,g)).next())});/* empty css                         */import{q as T,r as C,o as I,a8 as e,e as r,ai as _,h7 as x,G as F,Q as S,n as q,N as v,D as y,E as w,P as k,M as P,k as f}from"./index-ZVLuktk4.js";import{u as A}from"./hooks-CuzZ-_om.js";import{P as M}from"./index-CQnBjWL2.js";import"./_plugin-vue_export-helper-QGN-qG8u.js";const V={class:"custom-group-header"},B={class:"font-semibold"},D={class:"custom-footer"},U=T({__name:"ModelCategoryDrawerForm",props:{visible:{type:Boolean},values:{},isEdit:{type:Boolean}},emits:["update:visible","update:values","submit"],setup(b,{expose:g,emit:i}){const a=b,t=i,p=C(!1),n=C();I(()=>{});const d=[{label:r(()=>e("Category key")),prop:"key",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input category key"),trigger:["blur"]},{pattern:/^[a-z0-9-_]+$/,message:e("Key can only contain letters, numbers, dots, underscores and hyphens"),trigger:"blur"}],fieldProps:{placeholder:e("e.g., text-generation, image-analysis")},colProps:{span:17}},{label:r(()=>e("Status")),prop:"status",valueType:"select",fieldProps:{placeholder:"",clearable:!1},options:[{label:e("Active"),value:"active"},{label:e("Inactive"),value:"inactive"}],colProps:{span:7}},{label:r(()=>e("Category name")),prop:"name",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input category name"),trigger:["blur","change"]},{min:2,max:100,message:e("Length should be between 2 and 100 characters"),trigger:["blur","change"]}],fieldProps:{placeholder:""},colProps:{span:24}},{label:r(()=>e("Category type")),prop:"type",valueType:"select",fieldProps:{placeholder:"",clearable:!1},options:[{label:e("Model AI"),value:"ModelAI"},{label:e("Tools"),value:"Tools"}],colProps:{span:6}},{label:r(()=>e("Category Icon")),prop:"icon",valueType:"",renderField:()=>{var o;return _(x,{modelValue:(o=a==null?void 0:a.values)==null?void 0:o.icon,"onUpdate:modelValue":l=>{a!=null&&a.values&&(a.values.icon=l)},class:"w-full"})},fieldProps:{class:"w-full"},colProps:{span:6}},{label:r(()=>e("Color")),prop:"color",valueType:"color-picker",fieldProps:{placeholder:"",clearable:!0},colProps:{span:6}},{label:r(()=>e("Sort Order")),prop:"sortOrder",valueType:"input-number",fieldProps:{placeholder:"",min:0,max:9999,step:1},colProps:{span:6}},{label:r(()=>e("Description")),prop:"description",valueType:"textarea",fieldProps:{placeholder:"",showWordLimit:!0,autosize:{minRows:3,maxRows:6}},colProps:{span:24}}],s=o=>h(null,null,function*(){var c;if(!(!((c=n.value)!=null&&c.formInstance)||!(yield n.value.formInstance.validate())))try{p.value=!0,t("submit",o)}finally{setTimeout(()=>{p.value=!1},3e3)}}),u=()=>{var o;(o=n.value)!=null&&o.formInstance&&n.value.formInstance.resetFields()};return g({resetForm:u}),(o,l)=>{const c=F("el-button");return q(),S(f(M),{ref_key:"formRef",ref:n,visible:o.visible,"model-value":o.values,size:"50%",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0,destroyOnClose:!0,form:{columns:d,footerAlign:"center",hasFooter:!1,labelSuffix:"",labelPosition:"top",labelWidth:"auto",labelAlign:"left",requireAsteriskPosition:"right",rowProps:{gutter:10}},"onUpdate:visible":l[2]||(l[2]=m=>t("update:visible",m)),"onUpdate:modelValue":l[3]||(l[3]=m=>t("update:values",m)),onClose:u},{"drawer-header":v(()=>[y("div",V,[y("span",B,P(f(e)("Information Form")),1)])]),"drawer-footer":v(()=>[y("div",D,[w(c,{plain:"",onClick:l[0]||(l[0]=m=>t("update:visible",!1))},{default:v(()=>[k(P(f(e)("Cancel")),1)]),_:1}),w(c,{plain:"",type:"primary",loading:p.value,icon:f(A)("ri:save-2-line"),onClick:l[1]||(l[1]=m=>s(o.values))},{default:v(()=>[k(P(f(e)("Save")),1)]),_:1},8,["loading","icon"])])]),_:1},8,["visible","model-value","form"])}}});export{U as default};
