const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/ProviderDrawerForm-UOg2vzBk.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/js/hooks-CuzZ-_om.js","static/js/index-CQnBjWL2.js","static/js/_plugin-vue_export-helper-QGN-qG8u.js","static/css/plus-drawer-form-DaizY_En.css","static/js/ProviderFilterForm-Dj3utwjg.js"])))=>i.map(i=>d[i]);
var pe=Object.defineProperty,fe=Object.defineProperties;var me=Object.getOwnPropertyDescriptors;var re=Object.getOwnPropertySymbols;var ye=Object.prototype.hasOwnProperty,ve=Object.prototype.propertyIsEnumerable;var ae=(o,i,n)=>i in o?pe(o,i,{enumerable:!0,configurable:!0,writable:!0,value:n}):o[i]=n,Y=(o,i)=>{for(var n in i||(i={}))ye.call(i,n)&&ae(o,n,i[n]);if(re)for(var n of re(i))ve.call(i,n)&&ae(o,n,i[n]);return o},ne=(o,i)=>fe(o,me(i));var f=(o,i,n)=>new Promise((x,k)=>{var P=w=>{try{_(n.next(w))}catch(u){k(u)}},g=w=>{try{_(n.throw(w))}catch(u){k(u)}},_=w=>w.done?x(w.value):Promise.resolve(w.value).then(P,g);_((n=n.apply(o,i)).next())});import{r as b,g as ge,aA as oe,az as he,ax as d,a8 as t,ay as S,aD as be,ai as j,aC as C,q as we,o as xe,S as Ce,m as z,n as T,D as H,E as l,N as c,k as e,aI as Pe,G as I,P as se,M as E,I as V,aJ as h,L as $,Q as _e,A as ke,a5 as Re,a4 as N,aK as le,aL as Be,aM as ie,_ as De}from"./index-ZVLuktk4.js";import{g as Se,a as Te,b as Ee,r as Ae,c as Fe,d as ze,e as He,f as Ie,u as Ue,h as Ve}from"./auth-api-BCod4ziT.js";import{P as $e}from"./index-DMMM8XNs.js";import"./sortable.esm-7jaD_3Ar.js";import"./epTheme-DzIV6EF-.js";function Ne(){const o=b(!1),i=b({isTrashed:"no"}),n=ge({total:0,pageSize:20,currentPage:1,pageSizes:[10,20,50,100,200],background:!0,layout:"total, sizes, prev, pager, next, jumper"}),x=b([]),k=b([]),P=b({sortBy:"createdAt",sortOrder:"asc"}),g=b([]),_=b(),w=b(!1),u=b(!1),A=b({status:"active"}),m=()=>f(null,null,function*(){try{o.value=!0;const a=yield Te(he(ne(Y({},i.value),{order:`${P.value.sortBy}:${P.value.sortOrder}`,page:n.currentPage,limit:n.pageSize})));k.value=oe(a.data),n.total=a.total}catch(a){console.error("Error fetching providers:",a),d(t("Failed to fetch providers"),{type:"error"})}finally{o.value=!1}}),F=()=>f(null,null,function*(){try{const a=yield Se();g.value=oe(a.data)}catch(a){console.error("Error fetching providers dropdown:",a)}}),M=a=>{x.value=a},O=()=>f(null,null,function*(){yield m()}),W=a=>f(null,null,function*(){n.pageSize=a,n.currentPage=1,yield m()}),L=a=>f(null,null,function*(){P.value={sortBy:a.prop,sortOrder:a.order=="ascending"?"asc":"desc"},yield m()}),B=a=>f(null,null,function*(){var r,s;try{o.value=!0;const p=yield Ve(a);return p.success?(d(p.message||t("Create successful"),{type:"success"}),yield m(),!0):(d(p.message||t("Create failed"),{type:"error"}),!1)}catch(p){return d(((s=(r=p.response)==null?void 0:r.data)==null?void 0:s.message)||(p==null?void 0:p.message)||t("Create failed"),{type:"error"}),!1}finally{o.value=!1}}),R=(a,r)=>f(null,null,function*(){var s,p;try{o.value=!0;const y=yield Ue(a,r);return y.success?(d(y.message||t("Update successful"),{type:"success"}),yield m(),!0):(d(y.message||t("Update failed"),{type:"error"}),!1)}catch(y){return d(((p=(s=y.response)==null?void 0:s.data)==null?void 0:p.message)||(y==null?void 0:y.message)||t("Update failed"),{type:"error"}),!1}finally{o.value=!1}});return{loading:o,filterRef:i,pagination:n,records:k,multipleSelection:x,sort:P,providersDropdown:g,handleBulkDelete:a=>f(null,null,function*(){const r=a||x.value.map(s=>s.id);if(r.length===0){d(t("Please select items to delete"),{type:"warning"});return}try{yield S.confirm(t("Are you sure you want to delete selected items?"),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Ie({ids:r}),d(t("Deleted successfully"),{type:"success"}),m()}catch(s){s!=="cancel"&&(console.error("Error bulk deleting providers:",s),d(t("Delete failed"),{type:"error"}))}}),handleDelete:a=>f(null,null,function*(){try{yield S.confirm(t("Are you sure you want to delete this item?"),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield He(a.id),d(t("Deleted successfully"),{type:"success"}),yield m()}catch(r){r!=="cancel"&&(console.error("Error deleting provider:",r),d(t("Delete failed"),{type:"error"}))}}),handlePermanentDelete:a=>f(null,null,function*(){try{yield S.confirm(t("Are you sure you want to permanently delete this item? This action cannot be undone."),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield ze(a.id),d(t("Permanently deleted successfully"),{type:"success"}),m()}catch(r){r!=="cancel"&&(console.error("Error permanently deleting provider:",r),d(t("Permanent delete failed"),{type:"error"}))}}),handleBulkPermanentDelete:a=>f(null,null,function*(){const r=a||x.value.map(s=>s.id);if(r.length===0){d(t("Please select items to delete"),{type:"warning"});return}try{yield S.confirm(t("Are you sure you want to permanently delete selected items? This action cannot be undone."),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Fe({ids:r}),d(t("Permanently deleted successfully"),{type:"success"}),m()}catch(s){s!=="cancel"&&(console.error("Error bulk permanently deleting providers:",s),d(t("Permanent delete failed"),{type:"error"}))}}),handleRestore:a=>f(null,null,function*(){try{yield S.confirm(t("Are you sure you want to restore this item?"),t("Confirm"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"info"}),yield Ae(a.id),d(t("Restored successfully"),{type:"success"}),m()}catch(r){r!=="cancel"&&(console.error("Error restoring provider:",r),d(t("Restore failed"),{type:"error"}))}}),handleBulkRestore:a=>f(null,null,function*(){const r=a||x.value.map(s=>s.id);if(r.length===0){d(t("Please select items to restore"),{type:"warning"});return}try{yield S.confirm(t("Are you sure you want to restore selected items?"),t("Confirm"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"info"}),yield Ee({ids:r}),d(t("Restored successfully"),{type:"success"}),yield m()}catch(s){s!=="cancel"&&(console.error("Error bulk restoring providers:",s),d(t("Restore failed"),{type:"error"}))}}),fnGetProviders:m,fnGetProvidersDropdown:F,fnHandlePageChange:O,fnHandleSelectionChange:M,fnHandleSortChange:L,fnHandleSizeChange:W,filterVisible:w,drawerVisible:u,drawerValues:A,providerFormRef:_,handleSubmit:a=>f(null,null,function*(){var s;let r=!1;a.id!=null?r=yield R(Number(a.id),a):(r=yield B(a),r&&(A.value={status:"active"},(s=_.value)==null||s.resetForm()))}),handleFilter:a=>f(null,null,function*(){i.value=a,yield m()}),handleEdit:a=>{A.value=Y({},a),u.value=!0}}}const Me=[{type:"selection",width:"30px",sortable:!1},{type:"index",width:40,headerRenderer:()=>t("No.")},{prop:"key",align:"left",sortable:!0,width:140,headerRenderer:()=>t("Provider Key")},{prop:"name",align:"left",sortable:!0,minWidth:180,headerRenderer:()=>t("Provider Name")},{prop:"baseUrl",align:"left",minWidth:200,headerRenderer:()=>t("Base URL"),cellRenderer:({row:o})=>j("div",{title:o.baseUrl},o.baseUrl||"-")},{prop:"status",sortable:!1,align:"left",width:160,headerRenderer:()=>t("Status"),cellRenderer:({row:o})=>{const i={active:{type:"success",text:t("Active"),class:"bg-green-100 text-green-800",icon:"ri:checkbox-circle-fill",iconClass:"text-green-600"},inactive:{type:"danger",text:t("Inactive"),class:"bg-red-100 text-red-800",icon:"ri:close-circle-fill",iconClass:"text-red-600"}},n=i[o.status]||i.active;return j("span",{class:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${n.class}`},[j(C,{icon:n.icon,class:`w-3 h-3 mr-1.5 ${n.iconClass}`}),n.text])}},{prop:"createdAt",width:160,sortable:!0,headerRenderer:()=>t("Created at"),formatter:({createdAt:o})=>o?be(o).format("YYYY-MM-DD HH:mm"):"-"},{label:"",fixed:"right",width:140,slot:"operation",sortable:!1}],Oe={class:"main"},We={class:"ml-2"},Le={class:"ml-2"},Ge={class:"ml-2"},Ke={class:"ml-2"},Ye=we({__name:"index",setup(o){const i=le(()=>ie(()=>import("./ProviderDrawerForm-UOg2vzBk.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),n=le(()=>ie(()=>import("./ProviderFilterForm-Dj3utwjg.js"),__vite__mapDeps([7,1,2,4,5,6]))),x=b(),k=b(),{loading:P,filterRef:g,pagination:_,records:w,multipleSelection:u,handleBulkDelete:A,handleDelete:m,fnGetProviders:F,fnHandlePageChange:M,fnHandleSelectionChange:O,fnHandleSortChange:W,fnHandleSizeChange:L,filterVisible:B,drawerVisible:R,drawerValues:D,providerFormRef:G,handleSubmit:Q,handleFilter:q,handleBulkPermanentDelete:J,handleBulkRestore:X,handlePermanentDelete:Z,handleRestore:ee}=Ne(),te=a=>{D.value=Be(a,!0),R.value=!0};return xe(()=>{Ce(()=>{F()})}),(a,r)=>{const s=I("el-button"),p=I("el-tooltip"),y=I("el-dropdown-item"),de=I("el-dropdown-menu"),ce=I("el-dropdown");return T(),z("div",Oe,[H("div",{ref_key:"contentRef",ref:k,class:$(["flex",e(Re)()?"flex-wrap":""])},[l(e($e),{class:"!w-full",style:{transition:"width 220ms cubic-bezier(0.4, 0, 0.2, 1)"},title:e(t)("Provider Management"),columns:e(Me),onRefresh:e(F),onFilter:r[4]||(r[4]=v=>B.value=!0)},{buttons:c(()=>[l(p,{content:e(t)("Create new"),placement:"top"},{default:c(()=>[l(s,{type:"text",class:"font-bold text-[16px]",disabled:!e(h)("model-provider.create"),onClick:r[0]||(r[0]=()=>{R.value=!0})},{default:c(()=>[l(e(C),{icon:e(h)("model-provider.create")?"flat-color-icons:plus":"icons8:plus",width:"18px"},null,8,["icon"])]),_:1},8,["disabled"])]),_:1},8,["content"]),e(g).isTrashed==="yes"?(T(),z(V,{key:0},[l(p,{content:e(t)("Restore"),placement:"top"},{default:c(()=>[l(s,{type:"text",class:"font-bold text-[16px]",disabled:e(u).length===0||e(u).length>0&&!e(h)("model-provider.delete"),onClick:r[1]||(r[1]=()=>e(X)())},{default:c(()=>[l(e(C),{icon:"tabler:restore",width:"18px",class:$({"text-amber-800":e(u).length>0&&e(h)("model-provider.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"]),l(p,{content:e(t)("Bulk Destroy"),placement:"top"},{default:c(()=>[l(s,{type:"text",class:"font-bold text-[16px]",disabled:e(u).length===0||e(u).length>0&&!e(h)("model-provider.destroy"),onClick:r[2]||(r[2]=()=>e(J)())},{default:c(()=>[l(e(C),{icon:"tabler:trash-x-filled",width:"18px",class:$({"text-red-800":e(u).length>0&&e(h)("model-provider.destroy")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])],64)):(T(),z(V,{key:1},[e(g).isTrashed==="no"?(T(),_e(p,{key:0,content:e(t)("Bulk Delete"),placement:"top"},{default:c(()=>[l(s,{type:"text",class:"font-bold text-[16px]",disabled:e(u).length==0||e(u).length>0&&!e(h)("model-provider.delete"),onClick:r[3]||(r[3]=()=>e(A)())},{default:c(()=>[l(e(C),{icon:"tabler:trash",width:"18px",class:$({"text-red-800":e(u).length>0&&e(h)("model-provider.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])):ke("",!0)],64))]),default:c(({size:v,dynamicColumns:ue})=>[l(e(Pe),{ref_key:"tableRef",ref:x,"align-whole":"center","table-layout":"auto",loading:e(P),size:v,adaptive:"",border:"",adaptiveConfig:{offsetBottom:108},data:e(w),columns:ue,pagination:e(_),"header-cell-style":{background:"var(--el-fill-color-light)",color:"var(--el-text-color-primary)"},onSortChange:e(W),onPageSizeChange:e(L),onPageCurrentChange:e(M),onSelectionChange:e(O)},{operation:c(({row:U})=>[l(ce,{"split-button":"",trigger:"click",size:"small"},{dropdown:c(()=>[l(de,{class:"min-w-[130px]"},{default:c(()=>[l(y,{disabled:""},{default:c(()=>[se(E(e(t)("Action")),1)]),_:1}),e(g).isTrashed=="no"?(T(),z(V,{key:0},[l(y,{disabled:!e(h)("model-provider.edit"),onClick:K=>te(U)},{default:c(()=>[l(e(C),{icon:"material-symbols:edit",class:"text-blue-800"}),H("span",We,E(e(t)("Edit")),1)]),_:2},1032,["disabled","onClick"]),l(y,{disabled:!e(h)("model-provider.delete"),onClick:K=>e(m)(U)},{default:c(()=>[l(e(C),{icon:"tabler:trash",class:"text-red-800"}),H("span",Le,E(e(t)("Delete")),1)]),_:2},1032,["disabled","onClick"])],64)):(T(),z(V,{key:1},[l(y,{disabled:!e(h)("model-provider.delete"),onClick:K=>e(ee)(U)},{default:c(()=>[l(e(C),{icon:"tabler:restore",class:"text-amber-800"}),H("span",Ge,E(e(t)("Restore")),1)]),_:2},1032,["disabled","onClick"]),l(y,{disabled:!e(h)("model-provider.destroy"),onClick:K=>e(Z)(U)},{default:c(()=>[l(e(C),{icon:"tabler:trash-x",class:"text-red-800"}),H("span",Ke,E(e(t)("Destroy")),1)]),_:2},1032,["disabled","onClick"])],64))]),_:2},1024)]),default:c(()=>[se(E(e(t)("Action"))+" ",1)]),_:2},1024)]),_:2},1032,["loading","size","data","columns","pagination","onSortChange","onPageSizeChange","onPageCurrentChange","onSelectionChange"])]),_:1},8,["title","columns","onRefresh"])],2),l(e(i),{ref_key:"providerFormRef",ref:G,visible:e(R),"onUpdate:visible":r[5]||(r[5]=v=>N(R)?R.value=v:null),values:e(D),"onUpdate:values":r[6]||(r[6]=v=>N(D)?D.value=v:null),onSubmit:e(Q),onClose:r[7]||(r[7]=()=>{var v;(v=e(G))==null||v.resetForm(),D.value={status:"active"}})},null,8,["visible","values","onSubmit"]),l(e(n),{ref:"filterFormRef",visible:e(B),"onUpdate:visible":r[8]||(r[8]=v=>N(B)?B.value=v:null),values:e(g),"onUpdate:values":r[9]||(r[9]=v=>N(g)?g.value=v:null),onSubmit:e(q),onReset:r[10]||(r[10]=()=>{g.value={isTrashed:"no"},e(F)()})},null,8,["visible","values","onSubmit"])])}}}),et=De(Ye,[["__scopeId","data-v-9cf8bc24"]]);export{et as default};
