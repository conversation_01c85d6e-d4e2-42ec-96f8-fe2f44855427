# Sidebar Module Testing Guide

## 🧪 Comprehensive Testing Strategy

This guide provides detailed testing approaches for the upgraded Sidebar module to ensure all functionality works correctly and follows the established patterns.

## 📋 Pre-Testing Setup

### **1. Environment Preparation**
```bash
# Run migrations
php artisan migrate --path=Modules/Sidebar/database/migrations

# Seed permissions and sample data
php artisan db:seed --class=Modules\\Sidebar\\Database\\Seeders\\SidebarDatabaseSeeder

# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

### **2. Database Verification**
```sql
-- Verify table structure
DESCRIBE sidebars;

-- Check if soft deletes column exists
SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'sidebars' AND COLUMN_NAME = 'deleted_at';

-- Verify sample data
SELECT id, title, name, parent_id, deleted_at FROM sidebars;

-- Check permissions
SELECT name, display_name FROM permissions WHERE name LIKE 'sidebar.%';
```

## 🔧 Unit Testing

### **SidebarService Tests**

Create `tests/Unit/SidebarServiceTest.php`:

```php
<?php

namespace Tests\Unit;

use Tests\TestCase;
use Modules\Sidebar\Services\SidebarService;
use Modules\Sidebar\Models\Sidebar;
use Illuminate\Support\Facades\Cache;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SidebarServiceTest extends TestCase
{
    use RefreshDatabase;

    protected SidebarService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(SidebarService::class);
    }

    /** @test */
    public function it_can_get_sidebars_with_caching()
    {
        // Create test data
        Sidebar::factory()->create(['title' => 'Test Sidebar']);
        
        // First call should hit database
        $result1 = $this->service->getSidebars();
        
        // Second call should hit cache
        $result2 = $this->service->getSidebars();
        
        $this->assertEquals($result1, $result2);
        $this->assertTrue(Cache::tags(['sidebars'])->has('proCMS.sidebars.tree'));
    }

    /** @test */
    public function it_can_find_sidebar_by_id()
    {
        $sidebar = Sidebar::factory()->create();
        
        $found = $this->service->findById($sidebar->id);
        
        $this->assertEquals($sidebar->id, $found->id);
    }

    /** @test */
    public function it_can_clear_cache()
    {
        // Create cache
        $this->service->getSidebars();
        $this->assertTrue(Cache::tags(['sidebars'])->has('proCMS.sidebars.tree'));
        
        // Clear cache
        $this->service->clearCache();
        $this->assertFalse(Cache::tags(['sidebars'])->has('proCMS.sidebars.tree'));
    }
}
```

### **SidebarObserver Tests**

Create `tests/Unit/SidebarObserverTest.php`:

```php
<?php

namespace Tests\Unit;

use Tests\TestCase;
use Modules\Sidebar\Models\Sidebar;
use Illuminate\Support\Facades\Cache;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SidebarObserverTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_clears_cache_when_sidebar_is_created()
    {
        // Set up cache
        Cache::tags(['sidebars'])->put('test-key', 'test-value', 60);
        
        // Create sidebar (should trigger observer)
        Sidebar::factory()->create();
        
        // Cache should be cleared
        $this->assertFalse(Cache::tags(['sidebars'])->has('test-key'));
    }

    /** @test */
    public function it_clears_cache_when_sidebar_is_updated()
    {
        $sidebar = Sidebar::factory()->create();
        
        // Set up cache
        Cache::tags(['sidebars'])->put('test-key', 'test-value', 60);
        
        // Update sidebar
        $sidebar->update(['title' => 'Updated Title']);
        
        // Cache should be cleared
        $this->assertFalse(Cache::tags(['sidebars'])->has('test-key'));
    }
}
```

## 🌐 Feature Testing

### **API Endpoint Tests**

Create `tests/Feature/SidebarApiTest.php`:

```php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use Modules\Sidebar\Models\Sidebar;
use Modules\User\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SidebarApiTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_get_public_sidebars()
    {
        Sidebar::factory()->count(3)->create();
        
        $response = $this->getJson('/api/v1/sidebars');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data'
                ]);
    }

    /** @test */
    public function it_can_get_sidebar_tree()
    {
        $parent = Sidebar::factory()->create(['parent_id' => 0]);
        Sidebar::factory()->create(['parent_id' => $parent->id]);
        
        $response = $this->getJson('/api/v1/sidebars/tree');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function authenticated_user_can_create_sidebar()
    {
        $user = User::factory()->create();
        $user->givePermissionTo('sidebar.create');
        
        $data = [
            'menu_type' => 0,
            'parent_id' => 0,
            'title' => 'Test Sidebar',
            'name' => 'test-sidebar',
            'path' => '/test',
            'rank' => 1,
            'show_link' => true,
            'show_parent' => true
        ];
        
        $response = $this->actingAs($user, 'api')
                        ->postJson('/api/v1/auth/sidebars', $data);
        
        $response->assertStatus(201);
        $this->assertDatabaseHas('sidebars', ['name' => 'test-sidebar']);
    }
}
```

## 🔍 Manual Testing Checklist

### **Public API Endpoints**
- [ ] **GET /api/v1/sidebars**
  - Returns sidebar tree structure
  - Response includes success message
  - Data is properly formatted

- [ ] **GET /api/v1/sidebars/tree**
  - Returns hierarchical tree structure
  - Parent-child relationships are correct

- [ ] **GET /api/v1/sidebars/{id}**
  - Returns single sidebar details
  - Returns 404 for non-existent sidebar

### **Authenticated API Endpoints**
- [ ] **GET /api/v1/auth/sidebars**
  - Requires authentication
  - Supports pagination (limit parameter)
  - Supports filtering (search, menu_type, etc.)
  - Returns paginated response

- [ ] **POST /api/v1/auth/sidebars**
  - Requires `sidebar.create` permission
  - Validates required fields
  - Prevents duplicate names/paths
  - Prevents self-referencing parent

- [ ] **PUT /api/v1/auth/sidebars/{id}**
  - Requires `sidebar.edit` permission
  - Updates sidebar successfully
  - Validates unique constraints

- [ ] **DELETE /api/v1/auth/sidebars/{id}/delete**
  - Requires `sidebar.delete` permission
  - Soft deletes sidebar
  - Sidebar still exists in database with deleted_at

- [ ] **PATCH /api/v1/auth/sidebars/{id}/restore**
  - Requires `sidebar.delete` permission
  - Restores soft-deleted sidebar
  - Clears deleted_at timestamp

- [ ] **DELETE /api/v1/auth/sidebars/{id}**
  - Requires `sidebar.destroy` permission
  - Permanently deletes sidebar
  - Sidebar removed from database

### **Bulk Operations**
- [ ] **DELETE /api/v1/auth/sidebars/bulk/delete**
  - Soft deletes multiple sidebars
  - Returns count of deleted items

- [ ] **PATCH /api/v1/auth/sidebars/bulk/restore**
  - Restores multiple soft-deleted sidebars
  - Returns count of restored items

- [ ] **DELETE /api/v1/auth/sidebars/bulk/destroy**
  - Permanently deletes multiple sidebars
  - Returns count of destroyed items

### **Caching Verification**
- [ ] **Cache Creation**
  - First API call creates cache entries
  - Subsequent calls use cached data

- [ ] **Cache Invalidation**
  - Creating sidebar clears cache
  - Updating sidebar clears cache
  - Deleting sidebar clears cache

- [ ] **Cache Keys**
  - `proCMS.sidebars.tree` exists after tree request
  - `proCMS.sidebars.active.list` exists after active request
  - `proCMS.sidebars.{id}` exists after single sidebar request

### **Permission Testing**
- [ ] **View Permission** (`sidebar.view`)
  - Can access index and show endpoints
  - Cannot access without permission

- [ ] **Create Permission** (`sidebar.create`)
  - Can create new sidebars
  - Cannot create without permission

- [ ] **Edit Permission** (`sidebar.edit`)
  - Can update existing sidebars
  - Cannot update without permission

- [ ] **Delete Permission** (`sidebar.delete`)
  - Can soft delete and restore sidebars
  - Cannot delete without permission

- [ ] **Destroy Permission** (`sidebar.destroy`)
  - Can permanently delete sidebars
  - Cannot destroy without permission

## 🚀 Performance Testing

### **Load Testing**
```bash
# Test API performance with multiple requests
ab -n 1000 -c 10 http://your-app.test/api/v1/sidebars

# Test with authentication
ab -n 1000 -c 10 -H "Authorization: Bearer YOUR_TOKEN" http://your-app.test/api/v1/auth/sidebars
```

### **Cache Performance**
- [ ] First request takes longer (cache miss)
- [ ] Subsequent requests are faster (cache hit)
- [ ] Cache invalidation works correctly
- [ ] Memory usage is reasonable

## 📊 Expected Results

### **Response Times**
- **Cached requests**: < 50ms
- **Database requests**: < 200ms
- **Bulk operations**: < 500ms

### **Cache Hit Ratio**
- Should achieve > 80% cache hit ratio in normal usage
- Cache should persist for 5 minutes
- Cache should invalidate immediately on data changes

The testing is complete when all checklist items pass and performance metrics are within expected ranges.
