<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sidebars', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('menu_type')->default(0);
            $table->bigInteger('parent_id')->default(0);
            $table->string('title');
            $table->string('name');
            $table->string('path');
            $table->string('component')->nullable();
            $table->bigInteger('rank')->nullable();
            $table->string('redirect')->nullable();
            $table->string('icon')->nullable();
            $table->string('extra_icon')->nullable();
            $table->string('enter_transition')->nullable();
            $table->string('leave_transition')->nullable();
            $table->string('active_path')->nullable();
            $table->text('roles')->nullable();
            $table->text('auths')->nullable();
            $table->string('frame_src')->nullable();
            $table->boolean('frame_loading')->default(true);
            $table->boolean('keep_alive')->default(false);
            $table->boolean('hidden_tag')->default(false);
            $table->boolean('fixed_tag')->default(false);
            $table->boolean('show_link')->default(true);
            $table->boolean('show_parent')->default(false);
            $table->timestamps();

            // Add indexes for better performance
            $table->index('parent_id');
            $table->index('menu_type');
            $table->index('path');
            $table->index(['menu_type', 'parent_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sidebars');
    }
};