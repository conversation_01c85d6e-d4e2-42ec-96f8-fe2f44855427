<?php

namespace Modules\ChatBot\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\BotShareLink;
use Modules\ChatBot\Models\Conversation;

class WidgetAuthMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip authentication for health check
        if ($request->is('*/widget/health')) {
            return $next($request);
        }

        $hasValidAuth = false;
        $authType = 'none';
        $bot = null;

        // Try API key authentication
        if ($request->hasHeader('Authorization')) {
            $apiKey = str_replace('Bearer ', '', $request->header('Authorization'));

            if (str_starts_with($apiKey, 'pk_')) {
                // Extract bot UUID from route or request
                $botIdentifier = $this->extractBotIdentifier($request);

                if ($botIdentifier) {
                    // Try direct bot UUID match
                    $bot = Bot::where('uuid', $botIdentifier)
                             ->where('api_key', $apiKey)
                             ->active()
                             ->first();

                    // If not found, try to get bot from conversation UUID
                    if (!$bot && $request->route('uuid')) {
                        $conversation = Conversation::with('bot')
                                                  ->where('uuid', $request->route('uuid'))
                                                  ->first();

                        if ($conversation && $conversation->bot->api_key === $apiKey) {
                            $bot = $conversation->bot;
                        }
                    }

                    if ($bot) {
                        $hasValidAuth = true;
                        $authType = 'api_key';
                    }
                }
            }
        }

        // Try share token authentication if API key failed
        if (!$hasValidAuth) {
            $shareToken = $this->extractBotIdentifier($request);
            
            if ($shareToken) {
                $shareLink = BotShareLink::with(['bot'])
                                       ->where('token', $shareToken)
                                       ->active()
                                       ->first();

                if ($shareLink && $shareLink->bot->canBeShared()) {
                    $hasValidAuth = true;
                    $authType = 'share_token';
                    $bot = $shareLink->bot;
                }
            }
        }

        if (!$hasValidAuth) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Valid API key or share token required.',
                'error_code' => 'WIDGET_AUTH_FAILED'
            ], 401);
        }

        // Add auth info to request for controllers
        $request->merge([
            'widget_auth_type' => $authType,
            'widget_bot' => $bot,
        ]);

        return $next($request);
    }

    /**
     * Extract bot identifier from request.
     */
    private function extractBotIdentifier(Request $request): ?string
    {
        // Try route parameter first
        if ($request->route('identifier')) {
            return $request->route('identifier');
        }

        // Try request body
        if ($request->has('bot_uuid')) {
            return $request->input('bot_uuid');
        }

        // Try conversation UUID (for message endpoints)
        if ($request->route('uuid')) {
            // This is a conversation UUID, we'll need to resolve it in the controller
            return $request->route('uuid');
        }

        return null;
    }
}
