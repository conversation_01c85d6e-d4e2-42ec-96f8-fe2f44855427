var O=Object.defineProperty,A=Object.defineProperties;var M=Object.getOwnPropertyDescriptors;var F=Object.getOwnPropertySymbols;var L=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable;var I=(t,s,n)=>s in t?O(t,s,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[s]=n,w=(t,s)=>{for(var n in s||(s={}))L.call(s,n)&&I(t,n,s[n]);if(F)for(var n of F(s))R.call(s,n)&&I(t,n,s[n]);return t},C=(t,s)=>A(t,M(s));var h=(t,s,n)=>new Promise((a,u)=>{var p=i=>{try{c(n.next(i))}catch(v){u(v)}},d=i=>{try{c(n.throw(i))}catch(v){u(v)}},c=i=>i.done?a(i.value):Promise.resolve(i.value).then(p,d);c((n=n.apply(t,s)).next())});/* empty css                         */import{q,y as N,r as S,e,a8 as l,ai as _,h7 as E,G as V,Q as W,n as D,N as y,D as T,E as k,P as x,M as P,k as f,aA as B,h9 as H,ha as U,a1 as $}from"./index-ZVLuktk4.js";import{u as K}from"./hooks-CuzZ-_om.js";import{g as z}from"./auth-api-fYhl1v86.js";import{P as G}from"./index-CQnBjWL2.js";import"./_plugin-vue_export-helper-QGN-qG8u.js";const Q=[{label:"Menu",value:0},{label:"Iframe",value:1},{label:"External Link",value:2},{label:"Button",value:3}],j=[{label:"Show",tip:"Will be displayed in menu",value:!0},{label:"Hide",tip:"Will not be displayed in menu",value:!1}],J=[{label:"Fixed",tip:"Current menu name is fixed in tabs and cannot be closed",value:!0},{label:"Not Fixed",tip:"Current menu name is not fixed in tabs and can be closed",value:!1}],X=[{label:"Cache",tip:"Will save the page's overall state, state will be cleared after refresh",value:!0},{label:"No Cache",tip:"Will not save the page's overall state",value:!1}],Y=[{label:"Allow",tip:"Current menu name or custom info can be added to tabs",value:!1},{label:"Forbid",tip:"Current menu name or custom info cannot be added to tabs",value:!0}],Z=[{label:"Show",tip:"Will show parent menu",value:!0},{label:"Hide",tip:"Will not show parent menu",value:!1}],ee=[{label:"Enable",tip:"Has first-time loading animation",value:!0},{label:"Disable",tip:"No first-time loading animation",value:!1}],ae={class:"custom-group-header"},le={class:"font-semibold"},oe={class:"custom-footer"},me=q({__name:"SidebarDrawerForm",props:{visible:{type:Boolean},values:{}},emits:["update:visible","update:values","submit"],setup(t,{expose:s,emit:n}){const a=t;N(()=>a.values,o=>{},{deep:!0,immediate:!0});const u=n,p=S(),d=S(!1),c=()=>h(null,null,function*(){try{const{data:o}=yield z();let r=B(o);const m=H(r);u("update:values",C(w({},a.values),{higherMenuOptions:U($(m))}))}catch(o){console.error("Error fetching sidebars:",o)}});N(()=>a.visible,o=>{o&&c()});const i=[{label:e(()=>l("Menu Type")),prop:"menuType",valueType:"select",options:Q,colProps:{span:24}},{label:e(()=>l("Roles")),prop:"roles",valueType:"select",fieldProps:{multiple:!0},options:e(()=>a.values.roleList),colProps:{span:24}},{label:e(()=>l("Permission")),prop:"auths",valueType:"cascader",options:e(()=>a.values.permissionList),hideInForm:e(()=>Number(a.values.menuType)!==3),colProps:{span:24}},{label:e(()=>l("Parent Menu")),prop:"parentId",valueType:"cascader",options:e(()=>a.values.higherMenuOptions),fieldProps:{props:{value:"id",label:"title",emitPath:!1,checkStrictly:!0},clearable:!0,filterable:!0},colProps:{span:12}},{label:e(()=>l("Menu Name")),prop:"title",valueType:"input",required:!0,rules:[{required:!0,message:l("Please enter menu name"),trigger:["blur","change"]}],colProps:{span:12}},{label:e(()=>l("Route Name")),prop:"name",valueType:"input",required:!0,hideInForm:e(()=>a.values.menuType===3),rules:[{required:!0,message:l("Please enter route name"),trigger:["blur","change"]}],colProps:{span:12}},{label:e(()=>l("Route Path")),prop:"path",valueType:"input",required:!0,hideInForm:e(()=>a.values.menuType===3),rules:[{required:!0,message:l("Please enter route path"),trigger:["blur","change"]}],colProps:{span:12}},{label:e(()=>l("Component Path")),prop:"component",valueType:"input",hideInForm:e(()=>a.values.menuType!==0),colProps:{span:12}},{label:e(()=>l("Menu Order")),prop:"rank",valueType:"input-number",fieldProps:{min:1,max:9999,class:"!w-full"},colProps:{span:12}},{label:e(()=>l("Route Redirect")),prop:"redirect",valueType:"input",hideInForm:e(()=>Number(a.values.menuType)!==0),colProps:{span:12}},{label:e(()=>l("Menu Icon")),prop:"icon",valueType:"",hideInForm:e(()=>Number(a.values.menuType)===3),renderField:()=>{var o;return _(E,{modelValue:(o=a==null?void 0:a.values)==null?void 0:o.icon,"onUpdate:modelValue":r=>{a!=null&&a.values&&(a.values.icon=r)},class:"w-full"})},fieldProps:{class:"w-full"},colProps:{span:12}},{label:e(()=>l("Extra Icon")),prop:"extraIcon",valueType:"input",hideInForm:e(()=>Number(a.values.menuType)===3),colProps:{span:12}},{label:e(()=>l("Enter Animation")),prop:"enterTransition",valueType:"",hideInForm:e(()=>Number(a.values.menuType)>=2),renderComponent:"ReAnimateSelector",colProps:{span:12}},{label:e(()=>l("Leave Animation")),prop:"leaveTransition",valueType:"",hideInForm:e(()=>Number(a.values.menuType)>=2),renderComponent:"ReAnimateSelector",colProps:{span:12}},{label:e(()=>l("Active Menu")),prop:"activePath",valueType:"input",hideInForm:e(()=>Number(a.values.menuType)!==0),colProps:{span:12}},{label:e(()=>l("Frame URL")),prop:"frameSrc",valueType:"input",hideInForm:e(()=>Number(a.values.menuType)!==1),colProps:{span:12}},{label:e(()=>l("Frame Loading")),prop:"frameLoading",valueType:"plus-radio",hideInForm:e(()=>Number(a.values.menuType)!==1),options:ee,colProps:{span:6}},{label:e(()=>l("Show Menu")),prop:"showLink",valueType:"plus-radio",hideInForm:e(()=>a.values.menuType===3),options:j,colProps:{span:6}},{label:e(()=>l("Show Parent")),prop:"showParent",valueType:"plus-radio",hideInForm:e(()=>a.values.menuType===3),options:Z,colProps:{span:6}},{label:e(()=>l("Keep Alive")),prop:"keepAlive",valueType:"plus-radio",hideInForm:e(()=>Number(a.values.menuType)>=2),options:X,colProps:{span:6}},{label:e(()=>l("Hidden Tag")),prop:"hiddenTag",valueType:"plus-radio",hideInForm:e(()=>Number(a.values.menuType)>=2),options:Y,colProps:{span:6}},{label:e(()=>l("Fixed Tag")),prop:"fixedTag",valueType:"plus-radio",hideInForm:e(()=>Number(a.values.menuType)>=2),options:J,colProps:{span:6}}],v=o=>h(null,null,function*(){var r;(r=p.value)!=null&&r.formInstance&&(yield p.value.formInstance.validate(m=>h(null,null,function*(){if(m)try{d.value=!0,u("submit",o)}finally{d.value=!1}})))}),g=()=>{var o;(o=p.value)!=null&&o.formInstance&&p.value.formInstance.resetFields()};return s({resetForm:g}),(o,r)=>{const m=V("el-button");return D(),W(f(G),{ref_key:"formRef",ref:p,visible:o.visible,"model-value":o.values,size:"65%",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0,destroyOnClose:!0,form:{columns:i,footerAlign:"center",hasFooter:!1,labelSuffix:"",labelPosition:"top",labelWidth:"auto",labelAlign:"left",requireAsteriskPosition:"right",rowProps:{gutter:20}},"onUpdate:visible":r[2]||(r[2]=b=>u("update:visible",b)),"onUpdate:modelValue":r[3]||(r[3]=b=>u("update:values",b)),onClose:g},{"drawer-header":y(()=>[T("div",ae,[T("span",le,P(f(l)("Sidebar Information")),1)])]),"drawer-footer":y(()=>[T("div",oe,[k(m,{plain:"",onClick:r[0]||(r[0]=b=>u("update:visible",!1))},{default:y(()=>[x(P(f(l)("Cancel")),1)]),_:1}),k(m,{plain:"",type:"primary",loading:d.value,icon:f(K)("ri:save-2-line"),onClick:r[1]||(r[1]=b=>v(o.values))},{default:y(()=>[x(P(f(l)("Save")),1)]),_:1},8,["loading","icon"])])]),_:1},8,["visible","model-value","form"])}}});export{me as default};
