<?php

namespace Modules\Sidebar\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Core\Traits\ResponseTrait;
use Modules\Sidebar\Models\Sidebar;
use Modules\Sidebar\Http\Requests\SidebarRequest;
use Modules\Sidebar\Http\Requests\BulkSidebarRequest;
use Modules\Sidebar\Http\Filters\SidebarFilter;
use Modules\Sidebar\Facades\Sidebar as SidebarFacade;
use Throwable;

class SidebarController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|sidebar.view')->only(['index', 'show', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|sidebar.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|sidebar.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|sidebar.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|sidebar.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        // Limit pagination to prevent abuse
        $limit = min($request->input('limit', 10), 100);

        $sidebars = Sidebar::query()
            ->filter(new SidebarFilter($request))
            ->orderBy('rank')
            ->paginate($limit);

        return $this->paginatedResponse($sidebars, __('Sidebars retrieved successfully.'));
    }

    /**
     * Get sidebar dropdown options
     */
    public function dropdown(): JsonResponse
    {
        return $this->successResponse(
            SidebarFacade::getDropdownOptions(),
            __('Sidebar dropdown options retrieved successfully.')
        );
    }

    /**
     * Store a newly created resource in storage.
     * @throws Throwable
     */
    public function store(SidebarRequest $request): JsonResponse
    {
        DB::beginTransaction();

        try {
            $data = $request->validated();

            if (empty($data['roles'])) {
                unset($data['roles']);
            }

            $sidebar = Sidebar::create($data);

            DB::commit();

            return $this->successResponse($sidebar, __('Sidebar created successfully.'), 201);
        } catch (Exception $e) {
            logger()->error('Sidebar Creation Failed', [
                'operation' => 'create',
                'user_id' => auth()->id(),
                'data' => $request->validated(),
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            DB::rollBack();
            return $this->errorResponse(__('Failed to create sidebar.'), 500);
        }
    }

    /**
     * Show the specified resource.
     */
    public function show(Sidebar $sidebar): JsonResponse
    {
        return $this->successResponse($sidebar);
    }

    /**
     * Update the specified resource in storage.
     * @throws Throwable
     */
    public function update(SidebarRequest $request, Sidebar $sidebar): JsonResponse
    {
        DB::beginTransaction();

        try {
            $data = $request->validated();

            if (empty($data['roles'])) {
                $data['roles'] = null;
            }

            $sidebar->update($data);

            DB::commit();

            return $this->successResponse(
                $sidebar->fresh(),
                __('Sidebar updated successfully.')
            );
        } catch (Exception $e) {
            logger()->error('Sidebar Update Failed', [
                'operation' => 'update',
                'sidebar_id' => $sidebar->id,
                'user_id' => auth()->id(),
                'data' => $request->validated(),
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            DB::rollBack();
            return $this->errorResponse(__('Failed to update sidebar.'), 500);
        }
    }

    /**
     * Soft delete the specified resource.
     */
    public function delete(Sidebar $sidebar): JsonResponse
    {
        try {
            $sidebar->delete();
            return $this->successResponse(null, __('Sidebar deleted successfully.'));
        } catch (Exception $e) {
            logger()->error('Sidebar Soft Delete Failed', [
                'operation' => 'soft_delete',
                'sidebar_id' => $sidebar->id,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            return $this->errorResponse(__('Failed to delete sidebar.'), 500);
        }
    }

    /**
     * Restore the specified resource.
     */
    public function restore(int $id): JsonResponse
    {
        try {
            $sidebar = Sidebar::withTrashed()->findOrFail($id);
            $sidebar->restore();
            return $this->successResponse($sidebar, __('Sidebar restored successfully.'));
        } catch (Exception $e) {
            logger()->error('Sidebar Restore Failed', [
                'operation' => 'restore',
                'sidebar_id' => $id,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            return $this->errorResponse(__('Failed to restore sidebar.'), 500);
        }
    }

    /**
     * Permanently delete the specified resource.
     */
    public function destroy(Sidebar $sidebar): JsonResponse
    {
        try {
            $sidebar->forceDelete();
            return $this->successResponse(null, __('Sidebar permanently deleted.'));
        } catch (Exception $e) {
            logger()->error('Sidebar Force Delete Failed', [
                'operation' => 'force_delete',
                'sidebar_id' => $sidebar->id,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            return $this->errorResponse(__('Failed to permanently delete sidebar.'), 500);
        }
    }

    /**
     * Bulk soft delete sidebars.
     */
    public function bulkDelete(BulkSidebarRequest $request): JsonResponse
    {
        try {
            $ids = $request->getIds();
            $deletedCount = Sidebar::whereIn('id', $ids)->delete();

            return $this->successResponse(
                ['deleted_count' => $deletedCount],
                __('Sidebars deleted successfully.', ['count' => $deletedCount])
            );
        } catch (Exception $e) {
            logger()->error('Sidebar Bulk Delete Failed', [
                'operation' => 'bulk_delete',
                'ids' => $request->getIds(),
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            return $this->errorResponse(__('Failed to delete sidebars.'), 500);
        }
    }

    /**
     * Bulk restore sidebars.
     */
    public function bulkRestore(BulkSidebarRequest $request): JsonResponse
    {
        try {
            $ids = $request->getIds();
            $restoredCount = Sidebar::withTrashed()->whereIn('id', $ids)->restore();

            return $this->successResponse(
                ['restored_count' => $restoredCount],
                __('Sidebars restored successfully.', ['count' => $restoredCount])
            );
        } catch (Exception $e) {
            logger()->error('Sidebar Bulk Restore Failed', [
                'operation' => 'bulk_restore',
                'ids' => $request->getIds(),
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            return $this->errorResponse(__('Failed to restore sidebars.'), 500);
        }
    }

    /**
     * Bulk permanently delete sidebars.
     */
    public function bulkDestroy(BulkSidebarRequest $request): JsonResponse
    {
        try {
            $ids = $request->getIds();
            $destroyedCount = Sidebar::withTrashed()->whereIn('id', $ids)->forceDelete();

            return $this->successResponse(
                ['destroyed_count' => $destroyedCount],
                __('Sidebars permanently deleted.', ['count' => $destroyedCount])
            );
        } catch (Exception $e) {
            logger()->error('Sidebar Bulk Destroy Failed', [
                'operation' => 'bulk_destroy',
                'ids' => $request->getIds(),
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            return $this->errorResponse(__('Failed to permanently delete sidebars.'), 500);
        }
    }
}
