<?php

namespace Modules\Sidebar\Services;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Modules\Sidebar\Models\Sidebar as Model;

class SidebarService
{
    /**
     * Cache tag for sidebars
     */
    private const CACHE_TAG = 'sidebars';

    /**
     * Cache TTL in minutes
     */
    private const CACHE_TTL = 5;

    /**
     * Get all sidebars as tree structure with caching
     */
    public function getSidebars(): array
    {
        if (!enabledCache()) {
            return $this->buildSidebarTree();
        }

        return Cache::tags([self::CACHE_TAG])
            ->remember(
                'proCMS.sidebars.tree',
                now()->addMinutes(self::CACHE_TTL),
                fn() => $this->buildSidebarTree()
            );
    }

    /**
     * Get sidebar tree structure
     */
    public function getSidebarTree(): array
    {
        return $this->getSidebars();
    }

    /**
     * Get active sidebars with caching
     */
    public function getActiveSidebars(): Collection
    {
        if (!enabledCache()) {
            return Model::active()->orderBy('rank')->get();
        }

        return Cache::tags([self::CACHE_TAG])
            ->remember(
                'proCMS.sidebars.active.list',
                now()->addMinutes(self::CACHE_TTL),
                fn() => Model::active()->orderBy('rank')->get()
            );
    }

    /**
     * Find sidebar by ID with caching
     */
    public function findById(int $id): ?Model
    {
        if (!enabledCache()) {
            return Model::find($id);
        }

        return Cache::tags([self::CACHE_TAG])
            ->remember(
                "proCMS.sidebars.{$id}",
                now()->addMinutes(self::CACHE_TTL),
                fn() => Model::find($id)
            );
    }

    /**
     * Get sidebar dropdown options
     */
    public function getDropdownOptions(): Collection
    {
        if (!enabledCache()) {
            return Model::select('id', 'title', 'parent_id')
                ->orderBy('rank')
                ->get();
        }

        return Cache::tags([self::CACHE_TAG])
            ->remember(
                'proCMS.sidebars.dropdown',
                now()->addMinutes(self::CACHE_TTL),
                fn() => Model::select('id', 'title', 'parent_id')
                    ->orderBy('rank')
                    ->get()
            );
    }

    /**
     * Clear all sidebar caches
     */
    public function clearCache(): void
    {
        if (enabledCache()) {
            Cache::tags([self::CACHE_TAG])->flush();
        }
    }

    /**
     * Build sidebar tree structure
     */
    private function buildSidebarTree(): array
    {
        return Model::buildRouterTree(Model::getSidebars()->toArray());
    }
}
