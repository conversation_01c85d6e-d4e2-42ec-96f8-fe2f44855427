<?php

use Illuminate\Support\Facades\Route;
use Modules\Sidebar\Http\Controllers\Auth\SidebarController as AuthSidebarController;

Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('sidebars', AuthSidebarController::class)->names('sidebar');

    // Additional routes for soft delete management
    Route::get('sidebars/trashed/list', [AuthSidebarController::class, 'trashed'])->name('sidebar.trashed');
    Route::patch('sidebars/{id}/restore', [AuthSidebarController::class, 'restore'])->name('sidebar.restore');
    Route::delete('sidebars/{id}/force-delete', [AuthSidebarController::class, 'destroy'])->name('sidebar.force-delete');

    // Bulk operations
    Route::delete('sidebars/bulk/delete', [AuthSidebarController::class, 'bulkDelete'])->name('sidebar.bulk-delete');
    Route::patch('sidebars/bulk/restore', [AuthSidebarController::class, 'bulkRestore'])->name('sidebar.bulk-restore');
    Route::delete('sidebars/bulk/destroy', [AuthSidebarController::class, 'bulkDestroy'])->name('sidebar.bulk-destroy');
});
