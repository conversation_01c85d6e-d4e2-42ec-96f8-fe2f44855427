import{q as $,r as N,e as b,G as d,Q as R,n as g,N as s,E as o,m as C,k as l,a8 as a,P as p,M as m,I as D,J as G,D as L}from"./index-ZVLuktk4.js";const M={class:"flex justify-end gap-4"},z=$({__name:"TranslationDrawerForm",props:{visible:{type:Boolean,default:!1},values:{default:()=>({translations:{}})},availableLocales:{default:()=>[{code:"en",name:"English"},{code:"vi",name:"Vietnamese"}]}},emits:["update:visible","update:values","submit","close"],setup(h,{expose:x,emit:E}){const c=h,u=E,i=N(),V=b(()=>{var e;return((e=c.values)==null?void 0:e.id)!=null}),k=b({get:()=>c.visible,set:e=>u("update:visible",e)}),n=b({get:()=>c.values||{translations:{}},set:e=>u("update:values",e)}),T=()=>{var e;(e=i.value)==null||e.validate(r=>{r&&u("submit",n.value)})},U=()=>{var e;(e=i.value)==null||e.resetFields(),u("update:values",{key:"",group:"",translations:{}})},y=()=>{u("close"),u("update:visible",!1)};return x({resetForm:()=>{var e;(e=i.value)==null||e.resetFields()}}),(e,r)=>{const v=d("el-input"),f=d("el-form-item"),w=d("el-divider"),F=d("el-form"),_=d("el-button"),q=d("el-drawer");return g(),R(q,{modelValue:k.value,"onUpdate:modelValue":r[2]||(r[2]=t=>k.value=t),title:V.value?l(a)("Edit Translation"):l(a)("Create Translation"),size:"600px",onClose:y},{footer:s(()=>[L("div",M,[o(_,{onClick:y},{default:s(()=>[p(m(l(a)("Cancel")),1)]),_:1}),o(_,{onClick:U},{default:s(()=>[p(m(l(a)("Reset")),1)]),_:1}),o(_,{type:"primary",onClick:T},{default:s(()=>[p(m(V.value?l(a)("Update"):l(a)("Create")),1)]),_:1})])]),default:s(()=>[o(F,{ref_key:"formRef",ref:i,model:n.value,"label-width":"120px","label-position":"right"},{default:s(()=>[o(f,{label:l(a)("Translation Key"),prop:"key",rules:[{required:!0,message:l(a)("Translation key is required")}]},{default:s(()=>[o(v,{modelValue:n.value.key,"onUpdate:modelValue":r[0]||(r[0]=t=>n.value.key=t),placeholder:l(a)("Enter translation key (e.g., auth.login)")},null,8,["modelValue","placeholder"])]),_:1},8,["label","rules"]),o(f,{label:l(a)("Group"),prop:"group",rules:[{required:!0,message:l(a)("Group is required")}]},{default:s(()=>[o(v,{modelValue:n.value.group,"onUpdate:modelValue":r[1]||(r[1]=t=>n.value.group=t),placeholder:l(a)("Enter group (e.g., auth, validation)")},null,8,["modelValue","placeholder"])]),_:1},8,["label","rules"]),o(w,null,{default:s(()=>[p(m(l(a)("Translations")),1)]),_:1}),(g(!0),C(D,null,G(e.availableLocales,t=>(g(),C("div",{key:t.code,class:"mb-4"},[o(f,{label:`${t.name} (${t.code.toUpperCase()})`},{default:s(()=>[o(v,{modelValue:n.value.translations[t.code],"onUpdate:modelValue":B=>n.value.translations[t.code]=B,type:"textarea",rows:2,placeholder:`${l(a)("Enter translation for")} ${t.name}`},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:2},1032,["label"])]))),128))]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}});export{z as default};
