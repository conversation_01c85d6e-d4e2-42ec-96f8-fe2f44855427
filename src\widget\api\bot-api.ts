/**
 * Bot API Client for Widget
 * Enhanced with retry logic, caching, error handling, and request/response interceptors
 */

import type { ApiResponse, BotConfig, ChatMessage } from '../types';

// Custom API Error class
export class ApiError extends Error {
  constructor(
    public status: number,
    public statusText: string,
    message: string,
    public data?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// API Client Configuration
interface ApiClientConfig {
  apiKey: string;
  baseUrl?: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  enableCache?: boolean;
  cacheTimeout?: number;
}

// Request/Response Interceptors
interface RequestInterceptor {
  (config: RequestInit): RequestInit | Promise<RequestInit>;
}

interface ResponseInterceptor {
  (response: Response): Response | Promise<Response>;
}

// Cache Entry
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

export class BotApiClient {
  private baseUrl: string;
  private apiKey: string;
  private timeout: number;
  private retryAttempts: number;
  private retryDelay: number;
  private enableCache: boolean;
  private cacheTimeout: number;

  // Interceptors
  private requestInterceptors: RequestInterceptor[] = [];
  private responseInterceptors: ResponseInterceptor[] = [];

  // Cache
  private cache = new Map<string, CacheEntry<any>>();

  // Request queue for rate limiting
  private requestQueue: Array<() => Promise<any>> = [];
  private isProcessingQueue = false;

  constructor(config: ApiClientConfig) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || this.getDefaultBaseUrl();
    this.timeout = config.timeout || 10000;
    this.retryAttempts = config.retryAttempts || 3;
    this.retryDelay = config.retryDelay || 1000;
    this.enableCache = config.enableCache ?? true;
    this.cacheTimeout = config.cacheTimeout || 300000; // 5 minutes

    // Setup default interceptors
    this.setupDefaultInterceptors();
  }

  /**
   * Setup default request/response interceptors
   */
  private setupDefaultInterceptors(): void {
    // Request interceptor for adding common headers
    this.addRequestInterceptor((config) => {
      const headers = new Headers(config.headers);

      // Add user agent and referrer for analytics
      if (typeof navigator !== 'undefined') {
        headers.set('X-User-Agent', navigator.userAgent);
      }

      if (typeof document !== 'undefined' && document.referrer) {
        headers.set('X-Referrer', document.referrer);
      }

      // Add timestamp
      headers.set('X-Request-Time', new Date().toISOString());

      return { ...config, headers };
    });

    // Response interceptor for error handling
    this.addResponseInterceptor(async (response) => {
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          response.status,
          response.statusText,
          errorData.message || 'Request failed',
          errorData
        );
      }
      return response;
    });
  }

  private getDefaultBaseUrl(): string {
    // Auto-detect base URL from current domain or use default
    if (typeof window !== 'undefined') {
      const currentDomain = window.location.origin;
      // Check if we're on the same domain as the main app
      if (currentDomain.includes('procms.com') || currentDomain.includes('localhost')) {
        return currentDomain;
      }
    }
    return 'https://api.procms.com';
  }

  /**
   * Add request interceptor
   */
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor);
  }

  /**
   * Add response interceptor
   */
  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor);
  }

  /**
   * Get from cache
   */
  private getFromCache<T>(key: string): T | null {
    if (!this.enableCache) return null;

    const entry = this.cache.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Set cache
   */
  private setCache<T>(key: string, data: T, ttl?: number): void {
    if (!this.enableCache) return;

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.cacheTimeout
    });
  }

  /**
   * Clear cache
   */
  clearCache(pattern?: string): void {
    if (pattern) {
      const regex = new RegExp(pattern);
      for (const key of this.cache.keys()) {
        if (regex.test(key)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Enhanced request method with retry logic, caching, and interceptors
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    useCache = true
  ): Promise<ApiResponse<T>> {
    const cacheKey = `${endpoint}_${JSON.stringify(options)}`;

    // Check cache first for GET requests
    if (useCache && (!options.method || options.method === 'GET')) {
      const cached = this.getFromCache<ApiResponse<T>>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const url = `${this.baseUrl}${endpoint}`;

    let config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        ...options.headers,
      },
    };

    // Apply request interceptors
    for (const interceptor of this.requestInterceptors) {
      config = await interceptor(config);
    }

    // Add timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    config.signal = controller.signal;

    let lastError: Error;

    // Retry logic
    for (let attempt = 0; attempt <= this.retryAttempts; attempt++) {
      try {
        const response = await fetch(url, config);
        clearTimeout(timeoutId);

        // Apply response interceptors
        let processedResponse = response;
        for (const interceptor of this.responseInterceptors) {
          processedResponse = await interceptor(processedResponse);
        }

        const data = await processedResponse.json();

        // Cache successful GET requests
        if (useCache && (!options.method || options.method === 'GET') && data.success) {
          this.setCache(cacheKey, data);
        }

        return data;

      } catch (error) {
        clearTimeout(timeoutId);
        lastError = error as Error;

        // Don't retry on certain errors
        if (error instanceof ApiError && error.status >= 400 && error.status < 500) {
          throw error;
        }

        // Don't retry on last attempt
        if (attempt === this.retryAttempts) {
          break;
        }

        // Wait before retry with exponential backoff
        const delay = this.retryDelay * Math.pow(2, attempt);
        await this.sleep(delay);

        console.warn(`Request failed, retrying (${attempt + 1}/${this.retryAttempts + 1}):`, error);
      }
    }

    console.error('API Request failed after all retries:', lastError);
    throw lastError;
  }

  /**
   * Get bot configuration by UUID (cached)
   */
  async getBotConfig(botUuid: string, forceRefresh = false): Promise<ApiResponse<BotConfig>> {
    if (forceRefresh) {
      this.clearCache(`/api/public/bots/${botUuid}/config`);
    }
    return this.request<BotConfig>(`/api/public/bots/${botUuid}/config`);
  }

  /**
   * Validate API key and bot access
   */
  async validateAccess(botUuid: string): Promise<ApiResponse<{ valid: boolean; permissions: string[] }>> {
    return this.request(`/api/public/bots/${botUuid}/validate`, {}, false); // Don't cache validation
  }

  /**
   * Get bot public info (for widget display)
   */
  async getBotPublicInfo(botUuid: string): Promise<ApiResponse<{
    name: string;
    logo?: string;
    description?: string;
    greetingMessage: string;
    starterMessages?: string[];
    theme?: any;
  }>> {
    return this.request(`/api/public/bots/${botUuid}/info`);
  }

  /**
   * Check bot availability and status
   */
  async checkBotStatus(botUuid: string): Promise<ApiResponse<{
    status: string;
    available: boolean;
    maintenanceMessage?: string;
  }>> {
    return this.request(`/api/public/bots/${botUuid}/status`, {}, false);
  }

  /**
   * Start a new conversation with enhanced metadata
   */
  async startConversation(
    botUuid: string,
    userId?: string,
    metadata?: Record<string, any>
  ): Promise<ApiResponse<{
    conversationId: string;
    sessionId?: string;
    expiresAt?: string;
  }>> {
    const conversationMetadata = {
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
      referrer: typeof document !== 'undefined' ? document.referrer : '',
      timestamp: new Date().toISOString(),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: typeof navigator !== 'undefined' ? navigator.language : 'en',
      screenResolution: typeof screen !== 'undefined' ? `${screen.width}x${screen.height}` : '',
      ...metadata
    };

    return this.request(`/api/public/bots/${botUuid}/conversations`, {
      method: 'POST',
      body: JSON.stringify({
        userId,
        metadata: conversationMetadata
      })
    }, false);
  }

  /**
   * Send message to bot with enhanced features
   */
  async sendMessage(
    botUuid: string,
    conversationId: string,
    message: string,
    options?: {
      messageType?: 'text' | 'quick_reply' | 'postback';
      attachments?: Array<{ type: string; url: string; name?: string }>;
      metadata?: Record<string, any>;
    }
  ): Promise<ApiResponse<{
    response: string;
    messageId: string;
    botMessageId: string;
    tokens?: number;
    model?: string;
    responseTime?: number;
    suggestions?: string[];
  }>> {
    const payload = {
      message,
      messageType: options?.messageType || 'text',
      attachments: options?.attachments || [],
      metadata: {
        timestamp: new Date().toISOString(),
        clientTimestamp: Date.now(),
        ...options?.metadata
      }
    };

    return this.request(`/api/public/bots/${botUuid}/conversations/${conversationId}/messages`, {
      method: 'POST',
      body: JSON.stringify(payload)
    }, false);
  }

  /**
   * Send typing indicator
   */
  async sendTypingIndicator(
    botUuid: string,
    conversationId: string,
    isTyping: boolean
  ): Promise<ApiResponse<void>> {
    return this.request(`/api/public/bots/${botUuid}/conversations/${conversationId}/typing`, {
      method: 'POST',
      body: JSON.stringify({ isTyping })
    }, false);
  }

  /**
   * Mark message as read
   */
  async markMessageAsRead(
    botUuid: string,
    conversationId: string,
    messageId: string
  ): Promise<ApiResponse<void>> {
    return this.request(`/api/public/bots/${botUuid}/conversations/${conversationId}/messages/${messageId}/read`, {
      method: 'POST'
    }, false);
  }

  /**
   * Get conversation history with pagination
   */
  async getConversationHistory(
    botUuid: string,
    conversationId: string,
    options?: {
      limit?: number;
      offset?: number;
      since?: string;
      includeMetadata?: boolean;
    }
  ): Promise<ApiResponse<{
    messages: ChatMessage[];
    total: number;
    hasMore: boolean;
    nextOffset?: number;
  }>> {
    const params = new URLSearchParams();
    if (options?.limit) params.set('limit', options.limit.toString());
    if (options?.offset) params.set('offset', options.offset.toString());
    if (options?.since) params.set('since', options.since);
    if (options?.includeMetadata) params.set('include_metadata', 'true');

    const query = params.toString() ? `?${params.toString()}` : '';
    return this.request(`/api/public/bots/${botUuid}/conversations/${conversationId}/messages${query}`);
  }

  /**
   * Get conversation summary
   */
  async getConversationSummary(
    botUuid: string,
    conversationId: string
  ): Promise<ApiResponse<{
    messageCount: number;
    duration: number;
    startedAt: string;
    lastMessageAt: string;
    status: string;
    satisfaction?: number;
    tags?: string[];
  }>> {
    return this.request(`/api/public/bots/${botUuid}/conversations/${conversationId}/summary`);
  }

  /**
   * End conversation with feedback
   */
  async endConversation(
    botUuid: string,
    conversationId: string,
    feedback?: {
      rating?: number;
      comment?: string;
      tags?: string[];
      resolved?: boolean;
    }
  ): Promise<ApiResponse<{
    endedAt: string;
    duration: number;
    messageCount: number;
  }>> {
    return this.request(`/api/public/bots/${botUuid}/conversations/${conversationId}/end`, {
      method: 'POST',
      body: JSON.stringify({
        feedback,
        endedAt: new Date().toISOString()
      })
    }, false);
  }

  /**
   * Submit conversation feedback
   */
  async submitFeedback(
    botUuid: string,
    conversationId: string,
    feedback: {
      rating: number;
      comment?: string;
      categories?: string[];
      helpful?: boolean;
    }
  ): Promise<ApiResponse<void>> {
    return this.request(`/api/public/bots/${botUuid}/conversations/${conversationId}/feedback`, {
      method: 'POST',
      body: JSON.stringify(feedback)
    }, false);
  }

  /**
   * Upload file for conversation
   */
  async uploadFile(
    botUuid: string,
    conversationId: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<{
    fileId: string;
    fileName: string;
    fileSize: number;
    fileType: string;
    downloadUrl: string;
  }>> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('conversationId', conversationId);

    // Create XMLHttpRequest for progress tracking
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100;
            onProgress(progress);
          }
        });
      }

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve(JSON.parse(xhr.responseText));
        } else {
          reject(new ApiError(xhr.status, xhr.statusText, 'File upload failed'));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('File upload failed'));
      });

      xhr.open('POST', `${this.baseUrl}/api/public/bots/${botUuid}/conversations/${conversationId}/files`);
      xhr.setRequestHeader('Authorization', `Bearer ${this.apiKey}`);
      xhr.send(formData);
    });
  }

  /**
   * Get widget analytics
   */
  async getWidgetAnalytics(
    botUuid: string,
    timeframe: '1h' | '24h' | '7d' | '30d' = '24h'
  ): Promise<ApiResponse<{
    totalConversations: number;
    totalMessages: number;
    averageResponseTime: number;
    satisfactionScore: number;
    topQuestions: Array<{ question: string; count: number }>;
  }>> {
    return this.request(`/api/public/bots/${botUuid}/analytics?timeframe=${timeframe}`);
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<ApiResponse<{
    status: 'healthy' | 'degraded' | 'down';
    timestamp: string;
    version: string;
  }>> {
    return this.request('/api/health', {}, false);
  }

  /**
   * Get API client configuration
   */
  getConfig(): ApiClientConfig {
    return {
      apiKey: this.apiKey,
      baseUrl: this.baseUrl,
      timeout: this.timeout,
      retryAttempts: this.retryAttempts,
      retryDelay: this.retryDelay,
      enableCache: this.enableCache,
      cacheTimeout: this.cacheTimeout
    };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<ApiClientConfig>): void {
    if (config.baseUrl) this.baseUrl = config.baseUrl;
    if (config.timeout) this.timeout = config.timeout;
    if (config.retryAttempts) this.retryAttempts = config.retryAttempts;
    if (config.retryDelay) this.retryDelay = config.retryDelay;
    if (config.enableCache !== undefined) this.enableCache = config.enableCache;
    if (config.cacheTimeout) this.cacheTimeout = config.cacheTimeout;
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    keys: string[];
    hitRate?: number;
  } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  /**
   * Destroy client and cleanup resources
   */
  destroy(): void {
    this.clearCache();
    this.requestInterceptors.length = 0;
    this.responseInterceptors.length = 0;
    this.requestQueue.length = 0;
  }
}

// Singleton instance management
const apiClients = new Map<string, BotApiClient>();

/**
 * Get or create API client instance
 */
export function getBotApiClient(
  apiKey: string,
  config?: Partial<ApiClientConfig>
): BotApiClient {
  const clientKey = `${apiKey}_${config?.baseUrl || ''}`;

  if (!apiClients.has(clientKey)) {
    apiClients.set(clientKey, new BotApiClient({
      apiKey,
      ...config
    }));
  }

  return apiClients.get(clientKey)!;
}

/**
 * Remove API client instance
 */
export function removeBotApiClient(apiKey: string, baseUrl?: string): void {
  const clientKey = `${apiKey}_${baseUrl || ''}`;
  const client = apiClients.get(clientKey);
  if (client) {
    client.destroy();
    apiClients.delete(clientKey);
  }
}

/**
 * Clear all API client instances
 */
export function clearAllApiClients(): void {
  for (const client of apiClients.values()) {
    client.destroy();
  }
  apiClients.clear();
}

// Convenience functions for common operations
export async function getBotConfiguration(
  botUuid: string,
  apiKey: string,
  forceRefresh = false
): Promise<ApiResponse<BotConfig>> {
  const client = getBotApiClient(apiKey);
  return client.getBotConfig(botUuid, forceRefresh);
}

export async function validateBotAccess(
  botUuid: string,
  apiKey: string
): Promise<ApiResponse<{ valid: boolean; permissions: string[] }>> {
  const client = getBotApiClient(apiKey);
  return client.validateAccess(botUuid);
}

export async function startBotConversation(
  botUuid: string,
  apiKey: string,
  userId?: string,
  metadata?: Record<string, any>
): Promise<ApiResponse<{ conversationId: string; sessionId?: string; expiresAt?: string }>> {
  const client = getBotApiClient(apiKey);
  return client.startConversation(botUuid, userId, metadata);
}

export async function sendBotMessage(
  botUuid: string,
  apiKey: string,
  conversationId: string,
  message: string,
  options?: any
): Promise<ApiResponse<any>> {
  const client = getBotApiClient(apiKey);
  return client.sendMessage(botUuid, conversationId, message, options);
}

// Export types
export type { ApiClientConfig, RequestInterceptor, ResponseInterceptor };
