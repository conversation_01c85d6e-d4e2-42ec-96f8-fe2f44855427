<?php

namespace Modules\Auth\Tests;

use Tests\TestCase as BaseTestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Modules\User\Models\User;
use Modules\User\Enums\UserStatus;
use Modules\User\Enums\UserGender;

abstract class TestCase extends BaseTestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Clear cache before each test
        try {
            Cache::flush();
        } catch (\Exception $e) {
            // Ignore cache errors during testing
        }

        // Run migrations for required modules
        $this->artisan('migrate:fresh');
        
        // Seed necessary data for Auth module tests
        try {
            $this->artisan('db:seed', ['--class' => 'Modules\\User\\Database\\Seeders\\UserDatabaseSeeder']);
            $this->artisan('db:seed', ['--class' => 'Modules\\ActiveCode\\Database\\Seeders\\ActiveCodeDatabaseSeeder']);
            $this->artisan('db:seed', ['--class' => 'Modules\\Setting\\Database\\Seeders\\SettingDatabaseSeeder']);
        } catch (\Exception $e) {
            // Continue if seeders don't exist
        }
    }

    protected function tearDown(): void
    {
        // Clean up after each test
        try {
            Cache::flush();
        } catch (\Exception $e) {
            // Ignore cache errors during testing
        }
        
        DB::disconnect();
        parent::tearDown();
    }

    /**
     * Helper method to create authenticated user for API testing
     */
    protected function authenticatedUser(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'status' => UserStatus::Active,
            'is_verified' => true,
            'email_verified_at' => now(),
        ], $attributes));
    }

    /**
     * Helper method to get JWT token for user
     */
    protected function getJwtToken(User $user): string
    {
        return auth('api')->login($user);
    }

    /**
     * Helper method to make authenticated JSON request
     */
    protected function authenticatedJson(string $method, string $uri, array $data = [], array $headers = [])
    {
        $user = $this->authenticatedUser();
        $token = $this->getJwtToken($user);
        
        return $this->json($method, $uri, $data, array_merge([
            'Authorization' => 'Bearer ' . $token,
        ], $headers));
    }

    /**
     * Log error for debugging
     */
    protected function logError(string $message, \Exception $e): void
    {
        $logMessage = sprintf(
            "[%s] %s: %s\nFile: %s:%d\nTrace:\n%s",
            now()->toDateTimeString(),
            $message,
            $e->getMessage(),
            $e->getFile(),
            $e->getLine(),
            $e->getTraceAsString()
        );

        // Log to file
        file_put_contents(
            storage_path('logs/auth_test_errors.log'),
            $logMessage . "\n\n",
            FILE_APPEND | LOCK_EX
        );

        // Also log using Laravel logger
        logger()->error($message, [
            'exception' => $e,
            'test_class' => get_class($this),
        ]);
    }

    /**
     * Assert API response structure for Auth module
     */
    protected function assertAuthApiResponseStructure($response, string $dataKey = null): void
    {
        $expectedStructure = [
            'success',
            'message',
        ];

        if ($dataKey) {
            $expectedStructure['data'] = [$dataKey];
        }

        $response->assertJsonStructure($expectedStructure);
    }

    /**
     * Assert validation error response
     */
    protected function assertValidationError($response, array $fields = []): void
    {
        $response->assertStatus(422)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'errors'
                ]);

        if (!empty($fields)) {
            foreach ($fields as $field) {
                $response->assertJsonValidationErrors($field);
            }
        }
    }

    /**
     * Create test user with specific attributes
     */
    protected function createTestUser(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'status' => UserStatus::Active,
            'is_verified' => true,
            'email_verified_at' => now(),
            'gender' => UserGender::Male,
        ], $attributes));
    }

    /**
     * Create inactive test user for verification tests
     */
    protected function createInactiveUser(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'status' => UserStatus::Inactive,
            'is_verified' => false,
            'email_verified_at' => null,
        ], $attributes));
    }
}
