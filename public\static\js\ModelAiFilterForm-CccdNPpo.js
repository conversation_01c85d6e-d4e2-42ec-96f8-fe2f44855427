var c=(b,m,s)=>new Promise((t,r)=>{var y=o=>{try{i(s.next(o))}catch(p){r(p)}},u=o=>{try{i(s.throw(o))}catch(p){r(p)}},i=o=>o.done?t(o.value):Promise.resolve(o.value).then(y,u);i((s=s.apply(b,m)).next())});/* empty css                         */import{q as A,r as v,e as a,a8 as e,y as D,G as M,Q as I,n as R,N as f,D as g,E as C,P as T,M as P,k as n,aA as w,_ as S}from"./index-ZVLuktk4.js";import{g as V}from"./auth-api-BCod4ziT.js";import{g as Y}from"./auth-api-C3NnK5ai.js";import{u as x}from"./hooks-CuzZ-_om.js";import{P as B}from"./index-CQnBjWL2.js";import"./_plugin-vue_export-helper-QGN-qG8u.js";const O={class:"custom-group-header"},q={class:"font-semibold"},E={class:"custom-footer"},K=A({__name:"ModelAiFilterForm",props:{visible:{type:Boolean},values:{}},emits:["update:visible","update:values","submit","reset"],setup(b,{emit:m}){const s=b,t=m,r=v(!1),y=v(),u=v([]),i=v([]),o=[{label:a(()=>e("Model Key")),prop:"key",valueType:"input",fieldProps:{placeholder:"",clearable:!0}},{label:a(()=>e("Model Name")),prop:"name",valueType:"input",fieldProps:{placeholder:"",clearable:!0}},{label:a(()=>e("Model Provider")),prop:"modelProviderId",valueType:"select",fieldProps:{placeholder:"",clearable:!0},options:a(()=>[...u.value.map(l=>({label:l.name,value:l.id}))])},{label:a(()=>e("Model Category")),prop:"modelCategoryId",valueType:"select",fieldProps:{placeholder:"",clearable:!0},options:a(()=>[...i.value.map(l=>({label:l.name,value:l.id}))])},{label:a(()=>e("Streaming")),prop:"streaming",valueType:"select",fieldProps:{placeholder:"",clearable:!0},options:[{label:e("Yes"),value:!0},{label:e("No"),value:!1}]},{label:a(()=>e("Vision")),prop:"vision",valueType:"select",fieldProps:{placeholder:"",clearable:!0},options:[{label:e("Yes"),value:!0},{label:e("No"),value:!1}]},{label:a(()=>e("Function Calling")),prop:"functionCalling",valueType:"select",fieldProps:{placeholder:"",clearable:!0},options:[{label:e("Yes"),value:!0},{label:e("No"),value:!1}]},{label:a(()=>e("Is Default")),prop:"isDefault",valueType:"select",fieldProps:{placeholder:"",clearable:!0},options:[{label:e("Yes"),value:!0},{label:e("No"),value:!1}]},{label:a(()=>e("Status")),prop:"status",valueType:"select",fieldProps:{placeholder:""},options:[{label:e("Active"),value:"active"},{label:e("Inactive"),value:"inactive"},{label:e("Draft"),value:"draft"}]},{label:a(()=>e("Trashed")),prop:"isTrashed",valueType:"select",fieldProps:{placeholder:"",clearable:!1},options:[{label:e("No"),value:"no"},{label:e("Yes"),value:"yes"}]}],p=()=>c(null,null,function*(){try{r.value=!0,t("submit",s.values)}catch(l){console.error("Filter submission failed:",l)}finally{r.value=!1}}),F=()=>c(null,null,function*(){yield V().then(({data:l})=>{u.value=w(l)}).catch()}),N=()=>c(null,null,function*(){yield Y().then(({data:l})=>{i.value=w(l)}).catch()});D(()=>s.visible,()=>{s.visible&&(N(),F())});const k=()=>{t("reset")};return(l,d)=>{const _=M("el-button");return R(),I(n(B),{ref_key:"formRef",ref:y,visible:l.visible,"model-value":l.values,size:"30%",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0,destroyOnClose:!0,form:{columns:o,footerAlign:"center",hasFooter:!1,labelSuffix:"",labelPosition:"top",labelWidth:"auto",labelAlign:"left",requireAsteriskPosition:"right",rowProps:{gutter:10}},"onUpdate:visible":d[0]||(d[0]=h=>t("update:visible",h)),"onUpdate:modelValue":d[1]||(d[1]=h=>t("update:values",h))},{"drawer-header":f(()=>[g("div",O,[g("span",q,P(n(e)("Filter")),1)])]),"drawer-footer":f(()=>[g("div",E,[C(_,{plain:"",onClick:k},{default:f(()=>[T(P(n(e)("Reset")),1)]),_:1}),C(_,{plain:"",type:"primary",loading:r.value,icon:n(x)("ri:filter-2-line"),onClick:p},{default:f(()=>[T(P(n(e)("Apply Filter")),1)]),_:1},8,["loading","icon"])])]),_:1},8,["visible","model-value","form"])}}}),J=S(K,[["__scopeId","data-v-501f3377"]]);export{J as default};
