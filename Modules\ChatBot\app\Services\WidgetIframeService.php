<?php

namespace Modules\ChatBot\Services;

use Modules\ChatBot\Models\Bot;

class WidgetIframeService
{
    /**
     * Generate iframe embed code for a bot.
     */
    public static function generateIframeCode(
        Bot $bot,
        array $options = []
    ): string {
        $baseUrl = url('/widget');
        
        // Build query parameters
        $params = [
            'bot_uuid' => $bot->uuid,
            'api_key' => $bot->api_key,
        ];
        
        // Add optional parameters
        if (isset($options['user_id'])) {
            $params['user_id'] = $options['user_id'];
        }
        
        if (isset($options['theme'])) {
            $params['theme'] = $options['theme'];
        }
        
        if (isset($options['position'])) {
            $params['position'] = $options['position'];
        }
        
        if (isset($options['show_header'])) {
            $params['show_header'] = $options['show_header'] ? '1' : '0';
        }
        
        if (isset($options['show_avatar'])) {
            $params['show_avatar'] = $options['show_avatar'] ? '1' : '0';
        }
        
        if (isset($options['auto_open'])) {
            $params['auto_open'] = $options['auto_open'] ? '1' : '0';
        }
        
        if (isset($options['domain'])) {
            $params['domain'] = $options['domain'];
        }
        
        // Build iframe URL
        $iframeUrl = $baseUrl . '?' . http_build_query($params);
        
        // Default iframe attributes
        $width = $options['width'] ?? '400';
        $height = $options['height'] ?? '600';
        $frameborder = $options['frameborder'] ?? '0';
        $allowfullscreen = $options['allowfullscreen'] ?? true;
        
        // Generate iframe HTML
        $iframe = '<iframe';
        $iframe .= ' src="' . htmlspecialchars($iframeUrl) . '"';
        $iframe .= ' width="' . htmlspecialchars($width) . '"';
        $iframe .= ' height="' . htmlspecialchars($height) . '"';
        $iframe .= ' frameborder="' . htmlspecialchars($frameborder) . '"';
        $iframe .= ' style="border: none; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);"';
        
        if ($allowfullscreen) {
            $iframe .= ' allowfullscreen';
        }
        
        $iframe .= ' title="' . htmlspecialchars($bot->name . ' Chatbot') . '"';
        $iframe .= '></iframe>';
        
        return $iframe;
    }
    
    /**
     * Generate iframe embed code for share token.
     */
    public static function generateShareIframeCode(
        string $shareToken,
        array $options = []
    ): string {
        $baseUrl = url('/widget');
        
        // Build query parameters
        $params = [
            'bot_uuid' => $shareToken, // Use share token as bot_uuid
        ];
        
        // Add optional parameters (same as above)
        if (isset($options['user_id'])) {
            $params['user_id'] = $options['user_id'];
        }
        
        if (isset($options['theme'])) {
            $params['theme'] = $options['theme'];
        }
        
        // Build iframe URL
        $iframeUrl = $baseUrl . '?' . http_build_query($params);
        
        // Default iframe attributes
        $width = $options['width'] ?? '400';
        $height = $options['height'] ?? '600';
        $frameborder = $options['frameborder'] ?? '0';
        
        // Generate iframe HTML
        $iframe = '<iframe';
        $iframe .= ' src="' . htmlspecialchars($iframeUrl) . '"';
        $iframe .= ' width="' . htmlspecialchars($width) . '"';
        $iframe .= ' height="' . htmlspecialchars($height) . '"';
        $iframe .= ' frameborder="' . htmlspecialchars($frameborder) . '"';
        $iframe .= ' style="border: none; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);"';
        $iframe .= ' allowfullscreen';
        $iframe .= ' title="Chatbot Widget"';
        $iframe .= '></iframe>';
        
        return $iframe;
    }
    
    /**
     * Generate JavaScript embed code (alternative to iframe).
     */
    public static function generateJavaScriptCode(
        Bot $bot,
        array $options = []
    ): string {
        $config = [
            'botUuid' => $bot->uuid,
            'apiKey' => $bot->api_key,
            'baseUrl' => url('/api/v1/widget'),
            'theme' => $options['theme'] ?? 'light',
            'position' => $options['position'] ?? 'bottom-right',
            'autoOpen' => $options['auto_open'] ?? false,
            'showHeader' => $options['show_header'] ?? true,
            'showAvatar' => $options['show_avatar'] ?? true,
        ];
        
        $configJson = json_encode($config, JSON_UNESCAPED_SLASHES);
        
        return <<<HTML
<script>
(function() {
    // Widget configuration
    window.ProcMSWidgetConfig = {$configJson};
    
    // Load widget script
    var script = document.createElement('script');
    script.src = '{url('/js/widget.js')}';
    script.async = true;
    script.onload = function() {
        if (window.ProcMSChatWidget) {
            new window.ProcMSChatWidget(window.ProcMSWidgetConfig);
        }
    };
    document.head.appendChild(script);
})();
</script>
HTML;
    }
    
    /**
     * Get iframe URL for a bot.
     */
    public static function getIframeUrl(Bot $bot, array $options = []): string
    {
        $baseUrl = url('/widget');
        
        $params = [
            'bot_uuid' => $bot->uuid,
            'api_key' => $bot->api_key,
        ];
        
        // Add optional parameters
        foreach (['user_id', 'theme', 'position', 'domain'] as $key) {
            if (isset($options[$key])) {
                $params[$key] = $options[$key];
            }
        }
        
        foreach (['show_header', 'show_avatar', 'auto_open'] as $key) {
            if (isset($options[$key])) {
                $params[$key] = $options[$key] ? '1' : '0';
            }
        }
        
        return $baseUrl . '?' . http_build_query($params);
    }
    
    /**
     * Validate iframe parameters.
     */
    public static function validateIframeParams(array $params): array
    {
        $errors = [];
        
        if (empty($params['bot_uuid'])) {
            $errors[] = 'Bot UUID is required';
        }
        
        if (isset($params['width']) && !is_numeric($params['width'])) {
            $errors[] = 'Width must be numeric';
        }
        
        if (isset($params['height']) && !is_numeric($params['height'])) {
            $errors[] = 'Height must be numeric';
        }
        
        if (isset($params['theme']) && !in_array($params['theme'], ['light', 'dark', 'auto'])) {
            $errors[] = 'Theme must be light, dark, or auto';
        }
        
        if (isset($params['position']) && !in_array($params['position'], [
            'bottom-right', 'bottom-left', 'top-right', 'top-left', 'center'
        ])) {
            $errors[] = 'Invalid position value';
        }
        
        return $errors;
    }
}
