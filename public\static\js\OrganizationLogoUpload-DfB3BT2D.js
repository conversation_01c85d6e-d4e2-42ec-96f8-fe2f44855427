var U=(p,f,a)=>new Promise((t,l)=>{var _=e=>{try{n(a.next(e))}catch(o){l(o)}},m=e=>{try{n(a.throw(e))}catch(o){l(o)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(_,m);n((a=a.apply(p,f)).next())});import{q as v,r as w,G as b,m as u,n as c,D as h,A as k,E as g,N as y,Q as C,k as i,gb as z,M as x,a8 as s,gc as B,gN as d,_ as E}from"./index-ZVLuktk4.js";import{h as N}from"./auth-api-Buobw8mr.js";const O={class:"logo-upload-section mx-auto"},M={class:"flex justify-center mb-4"},G={key:0,class:"upload-loading"},R={class:"loading-text"},S=["src"],j={class:"text-xs text-center text-gray-500"},I={key:0,class:"text-xs text-center text-blue-500 mt-1"},P=v({__name:"OrganizationLogoUpload",props:{logoUrl:{},autoUpload:{type:Boolean,default:!0}},emits:["update:logoUrl","logoFileChange","uploadSuccess","uploadError"],setup(p,{emit:f}){const a=p,t=f,l=w(!1),_=e=>U(null,null,function*(){if(e.raw)try{a.logoUrl&&a.logoUrl.startsWith("blob:")&&URL.revokeObjectURL(a.logoUrl);const o=URL.createObjectURL(e.raw);t("update:logoUrl",o),t("logoFileChange",e.raw),a.autoUpload&&(yield m(e.raw))}catch(o){console.error("Error handling logo change:",o),d.error(s("Failed to process selected file"))}}),m=e=>U(null,null,function*(){try{l.value=!0;const o=yield N(e);if(o.success&&o.data){const r=o.data.logo_url;r&&t("update:logoUrl",r),t("uploadSuccess",o.data),d.success(s("Logo uploaded successfully"))}}catch(o){console.error("Logo upload failed:",o),t("uploadError",o),d.error(s("Logo upload failed: {error}",{error:o.message||s("Unknown error")}))}finally{l.value=!1}}),n=e=>{const o=e.type.startsWith("image/"),r=e.size/1024/1024<5;return o?r?!0:(d.error(s("Image size must be less than 5MB!")),!1):(d.error(s("Only image files are allowed!")),!1)};return(e,o)=>{const r=b("el-icon"),L=b("el-upload");return c(),u("div",O,[h("div",M,[g(L,{class:"logo-uploader",action:"#","show-file-list":!1,"auto-upload":!1,accept:"image/*","before-upload":n,disabled:l.value,onChange:_},{default:y(()=>[l.value?(c(),u("div",G,[g(r,{class:"is-loading"},{default:y(()=>[g(i(z))]),_:1}),h("span",R,x(i(s)("Uploading...")),1)])):a.logoUrl?(c(),u("img",{key:1,src:a.logoUrl,class:"logo-preview",alt:"organization logo"},null,8,S)):(c(),C(r,{key:2,class:"logo-uploader-icon"},{default:y(()=>[g(i(B))]),_:1}))]),_:1},8,["disabled"])]),h("p",j,x(i(s)("Upload JPG, PNG, JPEG images. Size under 5MB.")),1),a.autoUpload?(c(),u("p",I,x(i(s)("Files will be uploaded automatically")),1)):k("",!0)])}}}),J=E(P,[["__scopeId","data-v-12094b2d"]]);export{J as default};
