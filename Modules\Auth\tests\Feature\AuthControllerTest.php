<?php

namespace Modules\Auth\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\User\Models\User;
use Modules\User\Enums\UserStatus;
use Modules\User\Enums\UserGender;
use Modules\ActiveCode\Models\ActiveCode;

class AuthControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed necessary data
        $this->artisan('db:seed', ['--class' => 'Modules\\Auth\\Database\\Seeders\\AuthDatabaseSeeder']);
        $this->artisan('db:seed', ['--class' => 'Modules\\ActiveCode\\Database\\Seeders\\ActiveCodeDatabaseSeeder']);
    }

    /** @test */
    public function user_can_register_successfully_with_email_verification_required()
    {
        // Set email verification as required
        \Modules\Setting\Models\Setting::updateOrCreate(
            ['key' => 'auth_email_verification_required'],
            ['value' => 'true', 'type' => 'boolean']
        );

        $userData = [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'Test',
            'last_name' => 'User',
            'gender' => UserGender::Male->value,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user' => [
                            'id',
                            'username',
                            'email',
                            'first_name',
                            'last_name',
                            'status',
                            'is_verified'
                        ],
                        'email_verification_required'
                    ]
                ])
                ->assertJson([
                    'data' => [
                        'email_verification_required' => true
                    ]
                ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'username' => 'testuser',
            'status' => UserStatus::Inactive->value,
            'is_verified' => false,
        ]);

        $this->assertDatabaseHas('active_codes', [
            'identifier' => '<EMAIL>',
            'type' => 'email_verification',
        ]);
    }

    /** @test */
    public function user_can_register_successfully_without_email_verification_required()
    {
        // Set email verification as not required
        \Modules\Setting\Models\Setting::updateOrCreate(
            ['key' => 'auth_email_verification_required'],
            ['value' => 'false', 'type' => 'boolean']
        );

        $userData = [
            'username' => 'testuser2',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'Test',
            'last_name' => 'User',
            'gender' => UserGender::Male->value,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(201)
                ->assertJson([
                    'data' => [
                        'email_verification_required' => false
                    ]
                ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'username' => 'testuser2',
            'status' => UserStatus::Active->value,
            'is_verified' => true,
        ]);

        // OTP still sent for email verification (optional)
        $this->assertDatabaseHas('active_codes', [
            'identifier' => '<EMAIL>',
            'type' => 'email_verification',
        ]);
    }

    /** @test */
    public function user_cannot_register_when_registration_is_disabled()
    {
        // Disable registration
        \Modules\Setting\Models\Setting::updateOrCreate(
            ['key' => 'auth_registration_enabled'],
            ['value' => 'false', 'type' => 'boolean']
        );

        $userData = [
            'username' => 'testuser3',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'Test',
            'last_name' => 'User',
            'gender' => UserGender::Male->value,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                ]);

        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>',
        ]);
    }

    /** @test */
    public function user_can_verify_email_with_valid_code()
    {
        // Create inactive user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'status' => UserStatus::Inactive,
            'is_verified' => false,
            'email_verified_at' => null,
        ]);

        // Create verification code
        $activeCode = ActiveCode::create([
            'code' => '123456',
            'type' => 'email_verification',
            'identifier' => '<EMAIL>',
            'expires_at' => now()->addMinutes(15),
        ]);

        $response = $this->postJson('/api/v1/auth/verify-email', [
            'email' => '<EMAIL>',
            'code' => '123456',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ]);

        $user->refresh();
        $this->assertTrue($user->is_verified);
        $this->assertEquals(UserStatus::Active, $user->status);
        $this->assertNotNull($user->email_verified_at);
    }

    /** @test */
    public function user_can_login_with_valid_credentials()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'status' => UserStatus::Active,
            'is_verified' => true,
        ]);

        $response = $this->postJson('/api/v1/auth/login', [
            'login' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user',
                        'token',
                        'token_type',
                        'expires_in'
                    ]
                ]);
    }

    /** @test */
    public function user_cannot_login_with_unverified_email()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'status' => UserStatus::Active,
            'is_verified' => false,
        ]);

        $response = $this->postJson('/api/v1/auth/login', [
            'login' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                ]);
    }

    /** @test */
    public function user_can_request_password_reset()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $response = $this->postJson('/api/v1/auth/password/forgot', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ]);

        $this->assertDatabaseHas('active_codes', [
            'identifier' => '<EMAIL>',
            'type' => 'password_reset',
        ]);
    }

    /** @test */
    public function authenticated_user_can_get_profile()
    {
        $user = User::factory()->create([
            'status' => UserStatus::Active,
            'is_verified' => true,
        ]);

        $token = auth('api')->login($user);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/v1/auth/me');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'username',
                        'email',
                        'first_name',
                        'last_name',
                    ]
                ]);
    }

    /** @test */
    public function authenticated_user_can_logout()
    {
        $user = User::factory()->create([
            'status' => UserStatus::Active,
            'is_verified' => true,
        ]);

        $token = auth('api')->login($user);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/auth/logout');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ]);
    }
}
