var f=(g,y,n)=>new Promise((v,i)=>{var p=r=>{try{l(n.next(r))}catch(d){i(d)}},m=r=>{try{l(n.throw(r))}catch(d){i(d)}},l=r=>r.done?v(r.value):Promise.resolve(r.value).then(p,m);l((n=n.apply(g,y)).next())});/* empty css                         */import{q as _,r as h,a8 as e,e as u,y as I,G as F,Q as N,n as S,N as b,D as P,E as k,P as q,M as C,k as c,aA as x}from"./index-ZVLuktk4.js";import{u as A}from"./hooks-CuzZ-_om.js";import{i as R}from"./auth-api-CR4e_tAC.js";import{P as B}from"./index-CQnBjWL2.js";import"./_plugin-vue_export-helper-QGN-qG8u.js";const V={class:"custom-group-header"},O={class:"font-semibold"},$={class:"custom-footer"},W=_({__name:"RoleDrawerForm",props:{visible:{type:Boolean},values:{}},emits:["update:visible","update:values","submit"],setup(g,{expose:y,emit:n}){const v=g,i=n,p=h([]),m=h(!1),l=h(),r=[{label:u(()=>e("Role name")),prop:"name",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input role name"),trigger:["blur","change"]}],fieldProps:{placeholder:""}},{label:u(()=>e("Display Name")),prop:"displayName",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input display name"),trigger:["blur","change"]}],fieldProps:{placeholder:""}},{label:u(()=>e("Description")),prop:"description",valueType:"textarea",fieldProps:{placeholder:"",rows:3}},{label:u(()=>e("Status")),prop:"status",valueType:"select",fieldProps:{placeholder:""},options:[{label:e("Active"),value:"active"},{label:e("Inactive"),value:"inactive"}]},{label:u(()=>e("Permissions")),prop:"permissions",valueType:"cascader",required:!0,rules:[{required:!0,message:e("Please choose permissions"),trigger:["blur","change"]}],fieldProps:{props:{multiple:!0,checkStrictly:!1,emitPath:!1},placeholder:"",filterable:!0,collapseTags:!0,collapseTagsTooltip:!0,maxCollapseTags:3,clearable:!0},options:u(()=>[...p.value])}],d=a=>f(null,null,function*(){var t;if(!(!((t=l.value)!=null&&t.formInstance)||!(yield l.value.formInstance.validate())))try{m.value=!0,i("submit",a)}finally{setTimeout(()=>{m.value=!1},3e3)}}),w=()=>{var a;(a=l.value)!=null&&a.formInstance&&l.value.formInstance.resetFields()},D=()=>f(null,null,function*(){try{const{data:a}=yield R(),s=x(a)||[];p.value=s.map(t=>{var o;return{label:t.name,value:t.id,children:((o=t.children)==null?void 0:o.map(T=>({label:T.name,value:T.id})))||[]}})}catch(a){p.value=[]}});return I(()=>v.visible,()=>f(null,null,function*(){v.visible&&(yield D())})),y({resetForm:w}),(a,s)=>{const t=F("el-button");return S(),N(c(B),{ref_key:"formRef",ref:l,visible:a.visible,"model-value":a.values,size:"40%",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0,destroyOnClose:!0,form:{columns:r,footerAlign:"center",hasFooter:!1,labelSuffix:"",labelPosition:"top",labelWidth:"auto",labelAlign:"left",requireAsteriskPosition:"right",rowProps:{gutter:10}},"onUpdate:visible":s[2]||(s[2]=o=>i("update:visible",o)),"onUpdate:modelValue":s[3]||(s[3]=o=>i("update:values",o)),onClose:w},{"drawer-header":b(()=>[P("div",V,[P("span",O,C(c(e)("Information Form")),1)])]),"drawer-footer":b(()=>[P("div",$,[k(t,{plain:"",onClick:s[0]||(s[0]=o=>i("update:visible",!1))},{default:b(()=>[q(C(c(e)("Cancel")),1)]),_:1}),k(t,{plain:"",type:"primary",loading:m.value,icon:c(A)("ri:save-2-line"),onClick:s[1]||(s[1]=o=>d(a.values))},{default:b(()=>[q(C(c(e)("Save")),1)]),_:1},8,["loading","icon"])])]),_:1},8,["visible","model-value","form"])}}});export{W as default};
