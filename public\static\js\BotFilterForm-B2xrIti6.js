var y=(u,d,a)=>new Promise((o,s)=>{var c=l=>{try{r(a.next(l))}catch(t){s(t)}},b=l=>{try{r(a.throw(l))}catch(t){s(t)}},r=l=>l.done?o(l.value):Promise.resolve(l.value).then(c,b);r((a=a.apply(u,d)).next())});/* empty css                         */import{q as C,r as T,e as i,a8 as e,G as g,Q as F,n as B,N as p,D as m,E as _,P,M as f,k as n,_ as k}from"./index-ZVLuktk4.js";import{P as w}from"./index-CQnBjWL2.js";import"./_plugin-vue_export-helper-QGN-qG8u.js";const x={class:"custom-group-header"},A={class:"font-semibold"},N={class:"custom-footer"},V=C({__name:"BotFilterForm",props:{visible:{type:Boolean},values:{}},emits:["update:visible","update:values","submit","reset"],setup(u,{emit:d}){const a=u,o=d,s=T(!1),c=[{label:i(()=>e("Bot Name")),prop:"name",valueType:"input",fieldProps:{placeholder:""}},{label:i(()=>e("Visibility")),prop:"visibility",valueType:"select",options:[{label:e("Public"),value:"public"},{label:e("Private"),value:"private"}],fieldProps:{placeholder:""}},{label:i(()=>e("Bot Type")),prop:"botType",valueType:"select",options:[{label:e("Assistant"),value:"assistant"},{label:e("Chatbot"),value:"chatbot"}],fieldProps:{placeholder:""}},{label:i(()=>e("Status")),prop:"status",valueType:"select",options:[{label:e("Active"),value:"active"},{label:e("Draft"),value:"draft"},{label:e("Pause"),value:"pause"}],fieldProps:{placeholder:""}},{label:i(()=>e("Trashed")),prop:"isTrashed",valueType:"select",options:[{label:e("Yes"),value:"yes"},{label:e("No"),value:"no"}],fieldProps:{placeholder:"",clearable:!1}}],b=()=>y(null,null,function*(){try{s.value=!0,o("submit",a.values)}catch(l){console.error("Filter submission failed:",l)}finally{s.value=!1}}),r=()=>{o("reset")};return(l,t)=>{const h=g("el-button");return B(),F(n(w),{ref:"formRef",visible:a.visible,"model-value":a.values,size:"30%",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0,destroyOnClose:!0,form:{columns:c,footerAlign:"center",hasFooter:!1,labelSuffix:"",labelPosition:"top",labelWidth:"auto",labelAlign:"left",requireAsteriskPosition:"right",rowProps:{gutter:10}},"onUpdate:visible":t[0]||(t[0]=v=>o("update:visible",v)),"onUpdate:modelValue":t[1]||(t[1]=v=>o("update:values",v))},{"drawer-header":p(()=>[m("div",x,[m("span",A,f(n(e)("Filter")),1)])]),"drawer-footer":p(()=>[m("div",N,[_(h,{plain:"",onClick:r},{default:p(()=>[P(f(n(e)("Reset")),1)]),_:1}),_(h,{plain:"",type:"primary",loading:s.value,onClick:b},{default:p(()=>[P(f(n(e)("Apply Filter")),1)]),_:1},8,["loading"])])]),_:1},8,["visible","model-value","form"])}}}),E=k(V,[["__scopeId","data-v-b3e960b4"]]);export{E as default};
