<?php

namespace Modules\ChatBot\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Modules\ChatBot\Http\Filters\KnowledgeBaseFilter;
use Modules\ChatBot\Models\KnowledgeBase;
use Modules\Core\Traits\ResponseTrait;

class KnowledgeBaseController extends Controller
{
    use ResponseTrait;

    /**
     * Display a listing of public knowledge bases.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $knowledgeBases = KnowledgeBase::where('is_public', true)
                ->where('status', 'ready')
                ->filter(new KnowledgeBaseFilter($request))
                ->paginate($request->input('limit', 10));

            return $this->paginatedResponse($knowledgeBases, 'Public knowledge bases retrieved successfully.');
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Display the specified public knowledge base.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $knowledgeBase = KnowledgeBase::where('uuid', $id)
                ->where('is_public', true)
                ->where('status', 'ready')
                ->firstOrFail();

            return $this->successResponse(
                $knowledgeBase,
                'Public knowledge base retrieved successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Public knowledge base not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Download knowledge base file.
     */
    public function download(string $id)
    {
        try {
            $kb = KnowledgeBase::where('uuid', $id)->firstOrFail();

            $storagePath = $kb->storage_path;
            if (!$storagePath || !Storage::disk('local')->exists($storagePath)) {
                abort(404, 'File không tồn tại.');
            }

            return Storage::disk('local')->download($storagePath);
        } catch (ModelNotFoundException $e) {
            abort(404, 'Knowledge base not found.');
        }
    }
}
