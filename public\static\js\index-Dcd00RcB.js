const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/ModelToolDrawerForm-8rI2-LUV.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/js/auth-api-C3NnK5ai.js","static/js/index-CQnBjWL2.js","static/js/_plugin-vue_export-helper-QGN-qG8u.js","static/css/ModelToolDrawerForm-BW7-CqJG.css","static/css/plus-drawer-form-DaizY_En.css","static/js/ModelToolFilterForm-D1dSa_dc.js","static/css/ModelToolFilterForm-DRKANVNC.css"])))=>i.map(i=>d[i]);
var he=Object.defineProperty,ve=Object.defineProperties;var be=Object.getOwnPropertyDescriptors;var re=Object.getOwnPropertySymbols;var _e=Object.prototype.hasOwnProperty,ke=Object.prototype.propertyIsEnumerable;var ie=(e,i,u)=>i in e?he(e,i,{enumerable:!0,configurable:!0,writable:!0,value:u}):e[i]=u,de=(e,i)=>{for(var u in i||(i={}))_e.call(i,u)&&ie(e,u,i[u]);if(re)for(var u of re(i))ke.call(i,u)&&ie(e,u,i[u]);return e},ce=(e,i)=>ve(e,be(i));var y=(e,i,u)=>new Promise((T,R)=>{var w=v=>{try{B(u.next(v))}catch(m){R(m)}},b=v=>{try{B(u.throw(v))}catch(m){R(m)}},B=v=>v.done?T(v.value):Promise.resolve(v.value).then(w,b);B((u=u.apply(e,i)).next())});import{aN as C,az as ge,r as h,g as Ce,ax as d,a8 as a,ay as ue,aA as Te,aD as we,ai as q,fG as j,q as xe,o as Re,S as Be,G as P,m as N,n as $,D as z,E as c,N as p,k as s,aI as Me,P as pe,M as x,A as le,I as J,aJ as k,L as Q,Q as Se,a5 as De,a4 as X,aK as fe,aM as me,aL as Ve,_ as ze}from"./index-ZVLuktk4.js";import{P as Ae}from"./index-DMMM8XNs.js";import"./sortable.esm-7jaD_3Ar.js";import"./epTheme-DzIV6EF-.js";const Pe=e=>C.request("get","/api/auth/model-tools",{params:e}),$e=e=>C.request("post","/api/auth/model-tools",{data:ge(e)}),He=(e,i)=>C.request("put",`/api/auth/model-tools/${e}`,{data:ge(i)}),Fe=e=>C.request("delete",`/api/auth/model-tools/${e}`),Ie=e=>C.request("delete","/api/auth/model-tools/bulk-destroy",{data:e}),qe=e=>C.request("put",`/api/auth/model-tools/${e}/restore`),Ne=e=>C.request("put","/api/auth/model-tools/bulk-restore",{data:e}),Ee=e=>C.request("delete",`/api/auth/model-tools/${e}/force-delete`),Ue=e=>C.request("delete","/api/auth/model-tools/bulk-delete",{data:e}),Oe=(e,i)=>C.request("post",`/api/auth/model-tools/${e}/test`,{data:i}),Ge=e=>C.request("post",`/api/auth/model-tools/${e}/validate`);function Le(){const e=h(!1),i=h({}),u=Ce({total:0,pageSize:20,currentPage:1,pageSizes:[10,20,50,100,200],background:!0,layout:"total, sizes, prev, pager, next, jumper"}),T=h([]),R=h([]),w=h({sortBy:"name",sortOrder:"asc"}),b=h(!1),B=h(!1),v=h({status:"active",isActive:!0}),m=h(),E=h(!1),H=h(!1),M=h(""),g=()=>y(null,null,function*(){var l,n;e.value=!0;try{const o=yield Pe(ce(de({},i.value),{order:`${w.value.sortBy}:${w.value.sortOrder}`,page:u.currentPage,limit:u.pageSize}));R.value=Te(o.data),u.total=o.total}catch(o){console.error("Get Model Tools error:",o),d(((n=(l=o.response)==null?void 0:l.data)==null?void 0:n.message)||(o==null?void 0:o.message)||a("Get failed"),{type:"error"})}finally{e.value=!1}}),U=l=>y(null,null,function*(){var n,o;try{e.value=!0;const t=yield $e(l);return t.success?(d(t.message||a("Create successful"),{type:"success"}),yield g(),!0):(d((t==null?void 0:t.message)||a("Create failed"),{type:"error"}),!1)}catch(t){return d(((o=(n=t.response)==null?void 0:n.data)==null?void 0:o.message)||(t==null?void 0:t.message)||a("Create failed"),{type:"error"}),!1}finally{e.value=!1}}),O=(l,n)=>y(null,null,function*(){var o,t;try{e.value=!0;const r=yield He(l,n);return r.success?(d(r.message||a("Update successful"),{type:"success"}),yield g(),!0):(d((r==null?void 0:r.message)||a("Update failed"),{type:"error"}),!1)}catch(r){return d(((t=(o=r.response)==null?void 0:o.data)==null?void 0:t.message)||(r==null?void 0:r.message)||a("Update failed"),{type:"error"}),!1}finally{e.value=!1}}),Z=(l,n)=>y(null,null,function*(){var o,t;try{H.value=!0;const r=yield Oe(l,{parameters:n});r.success?(M.value=JSON.stringify(r.data,null,2),d(a("Test successful"),{type:"success"})):d(r.message||a("Test failed"),{type:"error"})}catch(r){d(((t=(o=r.response)==null?void 0:o.data)==null?void 0:t.message)||(r==null?void 0:r.message)||a("Test failed"),{type:"error"})}finally{H.value=!1}}),S=l=>y(null,null,function*(){var n,o;try{e.value=!0;const t=yield Ge(l);t.success?d(t.message||a("Validation successful"),{type:"success"}):d(t.message||a("Validation failed"),{type:"error"})}catch(t){d(((o=(n=t.response)==null?void 0:n.data)==null?void 0:o.message)||(t==null?void 0:t.message)||a("Validation failed"),{type:"error"})}finally{e.value=!1}}),D=l=>{T.value=l},V=({prop:l,order:n})=>{w.value.sortBy=l,w.value.sortOrder=n==="ascending"?"asc":"desc",g()},G=l=>{u.currentPage=l,g()},ee=l=>{u.pageSize=l,u.currentPage=1,g()},L=l=>y(null,null,function*(){var n,o;try{e.value=!0;const t=yield Ee(l);t.success?(d(t.message||a("Delete successful"),{type:"success"}),yield g()):d(t.message||a("Delete failed"),{type:"error"})}catch(t){d(((o=(n=t.response)==null?void 0:n.data)==null?void 0:o.message)||(t==null?void 0:t.message)||a("Delete failed"),{type:"error"})}finally{e.value=!1}}),Y=l=>y(null,null,function*(){var n,o;try{e.value=!0;const t=yield Ue({ids:l});t.success?(d(t.message||a("Bulk delete successful"),{type:"success"}),yield g()):d(t.message||a("Bulk delete failed"),{type:"error"})}catch(t){d(((o=(n=t.response)==null?void 0:n.data)==null?void 0:o.message)||(t==null?void 0:t.message)||a("Bulk delete failed"),{type:"error"})}finally{e.value=!1}}),te=l=>{ue.confirm(a("Are you sure to delete this item?"),a("Warning"),{confirmButtonText:a("Confirm"),cancelButtonText:a("Cancel"),type:"warning"}).then(()=>{i.value.isTrashed==="yes"?L(l):W(l)})},se=()=>{if(T.value.length===0){d(a("Please select items to delete"),{type:"warning"});return}ue.confirm(a("Are you sure to delete selected items?"),a("Warning"),{confirmButtonText:a("Confirm"),cancelButtonText:a("Cancel"),type:"warning"}).then(()=>{const l=T.value.map(n=>n.id);i.value.isTrashed==="yes"?Y(l):K()})},W=l=>y(null,null,function*(){var n,o;try{e.value=!0;const t=yield Fe(l);t.success?(d(t.message||a("Move to trash successful"),{type:"success"}),yield g()):d(t.message||a("Move to trash failed"),{type:"error"})}catch(t){d(((o=(n=t.response)==null?void 0:n.data)==null?void 0:o.message)||(t==null?void 0:t.message)||a("Move to trash failed"),{type:"error"})}finally{e.value=!1}}),K=()=>y(null,null,function*(){var l,n;try{e.value=!0;const o=T.value.map(r=>r.id),t=yield Ie({ids:o});t.success?(d(t.message||a("Bulk move to trash successful"),{type:"success"}),yield g()):d(t.message||a("Bulk move to trash failed"),{type:"error"})}catch(o){d(((n=(l=o.response)==null?void 0:l.data)==null?void 0:n.message)||(o==null?void 0:o.message)||a("Bulk move to trash failed"),{type:"error"})}finally{e.value=!1}});return{loading:e,filterRef:i,pagination:u,records:R,multipleSelection:T,sort:w,filterVisible:b,drawerVisible:B,drawerValues:v,modelToolFormRef:m,testDialogVisible:E,testLoading:H,testResult:M,fnGetModelTools:g,fnHandleCreateModelTool:U,fnHandleUpdateModelTool:O,fnHandleDelete:L,fnHandleBulkDelete:Y,fnTestModelTool:Z,fnValidateModelTool:S,fnHandleSelectionChange:D,fnHandleSortChange:V,fnHandlePageChange:G,fnHandleSizeChange:ee,handleDelete:te,handleBulkDelete:se,handleDestroy:W,handleBulkDestroy:K,handleRestore:l=>y(null,null,function*(){var n,o;try{e.value=!0;const t=yield qe(l);t.success?(d(t.message||a("Restore successful"),{type:"success"}),yield g()):d(t.message||a("Restore failed"),{type:"error"})}catch(t){d(((o=(n=t.response)==null?void 0:n.data)==null?void 0:o.message)||(t==null?void 0:t.message)||a("Restore failed"),{type:"error"})}finally{e.value=!1}}),handleBulkRestore:()=>y(null,null,function*(){var l,n;try{e.value=!0;const o=T.value.map(r=>r.id),t=yield Ne({ids:o});t.success?(d(t.message||a("Bulk restore successful"),{type:"success"}),yield g()):d(t.message||a("Bulk restore failed"),{type:"error"})}catch(o){d(((n=(l=o.response)==null?void 0:l.data)==null?void 0:n.message)||(o==null?void 0:o.message)||a("Bulk restore failed"),{type:"error"})}finally{e.value=!1}}),handleTest:l=>{v.value=l,E.value=!0,M.value=""},handleValidate:l=>y(null,null,function*(){yield S(l)}),handleSubmit:l=>y(null,null,function*(){var o;if(l.id!=null){yield O(Number(l.id),l);return}(yield U(l))&&(v.value={status:"active",isActive:!0},(o=m.value)==null||o.resetForm())}),handleFilter:l=>y(null,null,function*(){i.value=l,yield g()})}}const Ye=[{type:"selection",width:"30px",sortable:!1},{type:"index",width:40,headerRenderer:()=>a("No.")},{prop:"name",align:"left",sortable:!0,minWidth:160,headerRenderer:()=>a("Tool Name")},{prop:"slug",align:"left",sortable:!0,minWidth:140,headerRenderer:()=>a("Slug")},{prop:"toolType",align:"center",width:120,headerRenderer:()=>a("Tool Type"),cellRenderer:({row:e})=>q(j,{type:{function:"primary",plugin:"success",integration:"info",custom:"warning"}[e.toolType]||"info",size:"small"},()=>{var u;return(u=e.toolType)==null?void 0:u.toUpperCase()})},{prop:"version",align:"center",width:100,headerRenderer:()=>a("Version"),formatter:({version:e})=>e||"-"},{prop:"categories",align:"left",width:150,headerRenderer:()=>a("Categories"),cellRenderer:({row:e})=>!e.categories||e.categories.length===0?"-":q("div",{class:"flex flex-wrap gap-1"},e.categories.map(i=>q(j,{size:"small",type:"info"},()=>i.name)))},{prop:"isActive",align:"center",width:80,headerRenderer:()=>a("Active"),cellRenderer:({row:e})=>q(j,{type:e.isActive?"success":"danger",size:"small"},()=>e.isActive?a("Yes"):a("No"))},{prop:"status",align:"center",width:100,headerRenderer:()=>a("Status"),cellRenderer:({row:e})=>q(j,{type:e.status==="active"?"success":"danger",size:"small"},()=>{var i;return(i=e.status)==null?void 0:i.toUpperCase()})},{prop:"createdAt",width:160,headerRenderer:()=>a("Created at"),formatter:({createdAt:e})=>e?we(e).format("YYYY-MM-DD HH:mm"):"-"},{label:"",fixed:"right",width:140,slot:"operation",sortable:!1}],We={class:"main"},Ke={class:"ml-2"},je={class:"ml-2"},Je={class:"ml-2"},Qe={class:"ml-2"},Xe={class:"ml-2"},Ze={class:"ml-2"},et=xe({__name:"index",setup(e){const i=fe(()=>me(()=>import("./ModelToolDrawerForm-8rI2-LUV.js"),__vite__mapDeps([0,1,2,3,4,5,6,7]))),u=fe(()=>me(()=>import("./ModelToolFilterForm-D1dSa_dc.js"),__vite__mapDeps([8,1,2,3,4,5,9,7]))),T=h(),R=h(),{loading:w,filterRef:b,pagination:B,records:v,multipleSelection:m,handleBulkDelete:E,handleDelete:H,fnGetModelTools:M,fnHandlePageChange:g,fnHandleSelectionChange:U,fnHandleSortChange:O,fnHandleSizeChange:Z,filterVisible:S,drawerVisible:D,drawerValues:V,modelToolFormRef:G,handleSubmit:ee,handleFilter:L,handleBulkDestroy:Y,handleBulkRestore:te,handleDestroy:se,handleRestore:W,handleTest:K,handleValidate:oe}=Le();Re(()=>{Be(()=>{M()})});const ne=ae=>{V.value=Ve(ae,!0),D.value=!0};return(ae,f)=>{const _=P("IconifyIconOnline"),F=P("el-button"),l=P("el-tooltip"),n=P("el-dropdown-item"),o=P("el-dropdown-menu"),t=P("el-dropdown");return $(),N("div",We,[z("div",{ref_key:"contentRef",ref:R,class:Q(["flex",s(De)()?"flex-wrap":""])},[c(s(Ae),{class:"w-full",style:{transition:"width 220ms cubic-bezier(0.4, 0, 0.2, 1)"},title:s(a)("Model Tool Management"),columns:s(Ye),onRefresh:s(M),onFilter:f[1]||(f[1]=r=>S.value=!0)},{buttons:p(()=>[c(l,{content:s(a)("Create new"),placement:"top"},{default:p(()=>[c(F,{type:"text",class:"font-bold text-[16px]",disabled:!s(k)("model-tool.create"),onClick:f[0]||(f[0]=()=>{D.value=!0})},{default:p(()=>[c(_,{icon:s(k)("model-tool.create")?"flat-color-icons:plus":"icons8:plus",width:"18px"},null,8,["icon"])]),_:1},8,["disabled"])]),_:1},8,["content"]),s(b).isTrashed==="yes"?($(),N(J,{key:0},[c(l,{content:s(a)("Restore"),placement:"top"},{default:p(()=>[c(F,{type:"text",class:"font-bold text-[16px]",disabled:s(m).length===0||s(m).length>0&&!s(k)("model-tool.restore"),onClick:s(te)},{default:p(()=>[c(_,{icon:"tabler:restore",width:"18px",class:Q({"text-blue-600":s(m).length>0})},null,8,["class"])]),_:1},8,["disabled","onClick"])]),_:1},8,["content"]),c(l,{content:s(a)("Bulk Delete"),placement:"top"},{default:p(()=>[c(F,{type:"text",class:"font-bold text-[16px]",disabled:s(m).length===0||s(m).length>0&&!s(k)("model-tool.force-delete"),onClick:s(E)},{default:p(()=>[c(_,{icon:"tabler:trash-x-filled",width:"18px",class:Q({"text-red-700":s(m).length>0})},null,8,["class"])]),_:1},8,["disabled","onClick"])]),_:1},8,["content"])],64)):($(),N(J,{key:1},[s(b).isTrashed==="no"?($(),Se(l,{key:0,content:s(a)("Bulk Destroy"),placement:"top"},{default:p(()=>[c(F,{type:"text",class:"font-bold text-[16px]",disabled:s(m).length==0||s(m).length>0&&!s(k)("model-tool.destroy"),onClick:s(Y)},{default:p(()=>[c(_,{icon:"tabler:trash",width:"18px",class:Q({"text-red-700":s(m).length>0})},null,8,["class"])]),_:1},8,["disabled","onClick"])]),_:1},8,["content"])):le("",!0)],64))]),default:p(({size:r,dynamicColumns:ye})=>[c(s(Me),{ref_key:"tableRef",ref:T,"align-whole":"center","table-layout":"auto",loading:s(w),size:r,adaptive:"",border:"",adaptiveConfig:{offsetBottom:108},data:s(v),columns:ye,pagination:s(B),"header-cell-style":{background:"var(--el-fill-color-light)",color:"var(--el-text-color-primary)"},onSortChange:s(O),onPageSizeChange:s(Z),onPageCurrentChange:s(g),onSelectionChange:s(U)},{operation:p(({row:A})=>[c(t,{"split-button":"",trigger:"click",size:"small"},{dropdown:p(()=>[c(o,{class:"min-w-[130px]"},{default:p(()=>[c(n,{disabled:""},{default:p(()=>[pe(x(s(a)("Action")),1)]),_:1}),s(b).isTrashed!="yes"?($(),N(J,{key:0},[c(n,{disabled:!s(k)("model-tool.update"),onClick:I=>ne(A)},{default:p(()=>[c(_,{icon:"material-symbols:edit",class:"text-blue-600"}),z("span",Ke,x(s(a)("Edit")),1)]),_:2},1032,["disabled","onClick"]),c(n,{disabled:!s(k)("model-tool.test"),onClick:I=>s(K)(A)},{default:p(()=>[c(_,{icon:"material-symbols:play-arrow",class:"text-green-600"}),z("span",je,x(s(a)("Test Tool")),1)]),_:2},1032,["disabled","onClick"]),c(n,{disabled:!s(k)("model-tool.validate"),onClick:I=>s(oe)(A.id)},{default:p(()=>[c(_,{icon:"material-symbols:check-circle",class:"text-blue-600"}),z("span",Je,x(s(a)("Validate")),1)]),_:2},1032,["disabled","onClick"]),c(n,{disabled:!s(k)("model-tool.destroy"),onClick:I=>s(se)(A.id)},{default:p(()=>[c(_,{icon:"tabler:trash",class:"text-red-800"}),z("span",Qe,x(s(a)("Move to Trash")),1)]),_:2},1032,["disabled","onClick"])],64)):le("",!0),s(b).isTrashed=="yes"?($(),N(J,{key:1},[c(n,{disabled:!s(k)("model-tool.restore"),onClick:I=>s(W)(A.id)},{default:p(()=>[c(_,{icon:"tabler:restore",class:"text-red-800"}),z("span",Xe,x(s(a)("Restore")),1)]),_:2},1032,["disabled","onClick"]),c(n,{disabled:!s(k)("model-tool.force-delete"),onClick:I=>s(H)(A.id)},{default:p(()=>[c(_,{icon:"tabler:trash-x",class:"text-red-800"}),z("span",Ze,x(s(a)("Delete")),1)]),_:2},1032,["disabled","onClick"])],64)):le("",!0)]),_:2},1024)]),default:p(()=>[pe(x(s(a)("Action"))+" ",1)]),_:2},1024)]),_:2},1032,["loading","size","data","columns","pagination","onSortChange","onPageSizeChange","onPageCurrentChange","onSelectionChange"])]),_:1},8,["title","columns","onRefresh"])],2),c(s(i),{ref_key:"modelToolFormRef",ref:G,visible:s(D),"onUpdate:visible":f[2]||(f[2]=r=>X(D)?D.value=r:null),values:s(V),"onUpdate:values":f[3]||(f[3]=r=>X(V)?V.value=r:null),onSubmit:s(ee),onClose:f[4]||(f[4]=()=>{var r;(r=s(G).value)==null||r.resetForm(),V.value={status:"active",isActive:!0}})},null,8,["visible","values","onSubmit"]),c(s(u),{ref:"filterFormRef",visible:s(S),"onUpdate:visible":f[5]||(f[5]=r=>X(S)?S.value=r:null),values:s(b),"onUpdate:values":f[6]||(f[6]=r=>X(b)?b.value=r:null),onSubmit:s(L),onReset:f[7]||(f[7]=()=>{b.value={},s(M)()})},null,8,["visible","values","onSubmit"])])}}}),nt=ze(et,[["__scopeId","data-v-5ae5cbee"]]);export{nt as default};
