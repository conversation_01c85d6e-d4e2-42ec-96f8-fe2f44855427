<?php

namespace Modules\ChatBot\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WidgetConversationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'conversationId' => $this->uuid,
            'title' => $this->title,
            'bot' => [
                'name' => $this->bot->name,
                'logoUrl' => $this->bot->logo_url,
                'greetingMessage' => $this->bot->greeting_message,
            ],
        ];
    }
}
