const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/SidebarDrawerForm-nB_BsvKx.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/js/hooks-CuzZ-_om.js","static/js/auth-api-fYhl1v86.js","static/js/index-CQnBjWL2.js","static/js/_plugin-vue_export-helper-QGN-qG8u.js","static/css/plus-drawer-form-DaizY_En.css","static/js/SidebarFilterForm-BDnEYYDk.js"])))=>i.map(i=>d[i]);
var re=Object.defineProperty,se=Object.defineProperties;var ne=Object.getOwnPropertyDescriptors;var M=Object.getOwnPropertySymbols;var oe=Object.prototype.hasOwnProperty,le=Object.prototype.propertyIsEnumerable;var G=(s,o,t)=>o in s?re(s,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[o]=t,K=(s,o)=>{for(var t in o||(o={}))oe.call(o,t)&&G(s,t,o[t]);if(M)for(var t of M(o))le.call(o,t)&&G(s,t,o[t]);return s},L=(s,o)=>se(s,ne(o));var y=(s,o,t)=>new Promise((g,w)=>{var m=p=>{try{b(t.next(p))}catch(i){w(i)}},v=p=>{try{b(t.throw(p))}catch(i){w(i)}},b=p=>p.done?g(p.value):Promise.resolve(p.value).then(m,v);b((t=t.apply(s,o)).next())});import{r as f,g as ie,o as te,aA as j,ax as c,a8 as r,ay as Q,q as ue,e as ce,S as de,G as A,m as fe,n as O,D as I,E as h,N as k,k as a,aI as pe,Q as W,A as q,aJ as J,aL as ge,P as X,M as Y,L as me,a5 as ve,a4 as R,aK as Z,aM as ee,_ as be}from"./index-ZVLuktk4.js";import{g as ye,a as Se,b as Ce,d as we,u as he,c as _e}from"./auth-api-fYhl1v86.js";import{P as ke}from"./index-DMMM8XNs.js";import{columns as xe}from"./columns-BcjgZeLK.js";import{u as ae}from"./hooks-CuzZ-_om.js";import"./sortable.esm-7jaD_3Ar.js";import"./epTheme-DzIV6EF-.js";function ze(){const s=f(!1),o=f({isTrashed:!1}),t=ie({total:0,pageSize:20,currentPage:1,pageSizes:[10,20,50,100,200],background:!0,layout:"total, sizes, prev, pager, next, jumper"}),g=f([]),w=f([]),m=f({sortBy:"createdAt",sortOrder:"asc"}),v=f([]),b=f(),p=f(!1),i=f(!1),S=f({menuType:0}),u=()=>y(null,null,function*(){s.value=!0;try{const e=L(K({},o.value),{page:t.currentPage,per_page:t.pageSize,sort_by:m.value.sortBy,sort_order:m.value.sortOrder}),n=yield ye(e);w.value=j(n.data),t.total=n.total||0}catch(e){console.error("Error fetching sidebars:",e),c(r("Failed to fetch data"),{type:"error"})}finally{s.value=!1}}),C=()=>y(null,null,function*(){try{const e=yield Se();v.value=j(e.data)}catch(e){console.error("Error fetching sidebars dropdown:",e)}}),x=e=>y(null,null,function*(){try{s.value=!0;const n=yield _e(e);return n.success?(c(n.message||r("Create successful"),{type:"success"}),yield u(),!0):(c(n.message||r("Create failed"),{type:"error"}),!1)}catch(n){return console.error("Create sidebar error:",n),c(r("Create failed"),{type:"error"}),!1}finally{s.value=!1}}),B=(e,n)=>y(null,null,function*(){try{s.value=!0;const d=yield he(e,n);return d.success?(c(d.message||r("Update successful"),{type:"success"}),yield u(),!0):(c(d.message||r("Update failed"),{type:"error"}),!1)}catch(d){return console.error("Update sidebar error:",d),c(r("Update failed"),{type:"error"}),!1}finally{s.value=!1}}),_=e=>{g.value=e},T=e=>{m.value.sortBy=e.prop,m.value.sortOrder=e.order==="ascending"?"asc":"desc",u()},P=()=>{u()},F=e=>{t.pageSize=e,t.currentPage=1,u()},V=e=>y(null,null,function*(){try{yield Q.confirm(r("Are you sure you want to delete this item?"),r("Warning"),{confirmButtonText:r("OK"),cancelButtonText:r("Cancel"),type:"warning"}),s.value=!0;const n=yield we(e.id);n.success?(c(n.message||r("Delete successful"),{type:"success"}),yield u()):c(n.message||r("Delete failed"),{type:"error"})}catch(n){console.error("Delete sidebar error:",n),c(r("Delete failed"),{type:"error"})}finally{s.value=!1}}),E=()=>y(null,null,function*(){if(g.value.length===0){c(r("Please select items to delete"),{type:"warning"});return}try{yield Q.confirm(r("Are you sure you want to delete selected items?"),r("Warning"),{confirmButtonText:r("OK"),cancelButtonText:r("Cancel"),type:"warning"}),s.value=!0;const e=g.value.map(d=>d.id),n=yield Ce({ids:e});n.success?(c(n.message||r("Delete successful"),{type:"success"}),yield u(),g.value=[]):c(n.message||r("Delete failed"),{type:"error"})}catch(e){console.error("Bulk delete error:",e),c(r("Delete failed"),{type:"error"})}finally{s.value=!1}}),H=e=>y(null,null,function*(){var d,z;if(e.id!=null){(yield B(Number(e.id),e))&&(i.value=!1,S.value={menuType:0},(d=b.value)==null||d.resetForm());return}(yield x(e))&&(i.value=!1,S.value={menuType:0},(z=b.value)==null||z.resetForm())}),N=e=>y(null,null,function*(){o.value=e,t.currentPage=1,yield u()});return te(()=>{u(),C()}),{loading:s,filterRef:o,records:w,multipleSelection:g,pagination:t,sort:m,sidebarsDropdown:v,filterVisible:p,drawerVisible:i,drawerValues:S,sidebarFormRef:b,handleDelete:V,handleBulkDelete:E,fnGetSidebars:u,fnGetSidebarsDropdown:C,fnHandleSelectionChange:_,fnHandleSortChange:T,fnHandlePaginationChange:P,fnHandleSizeChange:F,handleSubmit:H,handleFilter:N}}const De={class:"main"},Re={class:"flex items-center gap-2"},Be={class:"flex justify-end mt-4"},Te=ue({__name:"index",setup(s){const o=Z(()=>ee(()=>import("./SidebarDrawerForm-nB_BsvKx.js"),__vite__mapDeps([0,1,2,3,4,5,6,7]))),t=Z(()=>ee(()=>import("./SidebarFilterForm-BDnEYYDk.js"),__vite__mapDeps([8,1,2,3,5,6])));ce(()=>["w-[22px]","h-[22px]","flex","justify-center","items-center","outline-none","rounded-[4px]","cursor-pointer","transition-colors","hover:bg-[#0000000f]","dark:hover:bg-[#ffffff1f]","dark:hover:text-[#ffffffd9]"]);const g=f(),w=f(),{loading:m,filterRef:v,records:b,multipleSelection:p,pagination:i,filterVisible:S,drawerVisible:u,drawerValues:C,sidebarFormRef:x,handleDelete:B,fnGetSidebars:_,fnHandleSelectionChange:T,fnHandleSortChange:P,fnHandlePaginationChange:F,fnHandleSizeChange:V,handleSubmit:E,handleFilter:H}=ze();return te(()=>{de(()=>{_()})}),(N,e)=>{var U;const n=A("TableButtons"),d=A("el-button"),z=A("el-pagination");return O(),fe("div",De,[I("div",{ref_key:"contentRef",ref:w,class:me(["flex",a(ve)()?"flex-wrap":""])},[h(a(ke),{class:"w-full",style:{transition:"width 220ms cubic-bezier(0.4, 0, 0.2, 1)"},border:"",title:a(r)("Sidebar Management"),columns:a(xe),isExpandAll:!1,tableRef:(U=g.value)==null?void 0:U.getTableRef(),onRefresh:a(_),onFilter:e[1]||(e[1]=l=>S.value=!0)},{buttons:k(()=>[h(n,{"multiple-selection":a(p),"is-trashed":"disabled","create-permission":"create-sidebars",onCreate:e[0]||(e[0]=l=>u.value=!0)},null,8,["multiple-selection"])]),default:k(({size:l,dynamicColumns:D})=>[h(a(pe),{ref_key:"tableRef",ref:g,"align-whole":"center","table-layout":"auto","row-key":"id",adaptive:"",border:"",loading:a(m),size:l,adaptiveConfig:{offsetBottom:108},data:a(b),columns:D,"header-cell-style":{background:"var(--el-fill-color-light)",color:"var(--el-text-color-primary)"},onSelectionChange:a(T),onSortChange:a(P)},{operation:k(({row:$})=>[I("div",Re,[a(J)("sidebar:update")?(O(),W(d,{key:0,type:"primary",size:"small",icon:a(ae)("ep:edit"),onClick:()=>{C.value=a(ge)($),u.value=!0}},{default:k(()=>[X(Y(a(r)("Edit")),1)]),_:2},1032,["icon","onClick"])):q("",!0),a(J)("sidebar:delete")?(O(),W(d,{key:1,type:"danger",size:"small",icon:a(ae)("ep:delete"),onClick:Pe=>a(B)($)},{default:k(()=>[X(Y(a(r)("Delete")),1)]),_:2},1032,["icon","onClick"])):q("",!0)])]),_:2},1032,["loading","size","data","columns","onSelectionChange","onSortChange"])]),_:1},8,["title","columns","tableRef","onRefresh"]),I("div",Be,[h(z,{"current-page":a(i).currentPage,"onUpdate:currentPage":e[2]||(e[2]=l=>a(i).currentPage=l),"page-size":a(i).pageSize,"onUpdate:pageSize":e[3]||(e[3]=l=>a(i).pageSize=l),"page-sizes":a(i).pageSizes,total:a(i).total,background:a(i).background,layout:a(i).layout,onSizeChange:a(V),onCurrentChange:a(F)},null,8,["current-page","page-size","page-sizes","total","background","layout","onSizeChange","onCurrentChange"])])],2),h(a(o),{ref_key:"sidebarFormRef",ref:x,visible:a(u),"onUpdate:visible":e[4]||(e[4]=l=>R(u)?u.value=l:null),values:a(C),"onUpdate:values":e[5]||(e[5]=l=>R(C)?C.value=l:null),onSubmit:a(E),onClose:e[6]||(e[6]=l=>{var D;(D=a(x).value)==null||D.resetForm(),C.value={menuType:0}})},null,8,["visible","values","onSubmit"]),h(a(t),{ref:"filterFormRef",visible:a(S),"onUpdate:visible":e[7]||(e[7]=l=>R(S)?S.value=l:null),values:a(v),"onUpdate:values":e[8]||(e[8]=l=>R(v)?v.value=l:null),onSubmit:a(H),onReset:e[9]||(e[9]=l=>{v.value={},a(_)()})},null,8,["visible","values","onSubmit"])])}}}),Ne=be(Te,[["__scopeId","data-v-2e80d430"]]);export{Ne as default};
