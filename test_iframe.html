<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Widget Iframe</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .iframe-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .iframe-wrapper {
            flex: 1;
            min-width: 300px;
        }
        
        iframe {
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Widget Iframe Testing</h1>
        
        <!-- Test 1: Basic Iframe -->
        <div class="test-section">
            <h2>Test 1: Basic Widget Iframe</h2>
            <p>Basic iframe với bot UUID và API key:</p>
            
            <div class="code-block">
&lt;iframe 
    src="http://127.0.0.1:8000/widget?bot_uuid=a716602f-4ccb-47f8-b24d-2421ea958fe7&api_key=pk_test_key"
    width="400" 
    height="600" 
    frameborder="0"
    title="Chatbot Widget"&gt;
&lt;/iframe&gt;
            </div>
            
            <div class="iframe-container">
                <div class="iframe-wrapper">
                    <iframe 
                        src="http://127.0.0.1:8000/widget?bot_uuid=a716602f-4ccb-47f8-b24d-2421ea958fe7&api_key=pk_test_key"
                        width="400" 
                        height="600" 
                        frameborder="0"
                        title="Chatbot Widget">
                    </iframe>
                </div>
            </div>
            
            <div id="status1" class="status" style="display: none;"></div>
        </div>
        
        <!-- Test 2: Customized Iframe -->
        <div class="test-section">
            <h2>Test 2: Customized Widget Iframe</h2>
            <p>Iframe với custom theme và options:</p>
            
            <div class="code-block">
&lt;iframe 
    src="http://127.0.0.1:8000/widget?bot_uuid=a716602f-4ccb-47f8-b24d-2421ea958fe7&api_key=pk_test_key&theme=dark&show_header=1&auto_open=0"
    width="350" 
    height="500" 
    frameborder="0"
    title="Dark Theme Chatbot"&gt;
&lt;/iframe&gt;
            </div>
            
            <div class="iframe-container">
                <div class="iframe-wrapper">
                    <iframe 
                        src="http://127.0.0.1:8000/widget?bot_uuid=a716602f-4ccb-47f8-b24d-2421ea958fe7&api_key=pk_test_key&theme=dark&show_header=1&auto_open=0"
                        width="350" 
                        height="500" 
                        frameborder="0"
                        title="Dark Theme Chatbot">
                    </iframe>
                </div>
            </div>
        </div>
        
        <!-- Test 3: Multiple Iframes -->
        <div class="test-section">
            <h2>Test 3: Multiple Widget Iframes</h2>
            <p>Nhiều iframe cùng lúc để test performance:</p>
            
            <div class="iframe-container">
                <div class="iframe-wrapper">
                    <h4>Widget 1 - Light Theme</h4>
                    <iframe 
                        src="http://127.0.0.1:8000/widget?bot_uuid=a716602f-4ccb-47f8-b24d-2421ea958fe7&api_key=pk_test_key&theme=light"
                        width="300" 
                        height="400" 
                        frameborder="0"
                        title="Light Theme Widget">
                    </iframe>
                </div>
                
                <div class="iframe-wrapper">
                    <h4>Widget 2 - Dark Theme</h4>
                    <iframe 
                        src="http://127.0.0.1:8000/widget?bot_uuid=a716602f-4ccb-47f8-b24d-2421ea958fe7&api_key=pk_test_key&theme=dark"
                        width="300" 
                        height="400" 
                        frameborder="0"
                        title="Dark Theme Widget">
                    </iframe>
                </div>
                
                <div class="iframe-wrapper">
                    <h4>Widget 3 - Compact</h4>
                    <iframe 
                        src="http://127.0.0.1:8000/widget?bot_uuid=a716602f-4ccb-47f8-b24d-2421ea958fe7&api_key=pk_test_key&show_header=0"
                        width="300" 
                        height="350" 
                        frameborder="0"
                        title="Compact Widget">
                    </iframe>
                </div>
            </div>
        </div>
        
        <!-- Test 4: Dynamic Iframe -->
        <div class="test-section">
            <h2>Test 4: Dynamic Iframe Creation</h2>
            <p>Tạo iframe động bằng JavaScript:</p>
            
            <button class="btn" onclick="createDynamicIframe()">Tạo Widget Iframe</button>
            <button class="btn" onclick="removeDynamicIframe()">Xóa Widget Iframe</button>
            
            <div id="dynamic-iframe-container" style="margin-top: 20px;"></div>
        </div>
        
        <!-- Test 5: Iframe Communication -->
        <div class="test-section">
            <h2>Test 5: Iframe Communication</h2>
            <p>Test communication giữa parent window và iframe:</p>
            
            <button class="btn" onclick="sendMessageToIframe()">Gửi Message tới Iframe</button>
            <button class="btn" onclick="listenToIframe()">Listen Messages từ Iframe</button>
            
            <div id="communication-log" class="code-block" style="height: 150px; overflow-y: auto;">
                <div>Communication log sẽ hiển thị ở đây...</div>
            </div>
            
            <iframe 
                id="communication-iframe"
                src="http://127.0.0.1:8000/widget?bot_uuid=a716602f-4ccb-47f8-b24d-2421ea958fe7&api_key=pk_test_key"
                width="400" 
                height="300" 
                frameborder="0"
                title="Communication Test Widget">
            </iframe>
        </div>
        
        <!-- Test 6: Error Handling -->
        <div class="test-section">
            <h2>Test 6: Error Handling</h2>
            <p>Test error handling với invalid parameters:</p>
            
            <div class="iframe-container">
                <div class="iframe-wrapper">
                    <h4>Invalid Bot UUID</h4>
                    <iframe 
                        src="http://127.0.0.1:8000/widget?bot_uuid=invalid-uuid"
                        width="300" 
                        height="300" 
                        frameborder="0"
                        title="Error Test 1">
                    </iframe>
                </div>
                
                <div class="iframe-wrapper">
                    <h4>Missing Parameters</h4>
                    <iframe 
                        src="http://127.0.0.1:8000/widget"
                        width="300" 
                        height="300" 
                        frameborder="0"
                        title="Error Test 2">
                    </iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dynamic iframe creation
        function createDynamicIframe() {
            const container = document.getElementById('dynamic-iframe-container');
            
            const iframe = document.createElement('iframe');
            iframe.src = 'http://127.0.0.1:8000/widget?bot_uuid=a716602f-4ccb-47f8-b24d-2421ea958fe7&api_key=pk_test_key&user_id=dynamic_user_' + Date.now();
            iframe.width = '400';
            iframe.height = '500';
            iframe.frameBorder = '0';
            iframe.title = 'Dynamic Widget';
            iframe.style.border = 'none';
            iframe.style.borderRadius = '8px';
            iframe.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
            
            container.innerHTML = '';
            container.appendChild(iframe);
            
            logCommunication('Dynamic iframe created');
        }
        
        function removeDynamicIframe() {
            const container = document.getElementById('dynamic-iframe-container');
            container.innerHTML = '';
            logCommunication('Dynamic iframe removed');
        }
        
        // Iframe communication
        function sendMessageToIframe() {
            const iframe = document.getElementById('communication-iframe');
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'parent-message',
                    data: { message: 'Hello from parent window!', timestamp: new Date().toISOString() }
                }, '*');
                logCommunication('Sent message to iframe: Hello from parent window!');
            }
        }
        
        function listenToIframe() {
            window.addEventListener('message', function(event) {
                if (event.data && event.data.type) {
                    logCommunication('Received from iframe: ' + JSON.stringify(event.data));
                }
            });
            logCommunication('Started listening to iframe messages');
        }
        
        function logCommunication(message) {
            const log = document.getElementById('communication-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }
        
        // Auto-start listening
        listenToIframe();
        
        // Test iframe loading
        window.addEventListener('load', function() {
            setTimeout(function() {
                const iframes = document.querySelectorAll('iframe');
                let loadedCount = 0;
                let errorCount = 0;
                
                iframes.forEach(function(iframe, index) {
                    iframe.addEventListener('load', function() {
                        loadedCount++;
                        logCommunication(`Iframe ${index + 1} loaded successfully`);
                    });
                    
                    iframe.addEventListener('error', function() {
                        errorCount++;
                        logCommunication(`Iframe ${index + 1} failed to load`);
                    });
                });
                
                setTimeout(function() {
                    logCommunication(`Total iframes: ${iframes.length}, Loaded: ${loadedCount}, Errors: ${errorCount}`);
                }, 5000);
            }, 1000);
        });
    </script>
</body>
</html>
