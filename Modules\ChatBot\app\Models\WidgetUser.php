<?php

namespace Modules\ChatBot\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class WidgetUser extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'widget_users';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'widget_id',
        'session_id',
        'ip_address',
        'user_agent',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get all conversations for this widget user.
     */
    public function conversations(): MorphMany
    {
        return $this->morphMany(Conversation::class, 'owner');
    }

    /**
     * Get the display name for this widget user.
     */
    public function getDisplayNameAttribute(): string
    {
        return "Widget User {$this->widget_id}";
    }

    /**
     * Get the full name for this widget user.
     */
    public function getFullNameAttribute(): string
    {
        return $this->getDisplayNameAttribute();
    }

    /**
     * Get the name for this widget user.
     */
    public function getNameAttribute(): string
    {
        return $this->getDisplayNameAttribute();
    }

    /**
     * Create or get widget user by widget ID.
     */
    public static function createOrGet(string $widgetId, array $metadata = []): self
    {
        return static::firstOrCreate(
            ['widget_id' => $widgetId],
            [
                'session_id' => session()->getId(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'metadata' => $metadata,
            ]
        );
    }
}
