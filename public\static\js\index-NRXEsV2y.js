const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/UserDrawerForm-CeEwCEID.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/js/auth-api-CR4e_tAC.js","static/js/index-CQnBjWL2.js","static/js/_plugin-vue_export-helper-QGN-qG8u.js","static/css/plus-drawer-form-DaizY_En.css","static/js/UserFilterForm-SXljmA0J.js"])))=>i.map(i=>d[i]);
var me=Object.defineProperty,ge=Object.defineProperties;var ye=Object.getOwnPropertyDescriptors;var re=Object.getOwnPropertySymbols;var he=Object.prototype.hasOwnProperty,xe=Object.prototype.propertyIsEnumerable;var le=(s,o,r)=>o in s?me(s,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):s[o]=r,J=(s,o)=>{for(var r in o||(o={}))he.call(o,r)&&le(s,r,o[r]);if(re)for(var r of re(o))xe.call(o,r)&&le(s,r,o[r]);return s},X=(s,o)=>ge(s,ye(o));var y=(s,o,r)=>new Promise((k,D)=>{var R=C=>{try{B(r.next(C))}catch(f){D(f)}},x=C=>{try{B(r.throw(C))}catch(f){D(f)}},B=C=>C.done?k(C.value):Promise.resolve(C.value).then(R,x);B((r=r.apply(s,o)).next())});import{aN as _,az as $,r as v,g as be,aA as oe,ax as c,a8 as t,ay as T,ai as p,aB as ve,aC as w,aD as we,q as Ce,o as ke,S as _e,m as I,n as A,D as H,E as i,N as u,k as e,aI as Re,G as V,P as ie,M as E,I as N,aJ as b,L as M,Q as Be,A as De,a5 as Pe,a4 as G,aK as ce,aL as Se,aM as de,_ as Ue}from"./index-ZVLuktk4.js";import{g as Te}from"./auth-api-CR4e_tAC.js";import{P as Ae}from"./index-DMMM8XNs.js";import"./sortable.esm-7jaD_3Ar.js";import"./epTheme-DzIV6EF-.js";const Ee=s=>_.request("get","/api/auth/users",{params:s}),$e=s=>_.request("post","/api/auth/users",{data:$(s)}),ze=(s,o)=>_.request("put",`/api/auth/users/${s}`,{data:$(o)}),Fe=s=>_.request("delete",`/api/auth/users/${s}/delete`),Ie=s=>_.request("delete","/api/auth/users/bulk/delete",{data:$(s)}),He=s=>_.request("delete",`/api/auth/users/${s}/force`),Ve=s=>_.request("delete","/api/auth/users/bulk/force",{data:$(s)}),qe=s=>_.request("put",`/api/auth/users/${s}/restore`),Le=s=>_.request("put","/api/auth/users/bulk/restore",{data:$(s)});function Ne(){const s=v(!1),o=v({isTrashed:"no"}),r=be({total:0,pageSize:20,currentPage:1,pageSizes:[10,20,50,100,200],background:!0,layout:"total, sizes, prev, pager, next, jumper"}),k=v([]),D=v([]),R=v({sortBy:"createdAt",sortOrder:"asc"}),x=v([]),B=v(),C=v(!1),f=v(!1),q=v({status:"active"}),m=()=>y(null,null,function*(){try{s.value=!0;const n=yield Ee($(X(J({},o.value),{order:`${R.value.sortBy}:${R.value.sortOrder}`,page:r.currentPage,limit:r.pageSize})));D.value=oe(n.data),r.total=n.total}catch(n){console.error("Error fetching users:",n),c(t("Failed to fetch users"),{type:"error"})}finally{s.value=!1}}),z=()=>y(null,null,function*(){try{const n=yield Te();x.value=oe(n.data)}catch(n){console.error("Error fetching roles dropdown:",n)}}),O=n=>{k.value=n},W=()=>{m()},j=n=>{r.pageSize=n,r.currentPage=1,m()},Y=(n,l)=>y(null,null,function*(){R.value={sortBy:n,sortOrder:l},yield m()}),S=n=>y(null,null,function*(){var l,a;try{s.value=!0;const d=yield $e(n);return d.success?(c(d.message||t("Create successful"),{type:"success"}),yield m(),f.value=!1,!0):(c(d.message||t("Create failed"),{type:"error"}),!1)}catch(d){return c(((a=(l=d.response)==null?void 0:l.data)==null?void 0:a.message)||(d==null?void 0:d.message)||t("Create failed"),{type:"error"}),!1}finally{s.value=!1}}),P=(n,l)=>y(null,null,function*(){var a,d;try{s.value=!0;const g=yield ze(n,l);return g.success?(c(g.message||t("Update successful"),{type:"success"}),yield m(),!0):(c(g.message||t("Update failed"),{type:"error"}),!1)}catch(g){return c(((d=(a=g.response)==null?void 0:a.data)==null?void 0:d.message)||(g==null?void 0:g.message)||t("Update failed"),{type:"error"}),!1}finally{s.value=!1}});return{loading:s,filterRef:o,pagination:r,records:D,multipleSelection:k,sort:R,rolesDropdown:x,handleBulkDelete:n=>y(null,null,function*(){const l=n||k.value.map(a=>a.id);if(l.length===0){c(t("Please select items to delete"),{type:"warning"});return}try{yield T.confirm(t("Are you sure you want to delete selected items?"),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Ie({ids:l}),c(t("Deleted successfully"),{type:"success"}),yield m()}catch(a){a!=="cancel"&&(console.error("Error bulk deleting users:",a),c(t("Delete failed"),{type:"error"}))}}),handleDelete:n=>y(null,null,function*(){try{yield T.confirm(t("Are you sure you want to delete this item?"),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Fe(n.id),c(t("Deleted successfully"),{type:"success"}),yield m()}catch(l){l!=="cancel"&&(console.error("Error deleting user:",l),c(t("Delete failed"),{type:"error"}))}}),handlePermanentDelete:n=>y(null,null,function*(){try{yield T.confirm(t("Are you sure you want to permanently delete this item? This action cannot be undone."),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield He(n.id),c(t("Permanently deleted successfully"),{type:"success"}),yield m()}catch(l){l!=="cancel"&&(console.error("Error permanently deleting user:",l),c(t("Permanent delete failed"),{type:"error"}))}}),handleBulkPermanentDelete:n=>y(null,null,function*(){const l=n||k.value.map(a=>a.id);if(l.length===0){c(t("Please select items to delete"),{type:"warning"});return}try{yield T.confirm(t("Are you sure you want to permanently delete selected items? This action cannot be undone."),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Ve({ids:l}),c(t("Permanently deleted successfully"),{type:"success"}),yield m()}catch(a){a!=="cancel"&&(console.error("Error bulk permanently deleting users:",a),c(t("Permanent delete failed"),{type:"error"}))}}),handleRestore:n=>y(null,null,function*(){try{yield T.confirm(t("Are you sure you want to restore this item?"),t("Confirm"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"info"}),yield qe(n.id),c(t("Restored successfully"),{type:"success"}),yield m()}catch(l){l!=="cancel"&&(console.error("Error restoring user:",l),c(t("Restore failed"),{type:"error"}))}}),handleBulkRestore:n=>y(null,null,function*(){const l=n||k.value.map(a=>a.id);if(l.length===0){c(t("Please select items to restore"),{type:"warning"});return}try{yield T.confirm(t("Are you sure you want to restore selected items?"),t("Confirm"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"info"}),yield Le({ids:l}),c(t("Restored successfully"),{type:"success"}),yield m()}catch(a){a!=="cancel"&&(console.error("Error bulk restoring users:",a),c(t("Restore failed"),{type:"error"}))}}),fnGetUsers:m,fnGetRolesDropdown:z,fnHandlePageChange:W,fnHandleSelectionChange:O,fnHandleSortChange:Y,fnHandleSizeChange:j,filterVisible:C,drawerVisible:f,drawerValues:q,userFormRef:B,handleSubmit:n=>y(null,null,function*(){var a;let l=!1;n.id!=null?l=yield P(Number(n.id),n):(l=yield S(n),l&&(q.value={status:"active"},(a=B.value)==null||a.resetForm()))}),handleFilter:n=>y(null,null,function*(){o.value=n,yield m()})}}const Me=[{type:"selection",width:"30px",sortable:!1},{label:"",prop:"avatar",width:100,cellRenderer:({row:s})=>p("div",{class:"flex items-center justify-center"},[p(ve,{size:60,src:s.avatar,icon:"User",class:"border border-gray-200"})])},{prop:"username",align:"left",sortable:!0,minWidth:280,headerRenderer:()=>t("User"),cellRenderer:({row:s})=>{const o={male:{icon:"mdi:gender-male",class:"text-blue-500"},female:{icon:"mdi:gender-female",class:"text-pink-500"},other:{icon:"mdi:gender-transgender",class:"text-purple-500"}},r=o[s.gender]||o.other;return p("div",{class:"flex flex-col justify-between"},[p("div",{class:""},[s.email&&p("div",{class:"flex text-xs font-medium text-gray-900"},[s.fullName||t("Unnamed"),s.gender&&p(w,{icon:r.icon,class:`ml-1.5 w-3 h-3 ${r.class}`})]),p("div",{class:"flex items-center"},[p("span",{class:"text-sm text-gray-500 mr-2"},`@${s.username}`)]),s.email&&p("div",{class:"text-xs text-gray-500 mt-1 flex items-center"},[p(w,{icon:"mdi:email-outline",class:"w-4 h-4 mr-1.5"}),p("span",s.email)]),s.phone&&p("div",{class:"text-xs text-gray-500 mt-1 flex items-center"},[p(w,{icon:"mdi:phone-outline",class:"w-4 h-4 mr-1.5"}),p("span",s.phone)])])])}},{prop:"lastLoginAt",align:"center",width:180,headerRenderer:()=>t("Last Login At"),formatter:({lastLoginAt:s})=>s?we(s).format("YYYY-MM-DD HH:mm"):"—"},{prop:"lastLoginIp",align:"center",width:150,headerRenderer:()=>t("Last Login IP")},{prop:"status",sortable:!1,align:"center",width:100,headerRenderer:()=>t("Status"),cellRenderer:({row:s})=>{const o={active:{type:"success",text:t("Active"),class:"bg-green-100 text-green-800",icon:"ri:checkbox-circle-fill",iconClass:"text-green-600"},inactive:{type:"danger",text:t("Inactive"),class:"bg-red-100 text-red-800",icon:"ri:close-circle-fill",iconClass:"text-red-600"}},r=o[s.status]||o.active;return p("span",{class:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${r.class}`},[p(w,{icon:r.icon,class:`w-3 h-3 mr-1.5 ${r.iconClass}`}),r.text])}},{label:"",fixed:"right",width:140,slot:"operation",sortable:!1}],Ge={class:"main"},Oe={class:"ml-2"},We={class:"ml-2"},je={class:"ml-2"},Ye={class:"ml-2"},Ke=Ce({__name:"index",setup(s){const o=ce(()=>de(()=>import("./UserDrawerForm-CeEwCEID.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),r=ce(()=>de(()=>import("./UserFilterForm-SXljmA0J.js"),__vite__mapDeps([7,1,2,4,5,6]))),k=v(),D=v(),{loading:R,filterRef:x,pagination:B,records:C,multipleSelection:f,handleBulkDelete:q,handleDelete:m,fnGetUsers:z,fnHandlePageChange:O,fnHandleSelectionChange:W,fnHandleSortChange:j,fnHandleSizeChange:Y,filterVisible:S,drawerVisible:P,drawerValues:U,userFormRef:K,handleSubmit:Z,handleFilter:ee,handleBulkPermanentDelete:te,handleBulkRestore:se,handlePermanentDelete:ae,handleRestore:ne}=Ne(),n=l=>{var d;const a=((d=l.roles)==null?void 0:d.map(g=>Number(g.id)))||[];U.value=X(J({},Se(l,!0)),{roles:a}),P.value=!0};return ke(()=>{_e(()=>{z()})}),(l,a)=>{const d=V("el-button"),g=V("el-tooltip"),F=V("el-dropdown-item"),ue=V("el-dropdown-menu"),fe=V("el-dropdown");return A(),I("div",Ge,[H("div",{ref_key:"contentRef",ref:D,class:M(["flex",e(Pe)()?"flex-wrap":""])},[i(e(Ae),{class:"!w-full",style:{transition:"width 220ms cubic-bezier(0.4, 0, 0.2, 1)"},title:e(t)("User Management"),columns:e(Me),onRefresh:e(z),onFilter:a[4]||(a[4]=h=>S.value=!0)},{buttons:u(()=>[i(g,{content:e(t)("Create new"),placement:"top"},{default:u(()=>[i(d,{type:"text",class:"font-bold text-[16px]",disabled:!e(b)("user.create"),onClick:a[0]||(a[0]=()=>{P.value=!0})},{default:u(()=>[i(e(w),{icon:e(b)("user.create")?"flat-color-icons:plus":"icons8:plus",width:"18px"},null,8,["icon"])]),_:1},8,["disabled"])]),_:1},8,["content"]),e(x).isTrashed==="yes"?(A(),I(N,{key:0},[i(g,{content:e(t)("Restore"),placement:"top"},{default:u(()=>[i(d,{type:"text",class:"font-bold text-[16px]",disabled:e(f).length===0||e(f).length>0&&!e(b)("user.delete"),onClick:a[1]||(a[1]=()=>e(se)())},{default:u(()=>[i(e(w),{icon:"tabler:restore",width:"18px",class:M({"text-amber-800":e(f).length>0&&e(b)("user.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"]),i(g,{content:e(t)("Bulk Destroy"),placement:"top"},{default:u(()=>[i(d,{type:"text",class:"font-bold text-[16px]",disabled:e(f).length===0||e(f).length>0&&!e(b)("user.destroy"),onClick:a[2]||(a[2]=()=>e(te)())},{default:u(()=>[i(e(w),{icon:"tabler:trash-x-filled",width:"18px",class:M({"text-red-700":e(f).length>0&&e(b)("user.destroy")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])],64)):(A(),I(N,{key:1},[e(x).isTrashed==="no"?(A(),Be(g,{key:0,content:e(t)("Bulk Delete"),placement:"top"},{default:u(()=>[i(d,{type:"text",class:"font-bold text-[16px]",disabled:e(f).length==0||e(f).length>0&&!e(b)("user.delete"),onClick:a[3]||(a[3]=()=>e(q)())},{default:u(()=>[i(e(w),{icon:"tabler:trash",width:"18px",class:M({"text-red-800":e(f).length>0&&e(b)("user.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])):De("",!0)],64))]),default:u(({size:h,dynamicColumns:pe})=>[i(e(Re),{ref_key:"tableRef",ref:k,"align-whole":"center","table-layout":"auto",loading:e(R),size:h,adaptive:"",adaptiveConfig:{offsetBottom:108},data:e(C),columns:pe,pagination:e(B),"header-cell-style":{background:"var(--el-fill-color-light)",color:"var(--el-text-color-primary)"},onSortChange:e(j),onPageSizeChange:e(Y),onPageCurrentChange:e(O),onSelectionChange:e(W)},{operation:u(({row:L})=>[i(fe,{"split-button":"",trigger:"click",size:"small"},{dropdown:u(()=>[i(ue,{class:"min-w-[130px]"},{default:u(()=>[i(F,{disabled:""},{default:u(()=>[ie(E(e(t)("Action")),1)]),_:1}),e(x).isTrashed=="no"?(A(),I(N,{key:0},[i(F,{disabled:!e(b)("user.edit"),onClick:Q=>n(L)},{default:u(()=>[i(e(w),{icon:"material-symbols:edit",class:"text-blue-800"}),H("span",Oe,E(e(t)("Edit")),1)]),_:2},1032,["disabled","onClick"]),i(F,{disabled:!e(b)("user.delete"),onClick:Q=>e(m)(L)},{default:u(()=>[i(e(w),{icon:"tabler:trash",class:"text-red-800"}),H("span",We,E(e(t)("Delete")),1)]),_:2},1032,["disabled","onClick"])],64)):(A(),I(N,{key:1},[i(F,{disabled:!e(b)("user.delete"),onClick:Q=>e(ne)(L)},{default:u(()=>[i(e(w),{icon:"tabler:restore",class:"text-red-800"}),H("span",je,E(e(t)("Restore")),1)]),_:2},1032,["disabled","onClick"]),i(F,{disabled:!e(b)("user.destroy"),onClick:Q=>e(ae)(L)},{default:u(()=>[i(e(w),{icon:"tabler:trash-x",class:"text-red-800"}),H("span",Ye,E(e(t)("Destroy")),1)]),_:2},1032,["disabled","onClick"])],64))]),_:2},1024)]),default:u(()=>[ie(E(e(t)("Action"))+" ",1)]),_:2},1024)]),_:2},1032,["loading","size","data","columns","pagination","onSortChange","onPageSizeChange","onPageCurrentChange","onSelectionChange"])]),_:1},8,["title","columns","onRefresh"])],2),i(e(o),{ref_key:"userFormRef",ref:K,visible:e(P),"onUpdate:visible":a[5]||(a[5]=h=>G(P)?P.value=h:null),values:e(U),"onUpdate:values":a[6]||(a[6]=h=>G(U)?U.value=h:null),onSubmit:e(Z),onClose:a[7]||(a[7]=()=>{var h;(h=e(K))==null||h.resetForm(),U.value={status:"active"}})},null,8,["visible","values","onSubmit"]),i(e(r),{ref:"filterFormRef",visible:e(S),"onUpdate:visible":a[8]||(a[8]=h=>G(S)?S.value=h:null),values:e(x),"onUpdate:values":a[9]||(a[9]=h=>G(x)?x.value=h:null),onSubmit:e(ee),onReset:a[10]||(a[10]=()=>{x.value={isTrashed:"no"},e(z)()})},null,8,["visible","values","onSubmit"])])}}}),st=Ue(Ke,[["__scopeId","data-v-debd8c2f"]]);export{st as default};
