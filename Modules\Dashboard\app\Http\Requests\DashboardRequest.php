<?php

namespace Modules\Dashboard\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DashboardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'period' => 'sometimes|string|in:day,week,month,year',
            'timezone' => 'sometimes|string|timezone',
            'include_charts' => 'sometimes|boolean',
            'include_stats' => 'sometimes|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'period.in' => 'The period must be one of: day, week, month, year',
            'timezone.timezone' => 'The timezone must be a valid timezone identifier',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'period' => 'time period',
            'timezone' => 'timezone',
            'include_charts' => 'include charts flag',
            'include_stats' => 'include statistics flag',
        ];
    }
}
