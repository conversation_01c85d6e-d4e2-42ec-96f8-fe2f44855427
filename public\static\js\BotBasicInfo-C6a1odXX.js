var M=Object.defineProperty,N=Object.defineProperties;var g=Object.getOwnPropertyDescriptors;var h=Object.getOwnPropertySymbols;var y=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable;var B=(t,l,e)=>l in t?M(t,l,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[l]=e,I=(t,l)=>{for(var e in l||(l={}))y.call(l,e)&&B(t,e,l[e]);if(h)for(var e of h(l))D.call(l,e)&&B(t,e,l[e]);return t},x=(t,l)=>N(t,g(l));import{q as E,G as d,m as A,n as m,D as p,E as a,M as _,k as i,a8 as c,N as n,I as U,J as F,Q as q,_ as G}from"./index-ZVLuktk4.js";const J={class:"card"},L={class:"section-title"},P={class:"option-content"},Q={class:"option-name"},S={class:"option-description"},$=E({__name:"BotBasicInfo",props:{values:{},aiModels:{}},emits:["update:values"],setup(t,{emit:l}){const e=t,k=l,r=(f,s)=>{k("update:values",x(I({},e.values),{[f]:s}))};return(f,s)=>{const v=d("el-input"),u=d("el-form-item"),b=d("el-col"),w=d("el-option"),C=d("el-select"),V=d("el-row");return m(),A("div",J,[p("h2",L,_(i(c)("Basic Information")),1),a(V,{gutter:20},{default:n(()=>[a(b,{xs:24,sm:12},{default:n(()=>[a(u,{label:i(c)("AI Assistant Name"),prop:"name"},{default:n(()=>[a(v,{"model-value":e.values.name,clearable:"",placeholder:i(c)("Example: Administrative Procedure Consultant"),"onUpdate:modelValue":s[0]||(s[0]=o=>r("name",o))},null,8,["model-value","placeholder"])]),_:1},8,["label"])]),_:1}),a(b,{xs:24,sm:12},{default:n(()=>[a(u,{label:i(c)("AI Model"),prop:"model"},{default:n(()=>[a(C,{"model-value":e.values.model,class:"w-full",placeholder:"Chọn mô hình AI","popper-class":"multi-line-select-dropdown",teleported:!1,"onUpdate:modelValue":s[1]||(s[1]=o=>r("model",o))},{default:n(()=>[(m(!0),A(U,null,F(e.aiModels,o=>(m(),q(w,{key:o.value,label:o.label,value:o.value},{default:n(()=>[p("div",P,[p("div",Q,_(o.label),1),p("div",S,_(o.description),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["model-value"])]),_:1},8,["label"])]),_:1})]),_:1}),a(u,{label:i(c)("Description")},{default:n(()=>[a(v,{"model-value":e.values.description,type:"textarea",placeholder:i(c)("Brief description of AI Assistant functions."),"onUpdate:modelValue":s[2]||(s[2]=o=>r("description",o))},null,8,["model-value","placeholder"])]),_:1},8,["label"])])}}}),H=G($,[["__scopeId","data-v-8f31ed15"]]);export{H as default};
