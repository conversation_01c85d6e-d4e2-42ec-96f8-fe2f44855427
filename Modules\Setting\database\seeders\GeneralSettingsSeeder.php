<?php

namespace Modules\Setting\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Language\Facades\LanguageFacade;
use Modules\Setting\Models\Setting;
use Modules\Setting\Models\SettingGroup;

class GeneralSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create General settings group
        $generalGroup = SettingGroup::updateOrCreate(
            ['key' => 'general'],
            [
                'label' => 'General Settings',
                'description' => 'General configuration settings for the CMS',
                'icon' => 'fas fa-cog',
                'sort_order' => 1,
            ]
        );

        // Site basic information
        Setting::updateOrCreate(
            ['key' => 'site_name'],
            [
                'value' => 'Laravel ProCMS',
                'type' => 'string',
                'input_type' => 'text',
                'group_id' => $generalGroup->id,
                'label' => 'Site Name',
                'description' => 'The name of your website',
                'is_public' => true,
                'sort_order' => 1,
                'validation_rules' => 'required|string|max:255',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'site_description'],
            [
                'value' => 'A powerful content management system built with <PERSON><PERSON>',
                'type' => 'string',
                'input_type' => 'textarea',
                'group_id' => $generalGroup->id,
                'label' => 'Site Description',
                'description' => 'A brief description of your website',
                'is_public' => true,
                'sort_order' => 2,
                'validation_rules' => 'nullable|string|max:500',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'site_keywords'],
            [
                'value' => 'laravel, cms, content management, php',
                'type' => 'string',
                'input_type' => 'text',
                'group_id' => $generalGroup->id,
                'label' => 'Site Keywords',
                'description' => 'SEO keywords for your website (comma separated)',
                'is_public' => true,
                'sort_order' => 3,
                'validation_rules' => 'nullable|string|max:255',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'site_logo'],
            [
                'value' => '/images/logo.png',
                'type' => 'string',
                'input_type' => 'file',
                'group_id' => $generalGroup->id,
                'label' => 'Site Logo',
                'description' => 'Upload your site logo',
                'is_public' => true,
                'sort_order' => 4,
                'validation_rules' => 'nullable|string|max:255',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'site_favicon'],
            [
                'value' => '/images/favicon.ico',
                'type' => 'string',
                'input_type' => 'file',
                'group_id' => $generalGroup->id,
                'label' => 'Site Favicon',
                'description' => 'Upload your site favicon',
                'is_public' => true,
                'sort_order' => 5,
                'validation_rules' => 'nullable|string|max:255',
            ]
        );

        // Contact information
        Setting::updateOrCreate(
            ['key' => 'contact_email'],
            [
                'value' => '<EMAIL>',
                'type' => 'string',
                'input_type' => 'email',
                'group_id' => $generalGroup->id,
                'label' => 'Contact Email',
                'description' => 'Primary contact email address',
                'is_public' => true,
                'sort_order' => 6,
                'validation_rules' => 'nullable|email|max:255',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'contact_phone'],
            [
                'value' => '+84 123 456 789',
                'type' => 'string',
                'input_type' => 'tel',
                'group_id' => $generalGroup->id,
                'label' => 'Contact Phone',
                'description' => 'Primary contact phone number',
                'is_public' => true,
                'sort_order' => 7,
                'validation_rules' => 'nullable|string|max:20',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'contact_address'],
            [
                'value' => 'Ho Chi Minh City, Vietnam',
                'type' => 'string',
                'input_type' => 'textarea',
                'group_id' => $generalGroup->id,
                'label' => 'Contact Address',
                'description' => 'Business address',
                'is_public' => true,
                'sort_order' => 8,
                'validation_rules' => 'nullable|string|max:500',
            ]
        );

        // Site settings
        Setting::updateOrCreate(
            ['key' => 'language'],
            [
                'value' => 'vi',
                'type' => 'string',
                'input_type' => 'select',
                'group_id' => $generalGroup->id,
                'label' => 'Language Default',
                'description' => 'Default language for the application',
                'options' => LanguageFacade::getActiveLanguages(),
                'is_public' => true,
                'sort_order' => 9,
                'validation_rules' => 'required|string|max:50',
            ]
        );

        // Site settings
        Setting::updateOrCreate(
            ['key' => 'timezone'],
            [
                'value' => 'Asia/Ho_Chi_Minh',
                'type' => 'string',
                'input_type' => 'select',
                'group_id' => $generalGroup->id,
                'label' => 'Timezone',
                'description' => 'Default timezone for the application',
                'options' => [
                    'Asia/Ho_Chi_Minh' => 'Ho Chi Minh (UTC+7)',
                    'Asia/Bangkok' => 'Bangkok (UTC+7)',
                    'Asia/Singapore' => 'Singapore (UTC+8)',
                    'UTC' => 'UTC (UTC+0)',
                ],
                'is_public' => false,
                'sort_order' => 9,
                'validation_rules' => 'required|string|max:50',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'date_format'],
            [
                'value' => 'd/m/Y',
                'type' => 'string',
                'input_type' => 'select',
                'group_id' => $generalGroup->id,
                'label' => 'Date Format',
                'description' => 'Default date format for display',
                'options' => [
                    'd/m/Y' => 'DD/MM/YYYY',
                    'm/d/Y' => 'MM/DD/YYYY',
                    'Y-m-d' => 'YYYY-MM-DD',
                    'd-m-Y' => 'DD-MM-YYYY',
                ],
                'is_public' => true,
                'sort_order' => 10,
                'validation_rules' => 'required|string|max:20',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'items_per_page'],
            [
                'value' => '10',
                'type' => 'integer',
                'input_type' => 'number',
                'group_id' => $generalGroup->id,
                'label' => 'Items per Page',
                'description' => 'Default number of items to display per page',
                'is_public' => false,
                'sort_order' => 11,
                'validation_rules' => 'required|integer|min:5|max:100',
            ]
        );

        // Social media links
        Setting::updateOrCreate(
            ['key' => 'social_facebook'],
            [
                'value' => '',
                'type' => 'string',
                'input_type' => 'url',
                'group_id' => $generalGroup->id,
                'label' => 'Facebook URL',
                'description' => 'Facebook page URL',
                'is_public' => true,
                'sort_order' => 12,
                'validation_rules' => 'nullable|url|max:255',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'social_twitter'],
            [
                'value' => '',
                'type' => 'string',
                'input_type' => 'url',
                'group_id' => $generalGroup->id,
                'label' => 'Twitter URL',
                'description' => 'Twitter profile URL',
                'is_public' => true,
                'sort_order' => 13,
                'validation_rules' => 'nullable|url|max:255',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'social_linkedin'],
            [
                'value' => '',
                'type' => 'string',
                'input_type' => 'url',
                'group_id' => $generalGroup->id,
                'label' => 'LinkedIn URL',
                'description' => 'LinkedIn profile URL',
                'is_public' => true,
                'sort_order' => 14,
                'validation_rules' => 'nullable|url|max:255',
            ]
        );

        // Maintenance mode
        Setting::updateOrCreate(
            ['key' => 'maintenance_mode'],
            [
                'value' => 'false',
                'type' => 'boolean',
                'input_type' => 'switch',
                'group_id' => $generalGroup->id,
                'label' => 'Maintenance Mode',
                'description' => 'Enable maintenance mode to temporarily disable the site',
                'is_public' => false,
                'sort_order' => 15,
                'validation_rules' => 'required|boolean',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'maintenance_message'],
            [
                'value' => 'We are currently performing scheduled maintenance. Please check back soon.',
                'type' => 'string',
                'input_type' => 'textarea',
                'group_id' => $generalGroup->id,
                'label' => 'Maintenance Message',
                'description' => 'Message to display when site is in maintenance mode',
                'is_public' => true,
                'sort_order' => 16,
                'validation_rules' => 'nullable|string|max:500',
            ]
        );
    }
}
