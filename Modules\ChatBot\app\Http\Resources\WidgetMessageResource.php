<?php

namespace Modules\ChatBot\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\ChatBot\Enums\MessageRole;

class WidgetMessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->uuid,
            'type' => $this->role === MessageRole::USER ? 'user' : 'bot',
            'content' => $this->content,
            'timestamp' => $this->created_at?->toISOString(),
            'status' => $this->when(
                $this->role !== MessageRole::USER,
                $this->status?->value
            ),
        ];
    }
}
