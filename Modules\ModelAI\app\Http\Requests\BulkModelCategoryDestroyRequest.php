<?php

namespace Modules\ModelAI\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class BulkModelCategoryDestroyRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                Rule::exists('model_categories', 'id')->whereNotNull('deleted_at'), // Only soft deleted categories
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('Category IDs'),
            'ids.*' => __('Category ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Please select at least one category.'),
            'ids.array' => __('Invalid data format.'),
            'ids.min' => __('Please select at least one category.'),
            'ids.*.required' => __('Category ID is required.'),
            'ids.*.integer' => __('Category ID must be an integer.'),
            'ids.*.exists' => __('One or more selected categories must be in trash to be permanently deleted.'),
        ];
    }
}
