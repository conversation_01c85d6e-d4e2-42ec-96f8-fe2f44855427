const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/ModelCategoryDrawerForm-zbZEDZjv.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/js/hooks-CuzZ-_om.js","static/js/index-CQnBjWL2.js","static/js/_plugin-vue_export-helper-QGN-qG8u.js","static/css/plus-drawer-form-DaizY_En.css","static/js/ModelCategoryFilterForm-8PkITtKm.js","static/css/ModelCategoryFilterForm-BPSnP7Tv.css"])))=>i.map(i=>d[i]);
var fe=Object.defineProperty,me=Object.defineProperties;var ge=Object.getOwnPropertyDescriptors;var ae=Object.getOwnPropertySymbols;var pe=Object.prototype.hasOwnProperty,ye=Object.prototype.propertyIsEnumerable;var ne=(s,i,r)=>i in s?fe(s,i,{enumerable:!0,configurable:!0,writable:!0,value:r}):s[i]=r,K=(s,i)=>{for(var r in i||(i={}))pe.call(i,r)&&ne(s,r,i[r]);if(ae)for(var r of ae(i))ye.call(i,r)&&ne(s,r,i[r]);return s},re=(s,i)=>me(s,ge(i));var m=(s,i,r)=>new Promise((v,R)=>{var k=w=>{try{_(r.next(w))}catch(u){R(u)}},h=w=>{try{_(r.throw(w))}catch(u){R(u)}},_=w=>w.done?v(w.value):Promise.resolve(w.value).then(k,h);_((r=r.apply(s,i)).next())});import{r as b,g as he,aA as oe,az as Ce,ax as c,a8 as t,ay as S,aD as be,ai as j,aC as x,q as we,o as xe,S as ve,m as z,n as T,D as H,E as l,N as d,k as e,aI as ke,G as I,P as se,M as E,I as M,aJ as C,L as $,Q as _e,A as Re,a5 as Be,a4 as N,aK as le,aL as De,aM as ie,_ as Pe}from"./index-ZVLuktk4.js";import{g as Se,a as Te,b as Ee,r as Ae,c as Fe,d as ze,e as He,f as Ie,u as Ve,h as Me}from"./auth-api-C3NnK5ai.js";import{P as $e}from"./index-DMMM8XNs.js";import"./sortable.esm-7jaD_3Ar.js";import"./epTheme-DzIV6EF-.js";function Ne(){const s=b(!1),i=b({isTrashed:"no"}),r=he({total:0,pageSize:20,currentPage:1,pageSizes:[10,20,50,100,200],background:!0,layout:"total, sizes, prev, pager, next, jumper"}),v=b([]),R=b([]),k=b({sortBy:"createdAt",sortOrder:"asc"}),h=b([]),_=b(),w=b(!1),u=b(!1),A=b({status:"active"}),g=()=>m(null,null,function*(){try{s.value=!0;const n=yield Te(Ce(re(K({},i.value),{order:`${k.value.sortBy}:${k.value.sortOrder}`,page:r.currentPage,limit:r.pageSize})));R.value=oe(n.data),r.total=n.total}catch(n){console.error("Error fetching categories:",n),c(t("Failed to fetch categories"),{type:"error"})}finally{s.value=!1}}),F=()=>m(null,null,function*(){try{const n=yield Se();h.value=oe(n.data)}catch(n){console.error("Error fetching categories dropdown:",n)}}),U=n=>{v.value=n},O=()=>m(null,null,function*(){yield g()}),W=n=>m(null,null,function*(){r.pageSize=n,r.currentPage=1,yield g()}),G=n=>m(null,null,function*(){k.value={sortBy:n.prop,sortOrder:n.order=="ascending"?"asc":"desc"},yield g()}),D=n=>m(null,null,function*(){var a,o;try{s.value=!0;const f=yield Me(n);return f.success?(c(f.message||t("Create successful"),{type:"success"}),yield g(),!0):(c(f.message||t("Create failed"),{type:"error"}),!1)}catch(f){return c(((o=(a=f.response)==null?void 0:a.data)==null?void 0:o.message)||(f==null?void 0:f.message)||t("Create failed"),{type:"error"}),!1}finally{s.value=!1}}),B=(n,a)=>m(null,null,function*(){var o,f;try{s.value=!0;const p=yield Ve(n,a);return p.success?(c(p.message||t("Update successful"),{type:"success"}),yield g(),!0):(c(p.message||t("Update failed"),{type:"error"}),!1)}catch(p){return c(((f=(o=p.response)==null?void 0:o.data)==null?void 0:f.message)||(p==null?void 0:p.message)||t("Update failed"),{type:"error"}),!1}finally{s.value=!1}});return{loading:s,filterRef:i,pagination:r,records:R,multipleSelection:v,sort:k,categoriesDropdown:h,handleBulkDelete:n=>m(null,null,function*(){const a=n||v.value.map(o=>o.id);if(a.length===0){c(t("Please select items to delete"),{type:"warning"});return}try{yield S.confirm(t("Are you sure you want to delete selected items?"),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Ie({ids:a}),c(t("Deleted successfully"),{type:"success"}),yield g()}catch(o){o!=="cancel"&&(console.error("Error bulk deleting categories:",o),c(t("Delete failed"),{type:"error"}))}}),handleDelete:n=>m(null,null,function*(){try{yield S.confirm(t("Are you sure you want to delete this item?"),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield He(n.id),c(t("Deleted successfully"),{type:"success"}),yield g()}catch(a){a!=="cancel"&&(console.error("Error deleting category:",a),c(t("Delete failed"),{type:"error"}))}}),handlePermanentDelete:n=>m(null,null,function*(){try{yield S.confirm(t("Are you sure you want to permanently delete this item? This action cannot be undone."),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield ze(n.id),c(t("Permanently deleted successfully"),{type:"success"}),yield g()}catch(a){a!=="cancel"&&(console.error("Error permanently deleting category:",a),c(t("Permanent delete failed"),{type:"error"}))}}),handleBulkPermanentDelete:n=>m(null,null,function*(){const a=n||v.value.map(o=>o.id);if(a.length===0){c(t("Please select items to delete"),{type:"warning"});return}try{yield S.confirm(t("Are you sure you want to permanently delete selected items? This action cannot be undone."),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Fe({ids:a}),c(t("Permanently deleted successfully"),{type:"success"}),yield g()}catch(o){o!=="cancel"&&(console.error("Error bulk permanently deleting categories:",o),c(t("Permanent delete failed"),{type:"error"}))}}),handleRestore:n=>m(null,null,function*(){try{yield S.confirm(t("Are you sure you want to restore this item?"),t("Confirm"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"info"}),yield Ae(n.id),c(t("Restored successfully"),{type:"success"}),yield g()}catch(a){a!=="cancel"&&(console.error("Error restoring category:",a),c(t("Restore failed"),{type:"error"}))}}),handleBulkRestore:n=>m(null,null,function*(){const a=n||v.value.map(o=>o.id);if(a.length===0){c(t("Please select items to restore"),{type:"warning"});return}try{yield S.confirm(t("Are you sure you want to restore selected items?"),t("Confirm"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"info"}),yield Ee({ids:a}),c(t("Restored successfully"),{type:"success"}),yield g()}catch(o){o!=="cancel"&&(console.error("Error bulk restoring categories:",o),c(t("Restore failed"),{type:"error"}))}}),fnGetCategories:g,fnGetCategoriesDropdown:F,fnHandlePageChange:O,fnHandleSelectionChange:U,fnHandleSortChange:G,fnHandleSizeChange:W,filterVisible:w,drawerVisible:u,drawerValues:A,categoryFormRef:_,handleSubmit:n=>m(null,null,function*(){var o;let a=!1;n.id!=null?a=yield B(Number(n.id),n):(a=yield D(n),a&&(A.value={status:"active"},(o=_.value)==null||o.resetForm()))}),handleFilter:n=>m(null,null,function*(){i.value=n,yield g()}),handleEdit:n=>{A.value=K({},n),u.value=!0}}}const Ue=[{type:"selection",width:"30px",sortable:!1},{type:"index",width:40,headerRenderer:()=>t("No.")},{prop:"key",align:"left",sortable:!0,minWidth:160,headerRenderer:()=>t("Category key")},{prop:"name",align:"left",sortable:!0,minWidth:160,headerRenderer:()=>t("Category Name")},{prop:"icon",align:"center",sortable:!0,width:100,headerRenderer:()=>t("Icon"),formatter:({icon:s})=>j(x,{icon:s,width:"24px",class:"mx-auto"},null)},{prop:"status",sortable:!1,align:"center",width:160,headerRenderer:()=>t("Status"),cellRenderer:({row:s})=>{const i={active:{type:"success",text:t("Active"),class:"bg-green-100 text-green-800",icon:"ri:checkbox-circle-fill",iconClass:"text-green-600"},inactive:{type:"danger",text:t("Inactive"),class:"bg-red-100 text-red-800",icon:"ri:close-circle-fill",iconClass:"text-red-600"}},r=i[s.status]||i.active;return j("span",{class:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${r.class}`},[j(x,{icon:r.icon,class:`w-3 h-3 mr-1.5 ${r.iconClass}`}),r.text])}},{prop:"createdAt",width:160,sortable:!0,headerRenderer:()=>t("Created at"),formatter:({createdAt:s})=>s?be(s).format("YYYY-MM-DD HH:mm"):"-"},{label:"",fixed:"right",width:140,slot:"operation",sortable:!1}],Oe={class:"main"},We={class:"ml-2"},Ge={class:"ml-2"},Le={class:"ml-2"},Ye={class:"ml-2"},Ke=we({__name:"index",setup(s){const i=le(()=>ie(()=>import("./ModelCategoryDrawerForm-zbZEDZjv.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),r=le(()=>ie(()=>import("./ModelCategoryFilterForm-8PkITtKm.js"),__vite__mapDeps([7,1,2,4,5,8,6]))),v=b(),R=b(),{loading:k,filterRef:h,pagination:_,records:w,multipleSelection:u,handleBulkDelete:A,handleDelete:g,fnGetCategories:F,fnHandlePageChange:U,fnHandleSelectionChange:O,fnHandleSortChange:W,fnHandleSizeChange:G,filterVisible:D,drawerVisible:B,drawerValues:P,categoryFormRef:L,handleSubmit:Q,handleFilter:q,handleBulkPermanentDelete:J,handleBulkRestore:X,handlePermanentDelete:Z,handleRestore:ee}=Ne(),te=n=>{P.value=De(n,!0),B.value=!0};return xe(()=>{ve(()=>{F()})}),(n,a)=>{const o=I("el-button"),f=I("el-tooltip"),p=I("el-dropdown-item"),ce=I("el-dropdown-menu"),de=I("el-dropdown");return T(),z("div",Oe,[H("div",{ref_key:"contentRef",ref:R,class:$(["flex",e(Be)()?"flex-wrap":""])},[l(e($e),{class:"!w-full",style:{transition:"width 220ms cubic-bezier(0.4, 0, 0.2, 1)"},title:e(t)("ModelCategory Management"),columns:e(Ue),onRefresh:e(F),onFilter:a[4]||(a[4]=y=>D.value=!0)},{buttons:d(()=>[l(f,{content:e(t)("Create new"),placement:"top"},{default:d(()=>[l(o,{type:"text",class:"font-bold text-[16px]",disabled:!e(C)("model-category.create"),onClick:a[0]||(a[0]=()=>{B.value=!0})},{default:d(()=>[l(e(x),{icon:e(C)("model-category.create")?"flat-color-icons:plus":"icons8:plus",width:"18px"},null,8,["icon"])]),_:1},8,["disabled"])]),_:1},8,["content"]),e(h).isTrashed==="yes"?(T(),z(M,{key:0},[l(f,{content:e(t)("Restore"),placement:"top"},{default:d(()=>[l(o,{type:"text",class:"font-bold text-[16px]",disabled:e(u).length===0||e(u).length>0&&!e(C)("model-category.delete"),onClick:a[1]||(a[1]=()=>e(X)())},{default:d(()=>[l(e(x),{icon:"tabler:restore",width:"18px",class:$({"text-amber-800":e(u).length>0&&e(C)("model-category.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"]),l(f,{content:e(t)("Bulk Destroy"),placement:"top"},{default:d(()=>[l(o,{type:"text",class:"font-bold text-[16px]",disabled:e(u).length===0||e(u).length>0&&!e(C)("model-category.destroy"),onClick:a[2]||(a[2]=()=>e(J)())},{default:d(()=>[l(e(x),{icon:"tabler:trash-x-filled",width:"18px",class:$({"text-red-800":e(u).length>0&&e(C)("model-category.destroy")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])],64)):(T(),z(M,{key:1},[e(h).isTrashed==="no"?(T(),_e(f,{key:0,content:e(t)("Bulk Delete"),placement:"top"},{default:d(()=>[l(o,{type:"text",class:"font-bold text-[16px]",disabled:e(u).length==0||e(u).length>0&&!e(C)("model-category.delete"),onClick:a[3]||(a[3]=()=>e(A)())},{default:d(()=>[l(e(x),{icon:"tabler:trash",width:"18px",class:$({"text-red-800":e(u).length>0&&e(C)("model-category.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])):Re("",!0)],64))]),default:d(({size:y,dynamicColumns:ue})=>[l(e(ke),{ref_key:"tableRef",ref:v,"align-whole":"center","table-layout":"auto",loading:e(k),size:y,adaptive:"",border:"",adaptiveConfig:{offsetBottom:108},data:e(w),columns:ue,pagination:e(_),"header-cell-style":{background:"var(--el-fill-color-light)",color:"var(--el-text-color-primary)"},onSortChange:e(W),onPageSizeChange:e(G),onPageCurrentChange:e(U),onSelectionChange:e(O)},{operation:d(({row:V})=>[l(de,{"split-button":"",trigger:"click",size:"small"},{dropdown:d(()=>[l(ce,{class:"min-w-[130px]"},{default:d(()=>[l(p,{disabled:""},{default:d(()=>[se(E(e(t)("Action")),1)]),_:1}),e(h).isTrashed=="no"?(T(),z(M,{key:0},[l(p,{disabled:!e(C)("model-category.edit"),onClick:Y=>te(V)},{default:d(()=>[l(e(x),{icon:"material-symbols:edit",class:"text-blue-800"}),H("span",We,E(e(t)("Edit")),1)]),_:2},1032,["disabled","onClick"]),l(p,{disabled:!e(C)("model-category.delete"),onClick:Y=>e(g)(V)},{default:d(()=>[l(e(x),{icon:"tabler:trash",class:"text-red-800"}),H("span",Ge,E(e(t)("Delete")),1)]),_:2},1032,["disabled","onClick"])],64)):(T(),z(M,{key:1},[l(p,{disabled:!e(C)("model-category.delete"),onClick:Y=>e(ee)(V)},{default:d(()=>[l(e(x),{icon:"tabler:restore",class:"text-amber-800"}),H("span",Le,E(e(t)("Restore")),1)]),_:2},1032,["disabled","onClick"]),l(p,{disabled:!e(C)("model-category.destroy"),onClick:Y=>e(Z)(V)},{default:d(()=>[l(e(x),{icon:"tabler:trash-x",class:"text-red-800"}),H("span",Ye,E(e(t)("Destroy")),1)]),_:2},1032,["disabled","onClick"])],64))]),_:2},1024)]),default:d(()=>[se(E(e(t)("Action"))+" ",1)]),_:2},1024)]),_:2},1032,["loading","size","data","columns","pagination","onSortChange","onPageSizeChange","onPageCurrentChange","onSelectionChange"])]),_:1},8,["title","columns","onRefresh"])],2),l(e(i),{ref_key:"categoryFormRef",ref:L,visible:e(B),"onUpdate:visible":a[5]||(a[5]=y=>N(B)?B.value=y:null),values:e(P),"onUpdate:values":a[6]||(a[6]=y=>N(P)?P.value=y:null),onSubmit:e(Q),onClose:a[7]||(a[7]=()=>{var y;(y=e(L))==null||y.resetForm(),P.value={status:"active"}})},null,8,["visible","values","onSubmit"]),l(e(r),{ref:"filterFormRef",visible:e(D),"onUpdate:visible":a[8]||(a[8]=y=>N(D)?D.value=y:null),values:e(h),"onUpdate:values":a[9]||(a[9]=y=>N(h)?h.value=y:null),onSubmit:e(q),onReset:a[10]||(a[10]=()=>{h.value={isTrashed:"no"},e(F)()})},null,8,["visible","values","onSubmit"])])}}}),et=Pe(Ke,[["__scopeId","data-v-1cbe2082"]]);export{et as default};
