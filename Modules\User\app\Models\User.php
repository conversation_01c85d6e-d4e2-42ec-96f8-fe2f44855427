<?php

namespace Modules\User\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Modules\Location\Models\Country;
use Modules\Location\Models\GeoDivision;
use Modules\Role\Enums\RoleStatus;
use Modules\Role\Models\Role;
use Modules\User\Database\Factories\UserFactory;
use Modules\User\Enums\UserGender;
use Modules\User\Enums\UserStatus;
use Spatie\Permission\Traits\HasRoles;
use Tymon\JWTAuth\Contracts\JWTSubject;

/**
 * @property array|mixed $active_code
 */
class User extends Authenticatable implements JWTSubject
{
    /** @use HasFactory<UserFactory> */
    use HasFactory, Notifiable, HasRoles, SoftDeletes;

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($user) {
            if (empty($user->uuid)) {
                $user->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'username',
        'password',
        'first_name',
        'last_name',
        'avatar',
        'birthday',
        'gender',
        'email',
        'email_verified_at',
        'phone',
        'phone_verified_at',
        'address',
        'geo_division_id',
        'country_id',
        'status',
        'last_login_at',
        'last_login_ip',
        'preferences',
        'is_verified',
        'newsletter_subscribed',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'id',
        'password',
        'remember_token',
        'deleted_at',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'birthday' => 'date',
            'gender' => UserGender::class,
            'email_verified_at' => 'datetime',
            'phone_verified_at' => 'datetime',
            'status' => UserStatus::class,
            'last_login_at' => 'datetime',
            'preferences' => 'array',
            'is_verified' => 'boolean',
            'newsletter_subscribed' => 'boolean',
            'password' => 'hashed',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): UserFactory
    {
        return UserFactory::new();
    }

    /**
     * Relationship: User belongs to a geographic division.
     */
    public function geoDivision(): BelongsTo
    {
        return $this->belongsTo(GeoDivision::class);
    }

    /**
     * Relationship: User belongs to a country.
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Relationship: User has many social accounts.
     */
    public function socialAccounts(): HasMany
    {
        return $this->hasMany(\Modules\Auth\Models\UserSocialAccount::class);
    }

    /**
     * Scope: Filter users by status.
     */
    public function scopeStatus(Builder $query, UserStatus|string $status): Builder
    {
        $statusValue = $status instanceof UserStatus ? $status->value : $status;
        return $query->where('status', $statusValue);
    }

    /**
     * Scope: Filter active users.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', UserStatus::Active->value);
    }

    /**
     * Scope: Filter verified users.
     */
    public function scopeVerified(Builder $query): Builder
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope: Filter users by gender.
     */
    public function scopeGender(Builder $query, UserGender|string $gender): Builder
    {
        $genderValue = $gender instanceof UserGender ? $gender->value : $gender;
        return $query->where('gender', $genderValue);
    }

    /**
     * Scope: Filter users by country.
     */
    public function scopeCountry(Builder $query, int $countryId): Builder
    {
        return $query->where('country_id', $countryId);
    }

    /**
     * Scope: Filter users by geographic division.
     */
    public function scopeGeoDivision(Builder $query, int $geoDivisionId): Builder
    {
        return $query->where('geo_division_id', $geoDivisionId);
    }

    /**
     * Scope: Find user by UUID.
     */
    public function scopeByUuid(Builder $query, string $uuid): Builder
    {
        return $query->where('uuid', $uuid);
    }

    /**
     * Find user by UUID or fail.
     */
    public static function findByUuid(string $uuid): ?User
    {
        return static::where('uuid', $uuid)->first();
    }

    /**
     * Find user by UUID or fail.
     */
    public static function findByUuidOrFail(string $uuid): User
    {
        return static::where('uuid', $uuid)->firstOrFail();
    }


    /**
     * Get the user's full name.
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get the user's avatar URL with fallback.
     */
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }

        // Generate avatar using initials
        $initials = strtoupper(substr($this->first_name, 0, 1) . substr($this->last_name, 0, 1));
        return "https://ui-avatars.com/api/?name={$initials}&background=random";
    }

    /**
     * Check if user is active.
     */
    public function isActive(): bool
    {
        return $this->status === UserStatus::Active;
    }

    /**
     * Check if user can login.
     */
    public function canLogin(): bool
    {
        return $this->status->canLogin();
    }

    /**
     * Check if user is verified.
     */
    public function isVerified(): bool
    {
        return $this->is_verified;
    }

    /**
     * Check if email is verified.
     */
    public function hasVerifiedEmail(): bool
    {
        return !is_null($this->email_verified_at);
    }

    /**
     * Check if phone is verified.
     */
    public function hasVerifiedPhone(): bool
    {
        return !is_null($this->phone_verified_at);
    }

    /**
     * Get user's age.
     */
    public function getAgeAttribute(): ?int
    {
        return $this->birthday ? $this->birthday->age : null;
    }

    /**
     * Get user's location string.
     */
    public function getLocationAttribute(): string
    {
        $location = [];

        if ($this->geoDivision) {
            $location[] = $this->geoDivision->name;
        }

        if ($this->country) {
            $location[] = $this->country->name;
        }

        return implode(', ', $location);
    }

    /**
     * Override hasPermissionTo to check role status and soft delete.
     */
    public function hasPermissionTo($permission, $guardName = null): bool
    {
        $guardName = $guardName ?? $this->getDefaultGuardName();

        // Get roles using our custom Role model
        $userRoles = Role::whereHas('users', function($query) {
            $query->where('users.id', $this->id);
        })->where('guard_name', $guardName)->get();

        // Check permissions from active roles only
        foreach ($userRoles as $role) {
            // Skip inactive or soft deleted roles
            $isActive = is_object($role->status) ?
                ($role->status === RoleStatus::Active) :
                ($role->status === 'active');

            if (!$isActive || $role->trashed()) {
                continue;
            }

            // Check if this active role has the permission
            $hasPermission = $role->permissions()->where('name', $permission)->count() > 0;
            if ($hasPermission) {
                return true;
            }
        }

        // Check direct permissions
        $directPermissions = $this->getDirectPermissions()
            ->where('guard_name', $guardName)
            ->where('name', $permission);

        return $directPermissions->count() > 0;
    }

    /**
     * Override can method to use our custom hasPermissionTo for permission strings,
     * but delegate to Gate/Policy system for model-based authorization.
     */
    public function can($abilities, $arguments = []): bool
    {
        // If we have arguments (like model instances), delegate to Gate/Policy system
        if (!empty($arguments)) {
            return parent::can($abilities, $arguments);
        }

        // For string abilities without arguments, try our custom logic first
        if (is_string($abilities)) {
            return $this->hasPermissionTo($abilities);
        }

        // For array abilities or other cases, use parent implementation
        return parent::can($abilities, $arguments);
    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier(): mixed
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims(): array
    {
        return [];
    }

    /**
     * Check if user has linked a specific OAuth provider.
     */
    public function hasLinkedProvider(string $provider): bool
    {
        return $this->socialAccounts()->where('provider', $provider)->exists();
    }

    /**
     * Get linked OAuth providers.
     */
    public function getLinkedProviders(): array
    {
        return $this->socialAccounts()->pluck('provider')->toArray();
    }

    /**
     * Get social account for a specific provider.
     */
    public function getSocialAccount(string $provider): ?\Modules\Auth\Models\UserSocialAccount
    {
        return $this->socialAccounts()->where('provider', $provider)->first();
    }

    /**
     * Get the default guard name for the user.
     */
    public function getDefaultGuardName(): string
    {
        return 'api';
    }
}
