<?php

namespace Modules\Sidebar\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Sidebar\Models\Sidebar;

class SidebarSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sidebars = [
            [
                'menu_type' => 0,
                'parent_id' => 0,
                'title' => 'Dashboard',
                'name' => 'dashboard',
                'path' => '/dashboard',
                'component' => 'Dashboard',
                'rank' => 1,
                'redirect' => null,
                'icon' => 'dashboard',
                'extra_icon' => null,
                'enter_transition' => null,
                'leave_transition' => null,
                'active_path' => '/dashboard',
                'roles' => ['admin', 'super-admin'],
                'auths' => [],
                'frame_src' => null,
                'frame_loading' => false,
                'keep_alive' => true,
                'hidden_tag' => false,
                'fixed_tag' => true,
                'show_link' => true,
                'show_parent' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'menu_type' => 0,
                'parent_id' => 0,
                'title' => 'Content Management',
                'name' => 'content',
                'path' => '/content',
                'component' => null,
                'rank' => 2,
                'redirect' => null,
                'icon' => 'content_paste',
                'extra_icon' => null,
                'enter_transition' => null,
                'leave_transition' => null,
                'active_path' => '/content',
                'roles' => ['admin', 'super-admin', 'editor'],
                'auths' => [],
                'frame_src' => null,
                'frame_loading' => false,
                'keep_alive' => false,
                'hidden_tag' => false,
                'fixed_tag' => false,
                'show_link' => true,
                'show_parent' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'menu_type' => 0,
                'parent_id' => 2, // Content Management parent
                'title' => 'Pages',
                'name' => 'pages',
                'path' => '/content/pages',
                'component' => 'Pages',
                'rank' => 1,
                'redirect' => null,
                'icon' => 'article',
                'extra_icon' => null,
                'enter_transition' => null,
                'leave_transition' => null,
                'active_path' => '/content/pages',
                'roles' => ['admin', 'super-admin', 'editor'],
                'auths' => [],
                'frame_src' => null,
                'frame_loading' => false,
                'keep_alive' => false,
                'hidden_tag' => false,
                'fixed_tag' => false,
                'show_link' => true,
                'show_parent' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'menu_type' => 0,
                'parent_id' => 2, // Content Management parent
                'title' => 'Blog',
                'name' => 'blog',
                'path' => '/content/blog',
                'component' => 'Blog',
                'rank' => 2,
                'redirect' => null,
                'icon' => 'rss_feed',
                'extra_icon' => null,
                'enter_transition' => null,
                'leave_transition' => null,
                'active_path' => '/content/blog',
                'roles' => ['admin', 'super-admin', 'editor'],
                'auths' => [],
                'frame_src' => null,
                'frame_loading' => false,
                'keep_alive' => false,
                'hidden_tag' => false,
                'fixed_tag' => false,
                'show_link' => true,
                'show_parent' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'menu_type' => 0,
                'parent_id' => 0,
                'title' => 'System Settings',
                'name' => 'settings',
                'path' => '/settings',
                'component' => null,
                'rank' => 99,
                'redirect' => null,
                'icon' => 'settings',
                'extra_icon' => null,
                'enter_transition' => null,
                'leave_transition' => null,
                'active_path' => '/settings',
                'roles' => ['super-admin'],
                'auths' => [],
                'frame_src' => null,
                'frame_loading' => false,
                'keep_alive' => false,
                'hidden_tag' => false,
                'fixed_tag' => false,
                'show_link' => true,
                'show_parent' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($sidebars as $sidebar) {
            Sidebar::updateOrCreate(
                ['name' => $sidebar['name']],
                $sidebar
            );
        }
    }
}
