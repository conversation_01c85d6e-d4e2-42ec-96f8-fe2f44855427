const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/TranslationDrawerForm-DvBeKq2Y.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/js/TranslationFilterForm-BinJJccR.js"])))=>i.map(i=>d[i]);
var oe=Object.defineProperty,re=Object.defineProperties;var ie=Object.getOwnPropertyDescriptors;var W=Object.getOwnPropertySymbols;var de=Object.prototype.hasOwnProperty,ce=Object.prototype.propertyIsEnumerable;var Y=(t,l,n)=>l in t?oe(t,l,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[l]=n,V=(t,l)=>{for(var n in l||(l={}))de.call(l,n)&&Y(t,n,l[n]);if(W)for(var n of W(l))ce.call(l,n)&&Y(t,n,l[n]);return t},j=(t,l)=>re(t,ie(l));var k=(t,l,n)=>new Promise((p,_)=>{var h=u=>{try{w(n.next(u))}catch(g){_(g)}},b=u=>{try{w(n.throw(u))}catch(g){_(g)}},w=u=>u.done?p(u.value):Promise.resolve(u.value).then(h,b);w((n=n.apply(t,l)).next())});import{aN as F,r as m,g as ue,ax as C,a8 as a,az as pe,aA as fe,ay as J,ai as f,fG as Q,aD as me,q as ge,o as ve,S as ye,m as he,n as be,D as $,E as r,N as c,k as e,aI as xe,G as D,P as X,M as P,aJ as T,aC as H,L as Z,a5 as Ce,a4 as A,aK as ee,aL as _e,aM as te,_ as we}from"./index-ZVLuktk4.js";import{P as Se}from"./index-DMMM8XNs.js";import"./sortable.esm-7jaD_3Ar.js";import"./epTheme-DzIV6EF-.js";const ke=t=>F.request("get","/api/translations",{params:t}),Te=t=>F.request("post","/api/translations",{data:t}),Re=t=>F.request("delete",`/api/translations/${t}`),De=t=>F.request("delete","/api/translations/bulk",{data:t});function ze(){const t=m(!1),l=m({isTrashed:"no"}),n=ue({total:0,pageSize:20,currentPage:1,pageSizes:[10,20,50,100,200],background:!0,layout:"total, sizes, prev, pager, next, jumper"}),p=m([]),_=m([]),h=m({sortBy:"createdAt",sortOrder:"asc"}),b=m(),w=m(!1),u=m(!1),g=m({status:"active"}),v=()=>k(null,null,function*(){try{t.value=!0;const s=yield ke(pe(j(V({},l.value),{order:`${h.value.sortBy}:${h.value.sortOrder}`,page:n.currentPage,limit:n.pageSize})));_.value=fe(s.data),n.total=s.total}catch(s){console.error("Error fetching translations:",s),C(a("Failed to fetch translations"),{type:"error"})}finally{t.value=!1}});return{loading:t,filterRef:l,pagination:n,records:_,multipleSelection:p,sort:h,handleBulkDelete:s=>k(null,null,function*(){const i=s||p.value.map(x=>x.id);if(i.length===0){C(a("Please select items to delete"),{type:"warning"});return}try{yield J.confirm(a("Are you sure you want to delete selected items?"),a("Warning"),{confirmButtonText:a("Confirm"),cancelButtonText:a("Cancel"),type:"warning"}),yield De({ids:i}),C(a("Deleted successfully"),{type:"success"}),v()}catch(x){x!=="cancel"&&(console.error("Error bulk deleting translations:",x),C(a("Delete failed"),{type:"error"}))}}),handleDelete:s=>k(null,null,function*(){try{yield J.confirm(a("Are you sure you want to delete this item?"),a("Warning"),{confirmButtonText:a("Confirm"),cancelButtonText:a("Cancel"),type:"warning"}),yield Re(s.id),C(a("Deleted successfully"),{type:"success"}),v()}catch(i){i!=="cancel"&&(console.error("Error deleting translation:",i),C(a("Delete failed"),{type:"error"}))}}),fnGetTranslations:v,fnHandlePageChange:()=>{v()},fnHandleSelectionChange:s=>{p.value=s},fnHandleSortChange:({prop:s,order:i})=>{h.value.sortBy=s,h.value.sortOrder=i==="ascending"?"asc":"desc",v()},fnHandleSizeChange:s=>{n.pageSize=s,n.currentPage=1,v()},filterVisible:w,drawerVisible:u,drawerValues:g,translationFormRef:b,handleSubmit:s=>k(null,null,function*(){var i,x,B;try{t.value=!0;const y=yield Te(s);y.success?(C(y.message||a("Translation saved successfully"),{type:"success"}),yield v(),u.value=!1,g.value={translations:{}},(i=b.value)==null||i.resetForm()):C(y.message||a("Save failed"),{type:"error"})}catch(y){C(((B=(x=y.response)==null?void 0:x.data)==null?void 0:B.message)||(y==null?void 0:y.message)||a("Save failed"),{type:"error"})}finally{t.value=!1}}),handleFilter:s=>k(null,null,function*(){l.value=s,yield v()})}}const Be=[{type:"selection",width:"30px",sortable:!1},{prop:"key",align:"left",sortable:!0,minWidth:200,headerRenderer:()=>a("Translation Key"),cellRenderer:({row:t})=>f("div",{class:"flex flex-col"},[f("div",{class:"font-medium text-gray-900"},t.key),t.group&&f("div",{class:"text-xs text-gray-500"},`Group: ${t.group}`)])},{prop:"group",align:"center",sortable:!0,width:120,headerRenderer:()=>a("Group"),cellRenderer:({row:t})=>f(Q,{type:"info",size:"small"},()=>t.group||"default")},{prop:"text",align:"left",sortable:!1,minWidth:400,headerRenderer:()=>a("Translations"),cellRenderer:({row:t})=>{const l=t.text||{},n=Object.keys(l);return n.length===0?f("div",{class:"text-gray-400 italic"},a("No translations")):f("div",{class:"space-y-1"},n.slice(0,3).map(p=>f("div",{class:"flex items-center gap-2"},[f(Q,{type:"primary",size:"small",class:"!mr-2"},()=>p.toUpperCase()),f("span",{class:"text-sm truncate max-w-xs",title:l[p]},l[p]||"-")])).concat(n.length>3?[f("div",{class:"text-xs text-gray-500"},`+${n.length-3} more...`)]:[]))}},{prop:"createdAt",align:"center",sortable:!0,width:150,headerRenderer:()=>a("Created At"),cellRenderer:({row:t})=>f("div",{class:"text-xs text-gray-600"},me(t.createdAt).format("DD/MM/YYYY HH:mm"))},{prop:"operation",fixed:"right",width:100,slot:"operation",headerRenderer:()=>a("Action")}],Pe={class:"main"},He={class:"ml-2"},Ae={class:"ml-2"},Fe=ge({__name:"index",setup(t){const l=ee(()=>te(()=>import("./TranslationDrawerForm-DvBeKq2Y.js"),__vite__mapDeps([0,1,2]))),n=ee(()=>te(()=>import("./TranslationFilterForm-BinJJccR.js"),__vite__mapDeps([3,1,2]))),p=m(),_=m(),{loading:h,filterRef:b,pagination:w,records:u,multipleSelection:g,handleBulkDelete:v,handleDelete:I,fnGetTranslations:z,fnHandlePageChange:M,fnHandleSelectionChange:O,fnHandleSortChange:G,fnHandleSizeChange:N,filterVisible:R,drawerVisible:S,drawerValues:s,translationFormRef:i,handleSubmit:x,handleFilter:B}=ze(),y=q=>{s.value=V({},_e(q,!0)),S.value=!0};return ve(()=>{ye(()=>{z()})}),(q,o)=>{const U=D("el-button"),K=D("el-tooltip"),E=D("el-dropdown-item"),ae=D("el-dropdown-menu"),ne=D("el-dropdown");return be(),he("div",Pe,[$("div",{ref_key:"contentRef",ref:_,class:Z(["flex",e(Ce)()?"flex-wrap":""])},[r(e(Se),{class:"!w-full",style:{transition:"width 220ms cubic-bezier(0.4, 0, 0.2, 1)"},title:e(a)("Translation Management"),columns:e(Be),onRefresh:e(z),onFilter:o[2]||(o[2]=d=>R.value=!0)},{buttons:c(()=>[r(K,{content:e(a)("Create new"),placement:"top"},{default:c(()=>[r(U,{type:"text",class:"font-bold text-[16px]",disabled:!e(T)("translation.create"),onClick:o[0]||(o[0]=()=>{S.value=!0})},{default:c(()=>[r(e(H),{icon:e(T)("translation.create")?"flat-color-icons:plus":"icons8:plus",width:"18px"},null,8,["icon"])]),_:1},8,["disabled"])]),_:1},8,["content"]),r(K,{content:e(a)("Bulk Delete"),placement:"top"},{default:c(()=>[r(U,{type:"text",class:"font-bold text-[16px]",disabled:e(g).length===0||e(g).length>0&&!e(T)("translation.delete"),onClick:o[1]||(o[1]=()=>e(v)())},{default:c(()=>[r(e(H),{icon:"tabler:trash",width:"18px",class:Z({"text-red-700":e(g).length>0&&e(T)("translation.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])]),default:c(({size:d,dynamicColumns:le})=>[r(e(xe),{ref_key:"tableRef",ref:p,"align-whole":"center","table-layout":"auto",loading:e(h),size:d,adaptive:"",adaptiveConfig:{offsetBottom:108},data:e(u),columns:le,pagination:e(w),"header-cell-style":{background:"var(--el-fill-color-light)",color:"var(--el-text-color-primary)"},onSortChange:e(G),onPageSizeChange:e(N),onPageCurrentChange:e(M),onSelectionChange:e(O)},{operation:c(({row:L})=>[r(ne,{"split-button":"",trigger:"click",size:"small"},{dropdown:c(()=>[r(ae,{class:"min-w-[130px]"},{default:c(()=>[r(E,{disabled:""},{default:c(()=>[X(P(e(a)("Action")),1)]),_:1}),r(E,{disabled:!e(T)("translation.edit"),onClick:se=>y(L)},{default:c(()=>[r(e(H),{icon:"material-symbols:edit",class:"text-blue-600"}),$("span",He,P(e(a)("Edit")),1)]),_:2},1032,["disabled","onClick"]),r(E,{disabled:!e(T)("translation.delete"),onClick:se=>e(I)(L)},{default:c(()=>[r(e(H),{icon:"tabler:trash",class:"text-red-800"}),$("span",Ae,P(e(a)("Delete")),1)]),_:2},1032,["disabled","onClick"])]),_:2},1024)]),default:c(()=>[X(P(e(a)("Action"))+" ",1)]),_:2},1024)]),_:2},1032,["loading","size","data","columns","pagination","onSortChange","onPageSizeChange","onPageCurrentChange","onSelectionChange"])]),_:1},8,["title","columns","onRefresh"])],2),r(e(l),{ref_key:"translationFormRef",ref:i,visible:e(S),"onUpdate:visible":o[3]||(o[3]=d=>A(S)?S.value=d:null),values:e(s),"onUpdate:values":o[4]||(o[4]=d=>A(s)?s.value=d:null),onSubmit:e(x),onClose:o[5]||(o[5]=()=>{var d;(d=e(i))==null||d.resetForm(),s.value={translations:{}}})},null,8,["visible","values","onSubmit"]),r(e(n),{ref:"filterFormRef",visible:e(R),"onUpdate:visible":o[6]||(o[6]=d=>A(R)?R.value=d:null),values:e(b),"onUpdate:values":o[7]||(o[7]=d=>A(b)?b.value=d:null),onSubmit:e(B),onReset:o[8]||(o[8]=()=>{b.value={isTrashed:"no"},e(z)()})},null,8,["visible","values","onSubmit"])])}}}),Oe=we(Fe,[["__scopeId","data-v-eb2e75a4"]]);export{Oe as default};
