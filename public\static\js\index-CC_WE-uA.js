const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/RoleDrawerForm-9TG5FD-L.js","static/js/index-ZVLuktk4.js","static/css/index-z8GKYKvf.css","static/js/hooks-CuzZ-_om.js","static/js/auth-api-CR4e_tAC.js","static/js/index-CQnBjWL2.js","static/js/_plugin-vue_export-helper-QGN-qG8u.js","static/css/plus-drawer-form-DaizY_En.css","static/js/RoleFilterForm-DoEiL9bt.js"])))=>i.map(i=>d[i]);
var ye=Object.defineProperty,ge=Object.defineProperties;var he=Object.getOwnPropertyDescriptors;var ae=Object.getOwnPropertySymbols;var be=Object.prototype.hasOwnProperty,xe=Object.prototype.propertyIsEnumerable;var oe=(l,r,a)=>r in l?ye(l,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):l[r]=a,U=(l,r)=>{for(var a in r||(r={}))be.call(r,a)&&oe(l,a,r[a]);if(ae)for(var a of ae(r))xe.call(r,a)&&oe(l,a,r[a]);return l},le=(l,r)=>ge(l,he(r));var g=(l,r,a)=>new Promise((R,S)=>{var _=v=>{try{k(a.next(v))}catch(d){S(d)}},h=v=>{try{k(a.throw(v))}catch(d){S(d)}},k=v=>v.done?R(v.value):Promise.resolve(v.value).then(_,h);k((a=a.apply(l,r)).next())});import{aN as C,az as $,r as x,g as ve,o as ue,aA as re,ax as c,a8 as t,ay as T,ai as M,aC as w,q as we,S as Ce,m as V,n as E,D as q,E as i,N as u,k as e,aI as Re,G as H,P as ie,M as A,I as O,aJ as b,L as W,Q as _e,A as ke,a5 as Se,a4 as G,aK as ce,aL as Be,aM as de,_ as De}from"./index-ZVLuktk4.js";import{P as Pe}from"./index-DMMM8XNs.js";import"./sortable.esm-7jaD_3Ar.js";import"./epTheme-DzIV6EF-.js";const Te=l=>C.request("get","/api/auth/roles",{params:l}),Ee=()=>C.request("get","/api/auth/roles/dropdown"),Ae=l=>C.request("post","/api/auth/roles",{data:$(l)}),$e=(l,r)=>C.request("put",`/api/auth/roles/${l}`,{data:$(r)}),ze=l=>C.request("delete",`/api/auth/roles/${l}/delete`),Fe=l=>C.request("delete","/api/auth/roles/bulk/delete",{data:$(l)}),Ie=l=>C.request("delete",`/api/auth/roles/${l}/force`),Ve=l=>C.request("delete","/api/auth/roles/bulk/force",{data:$(l)}),qe=l=>C.request("put",`/api/auth/roles/${l}/restore`),He=l=>C.request("put","/api/auth/roles/bulk/restore",{data:$(l)});function Ne(){const l=x(!1),r=x({isTrashed:"no"}),a=ve({total:0,pageSize:20,currentPage:1,pageSizes:[10,20,50,100,200],background:!0,layout:"total, sizes, prev, pager, next, jumper"}),R=x([]),S=x([]),_=x({sortBy:"createdAt",sortOrder:"asc"}),h=x([]),k=x(),v=x(!1),d=x(!1),z=x({status:"active"}),f=()=>g(null,null,function*(){try{l.value=!0;const s=yield Te($(le(U({},r.value),{order:`${_.value.sortBy}:${_.value.sortOrder}`,page:a.currentPage,limit:a.pageSize})));S.value=re(s.data),a.total=s.total}catch(s){console.error("Error fetching roles:",s),c(t("Failed to fetch roles"),{type:"error"})}finally{l.value=!1}}),F=()=>g(null,null,function*(){try{const s=yield Ee();h.value=re(s.data)}catch(s){console.error("Error fetching roles dropdown:",s)}}),L=s=>{R.value=s},K=s=>{a.currentPage=s,f()},j=s=>{a.pageSize=s,f()},Q=(s,n)=>{_.value={sortBy:s,sortOrder:n},f()},P=s=>g(null,null,function*(){var n,o;try{l.value=!0;const m=yield Ae(s);return m.success?(c(m.message||t("Create successful"),{type:"success"}),yield f(),d.value=!1,!0):(c(m.message||t("Create failed"),{type:"error"}),!1)}catch(m){return c(((o=(n=m.response)==null?void 0:n.data)==null?void 0:o.message)||(m==null?void 0:m.message)||t("Create failed"),{type:"error"}),!1}finally{l.value=!1}}),B=(s,n)=>g(null,null,function*(){var o,m;try{l.value=!0;const y=yield $e(s,n);return y.success?(c(y.message||t("Update successful"),{type:"success"}),yield f(),!0):(c(y.message||t("Update failed"),{type:"error"}),!1)}catch(y){return c(((m=(o=y.response)==null?void 0:o.data)==null?void 0:m.message)||(y==null?void 0:y.message)||t("Update failed"),{type:"error"}),!1}finally{l.value=!1}}),D=s=>g(null,null,function*(){try{yield T.confirm(t("Are you sure you want to delete this item?"),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield ze(s.id),c(t("Deleted successfully"),{type:"success"}),f()}catch(n){n!=="cancel"&&(console.error("Error deleting role:",n),c(t("Delete failed"),{type:"error"}))}}),N=s=>g(null,null,function*(){const n=s||R.value.map(o=>o.id);if(n.length===0){c(t("Please select items to delete"),{type:"warning"});return}try{yield T.confirm(t("Are you sure you want to delete selected items?"),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Fe({ids:n}),c(t("Deleted successfully"),{type:"success"}),f()}catch(o){o!=="cancel"&&(console.error("Error bulk deleting roles:",o),c(t("Delete failed"),{type:"error"}))}}),J=s=>g(null,null,function*(){try{yield T.confirm(t("Are you sure you want to permanently delete this item? This action cannot be undone."),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Ie(s.id),c(t("Permanently deleted successfully"),{type:"success"}),f()}catch(n){n!=="cancel"&&(console.error("Error permanently deleting role:",n),c(t("Permanent delete failed"),{type:"error"}))}}),X=s=>g(null,null,function*(){const n=s||R.value.map(o=>o.id);if(n.length===0){c(t("Please select items to delete"),{type:"warning"});return}try{yield T.confirm(t("Are you sure you want to permanently delete selected items? This action cannot be undone."),t("Warning"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"warning"}),yield Ve({ids:n}),c(t("Permanently deleted successfully"),{type:"success"}),f()}catch(o){o!=="cancel"&&(console.error("Error bulk permanently deleting roles:",o),c(t("Permanent delete failed"),{type:"error"}))}}),Y=s=>g(null,null,function*(){try{yield T.confirm(t("Are you sure you want to restore this item?"),t("Confirm"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"info"}),yield qe(s.id),c(t("Restored successfully"),{type:"success"}),f()}catch(n){n!=="cancel"&&(console.error("Error restoring role:",n),c(t("Restore failed"),{type:"error"}))}}),Z=s=>g(null,null,function*(){const n=s||R.value.map(o=>o.id);if(n.length===0){c(t("Please select items to restore"),{type:"warning"});return}try{yield T.confirm(t("Are you sure you want to restore selected items?"),t("Confirm"),{confirmButtonText:t("Confirm"),cancelButtonText:t("Cancel"),type:"info"}),yield He({ids:n}),c(t("Restored successfully"),{type:"success"}),f()}catch(o){o!=="cancel"&&(console.error("Error bulk restoring roles:",o),c(t("Restore failed"),{type:"error"}))}}),ee=s=>{z.value=U({},s),d.value=!0},te=s=>g(null,null,function*(){r.value=s,yield f()}),ne=s=>g(null,null,function*(){var o;let n=!1;s.id!=null?n=yield B(Number(s.id),s):(n=yield P(s),n&&(z.value={status:"active"},(o=k.value)==null||o.resetForm()))});return ue(()=>{f()}),{loading:l,filterRef:r,pagination:a,records:S,multipleSelection:R,sort:_,rolesDropdown:h,handleBulkDelete:N,handleDelete:D,handlePermanentDelete:J,handleBulkPermanentDelete:X,handleRestore:Y,handleBulkRestore:Z,fnGetRoles:f,fnGetRolesDropdown:F,fnHandlePageChange:K,fnHandleSelectionChange:L,fnHandleSortChange:Q,fnHandleSizeChange:j,filterVisible:v,drawerVisible:d,drawerValues:z,roleFormRef:k,handleSubmit:ne,handleFilter:te,handleEdit:ee}}const Ue=[{type:"selection",width:"30px",sortable:!1},{type:"index",width:40,headerRenderer:()=>t("No.")},{prop:"name",align:"left",sortable:"custom",width:190,headerRenderer:()=>t("Name")},{prop:"displayName",sortable:!1,align:"left",width:200,headerRenderer:()=>t("Display name")},{prop:"description",align:"left",minWidth:150,headerRenderer:()=>t("Description")},{prop:"isSystem",sortable:"custom",align:"center",width:140,headerRenderer:()=>t("System Role"),cellRenderer:({row:l})=>{const r=l.isSystem,a={icon:r?"ri:shield-check-fill":"ri:user-line",iconClass:r?"text-orange-600":"text-blue-600",class:r?"bg-orange-100":"bg-blue-100"};return M("span",{class:`inline-flex items-center justify-center w-8 h-8 rounded-full ${a.class}`},M(w,{icon:a.icon,class:`w-4 h-4 ${a.iconClass}`}))}},{prop:"status",sortable:!1,align:"center",width:100,headerRenderer:()=>t("Status"),cellRenderer:({row:l})=>{const r={active:{type:"success",text:t("Active"),class:"bg-green-100 text-green-800",icon:"ri:checkbox-circle-fill",iconClass:"text-green-600"},inactive:{type:"danger",text:t("Inactive"),class:"bg-red-100 text-red-800",icon:"ri:close-circle-fill",iconClass:"text-red-600"}},a=r[l.status]||r.active;return M("span",{class:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${a.class}`},[M(w,{icon:a.icon,class:`w-3 h-3 mr-1.5 ${a.iconClass}`}),a.text])}},{label:"",fixed:"right",width:140,slot:"operation",sortable:!1}],Me={class:"main"},Oe={class:"ml-2"},We={class:"ml-2"},Ge={class:"ml-2"},Le={class:"ml-2"},Ke=we({__name:"index",setup(l){const r=ce(()=>de(()=>import("./RoleDrawerForm-9TG5FD-L.js"),__vite__mapDeps([0,1,2,3,4,5,6,7]))),a=ce(()=>de(()=>import("./RoleFilterForm-DoEiL9bt.js"),__vite__mapDeps([8,1,2,3,5,6,7]))),R=x(),S=x(),{loading:_,filterRef:h,pagination:k,records:v,multipleSelection:d,handleBulkDelete:z,handleDelete:f,fnGetRoles:F,fnHandlePageChange:L,fnHandleSelectionChange:K,fnHandleSortChange:j,fnHandleSizeChange:Q,filterVisible:P,drawerVisible:B,drawerValues:D,roleFormRef:N,handleSubmit:J,handleFilter:X,handleBulkPermanentDelete:Y,handleBulkRestore:Z,handlePermanentDelete:ee,handleRestore:te}=Ne(),ne=s=>{const n=s.permissions.map(o=>Number(o.id));D.value=le(U({},Be(s,!0)),{permissions:n}),B.value=!0};return ue(()=>{Ce(()=>{F()})}),(s,n)=>{const o=H("el-button"),m=H("el-tooltip"),y=H("el-dropdown-item"),pe=H("el-dropdown-menu"),fe=H("el-dropdown");return E(),V("div",Me,[q("div",{ref_key:"contentRef",ref:S,class:W(["flex",e(Se)()?"flex-wrap":""])},[i(e(Pe),{class:"!w-full",style:{transition:"width 220ms cubic-bezier(0.4, 0, 0.2, 1)"},title:e(t)("Role Management"),columns:e(Ue),onRefresh:e(F),onFilter:n[4]||(n[4]=p=>P.value=!0)},{buttons:u(()=>[i(m,{content:e(t)("Create new"),placement:"top"},{default:u(()=>[i(o,{type:"text",class:"font-bold text-[16px]",disabled:!e(b)("role.create"),onClick:n[0]||(n[0]=()=>{B.value=!0})},{default:u(()=>[i(e(w),{icon:e(b)("role.create")?"flat-color-icons:plus":"icons8:plus",width:"18px"},null,8,["icon"])]),_:1},8,["disabled"])]),_:1},8,["content"]),e(h).isTrashed==="yes"?(E(),V(O,{key:0},[i(m,{content:e(t)("Restore"),placement:"top"},{default:u(()=>[i(o,{type:"text",class:"font-bold text-[16px]",disabled:e(d).length===0||e(d).length>0&&!e(b)("role.delete"),onClick:n[1]||(n[1]=()=>e(Z)())},{default:u(()=>[i(e(w),{icon:"tabler:restore",width:"18px",class:W({"text-blue-600":e(d).length>0&&!e(b)("role.delete")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"]),i(m,{content:e(t)("Bulk Destroy"),placement:"top"},{default:u(()=>[i(o,{type:"text",class:"font-bold text-[16px]",disabled:e(d).length===0||e(d).length>0&&!e(b)("role.destroy"),onClick:n[2]||(n[2]=()=>e(Y)())},{default:u(()=>[i(e(w),{icon:"tabler:trash-x-filled",width:"18px",class:W({"text-red-700":e(d).length>0&&!e(b)("role.destroy")})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])],64)):(E(),V(O,{key:1},[e(h).isTrashed==="no"?(E(),_e(m,{key:0,content:e(t)("Bulk Destroy"),placement:"top"},{default:u(()=>[i(o,{type:"text",class:"font-bold text-[16px]",disabled:e(d).length==0||e(d).length>0&&!e(b)("role.delete")||e(d).some(p=>p.isSystem),onClick:n[3]||(n[3]=()=>e(z)())},{default:u(()=>[i(e(w),{icon:"tabler:trash",width:"18px",class:W({"text-red-700":e(d).length>0&&!e(b)("role.delete")||!e(d).some(p=>p.isSystem)})},null,8,["class"])]),_:1},8,["disabled"])]),_:1},8,["content"])):ke("",!0)],64))]),default:u(({size:p,dynamicColumns:me})=>[i(e(Re),{ref_key:"tableRef",ref:R,"align-whole":"center","table-layout":"auto",loading:e(_),size:p,adaptive:"",border:"",adaptiveConfig:{offsetBottom:108},data:e(v),columns:me,pagination:e(k),"header-cell-style":{background:"var(--el-fill-color-light)",color:"var(--el-text-color-primary)"},onSortChange:e(j),onPageSizeChange:e(Q),onPageCurrentChange:e(L),onSelectionChange:e(K)},{operation:u(({row:I})=>[i(fe,{"split-button":"",trigger:"click",size:"small"},{dropdown:u(()=>[i(pe,{class:"min-w-[130px]"},{default:u(()=>[i(y,{disabled:""},{default:u(()=>[ie(A(e(t)("Action")),1)]),_:1}),e(h).isTrashed=="no"?(E(),V(O,{key:0},[i(y,{disabled:!e(b)("role.edit"),onClick:se=>ne(I)},{default:u(()=>[i(e(w),{icon:"material-symbols:edit",class:"text-blue-600"}),q("span",Oe,A(e(t)("Edit")),1)]),_:2},1032,["disabled","onClick"]),i(y,{disabled:!e(b)("role.delete")||I.isSystem,onClick:se=>e(f)(I)},{default:u(()=>[i(e(w),{icon:"tabler:trash",class:"text-red-800"}),q("span",We,A(e(t)("Delete")),1)]),_:2},1032,["disabled","onClick"])],64)):(E(),V(O,{key:1},[i(y,{disabled:!e(b)("role.delete"),onClick:se=>e(te)(I)},{default:u(()=>[i(e(w),{icon:"tabler:restore",class:"text-red-800"}),q("span",Ge,A(e(t)("Restore")),1)]),_:2},1032,["disabled","onClick"]),i(y,{disabled:!e(b)("role.destroy"),onClick:se=>e(ee)(I)},{default:u(()=>[i(e(w),{icon:"tabler:trash-x",class:"text-red-800"}),q("span",Le,A(e(t)("Delete")),1)]),_:2},1032,["disabled","onClick"])],64))]),_:2},1024)]),default:u(()=>[ie(A(e(t)("Action"))+" ",1)]),_:2},1024)]),_:2},1032,["loading","size","data","columns","pagination","onSortChange","onPageSizeChange","onPageCurrentChange","onSelectionChange"])]),_:1},8,["title","columns","onRefresh"])],2),i(e(r),{ref_key:"roleFormRef",ref:N,visible:e(B),"onUpdate:visible":n[5]||(n[5]=p=>G(B)?B.value=p:null),values:e(D),"onUpdate:values":n[6]||(n[6]=p=>G(D)?D.value=p:null),onSubmit:e(J),onClose:n[7]||(n[7]=()=>{var p;(p=e(N))==null||p.resetForm(),D.value={status:"active"}})},null,8,["visible","values","onSubmit"]),i(e(a),{ref:"filterFormRef",visible:e(P),"onUpdate:visible":n[8]||(n[8]=p=>G(P)?P.value=p:null),values:e(h),"onUpdate:values":n[9]||(n[9]=p=>G(h)?h.value=p:null),onSubmit:e(X),onReset:n[10]||(n[10]=()=>{h.value={isTrashed:"no"},e(F)()})},null,8,["visible","values","onSubmit"])])}}}),Ze=De(Ke,[["__scopeId","data-v-d58f5023"]]);export{Ze as default};
