# Auth Module Tests

## Overview

Comprehensive test suite for the Auth Module following Laravel ProCMS testing patterns. Tests are designed to validate all authentication functionality while logging errors for debugging.

## Test Structure

```
tests/
├── Feature/
│   └── AuthControllerTest.php        # API endpoint tests
├── Unit/
│   ├── AuthServiceTest.php           # Service layer tests
│   ├── AuthRequestTest.php           # Form validation tests
│   └── AuthFacadeTest.php            # Facade functionality tests
├── TestCase.php                      # Base test class
├── run_tests.php                     # Test runner script
└── README.md                         # This file
```

## Test Categories

### Unit Tests (3 test files, 50+ test methods)

**AuthServiceTest** (8 test methods)
- User registration functionality
- Password hashing validation
- User status management based on settings
- Login with valid/invalid credentials
- JWT token refresh and logout
- Error handling

**AuthRequestTest** (8 test methods)
- Validation rules for all request classes
- Valid/invalid data testing
- Unique constraint validation
- Password confirmation validation
- Email format validation
- Required field validation

**AuthFacadeTest** (10 test methods)
- Facade accessor validation
- Static method calls
- All authentication operations via facade
- Error handling through facade
- Service integration testing

### Feature Tests (1 test file, 8+ test methods)

**AuthControllerTest** (8 test methods)
- User registration API endpoints
- Email verification process
- Login/logout functionality
- Password reset workflow
- Protected route authentication
- Profile management
- Validation error responses

## Running Tests

### Run All Tests
```bash
# Run all Auth module tests
php artisan test Modules/Auth/tests/

# Run with coverage
php artisan test Modules/Auth/tests/ --coverage

# Run with detailed output
php artisan test Modules/Auth/tests/ --verbose
```

### Run Specific Test Types
```bash
# Run only unit tests
php artisan test Modules/Auth/tests/Unit/

# Run only feature tests
php artisan test Modules/Auth/tests/Feature/

# Run specific test file
php artisan test Modules/Auth/tests/Unit/AuthServiceTest.php
```

### Run with Custom Test Runner
```bash
# Run comprehensive test suite with detailed reporting
php Modules/Auth/tests/run_tests.php
```

## Test Data Management

### Factories
Tests use Laravel factories for consistent test data:

```php
// Create test user
$user = User::factory()->create([
    'status' => UserStatus::Active,
    'is_verified' => true,
]);

// Create inactive user for verification tests
$inactiveUser = User::factory()->create([
    'status' => UserStatus::Inactive,
    'is_verified' => false,
]);
```

### Database Refresh
All tests use `RefreshDatabase` trait to ensure clean state:

```php
use Illuminate\Foundation\Testing\RefreshDatabase;

class AuthServiceTest extends TestCase
{
    use RefreshDatabase;
    // ...
}
```

## Error Logging

### Automatic Error Logging
Tests automatically log errors without stopping execution:

```php
protected function logError(string $message, \Exception $e): void
{
    $errorLog = [
        'timestamp' => now()->toISOString(),
        'test_class' => static::class,
        'test_method' => $this->getName(),
        'message' => $message,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString(),
    ];

    file_put_contents(storage_path('logs/auth_test_errors.log'), 
        json_encode($errorLog) . "\n", FILE_APPEND | LOCK_EX);
}
```

### Log Files
- `storage/logs/auth_test_errors.log` - Detailed error logs
- `storage/logs/auth_test_results.log` - Test execution logs
- `storage/logs/auth_test_summary.json` - Test summary report

## Test Helpers

### Authentication Helper
```php
// Create authenticated user for API testing
$user = $this->authenticatedUser();
$token = $this->getJwtToken($user);

// Make authenticated request
$response = $this->authenticatedJson('GET', '/api/v1/auth/me');
```

### Response Structure Validation
```php
// Validate API response structure
$this->assertAuthApiResponseStructure($response, 'user');

// Validate validation errors
$this->assertValidationError($response, ['email', 'password']);
```

## Test Coverage

### Covered Functionality
- ✅ User registration (with/without email verification)
- ✅ Email verification process
- ✅ User login/logout
- ✅ JWT token management (refresh, invalidation)
- ✅ Password reset workflow
- ✅ Profile management
- ✅ Form validation for all requests
- ✅ Facade functionality
- ✅ Service layer methods
- ✅ Error handling and edge cases

### Authentication Scenarios
- ✅ Active user login
- ✅ Inactive user login prevention
- ✅ Unverified email login prevention
- ✅ Invalid credentials handling
- ✅ Token expiration and refresh
- ✅ Protected route access control

### Validation Testing
- ✅ Required field validation
- ✅ Email format validation
- ✅ Password confirmation validation
- ✅ Unique constraint validation
- ✅ Custom validation rules

## Dependencies

### Required Modules
- **User Module**: User model and enums
- **ActiveCode Module**: OTP verification
- **Setting Module**: Configuration management

### Test Dependencies
- **PHPUnit**: Testing framework
- **Laravel Testing**: HTTP testing utilities
- **JWT Auth**: Token authentication

## Troubleshooting

### Common Issues

**Database Connection Errors**
```bash
# Ensure test database is configured
php artisan config:cache
php artisan migrate:fresh --env=testing
```

**JWT Configuration Issues**
```bash
# Generate JWT secret
php artisan jwt:secret

# Clear config cache
php artisan config:clear
```

**Module Dependencies**
```bash
# Run module migrations
php artisan module:migrate Auth
php artisan module:migrate User
php artisan module:migrate ActiveCode
```

### Debug Mode
Enable detailed error logging by setting `APP_DEBUG=true` in testing environment.

## Continuous Integration

### GitHub Actions Example
```yaml
- name: Run Auth Module Tests
  run: |
    php artisan test Modules/Auth/tests/ --coverage-clover=coverage.xml
    php Modules/Auth/tests/run_tests.php
```

### Test Reports
The test runner generates comprehensive reports including:
- Test execution summary
- Performance metrics
- Error details
- Success/failure rates

## Contributing

When adding new tests:

1. Follow existing test patterns
2. Use error logging instead of stopping execution
3. Include both positive and negative test cases
4. Update this README with new test descriptions
5. Ensure all tests use `RefreshDatabase` trait

## Performance

### Test Execution Time
- Unit Tests: ~2-5 seconds
- Feature Tests: ~5-10 seconds
- Full Suite: ~10-15 seconds

### Optimization Tips
- Use database transactions where possible
- Mock external dependencies
- Minimize database operations in unit tests
- Use factories for consistent test data
