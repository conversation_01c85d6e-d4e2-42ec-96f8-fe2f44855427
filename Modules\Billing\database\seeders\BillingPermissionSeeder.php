<?php

namespace Modules\Billing\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;

class BillingPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedBillingPermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Seed billing module permissions.
     */
    private function seedBillingPermissions(): void
    {
        $permissions = [
            [
                'name' => 'billing.view',
                'display_name' => 'View Billing',
                'description' => 'Permission to view billing information and invoices',
                'module_name' => 'billing',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'billing.create',
                'display_name' => 'Create Billing',
                'description' => 'Permission to create new billing records and invoices',
                'module_name' => 'billing',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'billing.edit',
                'display_name' => 'Edit Billing',
                'description' => 'Permission to update billing information and invoices',
                'module_name' => 'billing',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'billing.delete',
                'display_name' => 'Soft Delete Billing',
                'description' => 'Permission to soft delete billing records',
                'module_name' => 'billing',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'billing.destroy',
                'display_name' => 'Force Delete Billing',
                'description' => 'Permission to permanently delete billing records',
                'module_name' => 'billing',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
            [
                'name' => 'billing.restore',
                'display_name' => 'Restore Billing',
                'description' => 'Permission to restore soft deleted billing records',
                'module_name' => 'billing',
                'sort_order' => 6,
                'guard_name' => 'api',
            ],
            [
                'name' => 'billing.invoice',
                'display_name' => 'Manage Invoices',
                'description' => 'Permission to generate and manage invoices',
                'module_name' => 'billing',
                'sort_order' => 7,
                'guard_name' => 'api',
            ],
            [
                'name' => 'billing.subscription',
                'display_name' => 'Manage Subscriptions',
                'description' => 'Permission to manage billing subscriptions',
                'module_name' => 'billing',
                'sort_order' => 8,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }

    /**
     * Assign billing permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();
        
        if ($superAdmin) {
            $billingPermissions = Permission::where('module_name', 'billing')->where('guard_name', 'api')->get();
            
            foreach ($billingPermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}
