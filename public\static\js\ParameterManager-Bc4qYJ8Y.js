var ue=Object.defineProperty;var G=Object.getOwnPropertySymbols;var re=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable;var J=(g,v,c)=>v in g?ue(g,v,{enumerable:!0,configurable:!0,writable:!0,value:c}):g[v]=c,C=(g,v)=>{for(var c in v||(v={}))re.call(v,c)&&J(g,c,v[c]);if(G)for(var c of G(v))de.call(v,c)&&J(g,c,v[c]);return g};import{q as ie,r as w,e as L,y as Q,G as ce,m as d,n,D as i,E as p,A as T,N as y,I as D,J as A,Q as P,k as s,gu as R,gv as H,P as N,gc as W,aG as U,M as _,a8 as me,gx as pe,gs as ve,gm as fe,gd as X,gD as Y,h6 as Z,gL as K,_ as ye}from"./index-ZVLuktk4.js";const _e={class:"space-y-4 w-full"},ge={class:"flex flex-col gap-2"},ke={class:"flex gap-3"},xe={key:0,class:"bg-blue-50 p-3 rounded-lg"},be={class:"flex items-center space-x-2 mb-2"},he={class:"font-medium"},we={class:"text-sm text-gray-600 mb-2"},Pe={key:0,class:"text-xs text-gray-500"},Ve={key:0},Ee={class:"flex items-center gap-2"},Ce={key:0,class:"text-gray-500 py-4 text-center"},De={key:1,class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"},Ae={class:"flex justify-between items-center"},Ne={class:"font-medium"},Ue={class:"flex gap-3"},Ie={class:"flex items-center gap-2"},Te={key:0,class:"text-gray-500 text-center py-4"},Se={key:1,class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"},je={class:"font-medium text-green-900"},Be=ie({__name:"ParameterManager",props:{defaultParameters:{},allowedParameters:{}},emits:["update:defaultParameters","update:allowedParameters"],setup(g,{emit:v}){const c=g,S=v,j=[{key:"temperature",type:"number",min:0,max:2,step:.1,value:.7,description:"Controls creativity"},{key:"max_tokens",type:"number",min:1,max:8192,step:1,value:1e3,description:"Maximum tokens"},{key:"top_p",type:"number",min:0,max:1,step:.01,value:.9,description:"Nucleus sampling"},{key:"frequency_penalty",type:"number",min:-2,max:2,step:.1,value:0,description:"Frequency penalty"},{key:"presence_penalty",type:"number",min:-2,max:2,step:.1,value:0,description:"Presence penalty"},{key:"stop_sequences",type:"string",value:"[]",description:"Stop sequences (JSON array)"}],u=w([]),m=w([]),o=w(""),x=w(""),V=w(!1),r=t=>j.find(l=>l.key===t),ee=(t,l)=>{const e=r(t);return{key:t,value:l,type:(e==null?void 0:e.type)||"string",min:e==null?void 0:e.min,max:e==null?void 0:e.max,step:e==null?void 0:e.step,description:(e==null?void 0:e.description)||""}},le=L(()=>j.filter(t=>!u.value.some(l=>l.key===t.key))),te=L(()=>u.value.filter(t=>!m.value.some(l=>l.key===t.key))),ae=()=>{const t=r(o.value);t&&(u.value.push(C({},t)),o.value="",b())},se=()=>{const t=u.value.find(l=>l.key===x.value);t&&(m.value.push(C({},t)),x.value="",b())},ne=t=>{const l=u.value.findIndex(e=>e.key===t);if(l!==-1){u.value.splice(l,1);const e=m.value.findIndex(f=>f.key===t);e!==-1&&m.value.splice(e,1),b()}},oe=t=>{t>=0&&t<m.value.length&&(m.value.splice(t,1),b())},I=(t,l)=>{const e=u.value[t];if(!e)return;e.value=l;const f=m.value.find(E=>E.key===e.key);f&&(f.value=l),b()},b=()=>{V.value=!0;const t=Object.fromEntries(u.value.filter(e=>e&&e.key).map(e=>[e.key,e.value])),l=m.value.filter(e=>e&&e.key).map(e=>e.key);S("update:defaultParameters",t),S("update:allowedParameters",l),setTimeout(()=>{V.value=!1},200)};return Q(()=>c.defaultParameters,t=>{if(!V.value){if(!t||typeof t!="object"){u.value=[];return}u.value=Object.entries(t).filter(([l,e])=>l&&e!==void 0&&e!==null).map(([l,e])=>ee(l,e))}},{immediate:!0}),Q(()=>c.allowedParameters,t=>{if(!V.value){if(!t||!Array.isArray(t)){m.value=[];return}m.value=t.filter(l=>l&&typeof l=="string").map(l=>{let e=u.value.find(f=>f.key===l);if(!e){const f=r(l);f&&(e=C({},f))}return e}).filter(Boolean)}},{immediate:!0}),(t,l)=>{var f,E,B,q,O,$,z,F,M;const e=ce("ElTag");return n(),d("div",_e,[i("div",ge,[i("div",ke,[p(s(H),{modelValue:o.value,"onUpdate:modelValue":l[0]||(l[0]=a=>o.value=a),placeholder:"Select parameter to add",class:"flex-1"},{default:y(()=>[(n(!0),d(D,null,A(le.value,a=>(n(),P(s(R),{key:a.key,label:`${a.key} - ${a.description}`,value:a.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),p(s(U),{icon:s(W),type:"primary",disabled:!o.value,onClick:ae},{default:y(()=>l[2]||(l[2]=[N(" Add to Default ")])),_:1,__:[2]},8,["icon","disabled"])]),o.value&&r(o.value)?(n(),d("div",xe,[i("div",be,[p(e,{type:((f=r(o.value))==null?void 0:f.type)==="number"?"warning":((E=r(o.value))==null?void 0:E.type)==="boolean"?"success":"info"},{default:y(()=>{var a;return[N(_((a=r(o.value))==null?void 0:a.type),1)]}),_:1},8,["type"]),i("span",he,_((B=r(o.value))==null?void 0:B.key),1)]),i("p",we,_((q=r(o.value))==null?void 0:q.description),1),((O=r(o.value))==null?void 0:O.type)==="number"?(n(),d("div",Pe,[N(" Range: "+_(($=r(o.value))==null?void 0:$.min)+" - "+_((z=r(o.value))==null?void 0:z.max)+" ",1),(F=r(o.value))!=null&&F.step?(n(),d("span",Ve," , Step: "+_((M=r(o.value))==null?void 0:M.step),1)):T("",!0)])):T("",!0)])):T("",!0)]),p(s(K),null,{header:y(()=>[i("div",Ee,[p(s(Y),null,{default:y(()=>[p(s(Z))]),_:1}),i("span",null,"Default Parameters ("+_(u.value.length)+")",1)])]),default:y(()=>[u.value.length===0?(n(),d("div",Ce,_(s(me)("No default parameters")),1)):(n(),d("div",De,[(n(!0),d(D,null,A(u.value,(a,h)=>(n(),d("div",{key:a.key,class:"px-1 border rounded bg-gray-50 flex flex-col gap-2"},[i("div",Ae,[i("div",Ne,_(a.key),1),i("div",null,[a.type==="number"?(n(),P(s(pe),{key:0,"model-value":a.value,min:a.min,max:a.max,step:a.step,size:"small",class:"w-full","onUpdate:modelValue":k=>I(h,k)},null,8,["model-value","min","max","step","onUpdate:modelValue"])):a.type==="boolean"?(n(),P(s(ve),{key:1,"model-value":a.value,"onUpdate:modelValue":k=>I(h,k)},null,8,["model-value","onUpdate:modelValue"])):(n(),P(s(fe),{key:2,"model-value":a.value,size:"small",class:"w-full","onUpdate:modelValue":k=>I(h,k)},null,8,["model-value","onUpdate:modelValue"]))]),p(s(U),{type:"danger",icon:s(X),size:"small",circle:"",onClick:k=>ne(a.key)},null,8,["icon","onClick"])])]))),128))]))]),_:1}),i("div",Ue,[p(s(H),{modelValue:x.value,"onUpdate:modelValue":l[1]||(l[1]=a=>x.value=a),placeholder:"Select from Default to allow",class:"flex-1"},{default:y(()=>[(n(!0),d(D,null,A(te.value,a=>(n(),P(s(R),{key:a.key,label:a.key,value:a.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),p(s(U),{type:"success",icon:s(W),disabled:!x.value,onClick:se},{default:y(()=>l[3]||(l[3]=[N(" Add to Allowed ")])),_:1,__:[3]},8,["icon","disabled"])]),p(s(K),null,{header:y(()=>[i("div",Ie,[p(s(Y),null,{default:y(()=>[p(s(Z))]),_:1}),i("span",null,"Allowed Parameters ("+_(m.value.length)+")",1)])]),default:y(()=>[m.value.length===0?(n(),d("div",Te," No allowed parameters ")):(n(),d("div",Se,[(n(!0),d(D,null,A(m.value,(a,h)=>(n(),d("div",{key:a.key,class:"p-4 border rounded bg-green-50 flex justify-between items-start"},[i("div",je,_(a.key),1),p(s(U),{type:"danger",icon:s(X),size:"small",circle:"",onClick:k=>oe(h)},null,8,["icon","onClick"])]))),128))]))]),_:1})])}}}),$e=ye(Be,[["__scopeId","data-v-511f12ce"]]);export{$e as default};
