var ol=Object.defineProperty,tl=Object.defineProperties;var sl=Object.getOwnPropertyDescriptors;var _e=Object.getOwnPropertySymbols;var nl=Object.prototype.hasOwnProperty,rl=Object.prototype.propertyIsEnumerable;var Oe=(e,o,u)=>o in e?ol(e,o,{enumerable:!0,configurable:!0,writable:!0,value:u}):e[o]=u,S=(e,o)=>{for(var u in o||(o={}))nl.call(o,u)&&Oe(e,u,o[u]);if(_e)for(var u of _e(o))rl.call(o,u)&&Oe(e,u,o[u]);return e},fe=(e,o)=>tl(e,sl(o));var ye=(e,o,u)=>new Promise((t,r)=>{var i=s=>{try{h(u.next(s))}catch(f){r(f)}},T=s=>{try{h(u.throw(s))}catch(f){r(f)}},h=s=>s.done?t(s.value):Promise.resolve(s.value).then(i,T);h((u=u.apply(e,o)).next())});import{k as l,g9 as ul,ge as il,a4 as $e,r as I,gf as dl,y as X,q as te,g as we,aw as Be,Q as g,n as d,aF as le,J as O,N as c,X as R,au as B,av as q,m as _,I as H,gg as Se,as as y,P as Q,M as J,A as j,fD as Ge,gh as pl,e as Y,gi as ze,B as He,gj as Ke,E as ee,D as be,gk as Ie,L as qe,fG as cl,gl as je,K as Fe,gm as Ve,gn as fl,go as ml,gp as vl,gq as bl,gr as hl,gs as gl,gt as yl,gu as We,gv as Ye,gw as kl,gx as Vl,gy as Pl,gz as Tl,gA as Cl,gB as El,gC as $l,g6 as Ae,ae as E,fH as Sl,gD as Je,gE as Il,gF as Fl,gG as Dl,gH as wl,gI as Bl,ac as Ll,gJ as Rl,gK as _l,aq as Qe,gL as Ol,H as jl,aG as Pe,gM as Al,gN as Te,gO as Nl}from"./index-ZVLuktk4.js";import{_ as se,u as he}from"./_plugin-vue_export-helper-QGN-qG8u.js";const Xe="2.10.3",Ul=Object.prototype.toString,Le=e=>Ul.call(e),Ze=e=>Le(e).slice(8,-1),ge=Array.isArray,Ce=e=>Le(e)==="[object Date]",A=e=>typeof e=="function",Re=e=>typeof e=="string",Ml=e=>typeof e=="boolean",Gl=e=>e!==null&&typeof e=="object",De=e=>Gl(e)&&A(e.then)&&A(e.catch),pe=e=>Le(e)==="[object Object]",zl=e=>{const o=l(e);return Re(o)?{content:o}:pe(o)?o:{content:""}},Hl=(e,o)=>{if(!pe(e))throw new Error(`${o} expected Object but got ${Ze(e)}`)},Ne=(e,o,u,t,r)=>ye(null,null,function*(){try{let i={};const T={row:u,index:t};return e?$e(e)?i=e.value:pe(e)?i=S({},e):A(e)?i=yield e(o,T):De(e)?i=yield e:i=e:i={},Hl(i,r),i}catch(i){return Promise.reject(i)}}),ce=(e,o)=>o?`plus-${e}-${o}`:`plus-${e}`,re=e=>`${ce("field",e)}`,ie=e=>`${ce("label",e)}`,ve=e=>`${ce("extra",e)}`,Ba=e=>`${ce("cell",e)}`,La=e=>`${ce("desc",e)}`,Ra=e=>`${ce("desc-label",e)}`,de=(e,o)=>{const u={};return Object.keys(e||{}).forEach(t=>{t.startsWith(o)&&(u[t]=e[t])}),u},Kl=(e,o)=>ul(e,o),ql=(e,o,u)=>il(e,o,u),xe=(e,o)=>{const u=e.split(".").map(i=>Number(i)),t=o.split(".").map(i=>Number(i)),r=Math.max(u.length,t.length);for(let i=0;i<r;i++){if((u[i]||0)>(t[i]||0))return 1;if((u[i]||0)<(t[i]||0))return-1}return 0},el=xe(Xe,"2.6.0")<0;xe(Xe,"2.9.9")<0;const Wl=e=>e?l(e):"",Ee=e=>{ge(e)||console.error("Uncaught TypeError: ",`options expected Array but got ${Ze(e)}`)},ke=(e,o)=>{const u=o.optionsMap||{};return o.valueType==="cascader"||!pe(u)?e:e.map(i=>{const T=i,h=(u==null?void 0:u.label)||"label",s=(u==null?void 0:u.value)||"value",f={[h]:T[h],[s]:T[s]};return u.label&&Reflect.deleteProperty(T,h),u.value&&Reflect.deleteProperty(T,s),fe(S({},T),{__origin:f,label:i[h],value:i[s]})})||[]},Yl=e=>{const o=I([]),u=I(!1);if(!e.options)o.value=[],u.value=!0;else if($e(e.options)||dl(e.options)||ge(e.options))X(()=>e.options,t=>{const r=$e(t)?t.value:t;o.value=ke(r,e),u.value=!0},{immediate:!0,deep:!0});else if(A(e.options)){const t=e.options,r=t(e);De(r)?r.then(i=>{o.value=ke(i,e),u.value=!0,Ee(o.value)}).catch(i=>{throw i}):(o.value=ke(r,e),u.value=!0)}else De(e.options)?e.options.then(r=>{o.value=ke(r,e),u.value=!0,Ee(o.value)}).catch(r=>{throw r}):(u.value=!0,Ee(e.options));return{customOptions:o,customOptionsIsReady:u}},Jl=Symbol("tableFormFieldRefInjectionKey"),Ql=Symbol("tableFormRowInfoInjectionKey"),Xl=["datetimerange","daterange","monthrange"],Zl=["rate","input-number","slider"],xl=["checkbox","cascader","plus-date-picker","plus-input-tag","transfer"];var ea=te({name:"PlusRadio",__name:"index",props:{modelValue:{type:[String,Number,Boolean],default:""},options:{default:()=>[]},isCancel:{type:Boolean,default:!0},fieldSlots:{default:void 0},fieldChildrenSlot:{default:void 0}},emits:["change","update:modelValue"],setup(e,{expose:o,emit:u}){const t=e,r=u,i=I(),T=I(),h=we({radio:""});X(()=>t.modelValue,b=>{h.radio=b},{immediate:!0});const s=Be(),f=(b,L,m)=>{if(!(Reflect.has(s,"disabled")||m!=null&&m.disabled)){if(t.isCancel)b.preventDefault();else return;h.radio=h.radio===L?"":L,r("update:modelValue",h.radio),r("change",h.radio)}},V=b=>{t.isCancel||(r("update:modelValue",b),r("change",b))};return o({radioInstance:i,radioGroupInstance:T}),(b,L)=>(d(),g(l(Ge),y({ref_key:"radioGroupInstance",ref:T,modelValue:h.radio,"onUpdate:modelValue":L[0]||(L[0]=m=>h.radio=m),class:"plus-radio"},b.$attrs),le({default:c(()=>[l(el)?(d(!0),_(H,{key:0},O(b.options,m=>(d(),g(l(Se),y({key:`${m.label}${m.value}`,ref_for:!0,ref_key:"radioInstance",ref:i,label:m.value},m.fieldItemProps,{onClick:N=>f(N,m.value,m.fieldItemProps),onChange:N=>V(m.value)}),{default:c(()=>[l(A)(m.fieldSlot)?(d(),g(R(m.fieldSlot),y({key:0,"model-value":h.radio,column:t},m),null,16,["model-value"])):l(A)(b.fieldChildrenSlot)?(d(),g(R(b.fieldChildrenSlot),y({key:1,"model-value":h.radio,column:t},m),null,16,["model-value"])):(d(),_(H,{key:2},[Q(J(m==null?void 0:m.label),1)],64))]),_:2},1040,["label","onClick","onChange"]))),128)):(d(),_(H,{key:1},[j(" element-plus 版本号大于等于2.6.0 "),(d(!0),_(H,null,O(b.options,m=>(d(),g(l(Se),y({key:`${m.label}${m.value}`,ref_for:!0,ref_key:"radioInstance",ref:i,value:m.value},m.fieldItemProps,{onClick:N=>f(N,m.value,m.fieldItemProps),onChange:N=>V(m.value)}),{default:c(()=>[l(A)(m.fieldSlot)?(d(),g(R(m.fieldSlot),y({key:0,"model-value":h.radio,column:t},m),null,16,["model-value"])):l(A)(b.fieldChildrenSlot)?(d(),g(R(b.fieldChildrenSlot),y({key:1,"model-value":h.radio,column:t},m),null,16,["model-value"])):(d(),_(H,{key:2},[Q(J(m==null?void 0:m.label),1)],64))]),_:2},1040,["value","onClick","onChange"]))),128))],64))]),_:2},[O(b.fieldSlots,(m,N)=>({name:N,fn:c(w=>[(d(),g(R(m),B(q(w)),null,16))])}))]),1040,["modelValue"]))}}),la=se(ea,[["__file","index.vue"]]);const aa=la;var oa=te({name:"PlusRender",__name:"index",props:{renderType:{default:void 0},callbackValue:{default:""},customFieldProps:{default:()=>({})},render:{},params:{default:()=>({})},handleChange:{}},setup(e){const o=e,u=I();X(()=>o.callbackValue,r=>{u.value=r},{flush:"post",immediate:!0});const t=()=>{if(!o.render)return;const r=S({},o.params),i=o.renderType==="form"?o.render(u.value,o.handleChange,r):o.render(u.value,r);if(pl(i)){const T=o.renderType==="form"?S(S({modelValue:u.value},o.customFieldProps),i.props):S(S({},o.customFieldProps),i.props);return fe(S({},i),{props:T})}else if(Re(i))return i};return(r,i)=>r.renderType==="form"?(d(),g(R(t),y({key:0,modelValue:u.value,"onUpdate:modelValue":i[0]||(i[0]=T=>u.value=T)},r.customFieldProps),null,16,["modelValue"])):(d(),g(R(t),B(y({key:1},r.customFieldProps)),null,16))}}),ta=se(oa,[["__file","index.vue"]]);const Ue=ta,sa={class:"plus-date-picker__middle"};var na=te({name:"PlusDatePicker",__name:"index",props:{modelValue:{default:()=>[]},rangeSeparator:{default:"/"},valueFormat:{default:"YYYY-MM-DD HH:mm:ss"},type:{default:"datetime"},startProps:{default:()=>({})},endProps:{default:()=>({})},disabled:{type:Boolean,default:!1},startDisabledDate:{type:Function,default:(e,o)=>o?e.getTime()>new Date(o).getTime():!1},endDisabledDate:{type:Function,default:(e,o)=>o?e.getTime()<new Date(o).getTime():!1}},emits:["change","focus","update:modelValue"],setup(e,{expose:o,emit:u}){const t=e,r=u,{t:i}=he(),T=Be(),h=Y(()=>S(S({},T),t.startProps)),s=Y(()=>S(S({},T),t.endProps)),f=I(),V=I(),b=we({start:"",end:""}),L=ze(),m=I(!1),N=p=>{m.value=!0,r("focus",p)},w=()=>{m.value=!1},C=p=>t.startDisabledDate&&A(t.startDisabledDate)?t.startDisabledDate(p,b.end):!1,P=p=>t.endDisabledDate&&A(t.endDisabledDate)?t.endDisabledDate(p,b.start):!1;X(()=>t.modelValue,p=>{const[F,$]=p;b.start=F,b.end=$},{immediate:!0});const v=()=>{const p=[b.start,b.end];r("update:modelValue",p),r("change",p)};return o({startPickerInstance:f,endPickerInstance:V}),(p,F)=>He((d(),_("div",{class:qe(["plus-date-picker",{"is-focus":m.value,"is-disabled":l(L)}])},[ee(l(Ie),y({ref_key:"startPickerInstance",ref:f,modelValue:b.start,"onUpdate:modelValue":F[0]||(F[0]=$=>b.start=$),type:p.type,"value-format":p.valueFormat,placeholder:l(i)("plus.datepicker.startPlaceholder"),"disabled-date":C,class:"plus-date-picker__start",clearable:"",disabled:l(L)},h.value,{onChange:v,onFocus:N}),null,16,["modelValue","type","value-format","placeholder","disabled"]),be("span",sa,J(p.rangeSeparator),1),ee(l(Ie),y({ref_key:"endPickerInstance",ref:V,modelValue:b.end,"onUpdate:modelValue":F[1]||(F[1]=$=>b.end=$),"value-format":p.valueFormat,type:p.type,placeholder:l(i)("plus.datepicker.endPlaceholder"),"disabled-date":P,class:"plus-date-picker__end",clearable:"",disabled:l(L)},s.value,{onChange:v,onFocus:N}),null,16,["modelValue","value-format","type","placeholder","disabled"])],2)),[[l(Ke),w]])}}),ra=se(na,[["__file","index.vue"]]);const ua=ra;var ia=te({name:"PlusInputTag",__name:"index",props:{modelValue:{default:()=>[]},trigger:{default:()=>["blur","enter","space"]},inputProps:{default:()=>({})},tagProps:{default:()=>({})},limit:{default:1/0},formatTag:{type:Function,default:void 0},retainInputValue:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","change","remove","blur","enter","space"],setup(e,{expose:o,emit:u}){const t=e,r=u,i=I(),T=I(),h=I(),s=we({tags:[],inputValue:"",isFocus:!1}),f=ze(),{t:V}=he();X(()=>t.modelValue,C=>{s.tags=C.slice(0,t.limit)},{immediate:!0});const b=()=>{s.isFocus=!1},L=()=>{var C;s.isFocus=!0,(C=i.value)==null||C.focus()},m=C=>{f.value||(s.tags=s.tags.filter(P=>P!==C),r("remove",C),r("update:modelValue",s.tags),r("change",s.tags))},N=()=>{s.inputValue.trim()&&!s.tags.includes(s.inputValue.trim())&&s.tags.length<t.limit&&s.tags.push(s.inputValue.trim()),t.retainInputValue||(s.inputValue=""),r("update:modelValue",s.tags),r("change",s.tags)},w=(C,P)=>{r(P,s.inputValue,C),(ge(t.trigger)?t.trigger:Re(t.trigger)?[t.trigger]:["blur","enter","space"]).includes(P)&&N()};return o({inputInstance:i,tagInstance:T}),(C,P)=>He((d(),_("div",{ref_key:"plusInputTagInstance",ref:h,class:qe(["plus-input-tag",{"is-focus":s.isFocus,"is-disabled":l(f)}]),onClick:L},[(d(!0),_(H,null,O(s.tags,v=>(d(),g(l(cl),y({ref_for:!0,ref_key:"tagInstance",ref:T,key:v,class:"plus-input-tag__tag"},C.tagProps,{closable:"",onClose:p=>m(v)}),{default:c(()=>[Q(J(C.formatTag&&l(A)(C.formatTag)?C.formatTag(v):v),1)]),_:2},1040,["onClose"]))),128)),s.tags.length<C.limit?(d(),g(l(Ve),y({key:0,ref_key:"inputInstance",ref:i,modelValue:s.inputValue,"onUpdate:modelValue":P[0]||(P[0]=v=>s.inputValue=v),class:"plus-input-tag__input",placeholder:s.tags.length?"":l(V)("plus.inputTag.placeholder"),disabled:l(f)||s.tags.length>=C.limit},C.inputProps,{clearable:"",onBlur:P[1]||(P[1]=v=>w(v,"blur")),onKeyup:[P[2]||(P[2]=je(Fe(v=>w(v,"enter"),["exact"]),["enter"])),P[3]||(P[3]=je(Fe(v=>w(v,"space"),["exact"]),["space"]))]}),null,16,["modelValue","placeholder","disabled"])):j("v-if",!0)],2)),[[l(Ke),b]])}}),da=se(ia,[["__file","index.vue"]]);const pa=da,ll={"plus-radio":{component:aa,hasOptions:!0},"plus-date-picker":{component:ua},"plus-input-tag":{component:pa},autocomplete:{component:$l,props:{placeholder:"plus.field.pleaseEnter"},hasSelectEvent:!0},cascader:{component:El,hasOptions:!0},checkbox:{component:Cl,children:Tl,hasVersionCompatibility:!0},"color-picker":{component:Pl},"date-picker":{component:Ie,props:{startPlaceholder:"plus.datepicker.startPlaceholder",endPlaceholder:"plus.datepicker.endPlaceholder"}},"input-number":{component:Vl,props:{placeholder:"plus.field.pleaseEnter"}},radio:{component:Ge,children:Se,hasVersionCompatibility:!0},rate:{component:kl},select:{component:Ye,children:We},slider:{component:yl},switch:{component:gl},"time-picker":{component:hl},"time-select":{component:bl},transfer:{component:vl},input:{component:Ve,props:{placeholder:"plus.field.pleaseEnter"}},textarea:{component:Ve,props:{type:"textarea",placeholder:"plus.field.pleaseEnter"}},"tree-select":{component:ml},"select-v2":{component:fl,hasOptions:!0}},ca=e=>Object.keys(ll).includes(e),me=e=>Reflect.get(ll,e)||{},fa={class:"el-form-item__error"},ma={class:"plus-form-item__label"};var va=te({name:"PlusFormItem",__name:"index",props:{modelValue:{default:""},hasLabel:{default:!0},label:{default:""},prop:{},fieldProps:{default:()=>({})},valueType:{default:void 0},options:{default:()=>[]},formItemProps:{default:()=>({})},renderField:{default:void 0},renderLabel:{default:void 0},tooltip:{default:""},fieldSlots:{default:()=>({})},fieldChildrenSlot:{default:void 0},renderErrorMessage:{default:void 0},optionsMap:{default:void 0},index:{default:0},clearable:{type:Boolean,default:!0}},emits:["update:modelValue","change"],setup(e,{expose:o,emit:u}){const t=wl,r=Sl,i=Je,T=Ve,h=Ye,s=We,f=e,V=u,{t:b}=he(),{customOptions:L,customOptionsIsReady:m}=Yl(f),N=I(),w=I(),C=I({}),P=I({}),v=I(),p=I(!1),F=I(!1),$=Y(()=>Wl(f.label)),U=Ae(Jl,{}),Z=Ae(Ql,{}),n=Y(()=>fe(S(S({},f),l(Z)),{label:$.value,fieldProps:P.value,formItemProps:C.value,options:L.value})),W=Y(()=>{var a,G,D,k,ae,oe;return f.valueType==="cascader"&&((G=(a=P.value)==null?void 0:a.props)==null?void 0:G.emitPath)===!1?!1:!!(xl.includes(f.valueType)||f.valueType==="select"&&((D=P.value)==null?void 0:D.multiple)===!0||f.valueType==="date-picker"&&Xl.includes((k=P.value)==null?void 0:k.type)||f.valueType==="time-picker"&&((ae=P.value)==null?void 0:ae.isRange)===!0||f.valueType==="tree-select"&&((oe=P.value)==null?void 0:oe.multiple)===!0)}),M=Y(()=>!!Zl.includes(f.valueType)),z=a=>{if(W.value)if(ge(a)){const[G,D]=a;Ce(G)||Ce(D)?v.value=[String(G),String(D)]:v.value=a}else v.value=[];else M.value?v.value=a==null||a===""?null:typeof a=="string"?Number(a):a:Ce(a)?v.value=String(a):v.value=a;F.value=!0},K=Y(()=>{const{hasOptions:a,hasSelectEvent:G,props:D}=me(f.valueType);return S(S(fe(S(S(S({},a?{options:L.value}:null),G?{onSelect:al}:null),D),{placeholder:D!=null&&D.placeholder?b(D==null?void 0:D.placeholder)+$.value:b("plus.field.pleaseSelect")+$.value}),f.valueType==="date-picker"?{startPlaceholder:D!=null&&D.startPlaceholder?b(D==null?void 0:D.startPlaceholder):"",endPlaceholder:D!=null&&D.startPlaceholder?b(D==null?void 0:D.endPlaceholder):""}:null),P.value)}),x=a=>S(S({},f.valueType==="select"?{label:a.label,value:a.value}:el?{label:a.value}:{label:a.label,value:a.value}),a.fieldItemProps),ne=Y(()=>{var a;return(a=n.value.index)!=null?a:f.index});X(()=>[f.formItemProps,v.value],()=>{Ne(f.formItemProps,v.value,l(n),l(ne),"formItemProps").then(a=>{C.value=a}).catch(a=>{throw a})},{immediate:!0,deep:!0,flush:"post"}),X(()=>[f.fieldProps,v.value],()=>{Ne(f.fieldProps,v.value,l(n),l(ne),"fieldProps").then(a=>{P.value=a,p.value=!0}).catch(a=>{throw a})},{immediate:!0,deep:!0,flush:"post"}),X(Y(()=>[f.modelValue,p.value,m.value]),([a,G,D])=>{G&&D&&z(a)},{immediate:!0,flush:"post"});const ue=a=>{V("update:modelValue",a),V("change",a)},al=({value:a})=>{ue(a)};return X(w,()=>{U.value={fieldInstance:w.value,valueIsReady:F}}),o({formItemInstance:N,fieldInstance:w}),(a,G)=>{var D;return F.value?(d(),g(l(t),y({key:0,ref_key:"formItemInstance",ref:N,label:a.hasLabel?$.value:"",prop:a.prop,class:"plus-form-item"},C.value,{"label-width":a.hasLabel?(D=C.value)==null?void 0:D.labelWidth:"0px"}),le({default:c(()=>[a.renderField&&l(A)(a.renderField)?(d(),_(H,{key:0},[F.value?(d(),g(l(Ue),{key:0,render:a.renderField,params:n.value,"callback-value":v.value,"custom-field-props":P.value,"render-type":"form","handle-change":ue},null,8,["render","params","callback-value","custom-field-props"])):j("v-if",!0)],64)):a.$slots[l(re)(a.prop)]?E(a.$slots,l(re)(a.prop),y({key:1},n.value,{column:f})):a.valueType==="select"&&P.value.multiple===!0?(d(),g(l(h),y({key:2,ref_key:"fieldInstance",ref:w,modelValue:v.value,"onUpdate:modelValue":G[0]||(G[0]=k=>v.value=k),placeholder:l(b)("plus.field.pleaseSelect")+$.value,class:"plus-form-item-field",clearable:a.clearable},P.value,{"onUpdate:modelValue":ue}),le({default:c(()=>[(d(!0),_(H,null,O(l(L),k=>(d(),g(l(s),y({key:k.label,label:k.label,value:k.value},k.fieldItemProps),{default:c(()=>[l(A)(k.fieldSlot)?(d(),g(R(k.fieldSlot),B(y({key:0},k)),null,16)):l(A)(a.fieldChildrenSlot)?(d(),g(R(a.fieldChildrenSlot),B(y({key:1},k)),null,16)):(d(),_(H,{key:2},[Q(J(k.label),1)],64))]),_:2},1040,["label","value"]))),128))]),_:2},[O(a.fieldSlots,(k,ae)=>({name:ae,fn:c(oe=>[(d(),g(R(k),B(q(oe)),null,16))])}))]),1040,["modelValue","placeholder","clearable"])):l(ca)(a.valueType)?(d(),_(H,{key:3},[j(" 统一处理 "),j(" has-children  "),l(me)(a.valueType).children?(d(),g(R(l(me)(a.valueType).component),y({key:0,ref_key:"fieldInstance",ref:w,modelValue:v.value,"onUpdate:modelValue":G[1]||(G[1]=k=>v.value=k),class:"plus-form-item-field",clearable:a.clearable},K.value,{"onUpdate:modelValue":ue}),le({default:c(()=>[(d(!0),_(H,null,O(l(L),k=>(d(),g(R(l(me)(a.valueType).children),y({key:k.label},x(k)),{default:c(()=>[l(A)(k.fieldSlot)?(d(),g(R(k.fieldSlot),y({key:0,"model-value":v.value,column:n.value},k),null,16,["model-value","column"])):l(A)(a.fieldChildrenSlot)?(d(),g(R(a.fieldChildrenSlot),y({key:1,"model-value":v.value,column:n.value},k),null,16,["model-value","column"])):(d(),_(H,{key:2},[Q(J(k.label),1)],64))]),_:2},1040))),128))]),_:2},[O(a.fieldSlots,(k,ae)=>({name:ae,fn:c(oe=>[(d(),g(R(k),y({value:v.value,column:n.value},oe),null,16,["value","column"]))])}))]),1040,["modelValue","clearable"])):(d(),_(H,{key:1},[j(" no-children  "),(d(),g(R(l(me)(a.valueType).component),y({ref_key:"fieldInstance",ref:w,modelValue:v.value,"onUpdate:modelValue":G[2]||(G[2]=k=>v.value=k),class:"plus-form-item-field",clearable:a.clearable,"field-children-slot":a.fieldChildrenSlot},K.value,{"onUpdate:modelValue":ue}),le({_:2},[O(a.fieldSlots,(k,ae)=>({name:ae,fn:c(oe=>[(d(),g(R(k),y({"model-value":v.value,column:n.value},oe),null,16,["model-value","column"]))])}))]),1040,["modelValue","clearable","field-children-slot"]))],2112))],64)):a.valueType==="text"?(d(),g(l(Fl),y({key:4,ref_key:"fieldInstance",ref:w,class:"plus-form-item-field"},P.value),{default:c(()=>[Q(J(v.value),1)]),_:1},16)):a.valueType==="divider"?(d(),g(l(Dl),y({key:5,ref_key:"fieldInstance",ref:w,class:"plus-form-item-field"},P.value),{default:c(()=>[Q(J(v.value),1)]),_:1},16)):(d(),g(l(T),y({key:6,ref_key:"fieldInstance",ref:w,modelValue:v.value,"onUpdate:modelValue":G[3]||(G[3]=k=>v.value=k),class:"plus-form-item-field",placeholder:l(b)("plus.field.pleaseEnter")+$.value,autocomplete:"off",clearable:a.clearable},P.value,{"onUpdate:modelValue":ue}),le({_:2},[O(a.fieldSlots,(k,ae)=>({name:ae,fn:c(oe=>[(d(),g(R(k),y({"model-value":v.value,column:n.value},oe),null,16,["model-value","column"]))])}))]),1040,["modelValue","placeholder","clearable"]))]),_:2},[l(A)(a.renderErrorMessage)?{name:"error",fn:c(({error:k})=>[be("div",fa,[(d(),g(R(a.renderErrorMessage),y(f,{value:v.value,error:k,label:$.value}),null,16,["value","error","label"]))])]),key:"0"}:void 0,a.hasLabel?{name:"label",fn:c(({label:k})=>[be("span",ma,[a.renderLabel&&l(A)(a.renderLabel)?(d(),_(H,{key:0},[F.value?(d(),g(l(Ue),{key:0,render:a.renderLabel,params:n.value,"callback-value":k,"custom-field-props":P.value},null,8,["render","params","callback-value","custom-field-props"])):j("v-if",!0)],64)):E(a.$slots,l(ie)(a.prop),B(y({key:1},n.value)),()=>[Q(J(k),1)]),a.tooltip?(d(),g(l(r),y({key:2,placement:"top"},l(zl)(a.tooltip)),{default:c(()=>[E(a.$slots,"tooltip-icon",{},()=>[ee(l(i),{class:"plus-table-column__label__icon",size:16},{default:c(()=>[ee(l(Il))]),_:1})])]),_:3},16)):j("v-if",!0)])]),key:"1"}:void 0]),1040,["label","prop","label-width"])):j("v-if",!0)}}}),ba=se(va,[["__file","index.vue"]]);const ha=ba;var ga=te({name:"PlusCollapseTransition",__name:"collapse-transition",props:{collapseDuration:{default:300},collapseTransition:{type:Boolean,default:!0}},setup(e){const o=e,u={beforeEnter(t){t.style.opacity=0},enter(t,r){requestAnimationFrame(()=>{t.style.transition=`opacity ${o.collapseDuration}ms linear`,t.style.opacity=1,r()})},leave(t,r){t.style.opacity=0,setTimeout(()=>{r()},o.collapseDuration/3*2)}};return(t,r)=>t.collapseTransition?(d(),g(Ll,y({key:0,name:"plus-collapse-transition",css:!1},Bl(u)),{default:c(()=>[E(t.$slots,"default")]),_:3},16)):E(t.$slots,"default",{key:1})}}),ya=se(ga,[["__file","collapse-transition.vue"]]);const ka={key:0,class:"plus-form-item-extra"};var Va=te({name:"PlusFormContent",__name:"form-content",props:{modelValue:{default:()=>({})},hasLabel:{type:Boolean,default:!0},columns:{default:()=>[]},rowProps:{default:()=>({})},colProps:{default:()=>({})},collapseDuration:{default:void 0},collapseTransition:{type:Boolean,default:void 0},clearable:{type:Boolean,default:!0}},emits:["update:modelValue","change"],setup(e,{emit:o}){const u=e,t=o,r=I({}),i=s=>{const f=l(s);return Ml(f)?f:u.hasLabel};X(()=>u.modelValue,s=>{r.value=s},{immediate:!0});const T=s=>Kl(r.value,s),h=(s,f)=>{ql(r.value,f.prop,s),t("update:modelValue",r.value),t("change",r.value,f)};return(s,f)=>(d(),g(l(_l),y(s.rowProps,{class:"plus-form__row"}),{default:c(()=>[ee(ya,{"collapse-duration":s.collapseDuration,"collapse-transition":s.collapseTransition},{default:c(()=>[(d(!0),_(H,null,O(s.columns,V=>(d(),g(l(Rl),y({key:V.prop},V.colProps||s.colProps),{default:c(()=>[ee(l(ha),y({"model-value":T(V.prop)},V,{clearable:s.clearable,"has-label":i(V.hasLabel),onChange:b=>h(b,V)}),le({_:2},[s.$slots[l(ie)(V.prop)]?{name:l(ie)(V.prop),fn:c(b=>[E(s.$slots,l(ie)(V.prop),B(q(b)))]),key:"0"}:void 0,s.$slots[l(re)(V.prop)]?{name:l(re)(V.prop),fn:c(b=>[E(s.$slots,l(re)(V.prop),B(q(b)))]),key:"1"}:void 0,s.$slots["tooltip-icon"]?{name:"tooltip-icon",fn:c(()=>[E(s.$slots,"tooltip-icon")]),key:"2"}:void 0]),1040,["model-value","clearable","has-label","onChange"]),j(" el-form-item 下一行额外的内容 "),V.renderExtra||s.$slots[l(ve)(V.prop)]?(d(),_("div",ka,[V.renderExtra&&l(A)(V.renderExtra)?(d(),g(R(V.renderExtra),B(y({key:0},V)),null,16)):s.$slots[l(ve)(V.prop)]?E(s.$slots,l(ve)(V.prop),B(y({key:1},V))):j("v-if",!0)])):j("v-if",!0)]),_:2},1040))),128))]),_:3},8,["collapse-duration","collapse-transition"]),j(" 搜索的footer插槽  "),E(s.$slots,"search-footer")]),_:3},16))}}),Me=se(Va,[["__file","form-content.vue"]]);const Pa={class:"plus-form__group__item__icon"};var Ta=te({name:"PlusForm",inheritAttrs:!1,__name:"index",props:{modelValue:{default:()=>({})},defaultValues:{default:()=>({})},columns:{default:()=>[]},labelWidth:{default:"80px"},labelPosition:{default:"left"},rowProps:{default:()=>({})},colProps:{default:()=>({})},labelSuffix:{default:":"},hasErrorTip:{type:Boolean,default:!0},hasFooter:{type:Boolean,default:!0},hasReset:{type:Boolean,default:!0},hasLabel:{type:Boolean,default:!0},submitText:{default:""},resetText:{default:""},submitLoading:{type:Boolean,default:!1},footerAlign:{default:"left"},rules:{default:()=>({})},group:{type:[Boolean,Array],default:!1},cardProps:{default:()=>({})},prevent:{type:Boolean,default:!1},collapseDuration:{default:void 0},collapseTransition:{type:Boolean,default:void 0},clearable:{type:Boolean,default:!0}},emits:["update:modelValue","submit","change","reset","submitError","validate"],setup(e,{expose:o,emit:u}){const t=e,r=u,{t:i}=he(),T=I(null),h=I({}),s=n=>n.filter(W=>l(W.hideInForm)!==!0),f=Y(()=>h.value),V=Y(()=>({justifyContent:t.footerAlign==="left"?"flex-start":t.footerAlign==="center"?"center":"flex-end"})),b=Y(()=>s(t.columns)),L=Y(()=>{var n;return ge(t.group)?(n=t.group)==null?void 0:n.filter(W=>l(W.hideInGroup)!==!0):t.group}),m=Be(),N=Y(()=>S(S({},m),t.prevent?{onSubmit:Fe((...n)=>{m!=null&&m.onSubmit&&A(m==null?void 0:m.onSubmit)&&m.onSubmit(...n)},["prevent"])}:{})),w=Qe(),C=de(w,ie()),P=de(w,re()),v=de(w,ve());X(()=>t.modelValue,n=>{h.value=n},{immediate:!0});const p=(n,W)=>{r("update:modelValue",h.value),r("change",h.value,W)},F=()=>{var n;(n=T.value)==null||n.clearValidate()},$=()=>ye(null,null,function*(){var n,W,M;try{if(yield(n=T.value)==null?void 0:n.validate())return r("submit",h.value),!0}catch(z){if(t.hasErrorTip){Te.closeAll();const K=pe(z)&&Object.values(z),x=K?(M=(W=K[0])==null?void 0:W[0])==null?void 0:M.message:void 0;Te.warning(x||i("plus.form.errorTip"))}r("submitError",z)}return!1}),U=()=>{F(),h.value=S({},t.defaultValues),r("update:modelValue",h.value),r("reset",h.value)},Z=(...n)=>{r("validate",...n)};return o({formInstance:T,handleSubmit:$,handleReset:U}),(n,W)=>(d(),g(l(Al),y({ref_key:"formInstance",ref:T,rules:n.rules,"label-width":n.hasLabel?n.labelWidth:0,class:["plus-form",n.hasLabel?"":"no-has-label"],"label-position":n.labelPosition,"validate-on-rule-change":!1,"label-suffix":n.hasLabel?n.labelSuffix:""},N.value,{model:f.value,onValidate:Z}),{default:c(()=>[E(n.$slots,"default",{},()=>[j(" 分组表单 "),L.value?(d(!0),_(H,{key:0},O(L.value,(M,z)=>(d(),g(l(Ol),y({key:l(M.title)},M.cardProps||n.cardProps,{class:"plus-form__group__item"}),{header:c(()=>[E(n.$slots,"group-header",{title:l(M.title),columns:M.columns,icon:M.icon,index:z},()=>[be("div",Pa,[M.icon?(d(),g(l(Je),{key:0},{default:c(()=>[(d(),g(R(M.icon)))]),_:2},1024)):j("v-if",!0),Q(" "+J(l(M.title)),1)])])]),default:c(()=>[ee(Me,{modelValue:h.value,"onUpdate:modelValue":W[0]||(W[0]=K=>h.value=K),"row-props":n.rowProps,"col-props":n.colProps,columns:s(M.columns),"has-label":n.hasLabel,"collapse-transition":n.collapseTransition,"collapse-duration":n.collapseDuration,clearable:n.clearable,onChange:p},le({_:2},[O(l(C),(K,x)=>({name:x,fn:c(ne=>[E(n.$slots,x,B(q(ne)))])})),O(l(P),(K,x)=>({name:x,fn:c(ne=>[E(n.$slots,x,B(q(ne)))])})),O(l(v),(K,x)=>({name:x,fn:c(ne=>[E(n.$slots,x,B(q(ne)))])})),n.$slots["tooltip-icon"]?{name:"tooltip-icon",fn:c(()=>[E(n.$slots,"tooltip-icon")]),key:"0"}:void 0]),1032,["modelValue","row-props","col-props","columns","has-label","collapse-transition","collapse-duration","clearable"])]),_:2},1040))),128)):(d(),_(H,{key:1},[j(" 普通表单 "),ee(Me,{modelValue:h.value,"onUpdate:modelValue":W[1]||(W[1]=M=>h.value=M),"row-props":n.rowProps,"col-props":n.colProps,columns:b.value,"has-label":n.hasLabel,"collapse-transition":n.collapseTransition,"collapse-duration":n.collapseDuration,clearable:n.clearable,onChange:p},le({_:2},[O(l(C),(M,z)=>({name:z,fn:c(K=>[E(n.$slots,z,B(q(K)))])})),O(l(P),(M,z)=>({name:z,fn:c(K=>[E(n.$slots,z,B(q(K)))])})),O(l(v),(M,z)=>({name:z,fn:c(K=>[E(n.$slots,z,B(q(K)))])})),n.$slots["search-footer"]?{name:"search-footer",fn:c(()=>[E(n.$slots,"search-footer")]),key:"0"}:void 0,n.$slots["tooltip-icon"]?{name:"tooltip-icon",fn:c(()=>[E(n.$slots,"tooltip-icon")]),key:"1"}:void 0]),1032,["modelValue","row-props","col-props","columns","has-label","collapse-transition","collapse-duration","clearable"])],64))]),n.hasFooter?(d(),_("div",{key:0,class:"plus-form__footer",style:jl(V.value)},[E(n.$slots,"footer",B(q({handleReset:U,handleSubmit:$})),()=>[n.hasReset?(d(),g(l(Pe),{key:0,onClick:U},{default:c(()=>[j(" 重置 "),Q(" "+J(n.resetText||l(i)("plus.form.resetText")),1)]),_:1})):j("v-if",!0),ee(l(Pe),{type:"primary",loading:n.submitLoading,onClick:$},{default:c(()=>[j(" 提交 "),Q(" "+J(n.submitText||l(i)("plus.form.submitText")),1)]),_:1},8,["loading"])])],4)):j("v-if",!0)]),_:3},16,["rules","label-width","class","label-position","label-suffix","model"]))}}),Ca=se(Ta,[["__file","index.vue"]]);const Ea=Ca,$a={class:"plus-drawer-form__footer"};var Sa=te({name:"PlusDrawerForm",__name:"index",props:{modelValue:{default:()=>({})},visible:{type:Boolean,default:!1},drawer:{default:()=>({})},size:{default:"540px"},form:{default:()=>({})},hasFooter:{type:Boolean,default:!0},cancelText:{default:""},confirmText:{default:""},confirmLoading:{type:Boolean,default:!1},hasErrorTip:{type:Boolean,default:!0}},emits:["update:modelValue","update:visible","confirm","change","cancel","confirmError"],setup(e,{expose:o,emit:u}){const t=e,r=u,{t:i}=he(),T=I(null),h=Y(()=>{var p;return(p=T.value)==null?void 0:p.formInstance}),s=I(),f=I({}),V=I(!1),b=Qe(),L=de(b,ie()),m=de(b,re()),N=de(b,ve());X(()=>t.visible,p=>{V.value=p},{immediate:!0}),X(()=>t.modelValue,p=>{f.value=p},{immediate:!0});const w=(p,F)=>{r("update:modelValue",p),r("change",p,F)},C=()=>ye(null,null,function*(){var p,F,$;try{(yield(p=h.value)==null?void 0:p.validate())&&r("confirm",f.value)}catch(U){if(t.hasErrorTip){Te.closeAll();const Z=pe(U)&&Object.values(U),n=Z?($=(F=Z[0])==null?void 0:F[0])==null?void 0:$.message:void 0;Te.warning(n||i("plus.form.errorTip"))}r("confirmError",U)}}),P=()=>{v(),r("update:visible",V.value),r("cancel")},v=()=>{V.value=!1};return o({drawerInstance:s,formInstance:h}),(p,F)=>(d(),g(l(Nl),y({ref_key:"drawerInstance",ref:s,modelValue:V.value,"onUpdate:modelValue":F[1]||(F[1]=$=>V.value=$),class:"plus-drawer-form",size:p.size||"540px",title:l(i)("plus.drawerForm.title"),"close-on-click-modal":!1,"close-on-press-escape":!1},p.$attrs,{onClose:P}),le({default:c(()=>[ee(l(Ea),y({ref_key:"formInstance",ref:T,modelValue:f.value,"onUpdate:modelValue":F[0]||(F[0]=$=>f.value=$),"has-footer":!1},p.form,{onChange:w}),le({_:2},[p.$slots["form-footer"]?{name:"footer",fn:c(()=>[E(p.$slots,"form-footer")]),key:"0"}:void 0,p.$slots["form-group-header"]?{name:"group-header",fn:c($=>[E(p.$slots,"form-group-header",B(q($)))]),key:"1"}:void 0,O(l(L),($,U)=>({name:U,fn:c(Z=>[E(p.$slots,U,B(q(Z)))])})),O(l(m),($,U)=>({name:U,fn:c(Z=>[E(p.$slots,U,B(q(Z)))])})),O(l(N),($,U)=>({name:U,fn:c(Z=>[E(p.$slots,U,B(q(Z)))])})),p.$slots["tooltip-icon"]?{name:"tooltip-icon",fn:c(()=>[E(p.$slots,"tooltip-icon")]),key:"2"}:void 0]),1040,["modelValue"])]),_:2},[p.$slots["drawer-header"]?{name:"header",fn:c(()=>[E(p.$slots,"drawer-header")]),key:"0"}:void 0,p.hasFooter?{name:"footer",fn:c(()=>[be("div",$a,[E(p.$slots,"drawer-footer",B(q({handleConfirm:C,handleCancel:v})),()=>[ee(l(Pe),{onClick:v},{default:c(()=>[Q(J(p.cancelText||l(i)("plus.drawerForm.cancelText")),1)]),_:1}),ee(l(Pe),{type:"primary",loading:p.confirmLoading,onClick:C},{default:c(()=>[Q(J(p.confirmText||l(i)("plus.drawerForm.confirmText")),1)]),_:1},8,["loading"])])])]),key:"1"}:void 0]),1040,["modelValue","size","title"]))}}),Ia=se(Sa,[["__file","index.vue"]]);const _a=Ia;export{_a as P,Ql as T,Re as a,ge as b,re as c,ve as d,Ea as e,Ue as f,Kl as g,Ba as h,A as i,Ne as j,Wl as k,La as l,Ra as m,ql as s,Yl as u};
