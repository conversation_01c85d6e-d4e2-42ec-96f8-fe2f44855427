<?php

namespace Modules\Sidebar\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Core\Traits\Filterable;
use Modules\Core\Traits\ModelTrait;

class Sidebar extends Model
{
    use ModelTrait, Filterable, SoftDeletes;

    protected $fillable = [
        'menu_type',
        'parent_id',
        'title',
        'name',
        'path',
        'component',
        'rank',
        'redirect',
        'icon',
        'extra_icon',
        'enter_transition',
        'leave_transition',
        'active_path',
        'roles',
        'auths',
        'frame_src',
        'frame_loading',
        'keep_alive',
        'hidden_tag',
        'fixed_tag',
        'show_link',
        'show_parent'
    ];

    protected $casts = [
        'menu_type' => 'integer',
        'parent_id' => 'integer',
        'rank' => 'integer',
        'roles' => 'array',
        'auths' => 'array',
        'frame_loading' => 'boolean',
        'keep_alive' => 'boolean',
        'hidden_tag' => 'boolean',
        'fixed_tag' => 'boolean',
        'show_link' => 'boolean',
        'show_parent' => 'boolean'
    ];

    protected array $filterable = [
        'menu_type',
        'parent_id',
        'path',
        'rank',
        'frame_loading',
        'keep_alive',
        'hidden_tag',
        'fixed_tag',
        'show_link',
        'show_parent'
    ];

    protected array $searchable = [
        'title',
        'name',
        'path',
        'component'
    ];

    /**
     * Get the parent sidebar
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Sidebar::class, 'parent_id');
    }

    /**
     * Get the children sidebars
     */
    public function children(): HasMany
    {
        return $this->hasMany(Sidebar::class, 'parent_id');
    }

    /**
     * Get all sidebars ordered by rank
     */
    public static function getSidebars(): Collection
    {
        return static::orderBy('rank')->get();
    }

    /**
     * Build router tree from array of routes
     */
    public static function  buildRouterTree(array $routes, ?int $parentId = 0): array
    {
        return (new static)->buildTree($routes, $parentId);
    }

    /**
     * Recursively build tree structure
     */
    protected function buildTree(array $routes, ?int $parentId = null): array
    {
        $tree = [];

        foreach ($routes as $route) {
            if ($route['parent_id'] === $parentId) {
                $children = $this->buildTree($routes, $route['id']);
                if (!empty($children)) {
                    $route['children'] = $children;
                }
                $tree[] = $this->transformRoute($route);

            }
        }
        return $tree;
    }

    /**
     * Transform route array to required format
     */
    protected function transformRoute(array $route): array
    {
        $meta = [];
        foreach ($this->fillable as $field) {
            if (isset($route[$field]) && !in_array($field, ['path', 'name', 'component', 'children'])) {
                if ($route[$field] !== null) {
                    if (in_array($field, ['enter_transition', 'leave_transition'])) {
                        $meta['transition'][$field] = $route[$field];
                    } else {
                        $meta[$field] = $route[$field];
                    }
                }
            }
        }

        $result = [
            'path' => $route['path'] ?? null,
            'name' => $route['name'] ?? null,
            'component' => $route['component'] ?? null,
            'meta' => !empty($meta) ? $meta : null,
            'children' => $route['children'] ?? null
        ];

        \Log::debug("Before filter:", $result);
        $filtered = array_filter($result, fn($value) => $value !== null);
        \Log::debug("After filter:", $filtered);

        return $filtered;
    }
}
