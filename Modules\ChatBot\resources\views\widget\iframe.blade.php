<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{{ $bot->name }} - Chatbot Widget</title>
    
    <!-- Security Headers -->
    <meta http-equiv="Content-Security-Policy" content="frame-ancestors *;">
    
    <!-- Favicon -->
    @if($bot->logo_url)
    <link rel="icon" type="image/png" href="{{ $bot->logo_url }}">
    @endif
    
    <!-- Widget Styles -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            height: 100%;
            font-family: {{ $bot->theme['font_family'] ?? 'system-ui, -apple-system, sans-serif' }};
            background: {{ $bot->theme['background_color'] ?? '#ffffff' }};
            color: {{ $bot->theme['text_color'] ?? '#333333' }};
            overflow: hidden;
        }
        
        #widget-container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .widget-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            flex-direction: column;
            gap: 16px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid {{ $bot->theme['primary_color'] ?? '#007bff' }};
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            color: #666;
            font-size: 14px;
        }
        
        .widget-error {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            flex-direction: column;
            gap: 16px;
            padding: 20px;
            text-align: center;
        }
        
        .error-icon {
            width: 48px;
            height: 48px;
            background: #ff4757;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        
        .error-message {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .retry-button {
            background: {{ $bot->theme['primary_color'] ?? '#007bff' }};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: {{ $bot->theme['border_radius'] ?? '8px' }};
            cursor: pointer;
            font-size: 14px;
        }
        
        .retry-button:hover {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div id="widget-container">
        <!-- Loading State -->
        <div id="loading-state" class="widget-loading">
            <div class="loading-spinner"></div>
            <div class="loading-text">Đang tải {{ $bot->name }}...</div>
        </div>
        
        <!-- Error State -->
        <div id="error-state" class="widget-error" style="display: none;">
            <div class="error-icon">!</div>
            <div class="error-message" id="error-message">Có lỗi xảy ra khi tải widget.</div>
            <button class="retry-button" onclick="retryLoad()">Thử lại</button>
        </div>
        
        <!-- Widget will be mounted here -->
        <div id="chat-widget" style="display: none; width: 100%; height: 100%;"></div>
    </div>

    <!-- Widget Configuration -->
    <script>
        // Widget configuration from server
        window.WIDGET_CONFIG = @json($config);
        window.API_BASE_URL = @json($apiBaseUrl);
        window.BOT_CONFIG = @json([
            'uuid' => $bot->uuid,
            'name' => $bot->name,
            'description' => $bot->description,
            'logoUrl' => $bot->logo_url,
            'greetingMessage' => $bot->greeting_message,
            'starterMessages' => $bot->starter_messages,
            'status' => $bot->status->value,
            'theme' => $bot->theme
        ]);
        
        // Widget initialization
        let widgetInstance = null;
        let retryCount = 0;
        const maxRetries = 3;
        
        function showLoading() {
            document.getElementById('loading-state').style.display = 'flex';
            document.getElementById('error-state').style.display = 'none';
            document.getElementById('chat-widget').style.display = 'none';
        }
        
        function showError(message) {
            document.getElementById('loading-state').style.display = 'none';
            document.getElementById('error-state').style.display = 'flex';
            document.getElementById('chat-widget').style.display = 'none';
            document.getElementById('error-message').textContent = message;
        }
        
        function showWidget() {
            document.getElementById('loading-state').style.display = 'none';
            document.getElementById('error-state').style.display = 'none';
            document.getElementById('chat-widget').style.display = 'block';
        }
        
        function retryLoad() {
            if (retryCount < maxRetries) {
                retryCount++;
                initializeWidget();
            } else {
                showError('Không thể tải widget sau nhiều lần thử. Vui lòng liên hệ hỗ trợ.');
            }
        }
        
        async function initializeWidget() {
            try {
                showLoading();
                
                // Load widget library (you'll need to implement this)
                // For now, we'll create a simple chat interface
                await createSimpleChatInterface();
                
                showWidget();
                
                // Notify parent window that widget is ready
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'widget-ready',
                        data: { botConfig: window.BOT_CONFIG }
                    }, '*');
                }
                
            } catch (error) {
                console.error('Widget initialization failed:', error);
                showError('Không thể khởi tạo widget: ' + error.message);
            }
        }
        
        async function createSimpleChatInterface() {
            const container = document.getElementById('chat-widget');
            
            // Create basic chat interface
            container.innerHTML = `
                <div style="display: flex; flex-direction: column; height: 100%; background: white;">
                    <!-- Header -->
                    <div style="background: ${window.BOT_CONFIG.theme?.primary_color || '#007bff'}; color: white; padding: 15px; display: flex; align-items: center; gap: 10px;">
                        ${window.BOT_CONFIG.logoUrl ? `<img src="${window.BOT_CONFIG.logoUrl}" alt="Bot Avatar" style="width: 32px; height: 32px; border-radius: 50%;">` : ''}
                        <div>
                            <div style="font-weight: bold; font-size: 16px;">${window.BOT_CONFIG.name}</div>
                            <div style="font-size: 12px; opacity: 0.9;">Online</div>
                        </div>
                    </div>
                    
                    <!-- Messages -->
                    <div id="messages-container" style="flex: 1; padding: 15px; overflow-y: auto; background: #f8f9fa;">
                        ${window.BOT_CONFIG.greetingMessage ? `
                            <div style="background: white; padding: 12px; border-radius: 12px; margin-bottom: 10px; box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                                ${window.BOT_CONFIG.greetingMessage}
                            </div>
                        ` : ''}
                        
                        ${window.BOT_CONFIG.starterMessages && window.BOT_CONFIG.starterMessages.length > 0 ? `
                            <div style="margin-top: 10px;">
                                <div style="font-size: 12px; color: #666; margin-bottom: 8px;">Gợi ý câu hỏi:</div>
                                ${window.BOT_CONFIG.starterMessages.map(msg => `
                                    <button onclick="sendStarterMessage('${msg}')" style="display: block; width: 100%; text-align: left; background: white; border: 1px solid #ddd; padding: 8px 12px; margin-bottom: 5px; border-radius: 8px; cursor: pointer; font-size: 14px;">
                                        ${msg}
                                    </button>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                    
                    <!-- Input -->
                    <div style="padding: 15px; border-top: 1px solid #eee; background: white;">
                        <div style="display: flex; gap: 10px;">
                            <input type="text" id="message-input" placeholder="Nhập tin nhắn..." style="flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 20px; outline: none;">
                            <button onclick="sendMessage()" style="background: ${window.BOT_CONFIG.theme?.primary_color || '#007bff'}; color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer;">Gửi</button>
                        </div>
                    </div>
                </div>
            `;
            
            // Add enter key listener
            document.getElementById('message-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        }
        
        function sendStarterMessage(message) {
            document.getElementById('message-input').value = message;
            sendMessage();
        }
        
        async function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message to UI
            addMessageToUI('user', message);
            input.value = '';
            
            // Add loading message
            const loadingId = addMessageToUI('bot', 'Đang xử lý...', true);
            
            try {
                // Here you would integrate with the actual API
                // For now, we'll simulate a response
                setTimeout(() => {
                    updateMessage(loadingId, 'Cảm ơn bạn đã nhắn tin! Đây là phản hồi mẫu từ ' + window.BOT_CONFIG.name + '.');
                }, 1000);
                
            } catch (error) {
                updateMessage(loadingId, 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại.');
            }
        }
        
        function addMessageToUI(type, content, isLoading = false) {
            const container = document.getElementById('messages-container');
            const messageId = 'msg_' + Date.now();
            
            const messageDiv = document.createElement('div');
            messageDiv.id = messageId;
            messageDiv.style.cssText = `
                margin-bottom: 10px;
                display: flex;
                ${type === 'user' ? 'justify-content: flex-end;' : 'justify-content: flex-start;'}
            `;
            
            const contentDiv = document.createElement('div');
            contentDiv.style.cssText = `
                max-width: 80%;
                padding: 12px;
                border-radius: 12px;
                ${type === 'user' 
                    ? `background: ${window.BOT_CONFIG.theme?.primary_color || '#007bff'}; color: white;`
                    : `background: white; color: #333; box-shadow: 0 1px 2px rgba(0,0,0,0.1);`
                }
                ${isLoading ? 'font-style: italic; opacity: 0.7;' : ''}
            `;
            contentDiv.textContent = content;
            
            messageDiv.appendChild(contentDiv);
            container.appendChild(messageDiv);
            
            // Scroll to bottom
            container.scrollTop = container.scrollHeight;
            
            return messageId;
        }
        
        function updateMessage(messageId, newContent) {
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                const contentDiv = messageElement.querySelector('div');
                contentDiv.textContent = newContent;
                contentDiv.style.fontStyle = 'normal';
                contentDiv.style.opacity = '1';
            }
        }
        
        // Initialize widget when page loads
        document.addEventListener('DOMContentLoaded', initializeWidget);
        
        // Handle messages from parent window
        window.addEventListener('message', function(event) {
            // Handle parent window messages if needed
            console.log('Received message from parent:', event.data);
        });
    </script>
</body>
</html>
