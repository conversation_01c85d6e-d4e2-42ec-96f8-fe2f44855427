<?php

namespace Modules\ChatBot\Http\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\BotShareLink;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Enums\ContentType;
use Modules\Core\Traits\ResponseTrait;
use Modules\ChatBot\Facades\MessageFacade;
use Illuminate\Support\Str;

class WidgetMessageController extends Controller
{
    use ResponseTrait;

    /**
     * Get messages for conversation.
     */
    public function index(Request $request, string $conversationUuid): JsonResponse
    {
        try {
            $conversation = Conversation::with(['bot'])
                                      ->where('uuid', $conversationUuid)
                                      ->active()
                                      ->firstOrFail();

            // Validate bot access
            $bot = $this->validateBotAccess($request, $conversation->bot->uuid);
            if (!$bot) {
                return $this->errorResponse('Bot access denied', 403);
            }

            // Get messages (latest first for widget)
            $messages = Message::where('conversation_id', $conversation->id)
                              ->where('status', MessageStatus::COMPLETED)
                              ->orderBy('created_at', 'asc')
                              ->orderBy('id', 'asc')
                              ->limit(100)
                              ->get()
                              ->map(function ($message) {
                                  return [
                                      'id' => $message->uuid,
                                      'type' => $message->role === MessageRole::USER ? 'user' : 'bot',
                                      'content' => $message->content,
                                      'timestamp' => $message->created_at?->toISOString(),
                                  ];
                              });

            return $this->successResponse([
                'messages' => $messages,
                'total' => $messages->count(),
            ], 'Messages retrieved successfully');

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Send message and get AI response.
     */
    public function sendMessage(Request $request, string $conversationUuid): JsonResponse
    {
        try {
            $request->validate([
                'message' => 'required|string|max:2000',
                'messageType' => 'nullable|string|in:text,quick_reply,postback',
                'attachments' => 'nullable|array|max:5',
                'attachments.*.type' => 'required_with:attachments|string',
                'attachments.*.url' => 'required_with:attachments|url',
                'attachments.*.name' => 'nullable|string',
                'metadata' => 'nullable|array',
            ]);

            $conversation = Conversation::with(['bot'])
                                      ->where('uuid', $conversationUuid)
                                      ->active()
                                      ->firstOrFail();

            // Validate bot access
            $bot = $this->validateBotAccess($request, $conversation->bot->uuid);
            if (!$bot) {
                return $this->errorResponse('Bot access denied', 403);
            }

            // Create user message
            $userMessage = Message::create([
                'uuid' => Str::uuid(),
                'conversation_id' => $conversation->id,
                'role' => MessageRole::USER,
                'content' => $request->message,
                'content_type' => ContentType::TEXT,
                'status' => MessageStatus::COMPLETED,
                'metadata' => array_merge([
                    'messageType' => $request->messageType ?? 'text',
                    'attachments' => $request->attachments ?? [],
                    'clientTimestamp' => $request->metadata['clientTimestamp'] ?? now()->timestamp,
                ], $request->metadata ?? []),
                'created_at' => now(),
            ]);

            // Update conversation last message time
            $conversation->update(['last_message_at' => now()]);

            // Generate AI response using existing service
            $aiMessage = MessageFacade::generateAIResponse($conversation, $userMessage);

            // Check if AI response is string (error) or Message object
            if (is_string($aiMessage)) {
                return $this->errorResponse($aiMessage, 500);
            }

            // Return minimal response for widget
            return $this->successResponse([
                'response' => $aiMessage->content ?: 'Đang xử lý tin nhắn của bạn...',
                'messageId' => $userMessage->uuid,
                'botMessageId' => $aiMessage->uuid,
                'status' => $aiMessage->status->value,
                'isProcessing' => $aiMessage->status === MessageStatus::PENDING,
                'messages' => [
                    [
                        'id' => $userMessage->uuid,
                        'type' => 'user',
                        'content' => $userMessage->content,
                        'timestamp' => $userMessage->created_at?->toISOString(),
                    ],
                    [
                        'id' => $aiMessage->uuid,
                        'type' => 'bot',
                        'content' => $aiMessage->content ?: 'Đang xử lý tin nhắn của bạn...',
                        'timestamp' => $aiMessage->created_at?->toISOString(),
                        'status' => $aiMessage->status->value,
                    ]
                ]
            ], 'Message sent and response is being processed', 201);

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Get message status (for polling).
     */
    public function getMessageStatus(Request $request, string $conversationUuid, string $messageUuid): JsonResponse
    {
        try {
            $conversation = Conversation::with(['bot'])
                                      ->where('uuid', $conversationUuid)
                                      ->active()
                                      ->firstOrFail();

            // Validate bot access
            $bot = $this->validateBotAccess($request, $conversation->bot->uuid);
            if (!$bot) {
                return $this->errorResponse('Bot access denied', 403);
            }

            // Get message
            $message = Message::where('uuid', $messageUuid)
                             ->where('conversation_id', $conversation->id)
                             ->firstOrFail();

            return $this->successResponse([
                'id' => $message->uuid,
                'type' => $message->role === MessageRole::USER ? 'user' : 'bot',
                'content' => $message->content,
                'timestamp' => $message->created_at?->toISOString(),
                'status' => $message->status->value,
                'isCompleted' => $message->status === MessageStatus::COMPLETED,
                'isFailed' => $message->status === MessageStatus::FAILED,
            ], 'Message status retrieved successfully');

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Validate bot access for widget.
     */
    private function validateBotAccess(Request $request, string $botUuid): ?Bot
    {
        // Try API key access
        if ($request->hasHeader('Authorization')) {
            $apiKey = str_replace('Bearer ', '', $request->header('Authorization'));
            
            if (str_starts_with($apiKey, 'pk_')) {
                $bot = Bot::where('uuid', $botUuid)
                         ->where('api_key', $apiKey)
                         ->active()
                         ->first();
                
                if ($bot) {
                    return $bot;
                }
            }
        }

        // Try share token access
        $shareLink = BotShareLink::with(['bot'])
                                ->where('token', $botUuid)
                                ->active()
                                ->first();

        if ($shareLink && $shareLink->bot->canBeShared()) {
            return $shareLink->bot;
        }

        return null;
    }
}
