import{q as p,r as i,j as g,k as o,y as k,o as x,a7 as I,B as R,m as S,n as B,D as L,S as y,_ as D}from"./index-ZVLuktk4.js";const E={class:"frame","element-loading-text":"Loading..."},P=["src"],w=p({name:"LayFrame",__name:"frame",props:{frameInfo:{}},setup(v){var m,u,d;const s=v,f=i(!0),t=g(),n=i(""),l=i(null);(m=o(t.meta))!=null&&m.frameSrc&&(n.value=(u=o(t.meta))==null?void 0:u.frameSrc),((d=o(t.meta))==null?void 0:d.frameLoading)===!1&&c();function c(){f.value=!1}function h(){y(()=>{const e=o(l);if(!e)return;const a=e;a.attachEvent?a.attachEvent("onload",()=>{c()}):e.onload=()=>{c()}})}return k(()=>t.fullPath,e=>{var a,r,_;t.name==="Redirect"&&e.includes((a=s.frameInfo)==null?void 0:a.fullPath)&&(n.value=e,f.value=!0),((r=s.frameInfo)==null?void 0:r.fullPath)===e&&(n.value=(_=s.frameInfo)==null?void 0:_.frameSrc)}),x(()=>{h()}),(e,a)=>{const r=I("loading");return R((B(),S("div",E,[L("iframe",{ref_key:"frameRef",ref:l,src:n.value,class:"frame-iframe"},null,8,P)])),[[r,f.value]])}}}),j=D(w,[["__scopeId","data-v-36bd368c"]]);export{j as default};
