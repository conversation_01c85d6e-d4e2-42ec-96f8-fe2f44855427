<?php

namespace Modules\ModelAI\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class BulkModelProviderDestroyRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                Rule::exists('model_providers', 'id')->whereNotNull('deleted_at'), // Only soft deleted providers
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('Provider IDs'),
            'ids.*' => __('Provider ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Please select at least one provider.'),
            'ids.array' => __('Invalid data format.'),
            'ids.min' => __('Please select at least one provider.'),
            'ids.*.required' => __('Provider ID is required.'),
            'ids.*.integer' => __('Provider ID must be an integer.'),
            'ids.*.exists' => __('One or more selected providers must be in trash to be permanently deleted.'),
        ];
    }
}
