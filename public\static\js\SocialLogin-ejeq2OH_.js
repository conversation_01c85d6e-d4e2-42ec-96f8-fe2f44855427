var b=(i,s,n)=>new Promise((c,t)=>{var r=e=>{try{a(n.next(e))}catch(o){t(o)}},d=e=>{try{a(n.throw(e))}catch(o){t(o)}},a=e=>e.done?c(e.value):Promise.resolve(e.value).then(r,d);a((n=n.apply(i,s)).next())});import{q as C,r as L,G as f,m as k,n as g,E as u,D as h,N as _,M as x,k as m,a8 as p,I as B,J as I,Q as S,aC as G,ax as N,_ as D}from"./index-ZVLuktk4.js";const E={class:"social-login"},F={class:"text-gray-500 text-xs"},O={class:"w-full flex justify-evenly gap-4"},V=C({__name:"SocialLogin",props:{providers:{default:()=>["facebook","google","twitter"]},loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},emits:["socialLogin"],setup(i,{emit:s}){const n=i,c=s,t=L({}),r={facebook:{icon:"logos:facebook",name:"Facebook",color:"#1877f2"},google:{icon:"flat-color-icons:google",name:"Google",color:"#4285f4"},twitter:{icon:"ant-design:twitter-circle-filled",name:"Twitter",color:"#1da1f2"},github:{icon:"akar-icons:github-fill",name:"GitHub",color:"#333"},linkedin:{icon:"akar-icons:linkedin-fill",name:"LinkedIn",color:"#0077b5"}},d=e=>b(null,null,function*(){if(!(n.disabled||n.loading||t.value[e]))try{t.value[e]=!0,c("socialLogin",e)}catch(o){N((o==null?void 0:o.message)||p("Social login failed"),{type:"error"})}finally{t.value[e]=!1}}),a=e=>r[e]||{icon:"ri:user-line",name:e,color:"#666"};return(e,o)=>{const y=f("el-divider"),v=f("el-button"),w=f("el-tooltip");return g(),k("div",E,[u(y,null,{default:_(()=>[h("p",F,x(m(p)("Or continue with")),1)]),_:1}),h("div",O,[(g(!0),k(B,null,I(e.providers,l=>(g(),S(w,{key:l,content:m(p)("Continue with {provider}",{provider:a(l).name}),placement:"top"},{default:_(()=>[u(v,{loading:t.value[l],disabled:e.disabled||e.loading,class:"social-button",size:"large",circle:"",onClick:$=>d(l)},{default:_(()=>[u(m(G),{icon:a(l).icon,width:"24",height:"24"},null,8,["icon"])]),_:2},1032,["loading","disabled","onClick"])]),_:2},1032,["content"]))),128))])])}}}),z=D(V,[["__scopeId","data-v-fbb08206"]]);export{z as default};
