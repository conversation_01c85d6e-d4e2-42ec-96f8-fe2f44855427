var B=(_,i,u)=>new Promise((v,C)=>{var U=b=>{try{m(u.next(b))}catch(h){C(h)}},A=b=>{try{m(u.throw(b))}catch(h){C(h)}},m=b=>b.done?v(b.value):Promise.resolve(b.value).then(U,A);m((u=u.apply(_,i)).next())});import{gQ as ce,gR as ue,gS as pe,gT as me,gU as fe,gV as ge,gW as $,q as _e,r as L,g as be,e as j,y as ye,G as g,a7 as ve,m as X,n as V,I as he,D as n,E as s,M as d,k as e,a8 as t,N as r,ga as W,gX as we,P as w,Q as z,gc as xe,X as K,gd as Fe,B as ke,A as Ve,L as Ce,gN as T,ay as Le,S as Te}from"./index-ZVLuktk4.js";import{h as Ue,i as ze}from"./auth-api-CublKEkJ.js";const N=_=>{var i;return((i=_.split(".").pop())==null?void 0:i.toLowerCase())||""},O=_=>{const i=N(_);return["jpg","jpeg","png","gif","bmp","webp","svg"].includes(i)?ce:["mp4","avi","mov","wmv","flv","webm","mkv"].includes(i)?ue:["mp3","wav","flac","aac","ogg","m4a"].includes(i)?pe:["pdf","doc","docx","txt","rtf"].includes(i)?me:["xls","xlsx","csv"].includes(i)?fe:["zip","rar","7z","tar","gz"].includes(i)?ge:$},q=_=>{const i=N(_);return i?{jpg:"JPG",jpeg:"JPEG",png:"PNG",gif:"GIF",bmp:"BMP",webp:"WebP",svg:"SVG",mp4:"MP4",avi:"AVI",mov:"MOV",wmv:"WMV",flv:"FLV",webm:"WebM",mkv:"MKV",mp3:"MP3",wav:"WAV",flac:"FLAC",aac:"AAC",ogg:"OGG",m4a:"M4A",pdf:"PDF",doc:"DOC",docx:"DOCX",txt:"TXT",rtf:"RTF",xls:"XLS",xlsx:"XLSX",csv:"CSV",zip:"ZIP",rar:"RAR","7z":"7Z",tar:"TAR",gz:"GZ"}[i]||i.toUpperCase():"File"},Z=_=>{const i=N(_);return["jpg","jpeg","png","gif","bmp","webp","svg"].includes(i)?"#67C23A":["mp4","avi","mov","wmv","flv","webm","mkv"].includes(i)?"#9C27B0":["mp3","wav","flac","aac","ogg","m4a"].includes(i)?"#FF9800":["pdf","doc","docx","txt","rtf"].includes(i)?"#409EFF":["xls","xlsx","csv"].includes(i)?"#67C23A":["zip","rar","7z","tar","gz"].includes(i)?"#8D6E63":"#909399"},Ae={class:"card"},De={class:"section-title"},Se={class:"el-upload__text"},Be={class:"flex justify-between items-center mb-4"},Ne={class:"text-sm text-gray-500"},Me={key:0,class:"text-center py-8 text-gray-500"},Re={class:"text-xs mt-2"},Pe={class:"flex items-center gap-2"},Ie={class:"text-xs"},Ee={class:"mb-4"},Ge={class:"text-sm text-gray-500 mb-4"},je={class:"mb-4 p-3 bg-gray-50 rounded-lg"},Xe={class:"grid grid-cols-1 md:grid-cols-3 gap-3"},We={class:"block text-sm font-medium text-gray-700 mb-1"},Ke={class:"block text-sm font-medium text-gray-700 mb-1"},Oe={class:"block text-sm font-medium text-gray-700 mb-1"},qe={class:"flex justify-between items-center mt-3"},Ze={class:"text-xs text-gray-500"},$e={class:"flex items-center gap-2"},Je={class:"flex items-center gap-2"},Qe={class:"text-xs"},He={class:"flex justify-end gap-3"},lt=_e({__name:"BotKnowledgeBase",props:{drawerValues:{},loading:{type:Boolean},fileLibrary:{},selectedFiles:{}},emits:["update:file-library","update:selected-files","update:knowledge-text","update:new-uploads","update:library-files","update:bot-files","delete-file","refresh-library"],setup(_,{emit:i}){const u=_,v=i,C=L(),U=L("files"),A=L([]),m=be({name:"",dateFrom:null,dateTo:null}),b=L(!1),h=L(!1),x=L([]),M=o=>o?new Date(o).toLocaleDateString("vi-VN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"",D=()=>B(null,null,function*(){try{const o=yield Ue({name:m.name,dateFrom:m.dateFrom,dateTo:m.dateTo});v("update:file-library",o.data)}catch(o){console.error("Get Knowledge base files error:",o)}}),J=()=>{m.name="",m.dateFrom=null,m.dateTo=null,D()},Q=()=>{},H=(o,l)=>{var F;const c=((F=u.drawerValues.knowledge)==null?void 0:F.newUploads)||[],p={name:o.data.name||l.name,storagePath:o.data.storage_path,size:l.size,type:l.type},f=[...c,p];v("update:new-uploads",f),v("refresh-library"),T.success(t("File uploaded successfully"))},R=j(()=>{var l;const o=((l=u.drawerValues.knowledge)==null?void 0:l.libraryFiles)||[];return u.fileLibrary.filter(c=>o.includes(c.uuid))}),S=j(()=>o=>{var c;return(((c=u.drawerValues.knowledge)==null?void 0:c.libraryFiles)||[]).includes(o)}),Y=()=>{h.value=!0,x.value=[]},ee=o=>{x.value=o},te=()=>{var p;const o=((p=u.drawerValues.knowledge)==null?void 0:p.libraryFiles)||[],l=x.value.map(f=>f.uuid).filter(f=>!o.includes(f));if(l.length===0){T.warning(t("Selected files are already added to this bot"));return}const c=[...o,...l];v("update:library-files",c),h.value=!1,x.value=[],T.success(t("Added {count} files to bot knowledge",{count:l.length}))},P=()=>{h.value=!1,x.value=[]},le=o=>{var p;const c=(((p=u.drawerValues.knowledge)==null?void 0:p.libraryFiles)||[]).filter(f=>f!==o.uuid);v("update:library-files",c),T.success(t("File removed from bot knowledge"))},ae=o=>B(null,null,function*(){var l,c;try{if(yield Le.confirm(t("Are you sure you want to remove this file?"),t("Warning"),{confirmButtonText:t("OK"),cancelButtonText:t("Cancel"),type:"warning"}),o.status==="success"&&((l=o.response)!=null&&l.data))try{const p=o.response.data.id||o.response.data.uuid||o.response.data.storage_path;yield ze({id:p});const F=(((c=u.drawerValues.knowledge)==null?void 0:c.newUploads)||[]).filter(k=>k.storage_path!==o.response.data.storage_path);v("update:new-uploads",F),T.success(t("File removed successfully"))}catch(p){return console.error("Error removing file from server:",p),T.error(t("Failed to remove file from server")),!1}return!0}catch(p){return!1}}),se=()=>{var l;if(!C.value)return;C.value.clearSelection(),(((l=u.drawerValues.knowledge)==null?void 0:l.libraryFiles)||[]).forEach(c=>{const p=u.fileLibrary.find(f=>f.uuid===c);p&&C.value.toggleRowSelection(p,!0)})};return ye(U,o=>{o==="library"&&!b.value&&Te(()=>{se(),b.value=!0})},{immediate:!0}),(o,l)=>{const c=g("el-icon"),p=g("el-upload"),f=g("el-tab-pane"),F=g("el-input"),k=g("el-button"),y=g("el-table-column"),oe=g("el-popconfirm"),I=g("el-table"),ne=g("el-tabs"),E=g("el-date-picker"),re=g("el-tag"),ie=g("el-dialog"),de=ve("loading");return V(),X(he,null,[n("div",Ae,[n("h2",De,d(e(t)("Knowledge Base")),1),s(ne,{modelValue:U.value,"onUpdate:modelValue":l[2]||(l[2]=a=>U.value=a),type:"border-card"},{default:r(()=>[s(f,{label:e(t)("Upload New Files"),name:"files"},{default:r(()=>{var a;return[s(p,{ref:"uploadRef","file-list":A.value,"onUpdate:fileList":l[0]||(l[0]=G=>A.value=G),class:"w-full",drag:"",action:"/api/auth/knowledge-bases/files/upload",multiple:"","auto-upload":!0,headers:{Authorization:`Bearer ${(a=e(W)().accessToken)!=null?a:e(W)()}`,"X-Requested-With":"XMLHttpRequest"},"before-remove":ae,onChange:Q,onSuccess:H},{default:r(()=>[s(c,{class:"el-icon--upload"},{default:r(()=>[s(e(we))]),_:1}),n("div",Se,[w(d(e(t)("Drag files here or"))+" ",1),n("em",null,d(e(t)("click to upload")),1)])]),_:1},8,["file-list","headers"])]}),_:1},8,["label"]),s(f,{label:e(t)("Text"),name:"text"},{default:r(()=>[s(F,{"model-value":u.drawerValues.knowledge.text,type:"textarea",rows:10,placeholder:e(t)("Paste text content here."),"onUpdate:modelValue":l[1]||(l[1]=a=>v("update:knowledge-text",a))},null,8,["model-value","placeholder"])]),_:1},8,["label"]),s(f,{label:e(t)("Document Library"),name:"library"},{default:r(()=>[n("div",Be,[n("p",Ne,d(e(t)("Files currently attached to this bot's knowledge base.")),1),s(k,{type:"primary",icon:e(xe),onClick:Y},{default:r(()=>[w(d(e(t)("Add Files")),1)]),_:1},8,["icon"])]),R.value.length?(V(),z(I,{key:1,data:R.value,height:"300"},{default:r(()=>[s(y,{prop:"name",label:e(t)("File Name")},null,8,["label"]),s(y,{label:e(t)("Type"),width:"120"},{default:r(({row:a})=>[n("div",Pe,[s(c,{color:e(Z)(a.name)},{default:r(()=>[(V(),z(K(e(O)(a.name))))]),_:2},1032,["color"]),n("span",Ie,d(e(q)(a.name)),1)])]),_:1},8,["label"]),s(y,{prop:"size",label:e(t)("Size"),width:"120"},null,8,["label"]),s(y,{label:e(t)("Upload Date"),width:"180"},{default:r(({row:a})=>[w(d(M(a.created_at)),1)]),_:1},8,["label"]),s(y,{label:e(t)("Actions"),width:"100",align:"center"},{default:r(({row:a})=>[s(oe,{title:e(t)("Remove this file from bot knowledge?"),onConfirm:G=>le(a)},{reference:r(()=>[s(k,{type:"danger",size:"small",icon:e(Fe),circle:""},null,8,["icon"])]),_:2},1032,["title","onConfirm"])]),_:1},8,["label"])]),_:1},8,["data"])):(V(),X("div",Me,[s(c,{class:"text-4xl mb-2"},{default:r(()=>[s(e($))]),_:1}),n("p",null,d(e(t)("No files attached to this bot yet.")),1),n("p",Re,d(e(t)("Click 'Add Files' to select files from your library.")),1)]))]),_:1},8,["label"])]),_:1},8,["modelValue"])]),s(ie,{modelValue:h.value,"onUpdate:modelValue":l[6]||(l[6]=a=>h.value=a),title:e(t)("Add Files from Library"),width:"800px",onClose:P},{footer:r(()=>[n("div",He,[s(k,{onClick:P},{default:r(()=>[w(d(e(t)("Cancel")),1)]),_:1}),s(k,{type:"primary",disabled:!x.value.length,onClick:te},{default:r(()=>[w(d(e(t)("Add Selected Files"))+" ("+d(x.value.length)+") ",1)]),_:1},8,["disabled"])])]),default:r(()=>[n("div",Ee,[n("p",Ge,d(e(t)("Select files from your library to add to this bot's knowledge.")),1),n("div",je,[n("div",Xe,[n("div",null,[n("label",We,d(e(t)("File Name")),1),s(F,{modelValue:m.name,"onUpdate:modelValue":l[3]||(l[3]=a=>m.name=a),placeholder:e(t)("Search by file name..."),clearable:"",size:"small",onInput:D},null,8,["modelValue","placeholder"])]),n("div",null,[n("label",Ke,d(e(t)("From Date")),1),s(E,{modelValue:m.dateFrom,"onUpdate:modelValue":l[4]||(l[4]=a=>m.dateFrom=a),type:"date",placeholder:e(t)("Select start date"),size:"small",style:{width:"100%"},onChange:D},null,8,["modelValue","placeholder"])]),n("div",null,[n("label",Oe,d(e(t)("To Date")),1),s(E,{modelValue:m.dateTo,"onUpdate:modelValue":l[5]||(l[5]=a=>m.dateTo=a),type:"date",placeholder:e(t)("Select end date"),size:"small",style:{width:"100%"},onChange:D},null,8,["modelValue","placeholder"])])]),n("div",qe,[n("span",Ze,d(e(t)("Total files: {count}",{count:u.fileLibrary.length})),1),s(k,{size:"small",onClick:J},{default:r(()=>[w(d(e(t)("Clear Filters")),1)]),_:1})])])]),ke((V(),z(I,{data:u.fileLibrary,height:"300","row-key":"uuid",onSelectionChange:ee},{default:r(()=>[s(y,{type:"selection",width:"55",selectable:a=>!S.value(a.uuid)},null,8,["selectable"]),s(y,{label:e(t)("File Name")},{default:r(({row:a})=>[n("div",$e,[n("span",{class:Ce({"text-gray-400":S.value(a.uuid)})},d(a.name),3),S.value(a.uuid)?(V(),z(re,{key:0,size:"small",type:"info"},{default:r(()=>[w(d(e(t)("Already Added")),1)]),_:1})):Ve("",!0)])]),_:1},8,["label"]),s(y,{label:e(t)("Type"),width:"120"},{default:r(({row:a})=>[n("div",Je,[s(c,{color:e(Z)(a.name)},{default:r(()=>[(V(),z(K(e(O)(a.name))))]),_:2},1032,["color"]),n("span",Qe,d(e(q)(a.name)),1)])]),_:1},8,["label"]),s(y,{prop:"size",label:e(t)("Size"),width:"120"},null,8,["label"]),s(y,{label:e(t)("Upload Date"),width:"180"},{default:r(({row:a})=>[w(d(M(a.created_at)),1)]),_:1},8,["label"])]),_:1},8,["data"])),[[de,u.loading]])]),_:1},8,["modelValue","title"])],64)}}});export{lt as _};
