var At=Object.defineProperty,Dt=Object.defineProperties;var Lt=Object.getOwnPropertyDescriptors;var st=Object.getOwnPropertySymbols;var qt=Object.prototype.hasOwnProperty,Ht=Object.prototype.propertyIsEnumerable;var it=e=>{throw TypeError(e)};var U=(e,t,s)=>t in e?At(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,H=(e,t)=>{for(var s in t||(t={}))qt.call(t,s)&&U(e,s,t[s]);if(st)for(var s of st(t))Ht.call(t,s)&&U(e,s,t[s]);return e},rt=(e,t)=>Dt(e,Lt(t));var y=(e,t,s)=>U(e,typeof t!="symbol"?t+"":t,s),at=(e,t,s)=>t.has(e)||it("Cannot "+s);var c=(e,t,s)=>(at(e,t,"read from private field"),s?s.call(e):t.get(e)),Z=(e,t,s)=>t.has(e)?it("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s);var n=(e,t,s)=>(at(e,t,"access private method"),s);var p=(e,t,s)=>new Promise((r,a)=>{var l=h=>{try{o(s.next(h))}catch(d){a(d)}},u=h=>{try{o(s.throw(h))}catch(d){a(d)}},o=h=>h.done?r(h.value):Promise.resolve(h.value).then(l,u);o((s=s.apply(e,t)).next())});import{m as dt,n as pt,D as j,p as Bt,q as Ft,r as $t,o as Rt,E as nt}from"./index-ZVLuktk4.js";const we="/static/png/bg-oEDCYcDF.png",Qt={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",class:"icon",viewBox:"0 0 1024 1024"};function _t(e,t){return pt(),dt("svg",Qt,t[0]||(t[0]=[j("path",{fill:"#386BF3",d:"M410.558.109c0 210.974-300.876 361.752-300.876 633.548 0 174.943 134.704 316.787 300.876 316.787s300.877-141.817 300.877-316.787C711.408 361.752 410.558 210.974 410.558.109"},null,-1),j("path",{fill:"#C3D2FB",d:"M613.469 73.665c0 211.055-300.877 361.914-300.877 633.547C312.592 882.156 447.296 1024 613.47 1024s300.876-141.817 300.876-316.788C914.29 435.58 613.469 284.72 613.469 73.665"},null,-1),j("path",{fill:"#303F5B",d:"M312.592 707.212c0-183.713 137.636-312.171 226.723-441.39 81.702 106.112 172.12 218.74 172.12 367.726A309.755 309.755 0 0 1 420.36 950.064a323.1 323.1 0 0 1-107.769-242.852z"},null,-1)]))}const ve={render:_t},zt={xmlns:"http://www.w3.org/2000/svg",width:"500",height:"380",viewBox:"0 0 897.318 556.975"};function Vt(e,t){return pt(),dt("svg",zt,t[0]||(t[0]=[Bt('<path fill="#f2f2f2" d="m217.339 502.047.998-22.434a72.46 72.46 0 0 1 33.795-8.555c-16.231 13.27-14.203 38.85-25.207 56.696a43.58 43.58 0 0 1-31.96 20.14l-13.583 8.317a73.03 73.03 0 0 1 15.393-59.18 70.5 70.5 0 0 1 12.965-12.045c3.253 8.578 7.599 17.06 7.599 17.06"></path><path fill="#cacaca" d="M796.921 36.552H164.598a1.016 1.016 0 0 1 0-2.03h632.324a1.016 1.016 0 0 1 0 2.03"></path><ellipse cx="186.953" cy="11.169" fill="#3f3d56" rx="10.925" ry="11.169"></ellipse><ellipse cx="224.695" cy="11.169" fill="#3f3d56" rx="10.925" ry="11.169"></ellipse><ellipse cx="262.437" cy="11.169" fill="#3f3d56" rx="10.925" ry="11.169"></ellipse><path fill="#3f3d56" d="M774.304 2.768h-26.81a2.03 2.03 0 0 0 0 4.06h26.81a2.03 2.03 0 0 0 0-4.06m0 7.62h-26.81a2.03 2.03 0 0 0 0 4.06h26.81a2.03 2.03 0 0 0 0-4.06m0 7.61h-26.81a2.03 2.03 0 0 0 0 4.06h26.81a2.03 2.03 0 0 0 0-4.06m-117.591 98.143h-434.01a8.07 8.07 0 0 0-8.07 8.06v204.87a8.08 8.08 0 0 0 8.07 8.07h434.01a8.077 8.077 0 0 0 8.06-8.07v-204.87a8.07 8.07 0 0 0-8.06-8.06"></path><path fill="#589ff8" d="M542.073 214.842a8.07 8.07 0 0 0-8.06 8.06v57.87a8.077 8.077 0 0 0 8.06 8.07h122.7v-74Z"></path><path fill="#589ff8" d="M871.088 288.837h-329.01a8.076 8.076 0 0 1-8.067-8.066v-57.868a8.075 8.075 0 0 1 8.067-8.066h329.01a8.075 8.075 0 0 1 8.066 8.066v57.868a8.076 8.076 0 0 1-8.066 8.066" opacity=".5"></path><circle cx="586.571" cy="255.537" r="13.089" fill="#fff"></circle><path fill="#fff" d="M860.894 251.734H624.38a3.898 3.898 0 1 1 0-7.796h236.514a3.898 3.898 0 1 1 0 7.796m-89.831 15.401H624.38a3.898 3.898 0 1 1 0-7.795h146.683a3.898 3.898 0 0 1 0 7.795"></path><path fill="#ffb6b6" d="m151.406 545.537 11.328-.001 5.389-43.693h-16.719z"></path><path fill="#2f2e41" d="M148.517 541.838h3.188l12.449-5.062 6.671 5.061h.001a14.22 14.22 0 0 1 14.217 14.217v.462l-36.526.001Z"></path><path fill="#ffb6b6" d="m49.051 530.809 10.139 5.053 24.314-36.701-14.963-7.458z"></path><path fill="#2f2e41" d="m48.115 526.21 2.854 1.422 13.4 1.022 3.712 7.507h.001a14.22 14.22 0 0 1 6.382 19.066l-.206.413-32.69-16.292Zm108.31-179.114-72.026 1.88 1.253 35.073s-1.253 9.395 1.252 11.9 3.758 2.505 2.506 6.89-4.491 46.273-4.491 46.273-29.562 52.27-28.31 53.522 2.506 0 1.253 3.132-2.505 1.879-1.252 3.132a46 46 0 0 1 3.131 3.757h20.416s1.142-6.263 1.142-6.889 1.252-4.384 1.252-5.01 35.67-38.418 35.67-38.418l7.515-62.631 18.163 61.378s0 53.863 1.253 55.116 1.252.626.626 3.132-3.132 1.878-1.253 3.757 2.505-1.252 1.88 1.88l-.627 3.13 24.062.27s2.506-5.28 1.253-7.159-1.178-1.366.35-4.44 2.155-3.702 1.529-4.328-.626-3.958-.626-3.958-9.031-123.183-9.031-125.062a6.25 6.25 0 0 1 .52-2.818v-2.55l-2.4-9.038Z"></path><path fill="#589ff8" d="M869.68 238.348a27.638 27.638 0 1 1 27.638-27.638 27.64 27.64 0 0 1-27.638 27.638"></path><path fill="#fff" d="M880.586 207.984h-8.18v-8.18a2.726 2.726 0 0 0-5.452 0v8.18h-8.179a2.726 2.726 0 1 0 0 5.452h8.18v8.18a2.726 2.726 0 0 0 5.452 0v-8.18h8.179a2.726 2.726 0 1 0 0-5.452"></path><path fill="#589ff8" d="M447.883 289.212h-105.01a8.08 8.08 0 0 0-8.07 8.07v39.86h121.14v-39.86a8.077 8.077 0 0 0-8.06-8.07"></path><path fill="#589ff8" d="M447.88 401.212H342.87a8.076 8.076 0 0 1-8.067-8.067v-95.867a8.075 8.075 0 0 1 8.067-8.067h105.01a8.075 8.075 0 0 1 8.066 8.067v95.867a8.076 8.076 0 0 1-8.066 8.067" opacity=".5"></path><circle cx="373.808" cy="321.563" r="13.089" fill="#fff"></circle><path fill="#fff" d="M426.131 354.547h-61.514a3.898 3.898 0 1 1 0-7.795h61.514a3.898 3.898 0 1 1 0 7.795M394.3 369.95h-29.683a3.898 3.898 0 0 1 0-7.797H394.3a3.898 3.898 0 0 1 0 7.796"></path><path fill="#589ff8" d="M340.68 429.348a27.638 27.638 0 1 1 27.638-27.638 27.64 27.64 0 0 1-27.638 27.638"></path><path fill="#fff" d="M351.586 398.984h-8.18v-8.18a2.726 2.726 0 1 0-5.452 0v8.18h-8.179a2.726 2.726 0 1 0 0 5.452h8.18v8.18a2.726 2.726 0 1 0 5.452 0v-8.18h8.179a2.726 2.726 0 1 0 0-5.452"></path><path fill="#589ff8" d="M327.887 228.266h-105.01a8.076 8.076 0 0 1-8.067-8.066v-95.867a8.075 8.075 0 0 1 8.067-8.067h105.01a8.075 8.075 0 0 1 8.066 8.067V220.2a8.076 8.076 0 0 1-8.066 8.066"></path><circle cx="253.816" cy="156.618" r="13.089" fill="#589ff8"></circle><path fill="#589ff8" d="M306.139 185.602h-61.514a3.898 3.898 0 1 1 0-7.795h61.514a3.898 3.898 0 1 1 0 7.795m-31.831 15.402h-29.683a3.898 3.898 0 1 1 0-7.796h29.683a3.898 3.898 0 1 1 0 7.796"></path><path fill="#589ff8" d="M327.887 228.266h-105.01a8.076 8.076 0 0 1-8.067-8.066v-95.867a8.075 8.075 0 0 1 8.067-8.067h105.01a8.075 8.075 0 0 1 8.066 8.067V220.2a8.076 8.076 0 0 1-8.066 8.066" opacity=".5"></path><circle cx="253.816" cy="156.618" r="13.089" fill="#fff"></circle><path fill="#fff" d="M306.139 185.602h-61.514a3.898 3.898 0 1 1 0-7.795h61.514a3.898 3.898 0 1 1 0 7.795m-31.831 15.402h-29.683a3.898 3.898 0 1 1 0-7.796h29.683a3.898 3.898 0 1 1 0 7.796"></path><circle cx="225.043" cy="115.951" r="21" fill="#ff6584"></circle><path fill="#ccc" d="M282.67 555.785a1.186 1.186 0 0 1-1.19 1.19H1.19a1.19 1.19 0 0 1 0-2.38h280.29a1.187 1.187 0 0 1 1.19 1.19"></path><path fill="#ffb6b6" d="M220.555 171.576a9.77 9.77 0 0 1-5.759 12.435 9.6 9.6 0 0 1-1.635.451l-5.547 33.96-13.01-12.013 7.262-30.407a9.806 9.806 0 0 1 8.59-10.76 9.55 9.55 0 0 1 10.099 6.334"></path><path fill="#3f3d56" d="M124.54 248.524s10.098-13.341 46.74-12.976l20.797-7.556 4.753-43.57 16.636 3.96-2.377 53.87-35.648 20.596-46.739 9.506Z"></path><circle cx="119.175" cy="198.983" r="21.747" fill="#ffb6b6" data-name="ab6171fa-7d69-4734-b81c-8dff60f9761b"></circle><path fill="#3f3d56" d="M82.367 363.878a.4.4 0 0 1-.114-.016c-.401-.112-.719-.2.73-12.73l1.564-9.903-1.526-8.744-2.568-2.568 4.127-4.127 3.463-9.838-5.993-8.88-6.875-36.317a28.97 28.97 0 0 1 15.91-31.478l7.958-2.325 2.896-5.31a9.52 9.52 0 0 1 8.286-4.962l14.573-.11a9.52 9.52 0 0 1 7.617 3.716l5.084 6.609 21.082 7.161-3.495 75.322a5.233 5.233 0 0 1 .359 7.695c-.22.221-.393.401-.5.52-.356.505.31 4.275 1.134 7.475l1.056 4.902a3.013 3.013 0 0 0-.548 4.398l1.347 1.59a7.6 7.6 0 0 1-6.508 8.536c-19.267 2.622-68.958 9.384-69.059 9.384"></path><path fill="#2f2e41" d="M113.612 219.665q-.14-.307-.278-.615c.036 0 .07.006.106.007Zm-16.789-41.441a6.05 6.05 0 0 1 3.792-1.64c1.406.046 2.832 1.316 2.54 2.693a22.35 22.35 0 0 1 26.896-10.085c3.495 1.233 6.922 3.7 7.725 7.318a6.6 6.6 0 0 0 .83 2.702 3.08 3.08 0 0 0 3.283.832l.034-.01a1.028 1.028 0 0 1 1.242 1.45l-.989 1.844a7.9 7.9 0 0 0 3.776-.08 1.027 1.027 0 0 1 1.09 1.598 17.9 17.9 0 0 1-14.269 7.334c-3.951-.024-7.943-1.386-11.789-.477a10.24 10.24 0 0 0-6.887 14.375c-1.182-1.292-3.466-.986-4.674.28a6.4 6.4 0 0 0-1.4 4.906 22.8 22.8 0 0 0 2.337 7.638 22.836 22.836 0 0 1-13.537-40.678"></path><path fill="#ffb6b6" d="M90.84 395.068a9.77 9.77 0 0 1-2.303-13.509 9.6 9.6 0 0 1 1.092-1.298l-14.675-31.123 17.527 2.525 11.249 29.167a9.806 9.806 0 0 1-.98 13.733 9.55 9.55 0 0 1-11.91.505"></path><path fill="#3f3d56" d="m86.395 378.074-23.352-52.483-.234-41.452 7.361-22.39a23.925 23.925 0 0 1 30.828-15.04l.162.058.068.158c.272.635 6.446 15.907-11.867 47.323l-3.686 21.496 12.933 49.274Z"></path>',37)]))}const Ce={render:Vt},mt=e=>Array.isArray(e),yt=e=>mt(e)?e:[e];let Ut=function(e){let t=function(f){return yt(f).forEach(b=>{var q;return m.set(Symbol((q=b.char)==null?void 0:q.innerText),a(H({},b)))}),this},s=()=>h().filter(f=>f.typeable),r=function(f,b){let q=[...m.keys()];m.set(q[f],a(b))},a=f=>(f.shouldPauseCursor=function(){return!!(this.typeable||this.cursorable||this.deletable)},f),l=function(){m.forEach(f=>delete f.done)},u=function(){m=new Map,t(e)},o=()=>m,h=()=>Array.from(m.values()),d=f=>m.delete(f),D=()=>{const f=[];for(let[,b]of o())b.done||f.push(b);return f},L=(f=!1)=>f?h():h().filter(b=>!b.done),T=(f,b=!1)=>b?m.delete(f):m.get(f).done=!0,m=new Map;return t(e),{add:t,set:r,wipe:u,done:T,reset:l,destroy:d,getItems:L,getQueue:o,getTypeable:s,getPendingQueueItems:D}};const bt="data-typeit-id",C="ti-cursor",Zt="END",jt={started:!1,completed:!1,frozen:!1,destroyed:!1},E={breakLines:!0,cursor:{autoPause:!0,autoPauseDelay:500,animation:{frames:[0,0,1].map(e=>({opacity:e})),options:{iterations:1/0,easing:"steps(2, start)",fill:"forwards"}}},cursorChar:"|",cursorSpeed:1e3,deleteSpeed:null,html:!0,lifeLike:!0,loop:!1,loopDelay:750,nextStringDelay:750,speed:100,startDelay:250,startDelete:!1,strings:[],waitUntilVisible:!1,beforeString:()=>{},afterString:()=>{},beforeStep:()=>{},afterStep:()=>{},afterComplete:()=>{}},Ot=`[${bt}]:before {content: '.'; display: inline-block; width: 0; visibility: hidden;}`,F=e=>document.createElement(e),Y=e=>document.createTextNode(e),gt=(e,t="")=>{let s=F("style");s.id=t,s.appendChild(Y(e)),document.head.appendChild(s)},lt=e=>(mt(e)||(e=[e/2,e/2]),e),ot=(e,t)=>Math.abs(Math.random()*(e+t-(e-t))+(e-t));let ut=e=>e/2;function Wt(e){let{speed:t,deleteSpeed:s,lifeLike:r}=e;return s=s!==null?s:t/3,r?[ot(t,ut(t)),ot(s,ut(s))]:[t,s]}const wt=e=>Array.from(e);let tt=e=>([...e.childNodes].forEach(t=>{if(t.nodeValue){[...t.nodeValue].forEach(s=>{t.parentNode.insertBefore(Y(s),t)}),t.remove();return}tt(t)}),e);const vt=e=>{let t=document.implementation.createHTMLDocument();return t.body.innerHTML=e,tt(t.body)};function Ct(e,t=!1,s=!1){let r=e.querySelector(`.${C}`),a=document.createTreeWalker(e,NodeFilter.SHOW_ALL,{acceptNode:o=>{var h,d;if(r&&s){if((h=o.classList)!=null&&h.contains(C))return NodeFilter.FILTER_ACCEPT;if(r.contains(o))return NodeFilter.FILTER_REJECT}return(d=o.classList)!=null&&d.contains(C)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT}}),l,u=[];for(;l=a.nextNode();)l.originalParent||(l.originalParent=l.parentNode),u.push(l);return t?u.reverse():u}function Jt(e){return Ct(vt(e))}function Xt(e,t=!0){return t?Jt(e):wt(e).map(Y)}const Gt=({index:e,newIndex:t,queueItems:s,cleanUp:r})=>{for(let a=e+1;a<t+1;a++)r(s[a][0])},Tt=e=>Number.isInteger(e),ht=({queueItems:e,selector:t,cursorPosition:s,to:r})=>{if(Tt(t))return t*-1;let a=new RegExp(Zt,"i").test(r),l=t?[...e].reverse().findIndex(({char:o})=>{let h=o.parentElement,d=h.matches(t);return a&&d?!0:d&&h.firstChild.isSameNode(o)}):-1;l<0&&(l=a?0:e.length-1);let u=a?0:1;return l-s+u},Kt=e=>(e.forEach(clearTimeout),[]),B=(e,t)=>new Array(t).fill(e);let _=e=>new Promise(t=>{requestAnimationFrame(()=>p(null,null,function*(){t(yield e())}))}),Pt=e=>e==null?void 0:e.getAnimations().find(t=>t.id===e.dataset.tiAnimationId),Et=({cursor:e,frames:t,options:s})=>{let r=e.animate(t,s);return r.pause(),r.id=e.dataset.tiAnimationId,_(()=>{_(()=>{r.play()})}),r},Yt=({cursor:e,options:t,cursorOptions:s})=>{if(!e||!s)return;let r=Pt(e),a;r&&(t.delay=r.effect.getComputedTiming().delay,a=r.currentTime,r.cancel());let l=Et({cursor:e,frames:s.animation.frames,options:t});return a&&(l.currentTime=a),l},ft=e=>{var t;return(t=e.func)==null?void 0:t.call(null)},te=l=>p(null,[l],function*({index:e,queueItems:t,wait:s,cursor:r,cursorOptions:a}){let u=t[e][1],o=[],h=e,d=u,D=()=>d&&!d.delay,L=u.shouldPauseCursor()&&a.autoPause;for(;D();)o.push(d),D()&&h++,d=t[h]?t[h][1]:null;if(o.length)return yield _(()=>p(null,null,function*(){for(let f of o)yield ft(f)})),h-1;let T=Pt(r),m;return T&&(m=rt(H({},T.effect.getComputedTiming()),{delay:L?a.autoPauseDelay:0})),yield s(()=>p(null,null,function*(){T&&L&&T.cancel(),yield _(()=>{ft(u)})}),u.delay),yield Yt({cursor:r,options:m,cursorOptions:a}),e});const ee=(e,t)=>{new IntersectionObserver((r,a)=>{r.forEach(l=>{l.isIntersecting&&(t(),a.unobserve(e))})},{threshold:1}).observe(e)},se=()=>Math.random().toString().substring(2,9),z=e=>"value"in e;let ie=e=>z(e)?wt(e.value):Ct(e,!0).filter(t=>!(t.childNodes.length>0)),P=e=>typeof e=="function"?e():e,et=(e,t=document,s=!1)=>t[`querySelector${s?"All":""}`](e),re=e=>/body/i.test(e==null?void 0:e.tagName),ae=(e,t)=>{if(z(e)){e.value=`${e.value}${t.textContent}`;return}t.innerHTML="";let s=re(t.originalParent)?e:t.originalParent||e,r=et("."+C,s)||null;r&&r.parentElement!==s&&(s=r.parentElement),s.insertBefore(t,r)};const ne=e=>/<(.+)>(.*?)<\/(.+)>/.test(e.outerHTML),S=(e,t)=>Object.assign({},e,t);let le=e=>{var t,s,r;if(typeof e=="object"){let a={},{frames:l,options:u}=E.cursor.animation;return a.animation=e.animation||{},a.animation.frames=((t=e.animation)==null?void 0:t.frames)||l,a.animation.options=S(u,((s=e.animation)==null?void 0:s.options)||{}),a.autoPause=(r=e.autoPause)!=null?r:E.cursor.autoPause,a.autoPauseDelay=e.autoPauseDelay||E.cursor.autoPauseDelay,a}return e===!0?E.cursor:e};const oe=(e,t)=>{if(!e)return;let s=e.parentNode;(s.childNodes.length>1||s.isSameNode(t)?e:s).remove()},ue=(e,t,s)=>{let r=t[s-1],a=et(`.${C}`,e);e=(r==null?void 0:r.parentNode)||e,e.insertBefore(a,r||null)};function he(e){return typeof e=="string"?et(e):e}let fe={"font-family":"","font-weight":"","font-size":"","font-style":"","line-height":"",color:"",transform:"translateX(-.125em)"},ce=(e,t)=>{let r=`${`[${bt}='${e}']`} .${C}`,a=getComputedStyle(t),l=Object.entries(fe).reduce((u,[o,h])=>`${u} ${o}: var(--ti-cursor-${o}, ${h||a[o]});`,"");gt(`${r} { display: inline-block; width: 0; ${l} }`,e)};function de(e){return e.replace(/<!--(.+?)-->/g,"").trim().split(/<br(?:\s*?)(?:\/)?>/)}let pe=(e,t,s)=>Math.min(Math.max(t+e,0),s.length),me=(e,t,s)=>new Promise(r=>{let a=()=>p(null,null,function*(){yield e(),r()});s.push(setTimeout(a,t||0))});var i,St,M,O,Mt,W,J,X,$,g,It,I,x,xt,V,Nt,kt,G,R,N,Q,v,k,A,K,w,ct;let ye=(ct=class{constructor(t,s={}){Z(this,i);y(this,"element");y(this,"timeouts");y(this,"cursorPosition");y(this,"predictedCursorPosition");y(this,"statuses",{started:!1,completed:!1,frozen:!1,destroyed:!1,firing:!1});y(this,"opts");y(this,"id");y(this,"queue");y(this,"cursor");y(this,"flushCallback",null);y(this,"unfreeze",()=>{});y(this,"is",function(t){return this.statuses[t]});Z(this,V,t=>{var s;this.opts.cursor=le((s=t.cursor)!=null?s:E.cursor),this.opts.strings=n(this,i,Nt).call(this,yt(this.opts.strings)),this.opts=S(this.opts,{html:!c(this,i,A)&&this.opts.html,nextStringDelay:lt(this.opts.nextStringDelay),loopDelay:lt(this.opts.loopDelay)})});this.opts=S(E,s),this.element=he(t),this.timeouts=[],this.cursorPosition=0,this.unfreeze=()=>{},this.predictedCursorPosition=null,this.statuses=S({},jt),this.id=se(),this.queue=Ut([{delay:this.opts.startDelay}]),c(this,V).call(this,s),this.cursor=n(this,i,kt).call(this),this.element.dataset.typeitId=this.id,gt(Ot),this.opts.strings.length&&n(this,i,xt).call(this)}go(){return this.statuses.started?this:(n(this,i,X).call(this),this.opts.waitUntilVisible?(ee(this.element,n(this,i,M).bind(this)),this):(n(this,i,M).call(this),this))}destroy(t=!0){this.timeouts=Kt(this.timeouts),P(t)&&this.cursor&&n(this,i,Q).call(this,this.cursor),this.statuses.destroyed=!0}reset(t){!this.is("destroyed")&&this.destroy(),t?(this.queue.wipe(),t(this)):this.queue.reset(),this.cursorPosition=0;for(let s in this.statuses)this.statuses[s]=!1;return this.element[n(this,i,$).call(this)?"value":"innerHTML"]="",this}type(t,s={}){t=P(t);let{instant:r}=s,a=n(this,i,I).call(this,s),u=Xt(t,this.opts.html).map(h=>({func:()=>n(this,i,R).call(this,h),char:h,delay:r||ne(h)?0:n(this,i,v).call(this),typeable:h.nodeType===Node.TEXT_NODE})),o=[a[0],{func:()=>p(this,null,function*(){return yield this.opts.beforeString(t,this)})},...u,{func:()=>p(this,null,function*(){return yield this.opts.afterString(t,this)})},a[1]];return n(this,i,g).call(this,o,s)}break(t={}){return n(this,i,g).call(this,{func:()=>n(this,i,R).call(this,F("BR")),typeable:!0},t)}move(t,s={}){t=P(t);let r=n(this,i,I).call(this,s),{instant:a,to:l}=s,u=ht({queueItems:this.queue.getTypeable(),selector:t===null?"":t,to:l,cursorPosition:c(this,i,k)}),o=u<0?-1:1;return this.predictedCursorPosition=c(this,i,k)+u,n(this,i,g).call(this,[r[0],...B({func:()=>n(this,i,O).call(this,o),delay:a?0:n(this,i,v).call(this),cursorable:!0},Math.abs(u)),r[1]],s)}exec(t,s={}){let r=n(this,i,I).call(this,s);return n(this,i,g).call(this,[r[0],{func:()=>t(this)},r[1]],s)}options(t,s={}){return t=P(t),n(this,i,x).call(this,t),n(this,i,g).call(this,{},s)}pause(t,s={}){return n(this,i,g).call(this,{delay:P(t)},s)}delete(t=null,s={}){t=P(t);let r=n(this,i,I).call(this,s),a=t,{instant:l,to:u}=s,o=this.queue.getTypeable(),h=a===null?o.length:Tt(a)?a:ht({queueItems:o,selector:a,cursorPosition:c(this,i,k),to:u});return n(this,i,g).call(this,[r[0],...B({func:n(this,i,N).bind(this),delay:l?0:n(this,i,v).call(this,1),deletable:!0},h),r[1]],s)}freeze(){this.statuses.frozen=!0}flush(t=null){return this.flushCallback=t||this.flushCallback,this.statuses.firing?this:(n(this,i,X).call(this),n(this,i,M).call(this,!1).then(()=>{if(this.queue.getPendingQueueItems().length>0)return this.flush();this.flushCallback(),this.flushCallback=null}),this)}getQueue(){return this.queue}getOptions(){return this.opts}updateOptions(t){return n(this,i,x).call(this,t)}getElement(){return this.element}empty(t={}){return n(this,i,g).call(this,{func:n(this,i,St).bind(this)},t)}},i=new WeakSet,St=function(){return p(this,null,function*(){if(n(this,i,$).call(this)){this.element.value="";return}c(this,i,w).forEach(n(this,i,Q).bind(this))})},M=function(t=!0){return p(this,null,function*(){this.statuses.started=!0,this.statuses.firing=!0;let s=r=>{this.queue.done(r,!t)};try{let r=[...this.queue.getQueue()];for(let l=0;l<r.length;l++){let[u,o]=r[l];if(!o.done){if(!o.deletable||o.deletable&&c(this,i,w).length){let h=yield n(this,i,W).call(this,l,r);Gt({index:l,newIndex:h,queueItems:r,cleanUp:s}),l=h}s(u)}}if(!t)return this.statuses.firing=!1,this;if(this.statuses.completed=!0,this.statuses.firing=!1,yield this.opts.afterComplete(this),!this.opts.loop)throw"";let a=this.opts.loopDelay;n(this,i,J).call(this,()=>p(this,null,function*(){yield n(this,i,Mt).call(this,a[0]),n(this,i,M).call(this)}),a[1])}catch(r){}return this.statuses.firing=!1,this})},O=function(t){return p(this,null,function*(){this.cursorPosition=pe(t,this.cursorPosition,c(this,i,w)),ue(this.element,c(this,i,w),this.cursorPosition)})},Mt=function(t){return p(this,null,function*(){let s=c(this,i,k);s&&(yield n(this,i,O).call(this,{value:s}));let r=c(this,i,w).map(a=>[Symbol(),{func:n(this,i,N).bind(this),delay:n(this,i,v).call(this,1),deletable:!0,shouldPauseCursor:()=>!0}]);for(let a=0;a<r.length;a++)yield n(this,i,W).call(this,a,r);this.queue.reset(),this.queue.set(0,{delay:t})})},W=function(t,s){return te({index:t,queueItems:s,wait:n(this,i,J).bind(this),cursor:this.cursor,cursorOptions:this.opts.cursor})},J=function(t,s,r=!1){return p(this,null,function*(){this.statuses.frozen&&(yield new Promise(a=>{this.unfreeze=()=>{this.statuses.frozen=!1,a()}})),r||(yield this.opts.beforeStep(this)),yield me(t,s,this.timeouts),r||(yield this.opts.afterStep(this))})},X=function(){return p(this,null,function*(){if(!n(this,i,$).call(this)&&this.cursor&&this.element.appendChild(this.cursor),c(this,i,K)){ce(this.id,this.element),this.cursor.dataset.tiAnimationId=this.id;let{animation:t}=this.opts.cursor,{frames:s,options:r}=t;Et({frames:s,cursor:this.cursor,options:H({duration:this.opts.cursorSpeed},r)})}})},$=function(){return z(this.element)},g=function(t,s){return this.queue.add(t),n(this,i,It).call(this,s),this},It=function(t={}){let s=t.delay;s&&this.queue.add({delay:s})},I=function(t={}){return[{func:()=>n(this,i,x).call(this,t)},{func:()=>n(this,i,x).call(this,this.opts)}]},x=function(t){return p(this,null,function*(){this.opts=S(this.opts,t)})},xt=function(){let t=this.opts.strings.filter(s=>!!s);t.forEach((s,r)=>{if(this.type(s),r+1===t.length)return;let a=this.opts.breakLines?[{func:()=>n(this,i,R).call(this,F("BR")),typeable:!0}]:B({func:n(this,i,N).bind(this),delay:n(this,i,v).call(this,1)},this.queue.getTypeable().length);n(this,i,G).call(this,a)})},V=new WeakMap,Nt=function(t){let s=this.element.innerHTML;return s?(this.element.innerHTML="",this.opts.startDelete?(this.element.innerHTML=s,tt(this.element),n(this,i,G).call(this,B({func:n(this,i,N).bind(this),delay:n(this,i,v).call(this,1),deletable:!0},c(this,i,w).length)),t):de(s).concat(t)):t},kt=function(){if(c(this,i,A))return null;let t=F("span");return t.className=C,c(this,i,K)?(t.innerHTML=vt(this.opts.cursorChar).innerHTML,t):(t.style.visibility="hidden",t)},G=function(t){let s=this.opts.nextStringDelay;this.queue.add([{delay:s[0]},...t,{delay:s[1]}])},R=function(t){ae(this.element,t)},N=function(){c(this,i,w).length&&(c(this,i,A)?this.element.value=this.element.value.slice(0,-1):n(this,i,Q).call(this,c(this,i,w)[this.cursorPosition]))},Q=function(t){oe(t,this.element)},v=function(t=0){return Wt(this.opts)[t]},k=function(){var t;return(t=this.predictedCursorPosition)!=null?t:this.cursorPosition},A=function(){return z(this.element)},K=function(){return!!this.opts.cursor&&!c(this,i,A)},w=function(){return ie(this.element)},ct);const Pe=Ft({name:"TypeIt",props:{options:{type:Object,default:()=>({})}},setup(e,{slots:t,expose:s}){function r(u){throw new TypeError(u)}function a(){return navigator.language}const l=$t(null);return Rt(()=>{const u=l.value.querySelector(".type-it");if(!u){const h=a()==="zh-CN"?"请确保有且只有一个具有class属性为 'type-it' 的元素":"Please make sure that there is only one element with a Class attribute with 'type-it'";r(h)}const o=new ye(u,e.options).go();s({typeIt:o})}),()=>{var u,o;return nt("div",{ref:l},[(o=(u=t.default)==null?void 0:u.call(t))!=null?o:nt("span",{class:"type-it"},null)])}}});export{Pe as T,ve as a,we as b,Ce as i};
