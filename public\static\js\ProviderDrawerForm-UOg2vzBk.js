var P=(h,v,o)=>new Promise((i,n)=>{var l=a=>{try{u(o.next(a))}catch(r){n(r)}},f=a=>{try{u(o.throw(a))}catch(r){n(r)}},u=a=>a.done?i(a.value):Promise.resolve(a.value).then(l,f);u((o=o.apply(h,v)).next())});/* empty css                         */import{q as C,r as y,o as k,a8 as e,e as t,G as _,Q as T,n as x,N as c,D as b,E as w,P as q,M as g,k as m}from"./index-ZVLuktk4.js";import{u as I}from"./hooks-CuzZ-_om.js";import{P as B}from"./index-CQnBjWL2.js";import"./_plugin-vue_export-helper-QGN-qG8u.js";const F={class:"custom-group-header"},A={class:"font-semibold"},D={class:"custom-footer"},E=C({__name:"ProviderDrawerForm",props:{visible:{type:Boolean},values:{},isEdit:{type:Boolean}},emits:["update:visible","update:values","submit"],setup(h,{expose:v,emit:o}){const i=o,n=y(!1),l=y();k(()=>{});const f=[{label:t(()=>e("Provider Key")),prop:"key",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input provider key"),trigger:["blur","change"]},{min:2,max:255,message:e("Length should be between 2 and 255 characters"),trigger:["blur","change"]}],fieldProps:{placeholder:""},colProps:{span:12}},{label:t(()=>e("Status")),prop:"status",valueType:"select",required:!0,rules:[{required:!0,message:e("Please input provider status"),trigger:["blur","change"]}],fieldProps:{placeholder:"",clearable:!0},options:[{label:e("Active"),value:"active"},{label:e("Inactive"),value:"inactive"}],colProps:{span:12}},{label:t(()=>e("Provider Name")),prop:"name",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input provider name"),trigger:["blur","change"]},{min:2,max:255,message:e("Length should be between 2 and 255 characters"),trigger:["blur","change"]}],fieldProps:{placeholder:""}},{label:t(()=>e("Description")),prop:"description",valueType:"textarea",fieldProps:{placeholder:"",rows:3}},{label:t(()=>e("Base URL")),prop:"baseUrl",required:!0,rules:[{required:!0,message:e("Please input base url"),trigger:["blur","change"]},{min:2,max:255,message:e("Length should be between 2 and 255 characters"),trigger:["blur","change"]}],valueType:"input",fieldProps:{placeholder:""}},{label:t(()=>e("API Key")),prop:"apiKey",valueType:"input",required:!0,rules:[{required:!0,message:e("Please input api key"),trigger:["blur","change"]}],fieldProps:{placeholder:"",type:"password",showPassword:!0}},{label:t(()=>e("Credentials")),prop:"credentials",valueType:"textarea",fieldProps:{placeholder:"",rows:4}}],u=r=>P(null,null,function*(){var p;if(!(!((p=l.value)!=null&&p.formInstance)||!(yield l.value.formInstance.validate())))try{n.value=!0,i("submit",r)}finally{setTimeout(()=>{n.value=!1},3e3)}}),a=()=>{var r;(r=l.value)!=null&&r.formInstance&&l.value.formInstance.resetFields()};return v({resetForm:a}),(r,s)=>{const p=_("el-button");return x(),T(m(B),{ref_key:"formRef",ref:l,visible:r.visible,"model-value":r.values,size:"50%",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0,destroyOnClose:!0,form:{columns:f,footerAlign:"center",hasFooter:!1,labelSuffix:"",labelPosition:"top",labelWidth:"auto",labelAlign:"left",requireAsteriskPosition:"right",rowProps:{gutter:10}},"onUpdate:visible":s[2]||(s[2]=d=>i("update:visible",d)),"onUpdate:modelValue":s[3]||(s[3]=d=>i("update:values",d)),onClose:a},{"drawer-header":c(()=>[b("div",F,[b("span",A,g(m(e)("Information Form")),1)])]),"drawer-footer":c(()=>[b("div",D,[w(p,{plain:"",onClick:s[0]||(s[0]=d=>i("update:visible",!1))},{default:c(()=>[q(g(m(e)("Cancel")),1)]),_:1}),w(p,{plain:"",type:"primary",loading:n.value,icon:m(I)("ri:save-2-line"),onClick:s[1]||(s[1]=d=>u(r.values))},{default:c(()=>[q(g(m(e)("Save")),1)]),_:1},8,["loading","icon"])])]),_:1},8,["visible","model-value","form"])}}});export{E as default};
