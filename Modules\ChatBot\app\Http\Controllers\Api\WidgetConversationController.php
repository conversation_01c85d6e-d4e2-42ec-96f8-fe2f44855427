<?php

namespace Modules\ChatBot\Http\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\ChatBot\Models\Bot;
use <PERSON>dules\ChatBot\Models\BotShareLink;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\WidgetUser;
use Modules\ChatBot\Enums\ConversationStatus;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Enums\ContentType;
use Modules\Core\Traits\ResponseTrait;
use Modules\ChatBot\Services\ConversationService;
use Modules\ChatBot\Facades\ConversationFacade;
use Illuminate\Support\Str;

class WidgetConversationController extends Controller
{
    use ResponseTrait;

    /**
     * Create or get conversation for widget.
     */
    public function createOrGet(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'bot_uuid' => 'required|string',
                'user_id' => 'nullable|string|max:255',
                'conversation_uuid' => 'nullable|string',
                'title' => 'nullable|string|max:255',
            ]);

            $bot = $this->validateBotAccess($request, $request->bot_uuid);
            if (!$bot) {
                return $this->errorResponse('Bot not found or access denied', 404);
            }

            // Create or get widget user
            $widgetUserId = $request->user_id ?: 'widget_' . Str::random(16);
            $widgetUser = WidgetUser::createOrGet($widgetUserId, [
                'bot_uuid' => $bot->uuid,
                'source' => 'widget',
                'created_via' => 'api',
            ]);

            // Try to find existing conversation
            $conversation = null;
            if ($request->conversation_uuid) {
                $conversation = Conversation::where('uuid', $request->conversation_uuid)
                                          ->where('bot_id', $bot->id)
                                          ->where('owner_id', $widgetUser->id)
                                          ->where('owner_type', WidgetUser::class)
                                          ->active()
                                          ->first();
            }

            // Create new conversation if not found
            if (!$conversation) {
                $conversation = Conversation::create([
                    'uuid' => Str::uuid(),
                    'bot_id' => $bot->id,
                    'owner_id' => $widgetUser->id,
                    'owner_type' => WidgetUser::class,
                    'title' => $request->title ?: $this->generateConversationTitle($bot),
                    'status' => ConversationStatus::ACTIVE,
                    'last_message_at' => now(),
                ]);

                // Create greeting message if bot has one
                if ($bot->hasGreetingMessage()) {
                    $this->createGreetingMessage($conversation, $bot);
                }
            }

            return $this->successResponse([
                'conversationId' => $conversation->uuid,
                'userId' => $widgetUserId,
                'title' => $conversation->title,
                'hasGreeting' => $bot->hasGreetingMessage(),
                'bot' => [
                    'name' => $bot->name,
                    'logoUrl' => $bot->logo_url,
                    'greetingMessage' => $bot->greeting_message,
                ]
            ], 'Conversation ready');

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Get conversation info for widget.
     */
    public function show(Request $request, string $conversationUuid): JsonResponse
    {
        try {
            $conversation = Conversation::with(['bot'])
                                      ->where('uuid', $conversationUuid)
                                      ->active()
                                      ->firstOrFail();

            // Validate bot access
            $bot = $this->validateBotAccess($request, $conversation->bot->uuid);
            if (!$bot) {
                return $this->errorResponse('Bot access denied', 403);
            }

            return $this->successResponse([
                'uuid' => $conversation->uuid,
                'title' => $conversation->title,
                'bot' => [
                    'name' => $conversation->bot->name,
                ],
            ], 'Conversation retrieved successfully');

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Validate bot access for widget.
     */
    private function validateBotAccess(Request $request, string $botUuid): ?Bot
    {
        // Try API key access
        if ($request->hasHeader('Authorization')) {
            $apiKey = str_replace('Bearer ', '', $request->header('Authorization'));
            
            if (str_starts_with($apiKey, 'pk_')) {
                $bot = Bot::where('uuid', $botUuid)
                         ->where('api_key', $apiKey)
                         ->active()
                         ->first();
                
                if ($bot) {
                    return $bot;
                }
            }
        }

        // Try share token access (botUuid might be share token)
        $shareLink = BotShareLink::with(['bot'])
                                ->where('token', $botUuid)
                                ->active()
                                ->first();

        if ($shareLink && $shareLink->bot->canBeShared()) {
            return $shareLink->bot;
        }

        return null;
    }

    /**
     * Generate conversation title.
     */
    private function generateConversationTitle(Bot $bot): string
    {
        return "Chat with {$bot->name} - " . now()->format('M j, Y g:i A');
    }

    /**
     * Create greeting message.
     */
    private function createGreetingMessage(Conversation $conversation, Bot $bot): void
    {
        if (!$bot->greeting_message) {
            return;
        }

        $conversation->messages()->create([
            'uuid' => Str::uuid(),
            'role' => MessageRole::ASSISTANT,
            'content' => $bot->greeting_message,
            'content_type' => ContentType::TEXT,
            'status' => MessageStatus::COMPLETED,
            'created_at' => now(),
        ]);

        $conversation->update(['last_message_at' => now()]);
    }
}
