<?php

namespace Modules\Sidebar\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use Modules\Core\Filters\BaseFilter;

class SidebarFilter extends BaseFilter
{
    /**
     * Apply filters to the query
     */
    public function apply(Builder $query): Builder
    {
        return $query
            ->when($this->request->filled('search'), function ($q) {
                $this->applySearch($q);
            })
            ->when($this->request->filled('menu_type'), function ($q) {
                $q->where('menu_type', $this->request->menu_type);
            })
            ->when($this->request->filled('parent_id'), function ($q) {
                $q->where('parent_id', $this->request->parent_id);
            })
            ->when($this->request->filled('status'), function ($q) {
                $this->applyStatusFilter($q);
            })
            ->when($this->request->filled('has_children'), function ($q) {
                $this->applyChildrenFilter($q);
            })
            ->when($this->request->filled('roles'), function ($q) {
                $this->applyRolesFilter($q);
            })
            ->when($this->request->filled('sort_by'), function ($q) {
                $this->applySorting($q);
            }, function ($q) {
                $q->orderBy('rank');
            });
    }

    /**
     * Apply search filter
     */
    protected function applySearch(Builder $query): void
    {
        $search = $this->request->search;
        
        $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('name', 'like', "%{$search}%")
              ->orWhere('path', 'like', "%{$search}%")
              ->orWhere('component', 'like', "%{$search}%");
        });
    }

    /**
     * Apply status filter
     */
    protected function applyStatusFilter(Builder $query): void
    {
        $status = $this->request->status;
        
        switch ($status) {
            case 'active':
                $query->where('show_link', true);
                break;
            case 'hidden':
                $query->where('show_link', false);
                break;
            case 'with_children':
                $query->has('children');
                break;
            case 'without_children':
                $query->doesntHave('children');
                break;
        }
    }

    /**
     * Apply children filter
     */
    protected function applyChildrenFilter(Builder $query): void
    {
        $hasChildren = filter_var($this->request->has_children, FILTER_VALIDATE_BOOLEAN);
        
        if ($hasChildren) {
            $query->has('children');
        } else {
            $query->doesntHave('children');
        }
    }

    /**
     * Apply roles filter
     */
    protected function applyRolesFilter(Builder $query): void
    {
        $roles = is_array($this->request->roles) 
            ? $this->request->roles 
            : [$this->request->roles];
            
        $query->where(function ($q) use ($roles) {
            foreach ($roles as $role) {
                $q->orWhereJsonContains('roles', $role);
            }
        });
    }

    /**
     * Apply sorting
     */
    protected function applySorting(Builder $query): void
    {
        $sortBy = $this->request->sort_by;
        $sortDirection = $this->request->sort_direction ?? 'asc';
        
        $allowedSorts = ['title', 'name', 'rank', 'menu_type', 'created_at', 'updated_at'];
        
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortDirection);
        }
    }
}
