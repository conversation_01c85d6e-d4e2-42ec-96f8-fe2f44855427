var N=(C,_,r)=>new Promise((g,V)=>{var R=c=>{try{d(r.next(c))}catch(v){V(v)}},y=c=>{try{d(r.throw(c))}catch(v){V(v)}},d=c=>c.done?g(c.value):Promise.resolve(c.value).then(R,y);d((r=r.apply(C,_)).next())});import{q as H,B as ae,ai as se,a7 as oe,g as P,fL as ne,u as le,r as h,e as T,fM as ie,o as re,j as ce,fN as ue,m as $,n as I,D as i,k as e,E as t,a4 as de,G as p,N as n,Q as q,X as me,a6 as pe,P as S,M as w,a8 as f,ap as fe,I as ve,J as _e,H as ge,L as ye,aC as he,fR as we,ax as b,i as be,fS as xe,_ as ke}from"./index-ZVLuktk4.js";import{e as Ce}from"./validation-Db2MkLBC.js";import{a as Ve,u as Re,b as Ie,d as Se}from"./dark-C3BJic5q.js";import{u as Ee}from"./useLayout-B65ZqU-V.js";import{b as De,i as Le,a as Ne,T as Te}from"./index-BnPdKjjT.js";import{u as Be}from"./hooks-CuzZ-_om.js";import{u as ze,g as Me}from"./useTranslation-Bgn2GAKO.js";import Fe from"./AuthNavigation-DpjgmbE5.js";import"./epTheme-DzIV6EF-.js";const k=H({name:"Motion",props:{delay:{type:Number,default:50}},render(){const{delay:C}=this,_=oe("motion");return ae(se("div",{},{default:()=>{var r,g;return[(g=(r=this.$slots).default)==null?void 0:g.call(r)]}}),[[_,{initial:{opacity:0,y:100},enter:{opacity:1,y:0,transition:{delay:C}}}]])}}),Oe=Ce(),$e=P({otp:[{validator:Oe,trigger:"blur"}]}),qe={class:"select-none"},He=["src"],Pe={class:"flex-c absolute right-5 top-3"},je={class:"login-container"},Ue={class:"img"},We={class:"login-box"},Ae={class:"login-form"},Ge={class:"outline-none"},Je={class:"text-center mb-4 text-gray-500"},Qe={class:"text-center mt-4"},Xe={class:"text-gray-500"},Ke=H({name:"VerifyEmail",__name:"index",setup(C){const _=ne(),r=le(),g=ze();Object.keys(r.settings).length===0&&r.fetchPublicSettings();const V=be(),R=ce(),y=h(!1),d=h(!1),c=h(!1),v=h(),x=h(0),E=h(null),{initStorage:j}=Ee();j();const{dataTheme:D,overallStyle:U,dataThemeChange:B}=Ve();B(U.value);const{getDropdownItemStyle:W,getDropdownItemClass:A}=Re(),m=P({email:"",otp:""}),G=T(()=>{var s;return(s=_.languages)==null?void 0:s.map(a=>({locale:a.code,native:a.nativeName}))}),z=T(()=>_.locale||ie.global.locale.value),L=T(()=>x.value===0&&!d.value);re(()=>{R.query.email&&(m.email=R.query.email),M()});const M=()=>{x.value=60,E.value=setInterval(()=>{x.value--,x.value<=0&&(clearInterval(E.value),E.value=null)},1e3)},F=s=>N(null,null,function*(){var a,o;if(s)try{yield s.validate(),y.value=!0;const l=yield we({email:m.email,code:m.otp});if(!l.success){b(l.message,{type:"error"});return}b(l.message,{type:"success"}),yield V.push("/login")}catch(l){b(((o=(a=l==null?void 0:l.response)==null?void 0:a.data)==null?void 0:o.message)||(l==null?void 0:l.message),{type:"error"})}finally{y.value=!1}}),J=()=>N(null,null,function*(){var s,a;if(L.value)try{d.value=!0;const o=yield xe({email:m.email});if(!o.success){b(o.message,{type:"error"});return}b(o.message,{type:"success"}),M()}catch(o){b(((a=(s=o==null?void 0:o.response)==null?void 0:s.data)==null?void 0:a.message)||(o==null?void 0:o.message),{type:"error"})}finally{d.value=!1}}),Q=fe(s=>F(s),1e3,!0),X=s=>{g.switchLanguage(s)};return ue(document,"keydown",({code:s})=>{["Enter","NumpadEnter"].includes(s)&&!c.value&&!y.value&&Q(v.value)}),(s,a)=>{const o=p("el-switch"),l=p("el-dropdown-item"),K=p("el-dropdown-menu"),Y=p("el-dropdown"),Z=p("el-input"),ee=p("el-form-item"),O=p("el-button"),te=p("el-form");return I(),$("div",qe,[i("img",{src:e(De),class:"wave",alt:"Bg"},null,8,He),i("div",Pe,[t(o,{modelValue:e(D),"onUpdate:modelValue":a[0]||(a[0]=u=>de(D)?D.value=u:null),"inline-prompt":"","active-icon":e(Se),"inactive-icon":e(Ie),onChange:e(B)},null,8,["modelValue","active-icon","inactive-icon","onChange"]),t(Y,{trigger:"click"},{dropdown:n(()=>[t(K,{class:"translation"},{default:n(()=>[(I(!0),$(ve,null,_e(G.value,u=>(I(),q(l,{key:u.locale,style:ge(e(W)(z.value,u.locale)),class:ye(["dark:!text-white",e(A)(z.value,u.locale)]),onClick:Ye=>X(u.locale)},{default:n(()=>[S(w(u.native),1)]),_:2},1032,["style","class","onClick"]))),128))]),_:1})]),default:n(()=>[t(e(Me),{class:"hover:text-primary hover:!bg-[transparent] w-[20px] h-[20px] ml-1.5 cursor-pointer outline-none duration-300"})]),_:1})]),i("div",je,[i("div",Ue,[(I(),q(me(pe(e(Le)))))]),i("div",We,[i("div",Ae,[t(e(Ne),{class:"avatar"}),t(e(k),null,{default:n(()=>[i("h2",Ge,[t(e(Te),{options:{strings:[e(f)("Verify Email")],cursor:!1,speed:100}},null,8,["options"])])]),_:1}),i("div",Je,[S(w(e(f)("We've sent a verification code to")),1),a[3]||(a[3]=i("br",null,null,-1)),i("strong",null,w(m.email),1)]),t(te,{ref_key:"ruleFormRef",ref:v,model:m,rules:e($e),size:"large"},{default:n(()=>[t(e(k),{delay:100},{default:n(()=>[t(ee,{prop:"otp"},{default:n(()=>[t(Z,{modelValue:m.otp,"onUpdate:modelValue":a[1]||(a[1]=u=>m.otp=u),clearable:"",placeholder:e(f)("OTP Code"),"prefix-icon":e(Be)("ri:shield-keyhole-line"),maxlength:"6",class:"text-center"},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),t(e(k),{delay:150},{default:n(()=>[t(O,{class:"w-full !uppercase",size:"large",type:"danger",round:"",loading:y.value,disabled:c.value,onClick:a[2]||(a[2]=u=>F(v.value))},{default:n(()=>[t(e(he),{icon:"ri:shield-check-line",width:"20",class:"mr-2"}),S(" "+w(e(f)("Verify Email")),1)]),_:1},8,["loading","disabled"])]),_:1}),t(e(k),{delay:200},{default:n(()=>[i("div",Qe,[i("span",Xe,w(e(f)("Didn't receive the code?")),1),t(O,{link:"",type:"primary",disabled:!L.value,loading:d.value,class:"ml-1",onClick:J},{default:n(()=>[S(w(L.value?e(f)("Resend Code"):e(f)("Resend in {seconds}s",{seconds:x.value})),1)]),_:1},8,["disabled","loading"])])]),_:1}),t(e(k),{delay:250},{default:n(()=>[t(Fe,{"show-login":!0,"show-register":!1,"show-forgot-password":!1,"custom-links":[{text:e(f)("Change Email"),path:"/register"}]},null,8,["custom-links"])]),_:1})]),_:1},8,["model","rules"])])])])])}}}),ct=ke(Ke,[["__scopeId","data-v-1d53c91e"]]);export{ct as default};
