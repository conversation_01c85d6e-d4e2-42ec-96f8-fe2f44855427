/**
 * Widget Loader - Hybrid Implementation
 * Handles both Widget and Iframe loading with smart detection
 */

import { createApp, App as VueApp } from 'vue';
import type { WidgetConfig, BotConfig, WidgetMode } from '../types';
import { detectWidgetMode } from './widget-detector';
import { getBotApiClient, ApiError } from '../api/bot-api';
import { EventEmitter } from './event-emitter';

export class WidgetLoader extends EventEmitter {
  private config: WidgetConfig;
  private botConfig: BotConfig | null = null;
  private container: HTMLElement | null = null;
  private mode: WidgetMode | null = null;
  private app: VueApp | null = null;
  private iframe: HTMLIFrameElement | null = null;
  private mounted = false;

  constructor(config: WidgetConfig) {
    super();
    this.config = this.normalizeConfig(config);
    this.validateConfig();
  }

  /**
   * Normalize and set default config values
   */
  private normalizeConfig(config: WidgetConfig): WidgetConfig {
    return {
      mode: 'auto',
      theme: 'light',
      position: 'bottom-right',
      showHeader: true,
      showAvatar: true,
      autoOpen: false,
      width: 400,
      height: 600,
      ...config
    };
  }

  /**
   * Validate required configuration
   */
  private validateConfig(): void {
    if (!this.config.botUuid) {
      throw new Error('Bot UUID is required');
    }

    if (!this.config.apiKey) {
      throw new Error('API Key is required');
    }

    // Validate API key format
    if (!this.config.apiKey.match(/^pk_(live|test)_[a-zA-Z0-9]{32}$/)) {
      console.warn('API key format may be invalid');
    }
  }

  /**
   * Load and mount the widget
   */
  async mount(selector: string | HTMLElement): Promise<void> {
    try {
      // Get container element
      this.container = typeof selector === 'string' 
        ? document.querySelector(selector)
        : selector;

      if (!this.container) {
        throw new Error(`Container not found: ${selector}`);
      }

      // Show loading state
      this.showLoadingState();

      // Load bot configuration
      await this.loadBotConfiguration();

      // Detect best mode
      this.mode = this.config.mode === 'auto' 
        ? detectWidgetMode(this.config)
        : this.config.mode as WidgetMode;

      // Mount based on detected mode
      if (this.mode === 'iframe') {
        await this.mountIframe();
      } else {
        await this.mountWidget();
      }

      this.mounted = true;
      this.emit('mounted', { mode: this.mode, botConfig: this.botConfig });

    } catch (error) {
      this.showErrorState(error as Error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Load bot configuration from API
   */
  private async loadBotConfiguration(): Promise<void> {
    try {
      // Get API client with enhanced features
      const apiClient = getBotApiClient(this.config.apiKey, {
        timeout: 10000,
        retryAttempts: 3,
        enableCache: true,
        cacheTimeout: 300000 // 5 minutes
      });

      // Add request interceptor for widget-specific headers
      apiClient.addRequestInterceptor((config) => {
        const headers = new Headers(config.headers);
        headers.set('X-Widget-Version', '1.0.0');
        headers.set('X-Widget-Mode', this.mode || 'auto');
        return { ...config, headers };
      });

      const response = await apiClient.getBotConfig(this.config.botUuid);

      if (!response.success || !response.data) {
        throw new Error(response.message || 'Failed to load bot configuration');
      }

      this.botConfig = response.data;

      // Validate bot status
      if (this.botConfig.status !== 'active') {
        throw new Error(`Bot is not active. Current status: ${this.botConfig.status}`);
      }

      this.emit('bot-loaded', this.botConfig);
    } catch (error) {
      if (error instanceof ApiError) {
        console.error(`API Error ${error.status}: ${error.message}`, error.data);
      } else {
        console.error('Failed to load bot configuration:', error);
      }
      throw error;
    }
  }

  /**
   * Mount as JavaScript widget
   */
  private async mountWidget(): Promise<void> {
    if (!this.container || !this.botConfig) return;

    try {
      // Dynamically import the chat widget component
      const { default: ChatWidget } = await import('../components/ChatWidget.vue');

      // Create Vue app
      this.app = createApp(ChatWidget, {
        config: this.config,
        botConfig: this.botConfig,
        onMessage: (data: any) => this.emit('message', data),
        onError: (error: any) => this.emit('error', error),
        onClose: () => this.emit('close'),
        onOpen: () => this.emit('open'),
        onReady: () => this.emit('ready')
      });

      // Mount to container
      this.app.mount(this.container);

    } catch (error) {
      console.error('Failed to mount widget:', error);
      // Fallback to iframe
      this.mode = 'iframe';
      await this.mountIframe();
    }
  }

  /**
   * Mount as iframe
   */
  private async mountIframe(): Promise<void> {
    if (!this.container) return;

    // Clear container
    this.container.innerHTML = '';

    // Create iframe
    this.iframe = document.createElement('iframe');
    
    // Build iframe URL with parameters
    const params = new URLSearchParams({
      bot_uuid: this.config.botUuid,
      api_key: this.config.apiKey,
      theme: this.config.theme || 'light',
      position: this.config.position || 'bottom-right',
      show_header: this.config.showHeader ? '1' : '0',
      show_avatar: this.config.showAvatar ? '1' : '0',
      auto_open: this.config.autoOpen ? '1' : '0',
      user_id: this.config.userId || '',
      domain: window.location.hostname,
      referrer: document.referrer
    });

    const iframeUrl = this.config.iframeUrl || this.getDefaultIframeUrl();
    this.iframe.src = `${iframeUrl}?${params.toString()}`;

    // Set iframe styles
    this.iframe.style.cssText = `
      width: 100%;
      height: 100%;
      border: none;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      background: white;
    `;

    // Set iframe attributes
    this.iframe.setAttribute('frameborder', '0');
    this.iframe.setAttribute('allowtransparency', 'true');
    this.iframe.setAttribute('title', `${this.botConfig?.name || 'Chatbot'} Widget`);

    // Handle iframe messages
    this.setupIframeMessaging();

    // Add to container
    this.container.appendChild(this.iframe);

    // Wait for iframe to load
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Iframe load timeout'));
      }, 10000);

      this.iframe!.onload = () => {
        clearTimeout(timeout);
        resolve(void 0);
      };

      this.iframe!.onerror = () => {
        clearTimeout(timeout);
        reject(new Error('Iframe failed to load'));
      };
    });
  }

  /**
   * Get default iframe URL
   */
  private getDefaultIframeUrl(): string {
    // Use same domain if possible, otherwise use default
    const currentDomain = window.location.origin;
    if (currentDomain.includes('procms.com') || currentDomain.includes('localhost')) {
      return `${currentDomain}/widget`;
    }
    return 'https://widget.procms.com';
  }

  /**
   * Setup iframe messaging
   */
  private setupIframeMessaging(): void {
    window.addEventListener('message', (event) => {
      // Validate origin
      const allowedOrigins = [
        'https://widget.procms.com',
        'https://procms.com',
        'http://localhost:8848',
        window.location.origin
      ];

      if (!allowedOrigins.some(origin => event.origin.startsWith(origin))) {
        return;
      }

      const { type, data } = event.data;
      
      // Forward iframe events
      switch (type) {
        case 'widget-ready':
          this.emit('ready');
          break;
        case 'widget-message':
          this.emit('message', data);
          break;
        case 'widget-error':
          this.emit('error', data);
          break;
        case 'widget-open':
          this.emit('open');
          break;
        case 'widget-close':
          this.emit('close');
          break;
        default:
          // Forward unknown events
          this.emit(type, data);
      }
    });
  }

  /**
   * Show loading state
   */
  private showLoadingState(): void {
    if (!this.container) return;

    this.container.innerHTML = `
      <div class="procms-widget-loading" style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      ">
        <div style="
          width: 32px;
          height: 32px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #007bff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 16px;
        "></div>
        <div style="color: #666; font-size: 14px;">Loading chatbot...</div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `;
  }

  /**
   * Show error state
   */
  private showErrorState(error: Error): void {
    if (!this.container) return;

    this.container.innerHTML = `
      <div class="procms-widget-error" style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        padding: 20px;
        background: #fff5f5;
        border: 1px solid #fed7d7;
        border-radius: 8px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      ">
        <div style="color: #e53e3e; font-size: 16px; margin-bottom: 8px;">⚠️ Error</div>
        <div style="color: #666; font-size: 14px; text-align: center;">
          ${error.message || 'Failed to load chatbot'}
        </div>
        <button onclick="window.location.reload()" style="
          margin-top: 12px;
          padding: 8px 16px;
          background: #007bff;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        ">Retry</button>
      </div>
    `;
  }

  /**
   * Unmount the widget
   */
  unmount(): void {
    if (!this.mounted) return;

    if (this.app) {
      this.app.unmount();
      this.app = null;
    }

    if (this.iframe) {
      this.iframe.remove();
      this.iframe = null;
    }

    if (this.container) {
      this.container.innerHTML = '';
    }

    this.mounted = false;
    this.emit('unmounted');
  }

  /**
   * Check if widget is mounted
   */
  isMounted(): boolean {
    return this.mounted;
  }

  /**
   * Get current mode
   */
  getMode(): WidgetMode | null {
    return this.mode;
  }

  /**
   * Get bot configuration
   */
  getBotConfig(): BotConfig | null {
    return this.botConfig;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<WidgetConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.mounted) {
      // Re-mount with new config
      this.unmount();
      this.mount(this.container!);
    }
  }
}
