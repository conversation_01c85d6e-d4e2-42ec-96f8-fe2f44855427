import{q as A,G as m,m as d,n as g,D as e,M as n,k as t,a8 as a,E as l,N as c,P as h,I as x,J as y,gd as E,gc as U,A as N,_ as H}from"./index-ZVLuktk4.js";const z={class:"card"},D={class:"section-title"},F={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},P={class:"space-y-6"},W={class:"flex justify-between w-full items-center"},$={class:"text-sm font-medium text-gray-500 mb-2 text-center"},j={class:"chat-preview-container"},q={class:"chat-preview-window"},J={class:"chat-preview-header"},L=["src"],T={class:"font-semibold text-gray-700"},K={class:"chat-preview-body"},O={class:"chat-message"},Q={key:0,class:"starter-suggestions"},R=A({__name:"BotChatInterface",props:{greetingMessage:{},starterMessages:{},name:{},logoUrl:{},loadingGreeting:{type:Boolean},loadingStarters:{type:Boolean}},emits:["update:greetingMessage","update:starterMessages","generateGreeting","generateStarters"],setup(S,{emit:b}){const s=S,i=b,C=o=>{i("update:greetingMessage",o)},k=(o,p)=>{const r=[...s.starterMessages||[]];r[o]=p,i("update:starterMessages",r)},G=()=>{const o=[...s.starterMessages||[],""];i("update:starterMessages",o)},I=o=>{const p=[...s.starterMessages||[]];p.splice(o,1),i("update:starterMessages",p)},B=()=>{i("generateGreeting")},V=()=>{i("generateStarters")};return(o,p)=>{var w;const r=m("el-button"),v=m("el-input"),f=m("el-form-item");return g(),d("div",z,[e("h2",D,n(t(a)("Chat Interface")),1),e("div",F,[e("div",P,[l(f,{label:t(a)("Welcome Message")},{default:c(()=>[l(v,{"model-value":s.greetingMessage,placeholder:t(a)("Example: Hello! How can I help you?"),type:"textarea",rows:3,"onUpdate:modelValue":C},{append:c(()=>[l(r,{class:"gemini-button",loading:s.loadingGreeting,onClick:B},{default:c(()=>[h(n(t(a)("✨ Generate")),1)]),_:1},8,["loading"])]),_:1},8,["model-value","placeholder"])]),_:1},8,["label"]),l(f,null,{label:c(()=>[e("div",W,[e("span",null,n(t(a)("Starter Suggestions")),1),l(r,{class:"gemini-button",loading:s.loadingStarters,round:"",size:"small",onClick:V},{default:c(()=>[h(n(t(a)("✨ Generate")),1)]),_:1},8,["loading"])])]),default:c(()=>[(g(!0),d(x,null,y(s.starterMessages,(_,u)=>(g(),d("div",{key:u,class:"flex items-center mb-2 w-full"},[l(v,{"model-value":_,placeholder:t(a)("Example: What is the tuition fee?"),class:"flex-1 mr-1","onUpdate:modelValue":M=>k(u,M)},null,8,["model-value","placeholder","onUpdate:modelValue"]),l(r,{type:"danger",icon:t(E),circle:"",plain:"",size:"small",onClick:M=>I(u)},null,8,["icon","onClick"])]))),128)),l(r,{icon:t(U),size:"small",onClick:G},{default:c(()=>[h(n(t(a)("Add Suggestion")),1)]),_:1},8,["icon"])]),_:1})]),e("div",null,[e("h3",$,n(t(a)("Preview")),1),e("div",j,[e("div",q,[e("div",J,[e("img",{src:s.logoUrl||"https://placehold.co/40x40/E2E8F0/4A5568?text=AI",class:"chat-preview-avatar",alt:"logo"},null,8,L),e("span",T,n(s.name||t(a)("AI Assistant")),1)]),e("div",K,[e("div",O,n(s.greetingMessage||t(a)("Hello! How can I help you?")),1),(w=s.starterMessages)!=null&&w.length?(g(),d("div",Q,[(g(!0),d(x,null,y(s.starterMessages,(_,u)=>(g(),d("div",{key:u,class:"starter-item"},n(_),1))),128))])):N("",!0)])])])])])])}}}),Y=H(R,[["__scopeId","data-v-b18d03b1"]]);export{Y as default};
