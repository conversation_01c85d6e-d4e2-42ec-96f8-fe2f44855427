import{u as S}from"./hooks-CuzZ-_om.js";import{q as O,r as m,e as T,gY as A,o as F,S as G,ao as J,G as c,m as d,n as u,D as e,M as i,E as o,N as l,P as C,k as n,a8 as y,L as b,I as P,J as Y}from"./index-ZVLuktk4.js";const K={class:"agents-content h-full"},Q={class:"flex flex-col h-full"},W={class:"flex items-center justify-between px-4 py-3"},X={class:"flex items-center"},Z=["src"],ee={class:"text-lg font-semibold text-gray-900"},te={class:"text-sm text-gray-500"},ne={class:"relative h-[32px]"},oe={class:"absolute top-0 left-0 right-0 flex justify-center"},se={key:0,class:"flex items-center justify-center py-8"},ie={key:1,class:"flex flex-col gap-1 px-3 py-2"},le=["onClick"],ae={class:"truncate font-semibold"},de=O({__name:"ChatSidebar",props:{mainView:{default:"list"},chatBot:{}},emits:["newConversation","showConversation"],setup(B,{emit:V}){const j=B,k=V,{fetchAllConversations:D,promptUpdateConversation:N,confirmDeleteConversation:E,conversations:U,currentConversationId:L}=j.chatBot,f=m(null),H=m(!1),$=m(400),_=m(null),v=T(()=>A().userInfo),q=(s,a)=>{f.value=a?s:null},z=()=>{k("newConversation")},R=s=>{k("showConversation",s)},x=()=>{if(_.value){const s=document.querySelector(".chat-header"),a=document.querySelector(".chat-footer"),r=s?s.getBoundingClientRect().height:81,g=a?a.getBoundingClientRect().height:125,h=_.value.parentElement.clientHeight-r-g;$.value=h>0?h:400}};return F(()=>{G(()=>{D(),x()}),window.addEventListener("resize",x)}),J(()=>{window.removeEventListener("resize",x)}),(s,a)=>{const r=c("IconifyIconOnline"),g=c("el-button"),w=c("el-dropdown-item"),h=c("el-dropdown-menu"),M=c("el-dropdown");return u(),d("div",{ref_key:"chatSidebarContainer",ref:_,class:"chat-sidebar-container w-full max-w-[360px] h-full relative rounded-l-xl bg-white border-r-2 border-gray-200"},[e("div",K,[e("div",Q,[e("header",W,[e("div",X,[e("img",{src:v.value.avatarUrl||"/bots/default.png",alt:"logo",class:"w-10 h-10 rounded-full mr-4 object-cover"},null,8,Z),e("div",null,[e("h2",ee,i(v.value.nickname),1),e("div",te,i(v.value.email),1)])])]),e("div",ne,[e("div",oe,[o(g,{type:"primary",class:"w-full !rounded-none",onClick:z},{default:l(()=>[o(r,{icon:"mdi:chat-plus-outline",class:"mr-2 text-2xl"}),C(" "+i(n(y)("New conversation")),1)]),_:1})])]),e("div",{class:b(["flex flex-col",{"h-full justify-center":H.value}])},[H.value?(u(),d("div",se,[o(r,{icon:"eos-icons:loading",class:"text-5xl text-amber-700"})])):(u(),d("div",ie,[(u(!0),d(P,null,Y(n(U),(t,I)=>(u(),d("div",{key:t.uuid,class:b(["group flex items-center justify-between hover:bg-primary hover:text-white cursor-pointer px-2 py-1 rounded-3xl text-xs text-primary",{"bg-primary text-white":f.value===t.uuid||t.uuid===n(L)}])},[e("div",{class:"py-2 flex-grow min-w-0 pr-3",onClick:p=>R(t)},[e("div",ae,i(t.title),1)],8,le),e("div",{class:b(["invisible group-hover:visible flex max-w-[80px]",{"!visible":f.value===t.uuid}])},[o(M,{trigger:"click",onVisibleChange:p=>q(t.uuid,p)},{dropdown:l(()=>[o(h,null,{default:l(()=>[o(w,{icon:n(S)("ri:edit-2-line"),onClick:p=>n(N)(I,t)},{default:l(()=>[C(i(n(y)("Change title")),1)]),_:2},1032,["icon","onClick"]),o(w,{icon:n(S)("ri:delete-bin-line"),class:"!text-red-700",onClick:p=>n(E)(I,t)},{default:l(()=>[C(i(n(y)("Delete")),1)]),_:2},1032,["icon","onClick"])]),_:2},1024)]),default:l(()=>[o(r,{icon:"mdi:dots-vertical",class:"text-xl text-white"})]),_:2},1032,["onVisibleChange"])],2)],2))),128))]))],2)])])],512)}}});export{de as default};
