var Qu=Object.defineProperty;var zi=Object.getOwnPropertySymbols;var tv=Object.prototype.hasOwnProperty,ev=Object.prototype.propertyIsEnumerable;var Da=(a,e,t)=>e in a?Qu(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t,Oi=(a,e)=>{for(var t in e||(e={}))tv.call(e,t)&&Da(a,t,e[t]);if(zi)for(var t of zi(e))ev.call(e,t)&&Da(a,t,e[t]);return a};var Bi=(a,e,t)=>Da(a,typeof e!="symbol"?e+"":e,t);var Te=(a,e,t)=>new Promise((r,n)=>{var i=l=>{try{s(t.next(l))}catch(u){n(u)}},o=l=>{try{s(t.throw(l))}catch(u){n(u)}},s=l=>l.done?r(l.value):Promise.resolve(l.value).then(i,o);s((t=t.apply(a,e)).next())});import{aR as _t,aS as ht,aT as Re,aU as Je,aV as ot,aW as pr,aX as nt,aY as z,aZ as rv,a_ as Pt,a$ as av,b0 as nv,b1 as iv,b2 as C,b3 as pl,b4 as dl,b5 as Fi,b6 as ov,b7 as sv,b8 as Zt,b9 as Tt,ba as zt,bb as lt,bc as gl,bd as V,be as Be,bf as dt,bg as Y,bh as wt,bi as qt,bj as sa,bk as la,bl as ft,bm as X,bn as lv,bo as uv,bp as H,bq as ne,br as ge,bs as fe,bt as it,bu as Et,bv as Fe,bw as U,bx as $t,by as Ot,bz as It,bA as ct,bB as B,bC as ua,bD as dr,bE as va,bF as et,bG as gr,bH as vv,bI as At,bJ as Bt,bK as rt,bL as Yt,bM as Kt,bN as Xn,bO as cv,bP as Jt,bQ as Zn,bR as ca,bS as hv,bT as xn,bU as Qt,bV as ha,bW as bn,bX as fv,bY as jt,bZ as fa,b_ as Lt,b$ as gt,c0 as pv,c1 as dv,c2 as gv,c3 as we,c4 as yv,c5 as yl,c6 as mv,c7 as Wi,c8 as qn,c9 as ml,ca as Ge,cb as yr,cc as j,cd as Sl,ce as Sv,cf as xv,cg as bv,ch as _v,ci as pa,cj as wv,ck as ir,cl as ze,cm as jn,cn as Kn,co as Dv,cp as Tv,cq as mr,cr as xl,cs as st,ct as bl,cu as _n,cv as Iv,cw as Av,cx as xt,cy as da,cz as Jn,cA as kt,cB as te,cC as Lv,cD as Cv,cE as ga,cF as Pv,cG as Mv,cH as Ev,cI as ya,cJ as _l,cK as Ur,cL as Rv,cM as $r,cN as wl,cO as Sr,cP as Qn,cQ as Mt,cR as or,cS as wn,cT as Ut,cU as Nv,cV as kv,cW as Vt,cX as bt,cY as Oe,cZ as Dl,c_ as Hi,c$ as Tr,d0 as Ui,d1 as Tl,d2 as Vv,d3 as Il,d4 as Dn,d5 as Al,d6 as $i,d7 as xr,d8 as ce,d9 as br,da as Gv,db as zv,dc as Ne,dd as Yi,de as Tn,df as Ov,dg as Ht,dh as In,di as ti,dj as ei,dk as ri,dl as _r,dm as ma,dn as Bv,dp as Ll,dq as Fv,dr as Cl,ds as Wv,dt as Hv,du as Xi,dv as sr,dw as Zi,dx as Uv,dy as $v,dz as ue,dA as Yv,dB as Xv,dC as wr,dD as Pl,dE as Zv,dF as Ml,dG as El,dH as Rl,dI as qv,dJ as lr,dK as Nl,dL as kl,dM as jv,dN as Kv,dO as Jv,dP as Vl,dQ as An,dR as Ln,dS as Qv,dT as Ta,dU as Gl,dV as ut,dW as ai,dX as Sa,dY as Ia,dZ as tc,d_ as ec,d$ as Qe,e0 as rc,e1 as qi,e2 as ac,e3 as ji,e4 as xa,e5 as Cn,e6 as nc,e7 as zl,e8 as Ki,e9 as ic,ea as oc,eb as sc,ec as Ji,ed as lc,ee as uc,ef as vc,eg as ni,eh as Qi,ei as to,ej as cc,ek as hc,el as fc,em as pc,en as dc,eo as gc,ep as yc,eq as mc,er as Sc,es as xc,et as Ol,eu as bc,ev as _c,ew as wc,ex as Dc,ey as Tc,ez as Ic,eA as Ac,eB as Lc,eC as Bl,eD as Cc,eE as Pc,eF as Mc,eG as Ec,eH as eo,eI as Rc,eJ as We,eK as Ir,eL as Nc,eM as kc,eN as Vc,eO as Gc,eP as zc,eQ as Oc,eR as Bc,eS as ii,eT as Aa,eU as Fc,eV as Wc,eW as Hc,eX as Uc,eY as $c,eZ as Yr,e_ as Yc,e$ as Xc,f0 as Zc,f1 as qc,f2 as oi,f3 as jc,f4 as Kc,f5 as Fl,f6 as Jc,f7 as Qc,f8 as th,f9 as eh,fa as rh,fb as ro,fc as ah,fd as nh,fe as Xr,ff as si,fg as ih,fh as li,fi as La,fj as oh,fk as sh,fl as lh,fm as uh,fn as vh,fo as ch,fp as hh,fq as fh,fr as ph,fs as dh,ft as gh,fu as yh,fv as mh,fw as Sh,fx as xh,fy as bh,fz as _h,fA as wh,fB as Dh,aN as Ar,q as Th,r as Ie,g as Ih,e as Lr,o as Ah,ao as Lh,m as ao,n as no,D as k,p as Ch,E as He,N as Ue,k as $e,fC as Ca,P as Ye,fD as Ph,aG as Mh,M as mt,L as io,H as Eh,S as Rh,fE as Cr,_ as Nh}from"./index-ZVLuktk4.js";function kh(a){if(a){for(var e=[],t=0;t<a.length;t++)e.push(a[t].slice());return e}}function Vh(a,e){var t=a.label,r=e&&e.getTextGuideLine();return{dataIndex:a.dataIndex,dataType:a.dataType,seriesIndex:a.seriesModel.seriesIndex,text:a.label.style.text,rect:a.hostRect,labelRect:a.rect,align:t.style.align,verticalAlign:t.style.verticalAlign,labelLinePoints:kh(r&&r.shape.points)}}var oo=["align","verticalAlign","width","height","fontSize"],St=new Re,Pa=_t(),Gh=_t();function Pr(a,e,t){for(var r=0;r<t.length;r++){var n=t[r];e[n]!=null&&(a[n]=e[n])}}var Mr=["x","y","rotation"],zh=function(){function a(){this._labelList=[],this._chartViewList=[]}return a.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},a.prototype._addLabel=function(e,t,r,n,i){var o=n.style,s=n.__hostTarget,l=s.textConfig||{},u=n.getComputedTransform(),v=n.getBoundingRect().plain();ht.applyTransform(v,v,u),u?St.setLocalTransform(u):(St.x=St.y=St.rotation=St.originX=St.originY=0,St.scaleX=St.scaleY=1),St.rotation=Je(St.rotation);var c=n.__hostTarget,h;if(c){h=c.getBoundingRect().plain();var f=c.getComputedTransform();ht.applyTransform(h,h,f)}var p=h&&c.getTextGuideLine();this._labelList.push({label:n,labelLine:p,seriesModel:r,dataIndex:e,dataType:t,layoutOption:i,computedLayoutOption:null,rect:v,hostRect:h,priority:h?h.width*h.height:0,defaultAttr:{ignore:n.ignore,labelGuideIgnore:p&&p.ignore,x:St.x,y:St.y,scaleX:St.scaleX,scaleY:St.scaleY,rotation:St.rotation,style:{x:o.x,y:o.y,align:o.align,verticalAlign:o.verticalAlign,width:o.width,height:o.height,fontSize:o.fontSize},cursor:n.cursor,attachedPos:l.position,attachedRot:l.rotation}})},a.prototype.addLabelsOfSeries=function(e){var t=this;this._chartViewList.push(e);var r=e.__model,n=r.get("labelLayout");(ot(n)||pr(n).length)&&e.group.traverse(function(i){if(i.ignore)return!0;var o=i.getTextContent(),s=nt(i);o&&!o.disableLabelLayout&&t._addLabel(s.dataIndex,s.dataType,r,o,n)})},a.prototype.updateLayoutConfig=function(e){var t=e.getWidth(),r=e.getHeight();function n(y,b){return function(){Fi(y,b)}}for(var i=0;i<this._labelList.length;i++){var o=this._labelList[i],s=o.label,l=s.__hostTarget,u=o.defaultAttr,v=void 0;ot(o.layoutOption)?v=o.layoutOption(Vh(o,l)):v=o.layoutOption,v=v||{},o.computedLayoutOption=v;var c=Math.PI/180;l&&l.setTextConfig({local:!1,position:v.x!=null||v.y!=null?null:u.attachedPos,rotation:v.rotate!=null?v.rotate*c:u.attachedRot,offset:[v.dx||0,v.dy||0]});var h=!1;if(v.x!=null?(s.x=z(v.x,t),s.setStyle("x",0),h=!0):(s.x=u.x,s.setStyle("x",u.style.x)),v.y!=null?(s.y=z(v.y,r),s.setStyle("y",0),h=!0):(s.y=u.y,s.setStyle("y",u.style.y)),v.labelLinePoints){var f=l.getTextGuideLine();f&&(f.setShape({points:v.labelLinePoints}),h=!1)}var p=Pa(s);p.needsUpdateLabelLine=h,s.rotation=v.rotate!=null?v.rotate*c:u.rotation,s.scaleX=u.scaleX,s.scaleY=u.scaleY;for(var d=0;d<oo.length;d++){var g=oo[d];s.setStyle(g,v[g]!=null?v[g]:u.style[g])}if(v.draggable){if(s.draggable=!0,s.cursor="move",l){var m=o.seriesModel;if(o.dataIndex!=null){var S=o.seriesModel.getData(o.dataType);m=S.getItemModel(o.dataIndex)}s.on("drag",n(l,m.getModel("labelLine")))}}else s.off("drag"),s.cursor=u.cursor}},a.prototype.layout=function(e){var t=e.getWidth(),r=e.getHeight(),n=rv(this._labelList),i=Pt(n,function(l){return l.layoutOption.moveOverlap==="shiftX"}),o=Pt(n,function(l){return l.layoutOption.moveOverlap==="shiftY"});av(i,0,t),nv(o,0,r);var s=Pt(n,function(l){return l.layoutOption.hideOverlap});iv(s)},a.prototype.processLabelsOverall=function(){var e=this;C(this._chartViewList,function(t){var r=t.__model,n=t.ignoreLabelLineUpdate,i=r.isAnimationEnabled();t.group.traverse(function(o){if(o.ignore&&!o.forceLabelAnimation)return!0;var s=!n,l=o.getTextContent();!s&&l&&(s=Pa(l).needsUpdateLabelLine),s&&e._updateLabelLine(o,r),i&&e._animateLabels(o,r)})})},a.prototype._updateLabelLine=function(e,t){var r=e.getTextContent(),n=nt(e),i=n.dataIndex;if(r&&i!=null){var o=t.getData(n.dataType),s=o.getItemModel(i),l={},u=o.getItemVisual(i,"style");if(u){var v=o.getVisual("drawType");l.stroke=u[v]}var c=s.getModel("labelLine");pl(e,dl(s),l),Fi(e,c)}},a.prototype._animateLabels=function(e,t){var r=e.getTextContent(),n=e.getTextGuideLine();if(r&&(e.forceLabelAnimation||!r.ignore&&!r.invisible&&!e.disableLabelAnimation&&!ov(e))){var i=Pa(r),o=i.oldLayout,s=nt(e),l=s.dataIndex,u={x:r.x,y:r.y,rotation:r.rotation},v=t.getData(s.dataType);if(o){r.attr(o);var h=e.prevStates;h&&(zt(h,"select")>=0&&r.attr(i.oldLayoutSelect),zt(h,"emphasis")>=0&&r.attr(i.oldLayoutEmphasis)),lt(r,u,t,l)}else if(r.attr(u),!sv(r).valueAnimation){var c=Zt(r.style.opacity,1);r.style.opacity=0,Tt(r,{style:{opacity:c}},t,l)}if(i.oldLayout=u,r.states.select){var f=i.oldLayoutSelect={};Pr(f,u,Mr),Pr(f,r.states.select,Mr)}if(r.states.emphasis){var p=i.oldLayoutEmphasis={};Pr(p,u,Mr),Pr(p,r.states.emphasis,Mr)}gl(r,l,v,t,t)}if(n&&!n.ignore&&!n.invisible){var i=Gh(n),o=i.oldLayout,d={points:n.shape.points};o?(n.attr({shape:o}),lt(n,{shape:d},t)):(n.setShape(d),n.style.strokePercent=0,Tt(n,{style:{strokePercent:1}},t)),i.oldLayout=d}},a}(),Ma=_t();function Oh(a){a.registerUpdateLifecycle("series:beforeupdate",function(e,t,r){var n=Ma(t).labelManager;n||(n=Ma(t).labelManager=new zh),n.clearLabels()}),a.registerUpdateLifecycle("series:layoutlabels",function(e,t,r){var n=Ma(t).labelManager;r.updatedSeries.forEach(function(i){n.addLabelsOfSeries(t.getViewOfSeriesModel(i))}),n.updateLayoutConfig(t),n.layout(t),n.processLabelsOverall()})}var Bh=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t,r){return Be(null,this,{useEncodeDefaulter:!0})},e.prototype.getProgressive=function(){var t=this.option.progressive;return t==null?this.option.large?5e3:this.get("progressive"):t},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return t==null?this.option.large?1e4:this.get("progressiveThreshold"):t},e.prototype.brushSelector=function(t,r,n){return n.point(r.getItemLayout(t))},e.prototype.getZLevelKey=function(){return this.getData().count()>this.getProgressiveThreshold()?this.id:""},e.type="series.scatter",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},emphasis:{scale:!0},clip:!0,select:{itemStyle:{borderColor:"#212121"}},universalTransition:{divideShape:"clone"}},e}(dt),Wl=4,Fh=function(){function a(){}return a}(),Wh=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r._off=0,r.hoverDataIdx=-1,r}return e.prototype.getDefaultShape=function(){return new Fh},e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.buildPath=function(t,r){var n=r.points,i=r.size,o=this.symbolProxy,s=o.shape,l=t.getContext?t.getContext():t,u=l&&i[0]<Wl,v=this.softClipShape,c;if(u){this._ctx=l;return}for(this._ctx=null,c=this._off;c<n.length;){var h=n[c++],f=n[c++];isNaN(h)||isNaN(f)||v&&!v.contain(h,f)||(s.x=h-i[0]/2,s.y=f-i[1]/2,s.width=i[0],s.height=i[1],o.buildPath(t,s,!0))}this.incremental&&(this._off=c,this.notClear=!0)},e.prototype.afterBrush=function(){var t=this.shape,r=t.points,n=t.size,i=this._ctx,o=this.softClipShape,s;if(i){for(s=this._off;s<r.length;){var l=r[s++],u=r[s++];isNaN(l)||isNaN(u)||o&&!o.contain(l,u)||i.fillRect(l-n[0]/2,u-n[1]/2,n[0],n[1])}this.incremental&&(this._off=s,this.notClear=!0)}},e.prototype.findDataIndex=function(t,r){for(var n=this.shape,i=n.points,o=n.size,s=Math.max(o[0],4),l=Math.max(o[1],4),u=i.length/2-1;u>=0;u--){var v=u*2,c=i[v]-s/2,h=i[v+1]-l/2;if(t>=c&&r>=h&&t<=c+s&&r<=h+l)return u}return-1},e.prototype.contain=function(t,r){var n=this.transformCoordToLocal(t,r),i=this.getBoundingRect();if(t=n[0],r=n[1],i.contain(t,r)){var o=this.hoverDataIdx=this.findDataIndex(t,r);return o>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var r=this.shape,n=r.points,i=r.size,o=i[0],s=i[1],l=1/0,u=1/0,v=-1/0,c=-1/0,h=0;h<n.length;){var f=n[h++],p=n[h++];l=Math.min(f,l),v=Math.max(f,v),u=Math.min(p,u),c=Math.max(p,c)}t=this._rect=new ht(l-o/2,u-s/2,v-l+o,c-u+s)}return t},e}(wt),Hh=function(){function a(){this.group=new Y}return a.prototype.updateData=function(e,t){this._clear();var r=this._create();r.setShape({points:e.getLayout("points")}),this._setCommon(r,e,t)},a.prototype.updateLayout=function(e){var t=e.getLayout("points");this.group.eachChild(function(r){if(r.startIndex!=null){var n=(r.endIndex-r.startIndex)*2,i=r.startIndex*4*2;t=new Float32Array(t.buffer,i,n)}r.setShape("points",t),r.reset()})},a.prototype.incrementalPrepareUpdate=function(e){this._clear()},a.prototype.incrementalUpdate=function(e,t,r){var n=this._newAdded[0],i=t.getLayout("points"),o=n&&n.shape.points;if(o&&o.length<2e4){var s=o.length,l=new Float32Array(s+i.length);l.set(o),l.set(i,s),n.endIndex=e.end,n.setShape({points:l})}else{this._newAdded=[];var u=this._create();u.startIndex=e.start,u.endIndex=e.end,u.incremental=!0,u.setShape({points:i}),this._setCommon(u,t,r)}},a.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},a.prototype._create=function(){var e=new Wh({cursor:"default"});return e.ignoreCoarsePointer=!0,this.group.add(e),this._newAdded.push(e),e},a.prototype._setCommon=function(e,t,r){var n=t.hostModel;r=r||{};var i=t.getVisual("symbolSize");e.setShape("size",i instanceof Array?i:[i,i]),e.softClipShape=r.clipShape||null,e.symbolProxy=qt(t.getVisual("symbol"),0,0,0,0),e.setColor=e.symbolProxy.setColor;var o=e.shape.size[0]<Wl;e.useStyle(n.getModel("itemStyle").getItemStyle(o?["color","shadowBlur","shadowColor"]:["color"]));var s=t.getVisual("style"),l=s&&s.fill;l&&e.setColor(l);var u=nt(e);u.seriesIndex=n.seriesIndex,e.on("mousemove",function(v){u.dataIndex=null;var c=e.hoverDataIdx;c>=0&&(u.dataIndex=c+(e.startIndex||0))})},a.prototype.remove=function(){this._clear()},a.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},a}(),Uh=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this._updateSymbolDraw(i,t);o.updateData(i,{clipShape:this._getClipShape(t)}),this._finished=!0},e.prototype.incrementalPrepareRender=function(t,r,n){var i=t.getData(),o=this._updateSymbolDraw(i,t);o.incrementalPrepareUpdate(i),this._finished=!1},e.prototype.incrementalRender=function(t,r,n){this._symbolDraw.incrementalUpdate(t,r.getData(),{clipShape:this._getClipShape(r)}),this._finished=t.end===r.getData().count()},e.prototype.updateTransform=function(t,r,n){var i=t.getData();if(this.group.dirty(),!this._finished||i.count()>1e4)return{update:!0};var o=sa("").reset(t,r,n);o.progress&&o.progress({start:0,end:i.count(),count:i.count()},i),this._symbolDraw.updateLayout(i)},e.prototype.eachRendered=function(t){this._symbolDraw&&this._symbolDraw.eachRendered(t)},e.prototype._getClipShape=function(t){if(t.get("clip",!0)){var r=t.coordinateSystem;return r&&r.getArea&&r.getArea(.1)}},e.prototype._updateSymbolDraw=function(t,r){var n=this._symbolDraw,i=r.pipelineContext,o=i.large;return(!n||o!==this._isLargeDraw)&&(n&&n.remove(),n=this._symbolDraw=o?new Hh:new la,this._isLargeDraw=o,this.group.removeAll()),this.group.add(n.group),n},e.prototype.remove=function(t,r){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},e.prototype.dispose=function(){},e.type="scatter",e}(ft);function $h(a){X(lv),a.registerSeriesModel(Bh),a.registerChartView(Uh),a.registerLayout(sa("scatter"))}function Yh(a){a.eachSeriesByType("radar",function(e){var t=e.getData(),r=[],n=e.coordinateSystem;if(n){var i=n.getIndicatorAxes();C(i,function(o,s){t.each(t.mapDimension(i[s].dim),function(l,u){r[u]=r[u]||[];var v=n.dataToPoint(l,s);r[u][s]=so(v)?v:lo(n)})}),t.each(function(o){var s=uv(r[o],function(l){return so(l)})||lo(n);r[o].push(s.slice()),t.setItemLayout(o,r[o])})}})}function so(a){return!isNaN(a[0])&&!isNaN(a[1])}function lo(a){return[a.cx,a.cy]}function Xh(a){var e=a.polar;if(e){H(e)||(e=[e]);var t=[];C(e,function(r,n){r.indicator?(r.type&&!r.shape&&(r.shape=r.type),a.radar=a.radar||[],H(a.radar)||(a.radar=[a.radar]),a.radar.push(r)):t.push(r)}),a.polar=t}C(a.series,function(r){r&&r.type==="radar"&&r.polarIndex&&(r.radarIndex=r.polarIndex)})}var Zh=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=t.coordinateSystem,o=this.group,s=t.getData(),l=this._data;function u(h,f){var p=h.getItemVisual(f,"symbol")||"circle";if(p!=="none"){var d=dr(h.getItemVisual(f,"symbolSize")),g=qt(p,-1,-1,2,2),m=h.getItemVisual(f,"symbolRotate")||0;return g.attr({style:{strokeNoScale:!0},z2:100,scaleX:d[0]/2,scaleY:d[1]/2,rotation:m*Math.PI/180||0}),g}}function v(h,f,p,d,g,m){p.removeAll();for(var S=0;S<f.length-1;S++){var y=u(d,g);y&&(y.__dimIdx=S,h[S]?(y.setPosition(h[S]),ua[m?"initProps":"updateProps"](y,{x:f[S][0],y:f[S][1]},t,g)):y.setPosition(f[S]),p.add(y))}}function c(h){return B(h,function(f){return[i.cx,i.cy]})}s.diff(l).add(function(h){var f=s.getItemLayout(h);if(f){var p=new ne,d=new ge,g={shape:{points:f}};p.shape.points=c(f),d.shape.points=c(f),Tt(p,g,t,h),Tt(d,g,t,h);var m=new Y,S=new Y;m.add(d),m.add(p),m.add(S),v(d.shape.points,f,S,s,h,!0),s.setItemGraphicEl(h,m)}}).update(function(h,f){var p=l.getItemGraphicEl(f),d=p.childAt(0),g=p.childAt(1),m=p.childAt(2),S={shape:{points:s.getItemLayout(h)}};S.shape.points&&(v(d.shape.points,S.shape.points,m,s,h,!1),fe(g),fe(d),lt(d,S,t),lt(g,S,t),s.setItemGraphicEl(h,p))}).remove(function(h){o.remove(l.getItemGraphicEl(h))}).execute(),s.eachItemGraphicEl(function(h,f){var p=s.getItemModel(f),d=h.childAt(0),g=h.childAt(1),m=h.childAt(2),S=s.getItemVisual(f,"style"),y=S.fill;o.add(h),d.useStyle(it(p.getModel("lineStyle").getLineStyle(),{fill:"none",stroke:y})),Et(d,p,"lineStyle"),Et(g,p,"areaStyle");var b=p.getModel("areaStyle"),_=b.isEmpty()&&b.parentModel.isEmpty();g.ignore=_,C(["emphasis","select","blur"],function(D){var T=p.getModel([D,"areaStyle"]),I=T.isEmpty()&&T.parentModel.isEmpty();g.ensureState(D).ignore=I&&_}),g.useStyle(it(b.getAreaStyle(),{fill:y,opacity:.7,decal:S.decal}));var x=p.getModel("emphasis"),w=x.getModel("itemStyle").getItemStyle();m.eachChild(function(D){if(D instanceof Fe){var T=D.style;D.useStyle(U({image:T.image,x:T.x,y:T.y,width:T.width,height:T.height},S))}else D.useStyle(S),D.setColor(y),D.style.strokeNoScale=!0;var I=D.ensureState("emphasis");I.style=$t(w);var A=s.getStore().get(s.getDimensionIndex(D.__dimIdx),f);(A==null||isNaN(A))&&(A=""),Ot(D,It(p),{labelFetcher:s.hostModel,labelDataIndex:f,labelDimIndex:D.__dimIdx,defaultText:A,inheritColor:y,defaultOpacity:S.opacity})}),ct(h,x.get("focus"),x.get("blurScope"),x.get("disabled"))}),this._data=s},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.type="radar",e}(ft),qh=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new va(et(this.getData,this),et(this.getRawData,this))},e.prototype.getInitialData=function(t,r){return gr(this,{generateCoord:"indicator_",generateCoordCount:1/0})},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=this.coordinateSystem,s=o.getIndicatorAxes(),l=this.getData().getName(t),u=l===""?this.name:l,v=vv(this,t);return At("section",{header:u,sortBlocks:!0,blocks:B(s,function(c){var h=i.get(i.mapDimension(c.dim),t);return At("nameValue",{markerType:"subItem",markerColor:v,name:c.name,value:h,sortParam:h})})})},e.prototype.getTooltipPosition=function(t){if(t!=null){for(var r=this.getData(),n=this.coordinateSystem,i=r.getValues(B(n.dimensions,function(u){return r.mapDimension(u)}),t),o=0,s=i.length;o<s;o++)if(!isNaN(i[o])){var l=n.getIndicatorAxes();return n.coordToPoint(l[o].dataToCoord(i[o]),o)}}},e.type="series.radar",e.dependencies=["radar"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"radar",legendHoverLink:!0,radarIndex:0,lineStyle:{width:2,type:"solid",join:"round"},label:{position:"top"},symbolSize:8},e}(dt),Xe=cv.value;function Er(a,e){return it({show:e},a)}var jh=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){var t=this.get("boundaryGap"),r=this.get("splitNumber"),n=this.get("scale"),i=this.get("axisLine"),o=this.get("axisTick"),s=this.get("axisLabel"),l=this.get("axisName"),u=this.get(["axisName","show"]),v=this.get(["axisName","formatter"]),c=this.get("axisNameGap"),h=this.get("triggerEvent"),f=B(this.get("indicator")||[],function(p){p.max!=null&&p.max>0&&!p.min?p.min=0:p.min!=null&&p.min<0&&!p.max&&(p.max=0);var d=l;p.color!=null&&(d=it({color:p.color},l));var g=Bt($t(p),{boundaryGap:t,splitNumber:r,scale:n,axisLine:i,axisTick:o,axisLabel:s,name:p.text,showName:u,nameLocation:"end",nameGap:c,nameTextStyle:d,triggerEvent:h},!1);if(rt(v)){var m=g.name;g.name=v.replace("{value}",m!=null?m:"")}else ot(v)&&(g.name=v(g.name,g));var S=new Yt(g,null,this.ecModel);return Kt(S,Xn.prototype),S.mainType="radar",S.componentIndex=this.componentIndex,S},this);this._indicatorModels=f},e.prototype.getIndicatorModels=function(){return this._indicatorModels},e.type="radar",e.defaultOption={z:0,center:["50%","50%"],radius:"75%",startAngle:90,axisName:{show:!0},boundaryGap:[0,0],splitNumber:5,axisNameGap:15,scale:!1,shape:"polygon",axisLine:Bt({lineStyle:{color:"#bbb"}},Xe.axisLine),axisLabel:Er(Xe.axisLabel,!1),axisTick:Er(Xe.axisTick,!1),splitLine:Er(Xe.splitLine,!0),splitArea:Er(Xe.splitArea,!0),indicator:[]},e}(Jt),Kh=["axisLine","axisTickLabel","axisName"],Jh=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=this.group;i.removeAll(),this._buildAxes(t),this._buildSplitLineAndArea(t)},e.prototype._buildAxes=function(t){var r=t.coordinateSystem,n=r.getIndicatorAxes(),i=B(n,function(o){var s=o.model.get("showName")?o.name:"",l=new Zn(o.model,{axisName:s,position:[r.cx,r.cy],rotation:o.angle,labelDirection:-1,tickDirection:-1,nameDirection:1});return l});C(i,function(o){C(Kh,o.add,o),this.group.add(o.getGroup())},this)},e.prototype._buildSplitLineAndArea=function(t){var r=t.coordinateSystem,n=r.getIndicatorAxes();if(!n.length)return;var i=t.get("shape"),o=t.getModel("splitLine"),s=t.getModel("splitArea"),l=o.getModel("lineStyle"),u=s.getModel("areaStyle"),v=o.get("show"),c=s.get("show"),h=l.get("color"),f=u.get("color"),p=H(h)?h:[h],d=H(f)?f:[f],g=[],m=[];function S(M,R,N){var G=N%R.length;return M[G]=M[G]||[],G}if(i==="circle")for(var y=n[0].getTicksCoords(),b=r.cx,_=r.cy,x=0;x<y.length;x++){if(v){var w=S(g,p,x);g[w].push(new ca({shape:{cx:b,cy:_,r:y[x].coord}}))}if(c&&x<y.length-1){var w=S(m,d,x);m[w].push(new hv({shape:{cx:b,cy:_,r0:y[x].coord,r:y[x+1].coord}}))}}else for(var D,T=B(n,function(M,R){var N=M.getTicksCoords();return D=D==null?N.length-1:Math.min(N.length-1,D),B(N,function(G){return r.coordToPoint(G.coord,R)})}),I=[],x=0;x<=D;x++){for(var A=[],P=0;P<n.length;P++)A.push(T[P][x]);if(A[0]&&A.push(A[0].slice()),v){var w=S(g,p,x);g[w].push(new ge({shape:{points:A}}))}if(c&&I){var w=S(m,d,x-1);m[w].push(new ne({shape:{points:A.concat(I)}}))}I=A.slice().reverse()}var E=l.getLineStyle(),L=u.getAreaStyle();C(m,function(M,R){this.group.add(xn(M,{style:it({stroke:"none",fill:d[R%d.length]},L),silent:!0}))},this),C(g,function(M,R){this.group.add(xn(M,{style:it({fill:"none",stroke:p[R%p.length]},E),silent:!0}))},this)},e.type="radar",e}(Qt),Qh=function(a){V(e,a);function e(t,r,n){var i=a.call(this,t,r,n)||this;return i.type="value",i.angle=0,i.name="",i}return e}(ha),tf=function(){function a(e,t,r){this.dimensions=[],this._model=e,this._indicatorAxes=B(e.getIndicatorModels(),function(n,i){var o="indicator_"+i,s=new Qh(o,new bn);return s.name=n.get("name"),s.model=n,n.axis=s,this.dimensions.push(o),s},this),this.resize(e,r)}return a.prototype.getIndicatorAxes=function(){return this._indicatorAxes},a.prototype.dataToPoint=function(e,t){var r=this._indicatorAxes[t];return this.coordToPoint(r.dataToCoord(e),t)},a.prototype.coordToPoint=function(e,t){var r=this._indicatorAxes[t],n=r.angle,i=this.cx+e*Math.cos(n),o=this.cy-e*Math.sin(n);return[i,o]},a.prototype.pointToData=function(e){var t=e[0]-this.cx,r=e[1]-this.cy,n=Math.sqrt(t*t+r*r);t/=n,r/=n;for(var i=Math.atan2(-r,t),o=1/0,s,l=-1,u=0;u<this._indicatorAxes.length;u++){var v=this._indicatorAxes[u],c=Math.abs(i-v.angle);c<o&&(s=v,l=u,o=c)}return[l,+(s&&s.coordToData(n))]},a.prototype.resize=function(e,t){var r=e.get("center"),n=t.getWidth(),i=t.getHeight(),o=Math.min(n,i)/2;this.cx=z(r[0],n),this.cy=z(r[1],i),this.startAngle=e.get("startAngle")*Math.PI/180;var s=e.get("radius");(rt(s)||jt(s))&&(s=[0,s]),this.r0=z(s[0],o),this.r=z(s[1],o),C(this._indicatorAxes,function(l,u){l.setExtent(this.r0,this.r);var v=this.startAngle+u*Math.PI*2/this._indicatorAxes.length;v=Math.atan2(Math.sin(v),Math.cos(v)),l.angle=v},this)},a.prototype.update=function(e,t){var r=this._indicatorAxes,n=this._model;C(r,function(s){s.scale.setExtent(1/0,-1/0)}),e.eachSeriesByType("radar",function(s,l){if(!(s.get("coordinateSystem")!=="radar"||e.getComponent("radar",s.get("radarIndex"))!==n)){var u=s.getData();C(r,function(v){v.scale.unionExtentFromData(u,u.mapDimension(v.dim))})}},this);var i=n.get("splitNumber"),o=new bn;o.setExtent(0,i),o.setInterval(1),C(r,function(s,l){fv(s.scale,s.model,o)})},a.prototype.convertToPixel=function(e,t,r){return console.warn("Not implemented."),null},a.prototype.convertFromPixel=function(e,t,r){return console.warn("Not implemented."),null},a.prototype.containPoint=function(e){return console.warn("Not implemented."),!1},a.create=function(e,t){var r=[];return e.eachComponent("radar",function(n){var i=new a(n,e,t);r.push(i),n.coordinateSystem=i}),e.eachSeriesByType("radar",function(n){n.get("coordinateSystem")==="radar"&&(n.coordinateSystem=r[n.get("radarIndex")||0])}),r},a.dimensions=[],a}();function ef(a){a.registerCoordinateSystem("radar",tf),a.registerComponentModel(jh),a.registerComponentView(Jh),a.registerVisual({seriesType:"radar",reset:function(e){var t=e.getData();t.each(function(r){t.setItemVisual(r,"legendIcon","roundRect")}),t.setVisual("legendIcon","roundRect")}})}function rf(a){X(ef),a.registerChartView(Zh),a.registerSeriesModel(qh),a.registerLayout(Yh),a.registerProcessor(fa("radar")),a.registerPreprocessor(Xh)}function ui(a,e,t){var r=a.target;r.x+=e,r.y+=t,r.dirty()}function vi(a,e,t,r){var n=a.target,i=a.zoomLimit,o=a.zoom=a.zoom||1;if(o*=e,i){var s=i.min||0,l=i.max||1/0;o=Math.max(Math.min(l,o),s)}var u=o/a.zoom;a.zoom=o,n.x-=(t-n.x)*(u-1),n.y-=(r-n.y)*(u-1),n.scaleX*=u,n.scaleY*=u,n.dirty()}function Hl(a){if(rt(a)){var e=new DOMParser;a=e.parseFromString(a,"text/xml")}var t=a;for(t.nodeType===9&&(t=t.firstChild);t.nodeName.toLowerCase()!=="svg"||t.nodeType!==1;)t=t.nextSibling;return t}var Ea,Zr={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},uo=pr(Zr),qr={"alignment-baseline":"textBaseline","stop-color":"stopColor"},vo=pr(qr),af=function(){function a(){this._defs={},this._root=null}return a.prototype.parse=function(e,t){t=t||{};var r=Hl(e);this._defsUsePending=[];var n=new Y;this._root=n;var i=[],o=r.getAttribute("viewBox")||"",s=parseFloat(r.getAttribute("width")||t.width),l=parseFloat(r.getAttribute("height")||t.height);isNaN(s)&&(s=null),isNaN(l)&&(l=null),Ct(r,n,null,!0,!1);for(var u=r.firstChild;u;)this._parseNode(u,n,i,null,!1,!1),u=u.nextSibling;sf(this._defs,this._defsUsePending),this._defsUsePending=[];var v,c;if(o){var h=ba(o);h.length>=4&&(v={x:parseFloat(h[0]||0),y:parseFloat(h[1]||0),width:parseFloat(h[2]),height:parseFloat(h[3])})}if(v&&s!=null&&l!=null&&(c=$l(v,{x:0,y:0,width:s,height:l}),!t.ignoreViewBox)){var f=n;n=new Y,n.add(f),f.scaleX=f.scaleY=c.scale,f.x=c.x,f.y=c.y}return!t.ignoreRootClip&&s!=null&&l!=null&&n.setClipPath(new Lt({shape:{x:0,y:0,width:s,height:l}})),{root:n,width:s,height:l,viewBoxRect:v,viewBoxTransform:c,named:i}},a.prototype._parseNode=function(e,t,r,n,i,o){var s=e.nodeName.toLowerCase(),l,u=n;if(s==="defs"&&(i=!0),s==="text"&&(o=!0),s==="defs"||s==="switch")l=t;else{if(!i){var v=Ea[s];if(v&&gt(Ea,s)){l=v.call(this,e,t);var c=e.getAttribute("name");if(c){var h={name:c,namedFrom:null,svgNodeTagLower:s,el:l};r.push(h),s==="g"&&(u=h)}else n&&r.push({name:n.name,namedFrom:n,svgNodeTagLower:s,el:l});t.add(l)}}var f=co[s];if(f&&gt(co,s)){var p=f.call(this,e),d=e.getAttribute("id");d&&(this._defs[d]=p)}}if(l&&l.isGroup)for(var g=e.firstChild;g;)g.nodeType===1?this._parseNode(g,l,r,u,i,o):g.nodeType===3&&o&&this._parseText(g,l),g=g.nextSibling},a.prototype._parseText=function(e,t){var r=new pv({style:{text:e.textContent},silent:!0,x:this._textX||0,y:this._textY||0});Rt(t,r),Ct(e,r,this._defsUsePending,!1,!1),nf(r,t);var n=r.style,i=n.fontSize;i&&i<9&&(n.fontSize=9,r.scaleX*=i/9,r.scaleY*=i/9);var o=(n.fontSize||n.fontFamily)&&[n.fontStyle,n.fontWeight,(n.fontSize||12)+"px",n.fontFamily||"sans-serif"].join(" ");n.font=o;var s=r.getBoundingRect();return this._textX+=s.width,t.add(r),r},a.internalField=function(){Ea={g:function(e,t){var r=new Y;return Rt(t,r),Ct(e,r,this._defsUsePending,!1,!1),r},rect:function(e,t){var r=new Lt;return Rt(t,r),Ct(e,r,this._defsUsePending,!1,!1),r.setShape({x:parseFloat(e.getAttribute("x")||"0"),y:parseFloat(e.getAttribute("y")||"0"),width:parseFloat(e.getAttribute("width")||"0"),height:parseFloat(e.getAttribute("height")||"0")}),r.silent=!0,r},circle:function(e,t){var r=new ca;return Rt(t,r),Ct(e,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),r:parseFloat(e.getAttribute("r")||"0")}),r.silent=!0,r},line:function(e,t){var r=new we;return Rt(t,r),Ct(e,r,this._defsUsePending,!1,!1),r.setShape({x1:parseFloat(e.getAttribute("x1")||"0"),y1:parseFloat(e.getAttribute("y1")||"0"),x2:parseFloat(e.getAttribute("x2")||"0"),y2:parseFloat(e.getAttribute("y2")||"0")}),r.silent=!0,r},ellipse:function(e,t){var r=new gv;return Rt(t,r),Ct(e,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),rx:parseFloat(e.getAttribute("rx")||"0"),ry:parseFloat(e.getAttribute("ry")||"0")}),r.silent=!0,r},polygon:function(e,t){var r=e.getAttribute("points"),n;r&&(n=po(r));var i=new ne({shape:{points:n||[]},silent:!0});return Rt(t,i),Ct(e,i,this._defsUsePending,!1,!1),i},polyline:function(e,t){var r=e.getAttribute("points"),n;r&&(n=po(r));var i=new ge({shape:{points:n||[]},silent:!0});return Rt(t,i),Ct(e,i,this._defsUsePending,!1,!1),i},image:function(e,t){var r=new Fe;return Rt(t,r),Ct(e,r,this._defsUsePending,!1,!1),r.setStyle({image:e.getAttribute("xlink:href")||e.getAttribute("href"),x:+e.getAttribute("x"),y:+e.getAttribute("y"),width:+e.getAttribute("width"),height:+e.getAttribute("height")}),r.silent=!0,r},text:function(e,t){var r=e.getAttribute("x")||"0",n=e.getAttribute("y")||"0",i=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0";this._textX=parseFloat(r)+parseFloat(i),this._textY=parseFloat(n)+parseFloat(o);var s=new Y;return Rt(t,s),Ct(e,s,this._defsUsePending,!1,!0),s},tspan:function(e,t){var r=e.getAttribute("x"),n=e.getAttribute("y");r!=null&&(this._textX=parseFloat(r)),n!=null&&(this._textY=parseFloat(n));var i=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0",s=new Y;return Rt(t,s),Ct(e,s,this._defsUsePending,!1,!0),this._textX+=parseFloat(i),this._textY+=parseFloat(o),s},path:function(e,t){var r=e.getAttribute("d")||"",n=dv(r);return Rt(t,n),Ct(e,n,this._defsUsePending,!1,!1),n.silent=!0,n}}}(),a}(),co={lineargradient:function(a){var e=parseInt(a.getAttribute("x1")||"0",10),t=parseInt(a.getAttribute("y1")||"0",10),r=parseInt(a.getAttribute("x2")||"10",10),n=parseInt(a.getAttribute("y2")||"0",10),i=new yl(e,t,r,n);return ho(a,i),fo(a,i),i},radialgradient:function(a){var e=parseInt(a.getAttribute("cx")||"0",10),t=parseInt(a.getAttribute("cy")||"0",10),r=parseInt(a.getAttribute("r")||"0",10),n=new yv(e,t,r);return ho(a,n),fo(a,n),n}};function ho(a,e){var t=a.getAttribute("gradientUnits");t==="userSpaceOnUse"&&(e.global=!0)}function fo(a,e){for(var t=a.firstChild;t;){if(t.nodeType===1&&t.nodeName.toLocaleLowerCase()==="stop"){var r=t.getAttribute("offset"),n=void 0;r&&r.indexOf("%")>0?n=parseInt(r,10)/100:r?n=parseFloat(r):n=0;var i={};Ul(t,i,i);var o=i.stopColor||t.getAttribute("stop-color")||"#000000";e.colorStops.push({offset:n,color:o})}t=t.nextSibling}}function Rt(a,e){a&&a.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),it(e.__inheritedStyle,a.__inheritedStyle))}function po(a){for(var e=ba(a),t=[],r=0;r<e.length;r+=2){var n=parseFloat(e[r]),i=parseFloat(e[r+1]);t.push([n,i])}return t}function Ct(a,e,t,r,n){var i=e,o=i.__inheritedStyle=i.__inheritedStyle||{},s={};a.nodeType===1&&(vf(a,e),Ul(a,o,s),r||cf(a,o,s)),i.style=i.style||{},o.fill!=null&&(i.style.fill=go(i,"fill",o.fill,t)),o.stroke!=null&&(i.style.stroke=go(i,"stroke",o.stroke,t)),C(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(l){o[l]!=null&&(i.style[l]=parseFloat(o[l]))}),C(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],function(l){o[l]!=null&&(i.style[l]=o[l])}),n&&(i.__selfStyle=s),o.lineDash&&(i.style.lineDash=B(ba(o.lineDash),function(l){return parseFloat(l)})),(o.visibility==="hidden"||o.visibility==="collapse")&&(i.invisible=!0),o.display==="none"&&(i.ignore=!0)}function nf(a,e){var t=e.__selfStyle;if(t){var r=t.textBaseline,n=r;!r||r==="auto"||r==="baseline"?n="alphabetic":r==="before-edge"||r==="text-before-edge"?n="top":r==="after-edge"||r==="text-after-edge"?n="bottom":(r==="central"||r==="mathematical")&&(n="middle"),a.style.textBaseline=n}var i=e.__inheritedStyle;if(i){var o=i.textAlign,s=o;o&&(o==="middle"&&(s="center"),a.style.textAlign=s)}}var of=/^url\(\s*#(.*?)\)/;function go(a,e,t,r){var n=t&&t.match(of);if(n){var i=mv(n[1]);r.push([a,e,i]);return}return t==="none"&&(t=null),t}function sf(a,e){for(var t=0;t<e.length;t++){var r=e[t];r[0].style[r[1]]=a[r[2]]}}var lf=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function ba(a){return a.match(lf)||[]}var uf=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,Ra=Math.PI/180;function vf(a,e){var t=a.getAttribute("transform");if(t){t=t.replace(/,/g," ");var r=[],n=null;t.replace(uf,function(c,h,f){return r.push(h,f),""});for(var i=r.length-1;i>0;i-=2){var o=r[i],s=r[i-1],l=ba(o);switch(n=n||yr(),s){case"translate":Ge(n,n,[parseFloat(l[0]),parseFloat(l[1]||"0")]);break;case"scale":ml(n,n,[parseFloat(l[0]),parseFloat(l[1]||l[0])]);break;case"rotate":qn(n,n,-parseFloat(l[0])*Ra,[parseFloat(l[1]||"0"),parseFloat(l[2]||"0")]);break;case"skewX":var u=Math.tan(parseFloat(l[0])*Ra);Wi(n,[1,0,u,1,0,0],n);break;case"skewY":var v=Math.tan(parseFloat(l[0])*Ra);Wi(n,[1,v,0,1,0,0],n);break;case"matrix":n[0]=parseFloat(l[0]),n[1]=parseFloat(l[1]),n[2]=parseFloat(l[2]),n[3]=parseFloat(l[3]),n[4]=parseFloat(l[4]),n[5]=parseFloat(l[5]);break}}e.setLocalTransform(n)}}var yo=/([^\s:;]+)\s*:\s*([^:;]+)/g;function Ul(a,e,t){var r=a.getAttribute("style");if(r){yo.lastIndex=0;for(var n;(n=yo.exec(r))!=null;){var i=n[1],o=gt(Zr,i)?Zr[i]:null;o&&(e[o]=n[2]);var s=gt(qr,i)?qr[i]:null;s&&(t[s]=n[2])}}}function cf(a,e,t){for(var r=0;r<uo.length;r++){var n=uo[r],i=a.getAttribute(n);i!=null&&(e[Zr[n]]=i)}for(var r=0;r<vo.length;r++){var n=vo[r],i=a.getAttribute(n);i!=null&&(t[qr[n]]=i)}}function $l(a,e){var t=e.width/a.width,r=e.height/a.height,n=Math.min(t,r);return{scale:n,x:-(a.x+a.width/2)*n+(e.x+e.width/2),y:-(a.y+a.height/2)*n+(e.y+e.height/2)}}function hf(a,e){var t=new af;return t.parse(a,e)}var ff=j(["rect","circle","line","ellipse","polygon","polyline","path","text","tspan","g"]),pf=function(){function a(e,t){this.type="geoSVG",this._usedGraphicMap=j(),this._freedGraphics=[],this._mapName=e,this._parsedXML=Hl(t)}return a.prototype.load=function(){var e=this._firstGraphic;if(!e){e=this._firstGraphic=this._buildGraphic(this._parsedXML),this._freedGraphics.push(e),this._boundingRect=this._firstGraphic.boundingRect.clone();var t=gf(e.named),r=t.regions,n=t.regionsMap;this._regions=r,this._regionsMap=n}return{boundingRect:this._boundingRect,regions:this._regions,regionsMap:this._regionsMap}},a.prototype._buildGraphic=function(e){var t,r;try{t=e&&hf(e,{ignoreViewBox:!0,ignoreRootClip:!0})||{},r=t.root,Sl(r!=null)}catch(g){throw new Error(`Invalid svg format
`+g.message)}var n=new Y;n.add(r),n.isGeoSVGGraphicRoot=!0;var i=t.width,o=t.height,s=t.viewBoxRect,l=this._boundingRect;if(!l){var u=void 0,v=void 0,c=void 0,h=void 0;if(i!=null?(u=0,c=i):s&&(u=s.x,c=s.width),o!=null?(v=0,h=o):s&&(v=s.y,h=s.height),u==null||v==null){var f=r.getBoundingRect();u==null&&(u=f.x,c=f.width),v==null&&(v=f.y,h=f.height)}l=this._boundingRect=new ht(u,v,c,h)}if(s){var p=$l(s,l);r.scaleX=r.scaleY=p.scale,r.x=p.x,r.y=p.y}n.setClipPath(new Lt({shape:l.plain()}));var d=[];return C(t.named,function(g){ff.get(g.svgNodeTagLower)!=null&&(d.push(g),df(g.el))}),{root:n,boundingRect:l,named:d}},a.prototype.useGraphic=function(e){var t=this._usedGraphicMap,r=t.get(e);return r||(r=this._freedGraphics.pop()||this._buildGraphic(this._parsedXML),t.set(e,r),r)},a.prototype.freeGraphic=function(e){var t=this._usedGraphicMap,r=t.get(e);r&&(t.removeKey(e),this._freedGraphics.push(r))},a}();function df(a){a.silent=!1,a.isGroup&&a.traverse(function(e){e.silent=!1})}function gf(a){var e=[],t=j();return C(a,function(r){if(r.namedFrom==null){var n=new Sv(r.name,r.el);e.push(n),t.set(r.name,n)}}),{regions:e,regionsMap:t}}var Pn=[126,25],mo="南海诸岛",me=[[[0,3.5],[7,11.2],[15,11.9],[30,7],[42,.7],[52,.7],[56,7.7],[59,.7],[64,.7],[64,0],[5,0],[0,3.5]],[[13,16.1],[19,14.7],[16,21.7],[11,23.1],[13,16.1]],[[12,32.2],[14,38.5],[15,38.5],[13,32.2],[12,32.2]],[[16,47.6],[12,53.2],[13,53.2],[18,47.6],[16,47.6]],[[6,64.4],[8,70],[9,70],[8,64.4],[6,64.4]],[[23,82.6],[29,79.8],[30,79.8],[25,82.6],[23,82.6]],[[37,70.7],[43,62.3],[44,62.3],[39,70.7],[37,70.7]],[[48,51.1],[51,45.5],[53,45.5],[50,51.1],[48,51.1]],[[51,35],[51,28.7],[53,28.7],[53,35],[51,35]],[[52,22.4],[55,17.5],[56,17.5],[53,22.4],[52,22.4]],[[58,12.6],[62,7],[63,7],[60,12.6],[58,12.6]],[[0,3.5],[0,93.1],[64,93.1],[64,0],[63,0],[63,92.4],[1,92.4],[1,3.5],[0,3.5]]];for(var ye=0;ye<me.length;ye++)for(var Ae=0;Ae<me[ye].length;Ae++)me[ye][Ae][0]/=10.5,me[ye][Ae][1]/=-10.5/.75,me[ye][Ae][0]+=Pn[0],me[ye][Ae][1]+=Pn[1];function yf(a,e){if(a==="china"){for(var t=0;t<e.length;t++)if(e[t].name===mo)return;e.push(new xv(mo,B(me,function(r){return{type:"polygon",exterior:r}}),Pn))}}var mf={南海诸岛:[32,80],广东:[0,-10],香港:[10,5],澳门:[-10,10],天津:[5,5]};function Sf(a,e){if(a==="china"){var t=mf[e.name];if(t){var r=e.getCenter();r[0]+=t[0]/10.5,r[1]+=-t[1]/(10.5/.75),e.setCenter(r)}}}var xf=[[[123.45165252685547,25.73527164402261],[123.49731445312499,25.73527164402261],[123.49731445312499,25.750734064600884],[123.45165252685547,25.750734064600884],[123.45165252685547,25.73527164402261]]];function bf(a,e){a==="china"&&e.name==="台湾"&&e.geometries.push({type:"polygon",exterior:xf[0]})}var _f="name",wf=function(){function a(e,t,r){this.type="geoJSON",this._parsedMap=j(),this._mapName=e,this._specialAreas=r,this._geoJSON=Tf(t)}return a.prototype.load=function(e,t){t=t||_f;var r=this._parsedMap.get(t);if(!r){var n=this._parseToRegions(t);r=this._parsedMap.set(t,{regions:n,boundingRect:Df(n)})}var i=j(),o=[];return C(r.regions,function(s){var l=s.name;e&&gt(e,l)&&(s=s.cloneShallow(l=e[l])),o.push(s),i.set(l,s)}),{regions:o,boundingRect:r.boundingRect||new ht(0,0,0,0),regionsMap:i}},a.prototype._parseToRegions=function(e){var t=this._mapName,r=this._geoJSON,n;try{n=r?bv(r,e):[]}catch(i){throw new Error(`Invalid geoJson format
`+i.message)}return yf(t,n),C(n,function(i){var o=i.name;Sf(t,i),bf(t,i);var s=this._specialAreas&&this._specialAreas[o];s&&i.transformTo(s.left,s.top,s.width,s.height)},this),n},a.prototype.getMapForUser=function(){return{geoJson:this._geoJSON,geoJSON:this._geoJSON,specialAreas:this._specialAreas}},a}();function Df(a){for(var e,t=0;t<a.length;t++){var r=a[t].getBoundingRect();e=e||r.clone(),e.union(r)}return e}function Tf(a){return rt(a)?typeof JSON!="undefined"&&JSON.parse?JSON.parse(a):new Function("return ("+a+");")():a}var Ze=j();const ie={registerMap:function(a,e,t){if(e.svg){var r=new pf(a,e.svg);Ze.set(a,r)}else{var n=e.geoJson||e.geoJSON;n&&!e.features?t=e.specialAreas:n=e;var r=new wf(a,n,t);Ze.set(a,r)}},getGeoResource:function(a){return Ze.get(a)},getMapForUser:function(a){var e=Ze.get(a);return e&&e.type==="geoJSON"&&e.getMapForUser()},load:function(a,e,t){var r=Ze.get(a);if(r)return r.load(e,t)}};var ci=["rect","circle","line","ellipse","polygon","polyline","path"],If=j(ci),Af=j(ci.concat(["g"])),Lf=j(ci.concat(["g"])),Yl=_t();function Rr(a){var e=a.getItemStyle(),t=a.get("areaColor");return t!=null&&(e.fill=t),e}function So(a){var e=a.style;e&&(e.stroke=e.stroke||e.fill,e.fill=null)}var Xl=function(){function a(e){var t=new Y;this.uid=_v("ec_map_draw"),this._controller=new pa(e.getZr()),this._controllerHost={target:t},this.group=t,t.add(this._regionsGroup=new Y),t.add(this._svgGroup=new Y)}return a.prototype.draw=function(e,t,r,n,i){var o=e.mainType==="geo",s=e.getData&&e.getData();o&&t.eachComponent({mainType:"series",subType:"map"},function(m){!s&&m.getHostGeoModel()===e&&(s=m.getData())});var l=e.coordinateSystem,u=this._regionsGroup,v=this.group,c=l.getTransformInfo(),h=c.raw,f=c.roam,p=!u.childAt(0)||i;p?(v.x=f.x,v.y=f.y,v.scaleX=f.scaleX,v.scaleY=f.scaleY,v.dirty()):lt(v,f,e);var d=s&&s.getVisual("visualMeta")&&s.getVisual("visualMeta").length>0,g={api:r,geo:l,mapOrGeoModel:e,data:s,isVisualEncodedByVisualMap:d,isGeo:o,transformInfoRaw:h};l.resourceType==="geoJSON"?this._buildGeoJSON(g):l.resourceType==="geoSVG"&&this._buildSVG(g),this._updateController(e,t,r),this._updateMapSelectHandler(e,u,r,n)},a.prototype._buildGeoJSON=function(e){var t=this._regionsGroupByName=j(),r=j(),n=this._regionsGroup,i=e.transformInfoRaw,o=e.mapOrGeoModel,s=e.data,l=e.geo.projection,u=l&&l.stream;function v(f,p){return p&&(f=p(f)),f&&[f[0]*i.scaleX+i.x,f[1]*i.scaleY+i.y]}function c(f){for(var p=[],d=!u&&l&&l.project,g=0;g<f.length;++g){var m=v(f[g],d);m&&p.push(m)}return p}function h(f){return{shape:{points:c(f)}}}n.removeAll(),C(e.geo.regions,function(f){var p=f.name,d=t.get(p),g=r.get(p)||{},m=g.dataIdx,S=g.regionModel;if(!d){d=t.set(p,new Y),n.add(d),m=s?s.indexOfName(p):null,S=e.isGeo?o.getRegionModel(p):s?s.getItemModel(m):null;var y=S.get("silent",!0);y!=null&&(d.silent=y),r.set(p,{dataIdx:m,regionModel:S})}var b=[],_=[];C(f.geometries,function(D){if(D.type==="polygon"){var T=[D.exterior].concat(D.interiors||[]);u&&(T=To(T,u)),C(T,function(A){b.push(new ne(h(A)))})}else{var I=D.points;u&&(I=To(I,u,!0)),C(I,function(A){_.push(new ge(h(A)))})}});var x=v(f.getCenter(),l&&l.project);function w(D,T){if(D.length){var I=new wv({culling:!0,segmentIgnoreThreshold:1,shape:{paths:D}});d.add(I),xo(e,I,m,S),bo(e,I,p,S,o,m,x),T&&(So(I),C(I.states,So))}}w(b),w(_,!0)}),t.each(function(f,p){var d=r.get(p),g=d.dataIdx,m=d.regionModel;_o(e,f,p,m,o,g),wo(e,f,p,m,o),Do(e,f,p,m,o)},this)},a.prototype._buildSVG=function(e){var t=e.geo.map,r=e.transformInfoRaw;this._svgGroup.x=r.x,this._svgGroup.y=r.y,this._svgGroup.scaleX=r.scaleX,this._svgGroup.scaleY=r.scaleY,this._svgResourceChanged(t)&&(this._freeSVG(),this._useSVG(t));var n=this._svgDispatcherMap=j(),i=!1;C(this._svgGraphicRecord.named,function(o){var s=o.name,l=e.mapOrGeoModel,u=e.data,v=o.svgNodeTagLower,c=o.el,h=u?u.indexOfName(s):null,f=l.getRegionModel(s);If.get(v)!=null&&c instanceof ir&&xo(e,c,h,f),c instanceof ir&&(c.culling=!0);var p=f.get("silent",!0);if(p!=null&&(c.silent=p),c.z2EmphasisLift=0,!o.namedFrom&&(Lf.get(v)!=null&&bo(e,c,s,f,l,h,null),_o(e,c,s,f,l,h),wo(e,c,s,f,l),Af.get(v)!=null)){var d=Do(e,c,s,f,l);d==="self"&&(i=!0);var g=n.get(s)||n.set(s,[]);g.push(c)}},this),this._enableBlurEntireSVG(i,e)},a.prototype._enableBlurEntireSVG=function(e,t){if(e&&t.isGeo){var r=t.mapOrGeoModel.getModel(["blur","itemStyle"]).getItemStyle(),n=r.opacity;this._svgGraphicRecord.root.traverse(function(i){if(!i.isGroup){ze(i);var o=i.ensureState("blur").style||{};o.opacity==null&&n!=null&&(o.opacity=n),i.ensureState("emphasis")}})}},a.prototype.remove=function(){this._regionsGroup.removeAll(),this._regionsGroupByName=null,this._svgGroup.removeAll(),this._freeSVG(),this._controller.dispose(),this._controllerHost=null},a.prototype.findHighDownDispatchers=function(e,t){if(e==null)return[];var r=t.coordinateSystem;if(r.resourceType==="geoJSON"){var n=this._regionsGroupByName;if(n){var i=n.get(e);return i?[i]:[]}}else if(r.resourceType==="geoSVG")return this._svgDispatcherMap&&this._svgDispatcherMap.get(e)||[]},a.prototype._svgResourceChanged=function(e){return this._svgMapName!==e},a.prototype._useSVG=function(e){var t=ie.getGeoResource(e);if(t&&t.type==="geoSVG"){var r=t.useGraphic(this.uid);this._svgGroup.add(r.root),this._svgGraphicRecord=r,this._svgMapName=e}},a.prototype._freeSVG=function(){var e=this._svgMapName;if(e!=null){var t=ie.getGeoResource(e);t&&t.type==="geoSVG"&&t.freeGraphic(this.uid),this._svgGraphicRecord=null,this._svgDispatcherMap=null,this._svgGroup.removeAll(),this._svgMapName=null}},a.prototype._updateController=function(e,t,r){var n=e.coordinateSystem,i=this._controller,o=this._controllerHost;o.zoomLimit=e.get("scaleLimit"),o.zoom=n.getZoom(),i.enable(e.get("roam")||!1);var s=e.mainType;function l(){var u={type:"geoRoam",componentType:s};return u[s+"Id"]=e.id,u}i.off("pan").on("pan",function(u){this._mouseDownFlag=!1,ui(o,u.dx,u.dy),r.dispatchAction(U(l(),{dx:u.dx,dy:u.dy,animation:{duration:0}}))},this),i.off("zoom").on("zoom",function(u){this._mouseDownFlag=!1,vi(o,u.scale,u.originX,u.originY),r.dispatchAction(U(l(),{totalZoom:o.zoom,zoom:u.scale,originX:u.originX,originY:u.originY,animation:{duration:0}}))},this),i.setPointerChecker(function(u,v,c){return n.containPoint([v,c])&&!jn(u,r,e)})},a.prototype.resetForLabelLayout=function(){this.group.traverse(function(e){var t=e.getTextContent();t&&(t.ignore=Yl(t).ignore)})},a.prototype._updateMapSelectHandler=function(e,t,r,n){var i=this;t.off("mousedown"),t.off("click"),e.get("selectedMode")&&(t.on("mousedown",function(){i._mouseDownFlag=!0}),t.on("click",function(o){i._mouseDownFlag&&(i._mouseDownFlag=!1)}))},a}();function xo(a,e,t,r){var n=r.getModel("itemStyle"),i=r.getModel(["emphasis","itemStyle"]),o=r.getModel(["blur","itemStyle"]),s=r.getModel(["select","itemStyle"]),l=Rr(n),u=Rr(i),v=Rr(s),c=Rr(o),h=a.data;if(h){var f=h.getItemVisual(t,"style"),p=h.getItemVisual(t,"decal");a.isVisualEncodedByVisualMap&&f.fill&&(l.fill=f.fill),p&&(l.decal=Kn(p,a.api))}e.setStyle(l),e.style.strokeNoScale=!0,e.ensureState("emphasis").style=u,e.ensureState("select").style=v,e.ensureState("blur").style=c,ze(e)}function bo(a,e,t,r,n,i,o){var s=a.data,l=a.isGeo,u=s&&isNaN(s.get(s.mapDimension("value"),i)),v=s&&s.getItemLayout(i);if(l||u||v&&v.showLabel){var c=l?t:i,h=void 0;(!s||i>=0)&&(h=n);var f=o?{normal:{align:"center",verticalAlign:"middle"}}:null;Ot(e,It(r),{labelFetcher:h,labelDataIndex:c,defaultText:t},f);var p=e.getTextContent();if(p&&(Yl(p).ignore=p.ignore,e.textConfig&&o)){var d=e.getBoundingRect().clone();e.textConfig.layoutRect=d,e.textConfig.position=[(o[0]-d.x)/d.width*100+"%",(o[1]-d.y)/d.height*100+"%"]}e.disableLabelAnimation=!0}else e.removeTextContent(),e.removeTextConfig(),e.disableLabelAnimation=null}function _o(a,e,t,r,n,i){a.data?a.data.setItemGraphicEl(i,e):nt(e).eventData={componentType:"geo",componentIndex:n.componentIndex,geoIndex:n.componentIndex,name:t,region:r&&r.option||{}}}function wo(a,e,t,r,n){a.data||Dv({el:e,componentModel:n,itemName:t,itemTooltipOption:r.get("tooltip")})}function Do(a,e,t,r,n){e.highDownSilentOnTouch=!!n.get("selectedMode");var i=r.getModel("emphasis"),o=i.get("focus");return ct(e,o,i.get("blurScope"),i.get("disabled")),a.isGeo&&Tv(e,n,t),o}function To(a,e,t){var r=[],n;function i(){n=[]}function o(){n.length&&(r.push(n),n=[])}var s=e({polygonStart:i,polygonEnd:o,lineStart:i,lineEnd:o,point:function(l,u){isFinite(l)&&isFinite(u)&&n.push([l,u])},sphere:function(){}});return!t&&s.polygonStart(),C(a,function(l){s.lineStart();for(var u=0;u<l.length;u++)s.point(l[u][0],l[u][1]);s.lineEnd()}),!t&&s.polygonEnd(),r}var Cf=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n,i){if(!(i&&i.type==="mapToggleSelect"&&i.from===this.uid)){var o=this.group;if(o.removeAll(),!t.getHostGeoModel()){if(this._mapDraw&&i&&i.type==="geoRoam"&&this._mapDraw.resetForLabelLayout(),i&&i.type==="geoRoam"&&i.componentType==="series"&&i.seriesId===t.id){var s=this._mapDraw;s&&o.add(s.group)}else if(t.needsDrawMap){var s=this._mapDraw||new Xl(n);o.add(s.group),s.draw(t,r,n,this,i),this._mapDraw=s}else this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;t.get("showLegendSymbol")&&r.getComponent("legend")&&this._renderSymbols(t,r,n)}}},e.prototype.remove=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null,this.group.removeAll()},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null},e.prototype._renderSymbols=function(t,r,n){var i=t.originalData,o=this.group;i.each(i.mapDimension("value"),function(s,l){if(!isNaN(s)){var u=i.getItemLayout(l);if(!(!u||!u.point)){var v=u.point,c=u.offset,h=new ca({style:{fill:t.getData().getVisual("style").fill},shape:{cx:v[0]+c*9,cy:v[1],r:3},silent:!0,z2:8+(c?0:mr+1)});if(!c){var f=t.mainSeries.getData(),p=i.getName(l),d=f.indexOfName(p),g=i.getItemModel(l),m=g.getModel("label"),S=f.getItemGraphicEl(d);Ot(h,It(g),{labelFetcher:{getFormattedLabel:function(y,b){return t.getFormattedLabel(d,b)}},defaultText:p}),h.disableLabelAnimation=!0,m.get("position")||h.setTextConfig({position:"bottom"}),S.onHoverStateChange=function(y){xl(h,y)}}o.add(h)}}})},e.type="map",e}(ft),Pf=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.needsDrawMap=!1,t.seriesGroup=[],t.getTooltipPosition=function(r){if(r!=null){var n=this.getData().getName(r),i=this.coordinateSystem,o=i.getRegion(n);return o&&i.dataToPoint(o.getCenter())}},t}return e.prototype.getInitialData=function(t){for(var r=gr(this,{coordDimensions:["value"],encodeDefaulter:st(bl,this)}),n=j(),i=[],o=0,s=r.count();o<s;o++){var l=r.getName(o);n.set(l,o)}var u=ie.load(this.getMapType(),this.option.nameMap,this.option.nameProperty);return C(u.regions,function(v){var c=v.name,h=n.get(c),f=v.properties&&v.properties.echartsStyle,p;h==null?(p={name:c},i.push(p)):p=r.getRawDataItem(h),f&&Bt(p,f)}),r.appendData(i),r},e.prototype.getHostGeoModel=function(){var t=this.option.geoIndex;return t!=null?this.ecModel.getComponent("geo",t):null},e.prototype.getMapType=function(){return(this.getHostGeoModel()||this).option.map},e.prototype.getRawValue=function(t){var r=this.getData();return r.get(r.mapDimension("value"),t)},e.prototype.getRegionModel=function(t){var r=this.getData();return r.getItemModel(r.indexOfName(t))},e.prototype.formatTooltip=function(t,r,n){for(var i=this.getData(),o=this.getRawValue(t),s=i.getName(t),l=this.seriesGroup,u=[],v=0;v<l.length;v++){var c=l[v].originalData.indexOfName(s),h=i.mapDimension("value");isNaN(l[v].originalData.get(h,c))||u.push(l[v].name)}return At("section",{header:u.join(", "),noHeader:!u.length,blocks:[At("nameValue",{name:s,value:o})]})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.getLegendIcon=function(t){var r=t.icon||"roundRect",n=qt(r,0,0,t.itemWidth,t.itemHeight,t.itemStyle.fill);return n.setStyle(t.itemStyle),n.style.stroke="none",r.indexOf("empty")>-1&&(n.style.stroke=n.style.fill,n.style.fill="#fff",n.style.lineWidth=2),n},e.type="series.map",e.dependencies=["geo"],e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"geo",map:"",left:"center",top:"center",aspectScale:null,showLegendSymbol:!0,boundingCoords:null,center:null,zoom:1,scaleLimit:null,selectedMode:!0,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444",areaColor:"#eee"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{areaColor:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},nameProperty:"name"},e}(dt);function Mf(a,e){var t={};return C(a,function(r){r.each(r.mapDimension("value"),function(n,i){var o="ec-"+r.getName(i);t[o]=t[o]||[],isNaN(n)||t[o].push(n)})}),a[0].map(a[0].mapDimension("value"),function(r,n){for(var i="ec-"+a[0].getName(n),o=0,s=1/0,l=-1/0,u=t[i].length,v=0;v<u;v++)s=Math.min(s,t[i][v]),l=Math.max(l,t[i][v]),o+=t[i][v];var c;return e==="min"?c=s:e==="max"?c=l:e==="average"?c=o/u:c=o,u===0?NaN:c})}function Ef(a){var e={};a.eachSeriesByType("map",function(t){var r=t.getHostGeoModel(),n=r?"o"+r.id:"i"+t.getMapType();(e[n]=e[n]||[]).push(t)}),C(e,function(t,r){for(var n=Mf(B(t,function(o){return o.getData()}),t[0].get("mapValueCalculation")),i=0;i<t.length;i++)t[i].originalData=t[i].getData();for(var i=0;i<t.length;i++)t[i].seriesGroup=t,t[i].needsDrawMap=i===0&&!t[i].getHostGeoModel(),t[i].setData(n.cloneShallow()),t[i].mainSeries=t[0]})}function Rf(a){var e={};a.eachSeriesByType("map",function(t){var r=t.getMapType();if(!(t.getHostGeoModel()||e[r])){var n={};C(t.seriesGroup,function(o){var s=o.coordinateSystem,l=o.originalData;o.get("showLegendSymbol")&&a.getComponent("legend")&&l.each(l.mapDimension("value"),function(u,v){var c=l.getName(v),h=s.getRegion(c);if(!(!h||isNaN(u))){var f=n[c]||0,p=s.dataToPoint(h.getCenter());n[c]=f+1,l.setItemLayout(v,{point:p,offset:f})}})});var i=t.getData();i.each(function(o){var s=i.getName(o),l=i.getItemLayout(o)||{};l.showLabel=!n[s],i.setItemLayout(o,l)}),e[r]=!0}})}var Io=_n,Dr=function(a){V(e,a);function e(t){var r=a.call(this)||this;return r.type="view",r.dimensions=["x","y"],r._roamTransformable=new Re,r._rawTransformable=new Re,r.name=t,r}return e.prototype.setBoundingRect=function(t,r,n,i){return this._rect=new ht(t,r,n,i),this._rect},e.prototype.getBoundingRect=function(){return this._rect},e.prototype.setViewRect=function(t,r,n,i){this._transformTo(t,r,n,i),this._viewRect=new ht(t,r,n,i)},e.prototype._transformTo=function(t,r,n,i){var o=this.getBoundingRect(),s=this._rawTransformable;s.transform=o.calculateTransform(new ht(t,r,n,i));var l=s.parent;s.parent=null,s.decomposeTransform(),s.parent=l,this._updateTransform()},e.prototype.setCenter=function(t,r){t&&(this._center=[z(t[0],r.getWidth()),z(t[1],r.getHeight())],this._updateCenterAndZoom())},e.prototype.setZoom=function(t){t=t||1;var r=this.zoomLimit;r&&(r.max!=null&&(t=Math.min(r.max,t)),r.min!=null&&(t=Math.max(r.min,t))),this._zoom=t,this._updateCenterAndZoom()},e.prototype.getDefaultCenter=function(){var t=this.getBoundingRect(),r=t.x+t.width/2,n=t.y+t.height/2;return[r,n]},e.prototype.getCenter=function(){return this._center||this.getDefaultCenter()},e.prototype.getZoom=function(){return this._zoom||1},e.prototype.getRoamTransform=function(){return this._roamTransformable.getLocalTransform()},e.prototype._updateCenterAndZoom=function(){var t=this._rawTransformable.getLocalTransform(),r=this._roamTransformable,n=this.getDefaultCenter(),i=this.getCenter(),o=this.getZoom();i=_n([],i,t),n=_n([],n,t),r.originX=i[0],r.originY=i[1],r.x=n[0]-i[0],r.y=n[1]-i[1],r.scaleX=r.scaleY=o,this._updateTransform()},e.prototype._updateTransform=function(){var t=this._roamTransformable,r=this._rawTransformable;r.parent=t,t.updateTransform(),r.updateTransform(),Iv(this.transform||(this.transform=[]),r.transform||yr()),this._rawTransform=r.getLocalTransform(),this.invTransform=this.invTransform||[],Av(this.invTransform,this.transform),this.decomposeTransform()},e.prototype.getTransformInfo=function(){var t=this._rawTransformable,r=this._roamTransformable,n=new Re;return n.transform=r.transform,n.decomposeTransform(),{roam:{x:n.x,y:n.y,scaleX:n.scaleX,scaleY:n.scaleY},raw:{x:t.x,y:t.y,scaleX:t.scaleX,scaleY:t.scaleY}}},e.prototype.getViewRect=function(){return this._viewRect},e.prototype.getViewRectAfterRoam=function(){var t=this.getBoundingRect().clone();return t.applyTransform(this.transform),t},e.prototype.dataToPoint=function(t,r,n){var i=r?this._rawTransform:this.transform;return n=n||[],i?Io(n,t,i):xt(n,t)},e.prototype.pointToData=function(t){var r=this.invTransform;return r?Io([],t,r):[t[0],t[1]]},e.prototype.convertToPixel=function(t,r,n){var i=Ao(r);return i===this?i.dataToPoint(n):null},e.prototype.convertFromPixel=function(t,r,n){var i=Ao(r);return i===this?i.pointToData(n):null},e.prototype.containPoint=function(t){return this.getViewRectAfterRoam().contain(t[0],t[1])},e.dimensions=["x","y"],e}(Re);function Ao(a){var e=a.seriesModel;return e?e.coordinateSystem:null}var Nf={geoJSON:{aspectScale:.75,invertLongitute:!0},geoSVG:{aspectScale:1,invertLongitute:!1}},Zl=["lng","lat"],Mn=function(a){V(e,a);function e(t,r,n){var i=a.call(this,t)||this;i.dimensions=Zl,i.type="geo",i._nameCoordMap=j(),i.map=r;var o=n.projection,s=ie.load(r,n.nameMap,n.nameProperty),l=ie.getGeoResource(r);i.resourceType=l?l.type:null;var u=i.regions=s.regions,v=Nf[l.type];i._regionsMap=s.regionsMap,i.regions=s.regions,i.projection=o;var c;if(o)for(var h=0;h<u.length;h++){var f=u[h].getBoundingRect(o);c=c||f.clone(),c.union(f)}else c=s.boundingRect;return i.setBoundingRect(c.x,c.y,c.width,c.height),i.aspectScale=o?1:Zt(n.aspectScale,v.aspectScale),i._invertLongitute=o?!1:v.invertLongitute,i}return e.prototype._transformTo=function(t,r,n,i){var o=this.getBoundingRect(),s=this._invertLongitute;o=o.clone(),s&&(o.y=-o.y-o.height);var l=this._rawTransformable;l.transform=o.calculateTransform(new ht(t,r,n,i));var u=l.parent;l.parent=null,l.decomposeTransform(),l.parent=u,s&&(l.scaleY=-l.scaleY),this._updateTransform()},e.prototype.getRegion=function(t){return this._regionsMap.get(t)},e.prototype.getRegionByCoord=function(t){for(var r=this.regions,n=0;n<r.length;n++){var i=r[n];if(i.type==="geoJSON"&&i.contain(t))return r[n]}},e.prototype.addGeoCoord=function(t,r){this._nameCoordMap.set(t,r)},e.prototype.getGeoCoord=function(t){var r=this._regionsMap.get(t);return this._nameCoordMap.get(t)||r&&r.getCenter()},e.prototype.dataToPoint=function(t,r,n){if(rt(t)&&(t=this.getGeoCoord(t)),t){var i=this.projection;return i&&(t=i.project(t)),t&&this.projectedToPoint(t,r,n)}},e.prototype.pointToData=function(t){var r=this.projection;return r&&(t=r.unproject(t)),t&&this.pointToProjected(t)},e.prototype.pointToProjected=function(t){return a.prototype.pointToData.call(this,t)},e.prototype.projectedToPoint=function(t,r,n){return a.prototype.dataToPoint.call(this,t,r,n)},e.prototype.convertToPixel=function(t,r,n){var i=Lo(r);return i===this?i.dataToPoint(n):null},e.prototype.convertFromPixel=function(t,r,n){var i=Lo(r);return i===this?i.pointToData(n):null},e}(Dr);Kt(Mn,Dr);function Lo(a){var e=a.geoModel,t=a.seriesModel;return e?e.coordinateSystem:t?t.coordinateSystem||(t.getReferringComponents("geo",da).models[0]||{}).coordinateSystem:null}function Co(a,e){var t=a.get("boundingCoords");if(t!=null){var r=t[0],n=t[1];if(isFinite(r[0])&&isFinite(r[1])&&isFinite(n[0])&&isFinite(n[1])){var i=this.projection;if(i){var o=r[0],s=r[1],l=n[0],u=n[1];r=[1/0,1/0],n=[-1/0,-1/0];var v=function(x,w,D,T){for(var I=D-x,A=T-w,P=0;P<=100;P++){var E=P/100,L=i.project([x+I*E,w+A*E]);Lv(r,r,L),Cv(n,n,L)}};v(o,s,l,s),v(l,s,l,u),v(l,u,o,u),v(o,u,l,s)}this.setBoundingRect(r[0],r[1],n[0]-r[0],n[1]-r[1])}}var c=this.getBoundingRect(),h=a.get("layoutCenter"),f=a.get("layoutSize"),p=e.getWidth(),d=e.getHeight(),g=c.width/c.height*this.aspectScale,m=!1,S,y;h&&f&&(S=[z(h[0],p),z(h[1],d)],y=z(f,Math.min(p,d)),!isNaN(S[0])&&!isNaN(S[1])&&!isNaN(y)&&(m=!0));var b;if(m)b={},g>1?(b.width=y,b.height=y/g):(b.height=y,b.width=y*g),b.y=S[1]-b.height/2,b.x=S[0]-b.width/2;else{var _=a.getBoxLayoutParams();_.aspect=g,b=te(_,{width:p,height:d})}this.setViewRect(b.x,b.y,b.width,b.height),this.setCenter(a.get("center"),e),this.setZoom(a.get("zoom"))}function kf(a,e){C(e.get("geoCoord"),function(t,r){a.addGeoCoord(r,t)})}var Vf=function(){function a(){this.dimensions=Zl}return a.prototype.create=function(e,t){var r=[];function n(o){return{nameProperty:o.get("nameProperty"),aspectScale:o.get("aspectScale"),projection:o.get("projection")}}e.eachComponent("geo",function(o,s){var l=o.get("map"),u=new Mn(l+s,l,U({nameMap:o.get("nameMap")},n(o)));u.zoomLimit=o.get("scaleLimit"),r.push(u),o.coordinateSystem=u,u.model=o,u.resize=Co,u.resize(o,t)}),e.eachSeries(function(o){var s=o.get("coordinateSystem");if(s==="geo"){var l=o.get("geoIndex")||0;o.coordinateSystem=r[l]}});var i={};return e.eachSeriesByType("map",function(o){if(!o.getHostGeoModel()){var s=o.getMapType();i[s]=i[s]||[],i[s].push(o)}}),C(i,function(o,s){var l=B(o,function(v){return v.get("nameMap")}),u=new Mn(s,s,U({nameMap:Jn(l)},n(o[0])));u.zoomLimit=kt.apply(null,B(o,function(v){return v.get("scaleLimit")})),r.push(u),u.resize=Co,u.resize(o[0],t),C(o,function(v){v.coordinateSystem=u,kf(u,v)})}),r},a.prototype.getFilledRegions=function(e,t,r,n){for(var i=(e||[]).slice(),o=j(),s=0;s<i.length;s++)o.set(i[s].name,i[s]);var l=ie.load(t,r,n);return C(l.regions,function(u){var v=u.name,c=o.get(v),h=u.properties&&u.properties.echartsStyle;c||(c={name:v},i.push(c)),h&&Bt(c,h)}),i},a}(),ql=new Vf,Gf=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r,n){var i=ie.getGeoResource(t.map);if(i&&i.type==="geoJSON"){var o=t.itemStyle=t.itemStyle||{};"color"in o||(o.color="#eee")}this.mergeDefaultAndTheme(t,n),ga(t,"label",["show"])},e.prototype.optionUpdated=function(){var t=this,r=this.option;r.regions=ql.getFilledRegions(r.regions,r.map,r.nameMap,r.nameProperty);var n={};this._optionModelMap=Pv(r.regions||[],function(i,o){var s=o.name;return s&&(i.set(s,new Yt(o,t,t.ecModel)),o.selected&&(n[s]=!0)),i},j()),r.selectedMap||(r.selectedMap=n)},e.prototype.getRegionModel=function(t){return this._optionModelMap.get(t)||new Yt(null,this,this.ecModel)},e.prototype.getFormattedLabel=function(t,r){var n=this.getRegionModel(t),i=r==="normal"?n.get(["label","formatter"]):n.get(["emphasis","label","formatter"]),o={name:t};if(ot(i))return o.status=r,i(o);if(rt(i))return i.replace("{a}",t!=null?t:"")},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.select=function(t){var r=this.option,n=r.selectedMode;if(n){n!=="multiple"&&(r.selectedMap=null);var i=r.selectedMap||(r.selectedMap={});i[t]=!0}},e.prototype.unSelect=function(t){var r=this.option.selectedMap;r&&(r[t]=!1)},e.prototype.toggleSelected=function(t){this[this.isSelected(t)?"unSelect":"select"](t)},e.prototype.isSelected=function(t){var r=this.option.selectedMap;return!!(r&&r[t])},e.type="geo",e.layoutMode="box",e.defaultOption={z:0,show:!0,left:"center",top:"center",aspectScale:null,silent:!1,map:"",boundingCoords:null,center:null,zoom:1,scaleLimit:null,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},regions:[]},e}(Jt);function Po(a,e){return a.pointToProjected?a.pointToProjected(e):a.pointToData(e)}function hi(a,e,t,r){var n=a.getZoom(),i=a.getCenter(),o=e.zoom,s=a.projectedToPoint?a.projectedToPoint(i):a.dataToPoint(i);if(e.dx!=null&&e.dy!=null&&(s[0]-=e.dx,s[1]-=e.dy,a.setCenter(Po(a,s),r)),o!=null){if(t){var l=t.min||0,u=t.max||1/0;o=Math.max(Math.min(n*o,u),l)/n}a.scaleX*=o,a.scaleY*=o;var v=(e.originX-a.x)*(o-1),c=(e.originY-a.y)*(o-1);a.x-=v,a.y-=c,a.updateTransform(),a.setCenter(Po(a,s),r),a.setZoom(o*n)}return{center:a.getCenter(),zoom:a.getZoom()}}var zf=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.focusBlurEnabled=!0,t}return e.prototype.init=function(t,r){this._api=r},e.prototype.render=function(t,r,n,i){if(this._model=t,!t.get("show")){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;return}this._mapDraw||(this._mapDraw=new Xl(n));var o=this._mapDraw;o.draw(t,r,n,this,i),o.group.on("click",this._handleRegionClick,this),o.group.silent=t.get("silent"),this.group.add(o.group),this.updateSelectStatus(t,r,n)},e.prototype._handleRegionClick=function(t){var r;Mv(t.target,function(n){return(r=nt(n).eventData)!=null},!0),r&&this._api.dispatchAction({type:"geoToggleSelect",geoId:this._model.id,name:r.name})},e.prototype.updateSelectStatus=function(t,r,n){var i=this;this._mapDraw.group.traverse(function(o){var s=nt(o).eventData;if(s)return i._model.isSelected(s.name)?n.enterSelect(o):n.leaveSelect(o),!0})},e.prototype.findHighDownDispatchers=function(t){return this._mapDraw&&this._mapDraw.findHighDownDispatchers(t,this._model)},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove()},e.type="geo",e}(Qt);function Of(a,e,t){ie.registerMap(a,e,t)}function jl(a){a.registerCoordinateSystem("geo",ql),a.registerComponentModel(Gf),a.registerComponentView(zf),a.registerImpl("registerMap",Of),a.registerImpl("getMap",function(t){return ie.getMapForUser(t)});function e(t,r){r.update="geo:updateSelectStatus",a.registerAction(r,function(n,i){var o={},s=[];return i.eachComponent({mainType:"geo",query:n},function(l){l[t](n.name);var u=l.coordinateSystem;C(u.regions,function(c){o[c.name]=l.isSelected(c.name)||!1});var v=[];C(o,function(c,h){o[h]&&v.push(h)}),s.push({geoIndex:l.componentIndex,name:v})}),{selected:o,allSelected:s,name:n.name}})}e("toggleSelected",{type:"geoToggleSelect",event:"geoselectchanged"}),e("select",{type:"geoSelect",event:"geoselected"}),e("unSelect",{type:"geoUnSelect",event:"geounselected"}),a.registerAction({type:"geoRoam",event:"geoRoam",update:"updateTransform"},function(t,r,n){var i=t.componentType||"series";r.eachComponent({mainType:i,query:t},function(o){var s=o.coordinateSystem;if(s.type==="geo"){var l=hi(s,t,o.get("scaleLimit"),n);o.setCenter&&o.setCenter(l.center),o.setZoom&&o.setZoom(l.zoom),i==="series"&&C(o.seriesGroup,function(u){u.setCenter(l.center),u.setZoom(l.zoom)})}})})}function Bf(a){X(jl),a.registerChartView(Cf),a.registerSeriesModel(Pf),a.registerLayout(Rf),a.registerProcessor(a.PRIORITY.PROCESSOR.STATISTIC,Ef),Ev("map",a.registerAction)}function Ff(a){var e=a;e.hierNode={defaultAncestor:null,ancestor:e,prelim:0,modifier:0,change:0,shift:0,i:0,thread:null};for(var t=[e],r,n;r=t.pop();)if(n=r.children,r.isExpand&&n.length)for(var i=n.length,o=i-1;o>=0;o--){var s=n[o];s.hierNode={defaultAncestor:null,ancestor:s,prelim:0,modifier:0,change:0,shift:0,i:o,thread:null},t.push(s)}}function Wf(a,e){var t=a.isExpand?a.children:[],r=a.parentNode.children,n=a.hierNode.i?r[a.hierNode.i-1]:null;if(t.length){$f(a);var i=(t[0].hierNode.prelim+t[t.length-1].hierNode.prelim)/2;n?(a.hierNode.prelim=n.hierNode.prelim+e(a,n),a.hierNode.modifier=a.hierNode.prelim-i):a.hierNode.prelim=i}else n&&(a.hierNode.prelim=n.hierNode.prelim+e(a,n));a.parentNode.hierNode.defaultAncestor=Yf(a,n,a.parentNode.hierNode.defaultAncestor||r[0],e)}function Hf(a){var e=a.hierNode.prelim+a.parentNode.hierNode.modifier;a.setLayout({x:e},!0),a.hierNode.modifier+=a.parentNode.hierNode.modifier}function Mo(a){return arguments.length?a:qf}function tr(a,e){return a-=Math.PI/2,{x:e*Math.cos(a),y:e*Math.sin(a)}}function Uf(a,e){return te(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function $f(a){for(var e=a.children,t=e.length,r=0,n=0;--t>=0;){var i=e[t];i.hierNode.prelim+=r,i.hierNode.modifier+=r,n+=i.hierNode.change,r+=i.hierNode.shift+n}}function Yf(a,e,t,r){if(e){for(var n=a,i=a,o=i.parentNode.children[0],s=e,l=n.hierNode.modifier,u=i.hierNode.modifier,v=o.hierNode.modifier,c=s.hierNode.modifier;s=Na(s),i=ka(i),s&&i;){n=Na(n),o=ka(o),n.hierNode.ancestor=a;var h=s.hierNode.prelim+c-i.hierNode.prelim-u+r(s,i);h>0&&(Zf(Xf(s,a,t),a,h),u+=h,l+=h),c+=s.hierNode.modifier,u+=i.hierNode.modifier,l+=n.hierNode.modifier,v+=o.hierNode.modifier}s&&!Na(n)&&(n.hierNode.thread=s,n.hierNode.modifier+=c-l),i&&!ka(o)&&(o.hierNode.thread=i,o.hierNode.modifier+=u-v,t=a)}return t}function Na(a){var e=a.children;return e.length&&a.isExpand?e[e.length-1]:a.hierNode.thread}function ka(a){var e=a.children;return e.length&&a.isExpand?e[0]:a.hierNode.thread}function Xf(a,e,t){return a.hierNode.ancestor.parentNode===e.parentNode?a.hierNode.ancestor:t}function Zf(a,e,t){var r=t/(e.hierNode.i-a.hierNode.i);e.hierNode.change-=r,e.hierNode.shift+=t,e.hierNode.modifier+=t,e.hierNode.prelim+=t,a.hierNode.change+=r}function qf(a,e){return a.parentNode===e.parentNode?1:2}var jf=function(){function a(){this.parentPoint=[],this.childPoints=[]}return a}(),Kf=function(a){V(e,a);function e(t){return a.call(this,t)||this}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new jf},e.prototype.buildPath=function(t,r){var n=r.childPoints,i=n.length,o=r.parentPoint,s=n[0],l=n[i-1];if(i===1){t.moveTo(o[0],o[1]),t.lineTo(s[0],s[1]);return}var u=r.orient,v=u==="TB"||u==="BT"?0:1,c=1-v,h=z(r.forkPosition,1),f=[];f[v]=o[v],f[c]=o[c]+(l[c]-o[c])*h,t.moveTo(o[0],o[1]),t.lineTo(f[0],f[1]),t.moveTo(s[0],s[1]),f[v]=s[v],t.lineTo(f[0],f[1]),f[v]=l[v],t.lineTo(f[0],f[1]),t.lineTo(l[0],l[1]);for(var p=1;p<i-1;p++){var d=n[p];t.moveTo(d[0],d[1]),f[v]=d[v],t.lineTo(f[0],f[1])}},e}(wt),Jf=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._mainGroup=new Y,t}return e.prototype.init=function(t,r){this._controller=new pa(r.getZr()),this._controllerHost={target:this.group},this.group.add(this._mainGroup)},e.prototype.render=function(t,r,n){var i=t.getData(),o=t.layoutInfo,s=this._mainGroup,l=t.get("layout");l==="radial"?(s.x=o.x+o.width/2,s.y=o.y+o.height/2):(s.x=o.x,s.y=o.y),this._updateViewCoordSys(t,n),this._updateController(t,r,n);var u=this._data;i.diff(u).add(function(v){Eo(i,v)&&Ro(i,v,null,s,t)}).update(function(v,c){var h=u.getItemGraphicEl(c);if(!Eo(i,v)){h&&ko(u,c,h,s,t);return}Ro(i,v,h,s,t)}).remove(function(v){var c=u.getItemGraphicEl(v);c&&ko(u,v,c,s,t)}).execute(),this._nodeScaleRatio=t.get("nodeScaleRatio"),this._updateNodeAndLinkScale(t),t.get("expandAndCollapse")===!0&&i.eachItemGraphicEl(function(v,c){v.off("click").on("click",function(){n.dispatchAction({type:"treeExpandAndCollapse",seriesId:t.id,dataIndex:c})})}),this._data=i},e.prototype._updateViewCoordSys=function(t,r){var n=t.getData(),i=[];n.each(function(c){var h=n.getItemLayout(c);h&&!isNaN(h.x)&&!isNaN(h.y)&&i.push([+h.x,+h.y])});var o=[],s=[];ya(i,o,s);var l=this._min,u=this._max;s[0]-o[0]===0&&(o[0]=l?l[0]:o[0]-1,s[0]=u?u[0]:s[0]+1),s[1]-o[1]===0&&(o[1]=l?l[1]:o[1]-1,s[1]=u?u[1]:s[1]+1);var v=t.coordinateSystem=new Dr;v.zoomLimit=t.get("scaleLimit"),v.setBoundingRect(o[0],o[1],s[0]-o[0],s[1]-o[1]),v.setCenter(t.get("center"),r),v.setZoom(t.get("zoom")),this.group.attr({x:v.x,y:v.y,scaleX:v.scaleX,scaleY:v.scaleY}),this._min=o,this._max=s},e.prototype._updateController=function(t,r,n){var i=this,o=this._controller,s=this._controllerHost,l=this.group;o.setPointerChecker(function(u,v,c){var h=l.getBoundingRect();return h.applyTransform(l.transform),h.contain(v,c)&&!jn(u,n,t)}),o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){ui(s,u.dx,u.dy),n.dispatchAction({seriesId:t.id,type:"treeRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){vi(s,u.scale,u.originX,u.originY),n.dispatchAction({seriesId:t.id,type:"treeRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),i._updateNodeAndLinkScale(t),n.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(t){var r=t.getData(),n=this._getNodeGlobalScale(t);r.eachItemGraphicEl(function(i,o){i.setSymbolScale(n)})},e.prototype._getNodeGlobalScale=function(t){var r=t.coordinateSystem;if(r.type!=="view")return 1;var n=this._nodeScaleRatio,i=r.scaleX||1,o=r.getZoom(),s=(o-1)*n+1;return s/i},e.prototype.dispose=function(){this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype.remove=function(){this._mainGroup.removeAll(),this._data=null},e.type="tree",e}(ft);function Eo(a,e){var t=a.getItemLayout(e);return t&&!isNaN(t.x)&&!isNaN(t.y)}function Ro(a,e,t,r,n){var i=!t,o=a.tree.getNodeByDataIndex(e),s=o.getModel(),l=o.getVisual("style").fill,u=o.isExpand===!1&&o.children.length!==0?l:"#fff",v=a.tree.root,c=o.parentNode===v?o:o.parentNode||o,h=a.getItemGraphicEl(c.dataIndex),f=c.getLayout(),p=h?{x:h.__oldX,y:h.__oldY,rawX:h.__radialOldRawX,rawY:h.__radialOldRawY}:f,d=o.getLayout();i?(t=new _l(a,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.x=p.x,t.y=p.y):t.updateData(a,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.__radialOldRawX=t.__radialRawX,t.__radialOldRawY=t.__radialRawY,t.__radialRawX=d.rawX,t.__radialRawY=d.rawY,r.add(t),a.setItemGraphicEl(e,t),t.__oldX=t.x,t.__oldY=t.y,lt(t,{x:d.x,y:d.y},n);var g=t.getSymbolPath();if(n.get("layout")==="radial"){var m=v.children[0],S=m.getLayout(),y=m.children.length,b=void 0,_=void 0;if(d.x===S.x&&o.isExpand===!0&&m.children.length){var x={x:(m.children[0].getLayout().x+m.children[y-1].getLayout().x)/2,y:(m.children[0].getLayout().y+m.children[y-1].getLayout().y)/2};b=Math.atan2(x.y-S.y,x.x-S.x),b<0&&(b=Math.PI*2+b),_=x.x<S.x,_&&(b=b-Math.PI)}else b=Math.atan2(d.y-S.y,d.x-S.x),b<0&&(b=Math.PI*2+b),o.children.length===0||o.children.length!==0&&o.isExpand===!1?(_=d.x<S.x,_&&(b=b-Math.PI)):(_=d.x>S.x,_||(b=b-Math.PI));var w=_?"left":"right",D=s.getModel("label"),T=D.get("rotate"),I=T*(Math.PI/180),A=g.getTextContent();A&&(g.setTextConfig({position:D.get("position")||w,rotation:T==null?-b:I,origin:"center"}),A.setStyle("verticalAlign","middle"))}var P=s.get(["emphasis","focus"]),E=P==="relative"?Ur(o.getAncestorsIndices(),o.getDescendantIndices()):P==="ancestor"?o.getAncestorsIndices():P==="descendant"?o.getDescendantIndices():null;E&&(nt(t).focus=E),Qf(n,o,v,t,p,f,d,r),t.__edge&&(t.onHoverStateChange=function(L){if(L!=="blur"){var M=o.parentNode&&a.getItemGraphicEl(o.parentNode.dataIndex);M&&M.hoverState===Rv||xl(t.__edge,L)}})}function Qf(a,e,t,r,n,i,o,s){var l=e.getModel(),u=a.get("edgeShape"),v=a.get("layout"),c=a.getOrient(),h=a.get(["lineStyle","curveness"]),f=a.get("edgeForkPosition"),p=l.getModel("lineStyle").getLineStyle(),d=r.__edge;if(u==="curve")e.parentNode&&e.parentNode!==t&&(d||(d=r.__edge=new wl({shape:En(v,c,h,n,n)})),lt(d,{shape:En(v,c,h,i,o)},a));else if(u==="polyline"&&v==="orthogonal"&&e!==t&&e.children&&e.children.length!==0&&e.isExpand===!0){for(var g=e.children,m=[],S=0;S<g.length;S++){var y=g[S].getLayout();m.push([y.x,y.y])}d||(d=r.__edge=new Kf({shape:{parentPoint:[o.x,o.y],childPoints:[[o.x,o.y]],orient:c,forkPosition:f}})),lt(d,{shape:{parentPoint:[o.x,o.y],childPoints:m}},a)}d&&!(u==="polyline"&&!e.isExpand)&&(d.useStyle(it({strokeNoScale:!0,fill:null},p)),Et(d,l,"lineStyle"),ze(d),s.add(d))}function No(a,e,t,r,n){var i=e.tree.root,o=Kl(i,a),s=o.source,l=o.sourceLayout,u=e.getItemGraphicEl(a.dataIndex);if(u){var v=e.getItemGraphicEl(s.dataIndex),c=v.__edge,h=u.__edge||(s.isExpand===!1||s.children.length===1?c:void 0),f=r.get("edgeShape"),p=r.get("layout"),d=r.get("orient"),g=r.get(["lineStyle","curveness"]);h&&(f==="curve"?$r(h,{shape:En(p,d,g,l,l),style:{opacity:0}},r,{cb:function(){t.remove(h)},removeOpt:n}):f==="polyline"&&r.get("layout")==="orthogonal"&&$r(h,{shape:{parentPoint:[l.x,l.y],childPoints:[[l.x,l.y]]},style:{opacity:0}},r,{cb:function(){t.remove(h)},removeOpt:n}))}}function Kl(a,e){for(var t=e.parentNode===a?e:e.parentNode||e,r;r=t.getLayout(),r==null;)t=t.parentNode===a?t:t.parentNode||t;return{source:t,sourceLayout:r}}function ko(a,e,t,r,n){var i=a.tree.getNodeByDataIndex(e),o=a.tree.root,s=Kl(o,i).sourceLayout,l={duration:n.get("animationDurationUpdate"),easing:n.get("animationEasingUpdate")};$r(t,{x:s.x+1,y:s.y+1},n,{cb:function(){r.remove(t),a.setItemGraphicEl(e,null)},removeOpt:l}),t.fadeOut(null,a.hostModel,{fadeLabel:!0,animation:l}),i.children.forEach(function(u){No(u,a,r,n,l)}),No(i,a,r,n,l)}function En(a,e,t,r,n){var i,o,s,l,u,v,c,h;if(a==="radial"){u=r.rawX,c=r.rawY,v=n.rawX,h=n.rawY;var f=tr(u,c),p=tr(u,c+(h-c)*t),d=tr(v,h+(c-h)*t),g=tr(v,h);return{x1:f.x||0,y1:f.y||0,x2:g.x||0,y2:g.y||0,cpx1:p.x||0,cpy1:p.y||0,cpx2:d.x||0,cpy2:d.y||0}}else u=r.x,c=r.y,v=n.x,h=n.y,(e==="LR"||e==="RL")&&(i=u+(v-u)*t,o=c,s=v+(u-v)*t,l=h),(e==="TB"||e==="BT")&&(i=u,o=c+(h-c)*t,s=v,l=h+(c-h)*t);return{x1:u,y1:c,x2:v,y2:h,cpx1:i,cpy1:o,cpx2:s,cpy2:l}}var Gt=_t();function Jl(a){var e=a.mainData,t=a.datas;t||(t={main:e},a.datasAttr={main:"data"}),a.datas=a.mainData=null,Ql(e,t,a),C(t,function(r){C(e.TRANSFERABLE_METHODS,function(n){r.wrapMethod(n,st(tp,a))})}),e.wrapMethod("cloneShallow",st(rp,a)),C(e.CHANGABLE_METHODS,function(r){e.wrapMethod(r,st(ep,a))}),Sl(t[e.dataType]===e)}function tp(a,e){if(ip(this)){var t=U({},Gt(this).datas);t[this.dataType]=e,Ql(e,t,a)}else fi(e,this.dataType,Gt(this).mainData,a);return e}function ep(a,e){return a.struct&&a.struct.update(),e}function rp(a,e){return C(Gt(e).datas,function(t,r){t!==e&&fi(t.cloneShallow(),r,e,a)}),e}function ap(a){var e=Gt(this).mainData;return a==null||e==null?e:Gt(e).datas[a]}function np(){var a=Gt(this).mainData;return a==null?[{data:a}]:B(pr(Gt(a).datas),function(e){return{type:e,data:Gt(a).datas[e]}})}function ip(a){return Gt(a).mainData===a}function Ql(a,e,t){Gt(a).datas={},C(e,function(r,n){fi(r,n,a,t)})}function fi(a,e,t,r){Gt(t).datas[e]=a,Gt(a).mainData=t,a.dataType=e,r.struct&&(a[r.structAttr]=r.struct,r.struct[r.datasAttr[e]]=a),a.getLinkedData=ap,a.getLinkedDataAll=np}var op=function(){function a(e,t){this.depth=0,this.height=0,this.dataIndex=-1,this.children=[],this.viewChildren=[],this.isExpand=!1,this.name=e||"",this.hostTree=t}return a.prototype.isRemoved=function(){return this.dataIndex<0},a.prototype.eachNode=function(e,t,r){ot(e)&&(r=t,t=e,e=null),e=e||{},rt(e)&&(e={order:e});var n=e.order||"preorder",i=this[e.attr||"children"],o;n==="preorder"&&(o=t.call(r,this));for(var s=0;!o&&s<i.length;s++)i[s].eachNode(e,t,r);n==="postorder"&&t.call(r,this)},a.prototype.updateDepthAndHeight=function(e){var t=0;this.depth=e;for(var r=0;r<this.children.length;r++){var n=this.children[r];n.updateDepthAndHeight(e+1),n.height>t&&(t=n.height)}this.height=t+1},a.prototype.getNodeById=function(e){if(this.getId()===e)return this;for(var t=0,r=this.children,n=r.length;t<n;t++){var i=r[t].getNodeById(e);if(i)return i}},a.prototype.contains=function(e){if(e===this)return!0;for(var t=0,r=this.children,n=r.length;t<n;t++){var i=r[t].contains(e);if(i)return i}},a.prototype.getAncestors=function(e){for(var t=[],r=e?this:this.parentNode;r;)t.push(r),r=r.parentNode;return t.reverse(),t},a.prototype.getAncestorsIndices=function(){for(var e=[],t=this;t;)e.push(t.dataIndex),t=t.parentNode;return e.reverse(),e},a.prototype.getDescendantIndices=function(){var e=[];return this.eachNode(function(t){e.push(t.dataIndex)}),e},a.prototype.getValue=function(e){var t=this.hostTree.data;return t.getStore().get(t.getDimensionIndex(e||"value"),this.dataIndex)},a.prototype.setLayout=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemLayout(this.dataIndex,e,t)},a.prototype.getLayout=function(){return this.hostTree.data.getItemLayout(this.dataIndex)},a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostTree,r=t.data.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getLevelModel=function(){return(this.hostTree.levelModels||[])[this.depth]},a.prototype.setVisual=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemVisual(this.dataIndex,e,t)},a.prototype.getVisual=function(e){return this.hostTree.data.getItemVisual(this.dataIndex,e)},a.prototype.getRawIndex=function(){return this.hostTree.data.getRawIndex(this.dataIndex)},a.prototype.getId=function(){return this.hostTree.data.getId(this.dataIndex)},a.prototype.getChildIndex=function(){if(this.parentNode){for(var e=this.parentNode.children,t=0;t<e.length;++t)if(e[t]===this)return t;return-1}return-1},a.prototype.isAncestorOf=function(e){for(var t=e.parentNode;t;){if(t===this)return!0;t=t.parentNode}return!1},a.prototype.isDescendantOf=function(e){return e!==this&&e.isAncestorOf(this)},a}(),pi=function(){function a(e){this.type="tree",this._nodes=[],this.hostModel=e}return a.prototype.eachNode=function(e,t,r){this.root.eachNode(e,t,r)},a.prototype.getNodeByDataIndex=function(e){var t=this.data.getRawIndex(e);return this._nodes[t]},a.prototype.getNodeById=function(e){return this.root.getNodeById(e)},a.prototype.update=function(){for(var e=this.data,t=this._nodes,r=0,n=t.length;r<n;r++)t[r].dataIndex=-1;for(var r=0,n=e.count();r<n;r++)t[e.getRawIndex(r)].dataIndex=r},a.prototype.clearLayouts=function(){this.data.clearItemLayouts()},a.createTree=function(e,t,r){var n=new a(t),i=[],o=1;s(e);function s(v,c){var h=v.value;o=Math.max(o,H(h)?h.length:1),i.push(v);var f=new op(Sr(v.name,""),n);c?sp(f,c):n.root=f,n._nodes.push(f);var p=v.children;if(p)for(var d=0;d<p.length;d++)s(p[d],f)}n.root.updateDepthAndHeight(0);var l=Qn(i,{coordDimensions:["value"],dimensionsCount:o}).dimensions,u=new Mt(l,t);return u.initData(i),r&&r(u),Jl({mainData:u,struct:n,structAttr:"tree"}),n.update(),n},a}();function sp(a,e){var t=e.children;a.parentNode!==e&&(t.push(a),a.parentNode=e)}function ur(a,e,t){if(a&&zt(e,a.type)>=0){var r=t.getData().tree.root,n=a.targetNode;if(rt(n)&&(n=r.getNodeById(n)),n&&r.contains(n))return{node:n};var i=a.targetNodeId;if(i!=null&&(n=r.getNodeById(i)))return{node:n}}}function tu(a){for(var e=[];a;)a=a.parentNode,a&&e.push(a);return e.reverse()}function di(a,e){var t=tu(a);return zt(t,e)>=0}function _a(a,e){for(var t=[];a;){var r=a.dataIndex;t.push({name:a.name,dataIndex:r,value:e.getRawValue(r)}),a=a.parentNode}return t.reverse(),t}var lp=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.hasSymbolVisual=!0,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t){var r={name:t.name,children:t.data},n=t.leaves||{},i=new Yt(n,this,this.ecModel),o=pi.createTree(r,this,s);function s(c){c.wrapMethod("getItemModel",function(h,f){var p=o.getNodeByDataIndex(f);return p&&p.children.length&&p.isExpand||(h.parentModel=i),h})}var l=0;o.eachNode("preorder",function(c){c.depth>l&&(l=c.depth)});var u=t.expandAndCollapse,v=u&&t.initialTreeDepth>=0?t.initialTreeDepth:l;return o.root.eachNode("preorder",function(c){var h=c.hostTree.data.getRawDataItem(c.dataIndex);c.isExpand=h&&h.collapsed!=null?!h.collapsed:c.depth<=v}),o.data},e.prototype.getOrient=function(){var t=this.get("orient");return t==="horizontal"?t="LR":t==="vertical"&&(t="TB"),t},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.formatTooltip=function(t,r,n){for(var i=this.getData().tree,o=i.root.children[0],s=i.getNodeByDataIndex(t),l=s.getValue(),u=s.name;s&&s!==o;)u=s.parentNode.name+"."+u,s=s.parentNode;return At("nameValue",{name:u,value:l,noValue:isNaN(l)||l==null})},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return r.treeAncestors=_a(n,this),r.collapsed=!n.isExpand,r},e.type="series.tree",e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"view",left:"12%",top:"12%",right:"12%",bottom:"12%",layout:"orthogonal",edgeShape:"curve",edgeForkPosition:"50%",roam:!1,nodeScaleRatio:.4,center:null,zoom:1,orient:"LR",symbol:"emptyCircle",symbolSize:7,expandAndCollapse:!0,initialTreeDepth:2,lineStyle:{color:"#ccc",width:1.5,curveness:.5},itemStyle:{color:"lightsteelblue",borderWidth:1.5},label:{show:!0},animationEasing:"linear",animationDuration:700,animationDurationUpdate:500},e}(dt);function up(a,e,t){for(var r=[a],n=[],i;i=r.pop();)if(n.push(i),i.isExpand){var o=i.children;if(o.length)for(var s=0;s<o.length;s++)r.push(o[s])}for(;i=n.pop();)e(i,t)}function qe(a,e){for(var t=[a],r;r=t.pop();)if(e(r),r.isExpand){var n=r.children;if(n.length)for(var i=n.length-1;i>=0;i--)t.push(n[i])}}function vp(a,e){a.eachSeriesByType("tree",function(t){cp(t,e)})}function cp(a,e){var t=Uf(a,e);a.layoutInfo=t;var r=a.get("layout"),n=0,i=0,o=null;r==="radial"?(n=2*Math.PI,i=Math.min(t.height,t.width)/2,o=Mo(function(y,b){return(y.parentNode===b.parentNode?1:2)/y.depth})):(n=t.width,i=t.height,o=Mo());var s=a.getData().tree.root,l=s.children[0];if(l){Ff(s),up(l,Wf,o),s.hierNode.modifier=-l.hierNode.prelim,qe(l,Hf);var u=l,v=l,c=l;qe(l,function(y){var b=y.getLayout().x;b<u.getLayout().x&&(u=y),b>v.getLayout().x&&(v=y),y.depth>c.depth&&(c=y)});var h=u===v?1:o(u,v)/2,f=h-u.getLayout().x,p=0,d=0,g=0,m=0;if(r==="radial")p=n/(v.getLayout().x+h+f),d=i/(c.depth-1||1),qe(l,function(y){g=(y.getLayout().x+f)*p,m=(y.depth-1)*d;var b=tr(g,m);y.setLayout({x:b.x,y:b.y,rawX:g,rawY:m},!0)});else{var S=a.getOrient();S==="RL"||S==="LR"?(d=i/(v.getLayout().x+h+f),p=n/(c.depth-1||1),qe(l,function(y){m=(y.getLayout().x+f)*d,g=S==="LR"?(y.depth-1)*p:n-(y.depth-1)*p,y.setLayout({x:g,y:m},!0)})):(S==="TB"||S==="BT")&&(p=n/(v.getLayout().x+h+f),d=i/(c.depth-1||1),qe(l,function(y){g=(y.getLayout().x+f)*p,m=S==="TB"?(y.depth-1)*d:i-(y.depth-1)*d,y.setLayout({x:g,y:m},!0)}))}}}function hp(a){a.eachSeriesByType("tree",function(e){var t=e.getData(),r=t.tree;r.eachNode(function(n){var i=n.getModel(),o=i.getModel("itemStyle").getItemStyle(),s=t.ensureUniqueItemVisual(n.dataIndex,"style");U(s,o)})})}function fp(a){a.registerAction({type:"treeExpandAndCollapse",event:"treeExpandAndCollapse",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"tree",query:e},function(r){var n=e.dataIndex,i=r.getData().tree,o=i.getNodeByDataIndex(n);o.isExpand=!o.isExpand})}),a.registerAction({type:"treeRoam",event:"treeRoam",update:"none"},function(e,t,r){t.eachComponent({mainType:"series",subType:"tree",query:e},function(n){var i=n.coordinateSystem,o=hi(i,e,void 0,r);n.setCenter&&n.setCenter(o.center),n.setZoom&&n.setZoom(o.zoom)})})}function pp(a){a.registerChartView(Jf),a.registerSeriesModel(lp),a.registerLayout(vp),a.registerVisual(hp),fp(a)}var Vo=["treemapZoomToNode","treemapRender","treemapMove"];function dp(a){for(var e=0;e<Vo.length;e++)a.registerAction({type:Vo[e],update:"updateView"},or);a.registerAction({type:"treemapRootToNode",update:"updateView"},function(t,r){r.eachComponent({mainType:"series",subType:"treemap",query:t},n);function n(i,o){var s=["treemapZoomToNode","treemapRootToNode"],l=ur(t,s,i);if(l){var u=i.getViewRoot();u&&(t.direction=di(u,l.node)?"rollUp":"drillDown"),i.resetViewRoot(l.node)}}})}function eu(a){var e=a.getData(),t=e.tree,r={};t.eachNode(function(n){for(var i=n;i&&i.depth>1;)i=i.parentNode;var o=wn(a.ecModel,i.name||i.dataIndex+"",r);n.setVisual("decal",o)})}var gp=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.preventUsingHoverLayer=!0,t}return e.prototype.getInitialData=function(t,r){var n={name:t.name,children:t.data};ru(n);var i=t.levels||[],o=this.designatedVisualItemStyle={},s=new Yt({itemStyle:o},this,r);i=t.levels=yp(i,r);var l=B(i||[],function(c){return new Yt(c,s,r)},this),u=pi.createTree(n,this,v);function v(c){c.wrapMethod("getItemModel",function(h,f){var p=u.getNodeByDataIndex(f),d=p?l[p.depth]:null;return h.parentModel=d||s,h})}return u.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=this.getRawValue(t),s=i.getName(t);return At("nameValue",{name:s,value:o})},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return r.treeAncestors=_a(n,this),r.treePathInfo=r.treeAncestors,r},e.prototype.setLayoutInfo=function(t){this.layoutInfo=this.layoutInfo||{},U(this.layoutInfo,t)},e.prototype.mapIdToIndex=function(t){var r=this._idIndexMap;r||(r=this._idIndexMap=j(),this._idIndexMapCount=0);var n=r.get(t);return n==null&&r.set(t,n=this._idIndexMapCount++),n},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var r=this.getRawData().tree.root;(!t||t!==r&&!r.contains(t))&&(this._viewRoot=r)},e.prototype.enableAriaDecal=function(){eu(this)},e.type="series.treemap",e.layoutMode="box",e.defaultOption={progressive:0,left:"center",top:"middle",width:"80%",height:"80%",sort:!0,clipWindow:"origin",squareRatio:.5*(1+Math.sqrt(5)),leafDepth:null,drillDownIcon:"▶",zoomToNodeRatio:.32*.32,scaleLimit:null,roam:!0,nodeClick:"zoomToNode",animation:!0,animationDurationUpdate:900,animationEasing:"quinticInOut",breadcrumb:{show:!0,height:22,left:"center",top:"bottom",emptyItemWidth:25,itemStyle:{color:"rgba(0,0,0,0.7)",textStyle:{color:"#fff"}},emphasis:{itemStyle:{color:"rgba(0,0,0,0.9)"}}},label:{show:!0,distance:0,padding:5,position:"inside",color:"#fff",overflow:"truncate"},upperLabel:{show:!1,position:[0,"50%"],height:20,overflow:"truncate",verticalAlign:"middle"},itemStyle:{color:null,colorAlpha:null,colorSaturation:null,borderWidth:0,gapWidth:0,borderColor:"#fff",borderColorSaturation:null},emphasis:{upperLabel:{show:!0,position:[0,"50%"],overflow:"truncate",verticalAlign:"middle"}},visualDimension:0,visualMin:null,visualMax:null,color:[],colorAlpha:null,colorSaturation:null,colorMappingBy:"index",visibleMin:10,childrenVisibleMin:null,levels:[]},e}(dt);function ru(a){var e=0;C(a.children,function(r){ru(r);var n=r.value;H(n)&&(n=n[0]),e+=n});var t=a.value;H(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),H(a.value)?a.value[0]=t:a.value=t}function yp(a,e){var t=Ut(e.get("color")),r=Ut(e.get(["aria","decal","decals"]));if(t){a=a||[];var n,i;C(a,function(s){var l=new Yt(s),u=l.get("color"),v=l.get("decal");(l.get(["itemStyle","color"])||u&&u!=="none")&&(n=!0),(l.get(["itemStyle","decal"])||v&&v!=="none")&&(i=!0)});var o=a[0]||(a[0]={});return n||(o.color=t.slice()),!i&&r&&(o.decal=r.slice()),a}}var mp=8,Go=8,Va=5,Sp=function(){function a(e){this.group=new Y,e.add(this.group)}return a.prototype.render=function(e,t,r,n){var i=e.getModel("breadcrumb"),o=this.group;if(o.removeAll(),!(!i.get("show")||!r)){var s=i.getModel("itemStyle"),l=i.getModel("emphasis"),u=s.getModel("textStyle"),v=l.getModel(["itemStyle","textStyle"]),c={pos:{left:i.get("left"),right:i.get("right"),top:i.get("top"),bottom:i.get("bottom")},box:{width:t.getWidth(),height:t.getHeight()},emptyItemWidth:i.get("emptyItemWidth"),totalWidth:0,renderList:[]};this._prepare(r,c,u),this._renderContent(e,c,s,l,u,v,n),Nv(o,c.pos,c.box)}},a.prototype._prepare=function(e,t,r){for(var n=e;n;n=n.parentNode){var i=Sr(n.getModel().get("name"),""),o=r.getTextRect(i),s=Math.max(o.width+mp*2,t.emptyItemWidth);t.totalWidth+=s+Go,t.renderList.push({node:n,text:i,width:s})}},a.prototype._renderContent=function(e,t,r,n,i,o,s){for(var l=0,u=t.emptyItemWidth,v=e.get(["breadcrumb","height"]),c=kv(t.pos,t.box),h=t.totalWidth,f=t.renderList,p=n.getModel("itemStyle").getItemStyle(),d=f.length-1;d>=0;d--){var g=f[d],m=g.node,S=g.width,y=g.text;h>c.width&&(h-=S-u,S=u,y=null);var b=new ne({shape:{points:xp(l,0,S,v,d===f.length-1,d===0)},style:it(r.getItemStyle(),{lineJoin:"bevel"}),textContent:new Vt({style:bt(i,{text:y})}),textConfig:{position:"inside"},z2:mr*1e4,onclick:st(s,m)});b.disableLabelAnimation=!0,b.getTextContent().ensureState("emphasis").style=bt(o,{text:y}),b.ensureState("emphasis").style=p,ct(b,n.get("focus"),n.get("blurScope"),n.get("disabled")),this.group.add(b),bp(b,e,m),l+=S+Go}},a.prototype.remove=function(){this.group.removeAll()},a}();function xp(a,e,t,r,n,i){var o=[[n?a:a-Va,e],[a+t,e],[a+t,e+r],[n?a:a-Va,e+r]];return!i&&o.splice(2,0,[a+t+Va,e+r/2]),!n&&o.push([a,e+r/2]),o}function bp(a,e,t){nt(a).eventData={componentType:"series",componentSubType:"treemap",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"treemap",selfType:"breadcrumb",nodeData:{dataIndex:t&&t.dataIndex,name:t&&t.name},treePathInfo:t&&_a(t,e)}}var _p=function(){function a(){this._storage=[],this._elExistsMap={}}return a.prototype.add=function(e,t,r,n,i){return this._elExistsMap[e.id]?!1:(this._elExistsMap[e.id]=!0,this._storage.push({el:e,target:t,duration:r,delay:n,easing:i}),!0)},a.prototype.finished=function(e){return this._finishedCallback=e,this},a.prototype.start=function(){for(var e=this,t=this._storage.length,r=function(){t--,t<=0&&(e._storage.length=0,e._elExistsMap={},e._finishedCallback&&e._finishedCallback())},n=0,i=this._storage.length;n<i;n++){var o=this._storage[n];o.el.animateTo(o.target,{duration:o.duration,delay:o.delay,easing:o.easing,setToFinal:!0,done:r,aborted:r})}return this},a}();function wp(){return new _p}var Rn=Y,zo=Lt,Oo=3,Bo="label",Fo="upperLabel",Dp=mr*10,Tp=mr*2,Ip=mr*3,Se=Tl([["fill","color"],["stroke","strokeColor"],["lineWidth","strokeWidth"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),Wo=function(a){var e=Se(a);return e.stroke=e.fill=e.lineWidth=null,e},jr=_t(),Ap=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._state="ready",t._storage=je(),t}return e.prototype.render=function(t,r,n,i){var o=r.findComponents({mainType:"series",subType:"treemap",query:i});if(!(zt(o,t)<0)){this.seriesModel=t,this.api=n,this.ecModel=r;var s=["treemapZoomToNode","treemapRootToNode"],l=ur(i,s,t),u=i&&i.type,v=t.layoutInfo,c=!this._oldTree,h=this._storage,f=u==="treemapRootToNode"&&l&&h?{rootNodeGroup:h.nodeGroup[l.node.getRawIndex()],direction:i.direction}:null,p=this._giveContainerGroup(v),d=t.get("animation"),g=this._doRender(p,t,f);d&&!c&&(!u||u==="treemapZoomToNode"||u==="treemapRootToNode")?this._doAnimation(p,g,t,f):g.renderFinally(),this._resetController(n),this._renderBreadcrumb(t,n,l)}},e.prototype._giveContainerGroup=function(t){var r=this._containerGroup;return r||(r=this._containerGroup=new Rn,this._initEvents(r),this.group.add(r)),r.x=t.x,r.y=t.y,r},e.prototype._doRender=function(t,r,n){var i=r.getData().tree,o=this._oldTree,s=je(),l=je(),u=this._storage,v=[];function c(S,y,b,_){return Lp(r,l,u,n,s,v,S,y,b,_)}d(i.root?[i.root]:[],o&&o.root?[o.root]:[],t,i===o||!o,0);var h=g(u);if(this._oldTree=i,this._storage=l,this._controllerHost){var f=this.seriesModel.layoutInfo,p=i.root.getLayout();p.width===f.width&&p.height===f.height&&(this._controllerHost.zoom=1)}return{lastsForAnimation:s,willDeleteEls:h,renderFinally:m};function d(S,y,b,_,x){_?(y=S,C(S,function(T,I){!T.isRemoved()&&D(I,I)})):new Oe(y,S,w,w).add(D).update(D).remove(st(D,null)).execute();function w(T){return T.getId()}function D(T,I){var A=T!=null?S[T]:null,P=I!=null?y[I]:null,E=c(A,P,b,x);E&&d(A&&A.viewChildren||[],P&&P.viewChildren||[],E,_,x+1)}}function g(S){var y=je();return S&&C(S,function(b,_){var x=y[_];C(b,function(w){w&&(x.push(w),jr(w).willDelete=!0)})}),y}function m(){C(h,function(S){C(S,function(y){y.parent&&y.parent.remove(y)})}),C(v,function(S){S.invisible=!0,S.dirty()})}},e.prototype._doAnimation=function(t,r,n,i){var o=n.get("animationDurationUpdate"),s=n.get("animationEasing"),l=(ot(o)?0:o)||0,u=(ot(s)?null:s)||"cubicOut",v=wp();C(r.willDeleteEls,function(c,h){C(c,function(f,p){if(!f.invisible){var d=f.parent,g,m=jr(d);if(i&&i.direction==="drillDown")g=d===i.rootNodeGroup?{shape:{x:0,y:0,width:m.nodeWidth,height:m.nodeHeight},style:{opacity:0}}:{style:{opacity:0}};else{var S=0,y=0;m.willDelete||(S=m.nodeWidth/2,y=m.nodeHeight/2),g=h==="nodeGroup"?{x:S,y,style:{opacity:0}}:{shape:{x:S,y,width:0,height:0},style:{opacity:0}}}g&&v.add(f,g,l,0,u)}})}),C(this._storage,function(c,h){C(c,function(f,p){var d=r.lastsForAnimation[h][p],g={};d&&(f instanceof Y?d.oldX!=null&&(g.x=f.x,g.y=f.y,f.x=d.oldX,f.y=d.oldY):(d.oldShape&&(g.shape=U({},f.shape),f.setShape(d.oldShape)),d.fadein?(f.setStyle("opacity",0),g.style={opacity:1}):f.style.opacity!==1&&(g.style={opacity:1})),v.add(f,g,l,0,u))})},this),this._state="animating",v.finished(et(function(){this._state="ready",r.renderFinally()},this)).start()},e.prototype._resetController=function(t){var r=this._controller,n=this._controllerHost;n||(this._controllerHost={target:this.group},n=this._controllerHost),r||(r=this._controller=new pa(t.getZr()),r.enable(this.seriesModel.get("roam")),n.zoomLimit=this.seriesModel.get("scaleLimit"),n.zoom=this.seriesModel.get("zoom"),r.on("pan",et(this._onPan,this)),r.on("zoom",et(this._onZoom,this)));var i=new ht(0,0,t.getWidth(),t.getHeight());r.setPointerChecker(function(o,s,l){return i.contain(s,l)})},e.prototype._clearController=function(){var t=this._controller;this._controllerHost=null,t&&(t.dispose(),t=null)},e.prototype._onPan=function(t){if(this._state!=="animating"&&(Math.abs(t.dx)>Oo||Math.abs(t.dy)>Oo)){var r=this.seriesModel.getData().tree.root;if(!r)return;var n=r.getLayout();if(!n)return;this.api.dispatchAction({type:"treemapMove",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:n.x+t.dx,y:n.y+t.dy,width:n.width,height:n.height}})}},e.prototype._onZoom=function(t){var r=t.originX,n=t.originY,i=t.scale;if(this._state!=="animating"){var o=this.seriesModel.getData().tree.root;if(!o)return;var s=o.getLayout();if(!s)return;var l=new ht(s.x,s.y,s.width,s.height),u=null,v=this._controllerHost;u=v.zoomLimit;var c=v.zoom=v.zoom||1;if(c*=i,u){var h=u.min||0,f=u.max||1/0;c=Math.max(Math.min(f,c),h)}var p=c/v.zoom;v.zoom=c;var d=this.seriesModel.layoutInfo;r-=d.x,n-=d.y;var g=yr();Ge(g,g,[-r,-n]),ml(g,g,[p,p]),Ge(g,g,[r,n]),l.applyTransform(g),this.api.dispatchAction({type:"treemapRender",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:l.x,y:l.y,width:l.width,height:l.height}})}},e.prototype._initEvents=function(t){var r=this;t.on("click",function(n){if(r._state==="ready"){var i=r.seriesModel.get("nodeClick",!0);if(i){var o=r.findTarget(n.offsetX,n.offsetY);if(o){var s=o.node;if(s.getLayout().isLeafRoot)r._rootToNode(o);else if(i==="zoomToNode")r._zoomToNode(o);else if(i==="link"){var l=s.hostTree.data.getItemModel(s.dataIndex),u=l.get("link",!0),v=l.get("target",!0)||"blank";u&&Dl(u,v)}}}}},this)},e.prototype._renderBreadcrumb=function(t,r,n){var i=this;n||(n=t.get("leafDepth",!0)!=null?{node:t.getViewRoot()}:this.findTarget(r.getWidth()/2,r.getHeight()/2),n||(n={node:t.getData().tree.root})),(this._breadcrumb||(this._breadcrumb=new Sp(this.group))).render(t,r,n.node,function(o){i._state!=="animating"&&(di(t.getViewRoot(),o)?i._rootToNode({node:o}):i._zoomToNode({node:o}))})},e.prototype.remove=function(){this._clearController(),this._containerGroup&&this._containerGroup.removeAll(),this._storage=je(),this._state="ready",this._breadcrumb&&this._breadcrumb.remove()},e.prototype.dispose=function(){this._clearController()},e.prototype._zoomToNode=function(t){this.api.dispatchAction({type:"treemapZoomToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype._rootToNode=function(t){this.api.dispatchAction({type:"treemapRootToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype.findTarget=function(t,r){var n,i=this.seriesModel.getViewRoot();return i.eachNode({attr:"viewChildren",order:"preorder"},function(o){var s=this._storage.background[o.getRawIndex()];if(s){var l=s.transformCoordToLocal(t,r),u=s.shape;if(u.x<=l[0]&&l[0]<=u.x+u.width&&u.y<=l[1]&&l[1]<=u.y+u.height)n={node:o,offsetX:l[0],offsetY:l[1]};else return!1}},this),n},e.type="treemap",e}(ft);function je(){return{nodeGroup:[],background:[],content:[]}}function Lp(a,e,t,r,n,i,o,s,l,u){if(!o)return;var v=o.getLayout(),c=a.getData(),h=o.getModel();if(c.setItemGraphicEl(o.dataIndex,null),!v||!v.isInView)return;var f=v.width,p=v.height,d=v.borderWidth,g=v.invisible,m=o.getRawIndex(),S=s&&s.getRawIndex(),y=o.viewChildren,b=v.upperHeight,_=y&&y.length,x=h.getModel("itemStyle"),w=h.getModel(["emphasis","itemStyle"]),D=h.getModel(["blur","itemStyle"]),T=h.getModel(["select","itemStyle"]),I=x.get("borderRadius")||0,A=Q("nodeGroup",Rn);if(!A)return;if(l.add(A),A.x=v.x||0,A.y=v.y||0,A.markRedraw(),jr(A).nodeWidth=f,jr(A).nodeHeight=p,v.isAboveViewRoot)return A;var P=Q("background",zo,u,Tp);P&&F(A,P,_&&v.upperLabelHeight);var E=h.getModel("emphasis"),L=E.get("focus"),M=E.get("blurScope"),R=E.get("disabled"),N=L==="ancestor"?o.getAncestorsIndices():L==="descendant"?o.getDescendantIndices():L;if(_)Hi(A)&&Tr(A,!1),P&&(Tr(P,!R),c.setItemGraphicEl(o.dataIndex,P),Ui(P,N,M));else{var G=Q("content",zo,u,Ip);G&&W(A,G),P.disableMorphing=!0,P&&Hi(P)&&Tr(P,!1),Tr(A,!R),c.setItemGraphicEl(o.dataIndex,A);var O=h.getShallow("cursor");O&&G.attr("cursor",O),Ui(A,N,M)}return A;function F(q,$,at){var tt=nt($);if(tt.dataIndex=o.dataIndex,tt.seriesIndex=a.seriesIndex,$.setShape({x:0,y:0,width:f,height:p,r:I}),g)K($);else{$.invisible=!1;var vt=o.getVisual("style"),Dt=vt.stroke,Ft=Wo(x);Ft.fill=Dt;var pt=Se(w);pt.fill=w.get("borderColor");var Wt=Se(D);Wt.fill=D.get("borderColor");var ee=Se(T);if(ee.fill=T.get("borderColor"),at){var De=f-2*d;J($,Dt,vt.opacity,{x:d,y:0,width:De,height:b})}else $.removeTextContent();$.setStyle(Ft),$.ensureState("emphasis").style=pt,$.ensureState("blur").style=Wt,$.ensureState("select").style=ee,ze($)}q.add($)}function W(q,$){var at=nt($);at.dataIndex=o.dataIndex,at.seriesIndex=a.seriesIndex;var tt=Math.max(f-2*d,0),vt=Math.max(p-2*d,0);if($.culling=!0,$.setShape({x:d,y:d,width:tt,height:vt,r:I}),g)K($);else{$.invisible=!1;var Dt=o.getVisual("style"),Ft=Dt.fill,pt=Wo(x);pt.fill=Ft,pt.decal=Dt.decal;var Wt=Se(w),ee=Se(D),De=Se(T);J($,Ft,Dt.opacity,null),$.setStyle(pt),$.ensureState("emphasis").style=Wt,$.ensureState("blur").style=ee,$.ensureState("select").style=De,ze($)}q.add($)}function K(q){!q.invisible&&i.push(q)}function J(q,$,at,tt){var vt=h.getModel(tt?Fo:Bo),Dt=Sr(h.get("name"),null),Ft=vt.getShallow("show");Ot(q,It(h,tt?Fo:Bo),{defaultText:Ft?Dt:null,inheritColor:$,defaultOpacity:at,labelFetcher:a,labelDataIndex:o.dataIndex});var pt=q.getTextContent();if(pt){var Wt=pt.style,ee=Vv(Wt.padding||0);tt&&(q.setTextConfig({layoutRect:tt}),pt.disableLabelLayout=!0),pt.beforeUpdate=function(){var Vi=Math.max((tt?tt.width:q.shape.width)-ee[1]-ee[3],0),Gi=Math.max((tt?tt.height:q.shape.height)-ee[0]-ee[2],0);(Wt.width!==Vi||Wt.height!==Gi)&&pt.setStyle({width:Vi,height:Gi})},Wt.truncateMinChar=2,Wt.lineOverflow="truncate",Z(Wt,tt,v);var De=pt.getState("emphasis");Z(De?De.style:null,tt,v)}}function Z(q,$,at){var tt=q?q.text:null;if(!$&&at.isLeafRoot&&tt!=null){var vt=a.get("drillDownIcon",!0);q.text=vt?vt+" "+tt:tt}}function Q(q,$,at,tt){var vt=S!=null&&t[q][S],Dt=n[q];return vt?(t[q][S]=null,yt(Dt,vt)):g||(vt=new $,vt instanceof ir&&(vt.z2=Cp(at,tt)),se(Dt,vt)),e[q][m]=vt}function yt(q,$){var at=q[m]={};$ instanceof Rn?(at.oldX=$.x,at.oldY=$.y):at.oldShape=U({},$.shape)}function se(q,$){var at=q[m]={},tt=o.parentNode,vt=$ instanceof Y;if(tt&&(!r||r.direction==="drillDown")){var Dt=0,Ft=0,pt=n.background[tt.getRawIndex()];!r&&pt&&pt.oldShape&&(Dt=pt.oldShape.width,Ft=pt.oldShape.height),vt?(at.oldX=0,at.oldY=Ft):at.oldShape={x:Dt,y:Ft,width:0,height:0}}at.fadein=!vt}}function Cp(a,e){return a*Dp+e}var Pp="itemStyle",au=_t();const Mp={seriesType:"treemap",reset:function(a){var e=a.getData().tree,t=e.root;t.isRemoved()||nu(t,{},a.getViewRoot().getAncestors(),a)}};function nu(a,e,t,r){var n=a.getModel(),i=a.getLayout(),o=a.hostTree.data;if(!(!i||i.invisible||!i.isInView)){var s=n.getModel(Pp),l=Ep(s,e,r),u=o.ensureUniqueItemVisual(a.dataIndex,"style"),v=s.get("borderColor"),c=s.get("borderColorSaturation"),h;c!=null&&(h=Ho(l),v=Rp(c,h)),u.stroke=v;var f=a.viewChildren;if(!f||!f.length)h=Ho(l),u.fill=h;else{var p=Np(a,n,i,s,l,f);C(f,function(d,g){if(d.depth>=t.length||d===t[d.depth]){var m=kp(n,l,d,g,p,r);nu(d,m,t,r)}})}}}function Ep(a,e,t){var r=U({},e),n=t.designatedVisualItemStyle;return C(["color","colorAlpha","colorSaturation"],function(i){n[i]=e[i];var o=a.get(i);n[i]=null,o!=null&&(r[i]=o)}),r}function Ho(a){var e=Ga(a,"color");if(e){var t=Ga(a,"colorAlpha"),r=Ga(a,"colorSaturation");return r&&(e=Il(e,null,null,r)),t&&(e=Dn(e,t)),e}}function Rp(a,e){return e!=null?Il(e,null,null,a):null}function Ga(a,e){var t=a[e];if(t!=null&&t!=="none")return t}function Np(a,e,t,r,n,i){if(!(!i||!i.length)){var o=za(e,"color")||n.color!=null&&n.color!=="none"&&(za(e,"colorAlpha")||za(e,"colorSaturation"));if(o){var s=e.get("visualMin"),l=e.get("visualMax"),u=t.dataExtent.slice();s!=null&&s<u[0]&&(u[0]=s),l!=null&&l>u[1]&&(u[1]=l);var v=e.get("colorMappingBy"),c={type:o.name,dataExtent:u,visual:o.range};c.type==="color"&&(v==="index"||v==="id")?(c.mappingMethod="category",c.loop=!0):c.mappingMethod="linear";var h=new Al(c);return au(h).drColorMappingBy=v,h}}}function za(a,e){var t=a.get(e);return H(t)&&t.length?{name:e,range:t}:null}function kp(a,e,t,r,n,i){var o=U({},e);if(n){var s=n.type,l=s==="color"&&au(n).drColorMappingBy,u=l==="index"?r:l==="id"?i.mapIdToIndex(t.getId()):t.getValue(a.get("visualDimension"));o[s]=n.mapValueToVisual(u)}return o}var vr=Math.max,Kr=Math.min,Uo=kt,gi=C,iu=["itemStyle","borderWidth"],Vp=["itemStyle","gapWidth"],Gp=["upperLabel","show"],zp=["upperLabel","height"];const Op={seriesType:"treemap",reset:function(a,e,t,r){var n=t.getWidth(),i=t.getHeight(),o=a.option,s=te(a.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),l=o.size||[],u=z(Uo(s.width,l[0]),n),v=z(Uo(s.height,l[1]),i),c=r&&r.type,h=["treemapZoomToNode","treemapRootToNode"],f=ur(r,h,a),p=c==="treemapRender"||c==="treemapMove"?r.rootRect:null,d=a.getViewRoot(),g=tu(d);if(c!=="treemapMove"){var m=c==="treemapZoomToNode"?$p(a,f,d,u,v):p?[p.width,p.height]:[u,v],S=o.sort;S&&S!=="asc"&&S!=="desc"&&(S="desc");var y={squareRatio:o.squareRatio,sort:S,leafDepth:o.leafDepth};d.hostTree.clearLayouts();var b={x:0,y:0,width:m[0],height:m[1],area:m[0]*m[1]};d.setLayout(b),ou(d,y,!1,0),b=d.getLayout(),gi(g,function(x,w){var D=(g[w+1]||d).getValue();x.setLayout(U({dataExtent:[D,D],borderWidth:0,upperHeight:0},b))})}var _=a.getData().tree.root;_.setLayout(Yp(s,p,f),!0),a.setLayoutInfo(s),su(_,new ht(-s.x,-s.y,n,i),g,d,0)}};function ou(a,e,t,r){var n,i;if(!a.isRemoved()){var o=a.getLayout();n=o.width,i=o.height;var s=a.getModel(),l=s.get(iu),u=s.get(Vp)/2,v=lu(s),c=Math.max(l,v),h=l-u,f=c-u;a.setLayout({borderWidth:l,upperHeight:c,upperLabelHeight:v},!0),n=vr(n-2*h,0),i=vr(i-h-f,0);var p=n*i,d=Bp(a,s,p,e,t,r);if(d.length){var g={x:h,y:f,width:n,height:i},m=Kr(n,i),S=1/0,y=[];y.area=0;for(var b=0,_=d.length;b<_;){var x=d[b];y.push(x),y.area+=x.getLayout().area;var w=Up(y,m,e.squareRatio);w<=S?(b++,S=w):(y.area-=y.pop().getLayout().area,$o(y,m,g,u,!1),m=Kr(g.width,g.height),y.length=y.area=0,S=1/0)}if(y.length&&$o(y,m,g,u,!0),!t){var D=s.get("childrenVisibleMin");D!=null&&p<D&&(t=!0)}for(var b=0,_=d.length;b<_;b++)ou(d[b],e,t,r+1)}}}function Bp(a,e,t,r,n,i){var o=a.children||[],s=r.sort;s!=="asc"&&s!=="desc"&&(s=null);var l=r.leafDepth!=null&&r.leafDepth<=i;if(n&&!l)return a.viewChildren=[];o=Pt(o,function(f){return!f.isRemoved()}),Wp(o,s);var u=Hp(e,o,s);if(u.sum===0)return a.viewChildren=[];if(u.sum=Fp(e,t,u.sum,s,o),u.sum===0)return a.viewChildren=[];for(var v=0,c=o.length;v<c;v++){var h=o[v].getValue()/u.sum*t;o[v].setLayout({area:h})}return l&&(o.length&&a.setLayout({isLeafRoot:!0},!0),o.length=0),a.viewChildren=o,a.setLayout({dataExtent:u.dataExtent},!0),o}function Fp(a,e,t,r,n){if(!r)return t;for(var i=a.get("visibleMin"),o=n.length,s=o,l=o-1;l>=0;l--){var u=n[r==="asc"?o-l-1:l].getValue();u/t*e<i&&(s=l,t-=u)}return r==="asc"?n.splice(0,o-s):n.splice(s,o-s),t}function Wp(a,e){return e&&a.sort(function(t,r){var n=e==="asc"?t.getValue()-r.getValue():r.getValue()-t.getValue();return n===0?e==="asc"?t.dataIndex-r.dataIndex:r.dataIndex-t.dataIndex:n}),a}function Hp(a,e,t){for(var r=0,n=0,i=e.length;n<i;n++)r+=e[n].getValue();var o=a.get("visualDimension"),s;return!e||!e.length?s=[NaN,NaN]:o==="value"&&t?(s=[e[e.length-1].getValue(),e[0].getValue()],t==="asc"&&s.reverse()):(s=[1/0,-1/0],gi(e,function(l){var u=l.getValue(o);u<s[0]&&(s[0]=u),u>s[1]&&(s[1]=u)})),{sum:r,dataExtent:s}}function Up(a,e,t){for(var r=0,n=1/0,i=0,o=void 0,s=a.length;i<s;i++)o=a[i].getLayout().area,o&&(o<n&&(n=o),o>r&&(r=o));var l=a.area*a.area,u=e*e*t;return l?vr(u*r/l,l/(u*n)):1/0}function $o(a,e,t,r,n){var i=e===t.width?0:1,o=1-i,s=["x","y"],l=["width","height"],u=t[s[i]],v=e?a.area/e:0;(n||v>t[l[o]])&&(v=t[l[o]]);for(var c=0,h=a.length;c<h;c++){var f=a[c],p={},d=v?f.getLayout().area/v:0,g=p[l[o]]=vr(v-2*r,0),m=t[s[i]]+t[l[i]]-u,S=c===h-1||m<d?m:d,y=p[l[i]]=vr(S-2*r,0);p[s[o]]=t[s[o]]+Kr(r,g/2),p[s[i]]=u+Kr(r,y/2),u+=S,f.setLayout(p,!0)}t[s[o]]+=v,t[l[o]]-=v}function $p(a,e,t,r,n){var i=(e||{}).node,o=[r,n];if(!i||i===t)return o;for(var s,l=r*n,u=l*a.option.zoomToNodeRatio;s=i.parentNode;){for(var v=0,c=s.children,h=0,f=c.length;h<f;h++)v+=c[h].getValue();var p=i.getValue();if(p===0)return o;u*=v/p;var d=s.getModel(),g=d.get(iu),m=Math.max(g,lu(d));u+=4*g*g+(3*g+m)*Math.pow(u,.5),u>$i&&(u=$i),i=s}u<l&&(u=l);var S=Math.pow(u/l,.5);return[r*S,n*S]}function Yp(a,e,t){if(e)return{x:e.x,y:e.y};var r={x:0,y:0};if(!t)return r;var n=t.node,i=n.getLayout();if(!i)return r;for(var o=[i.width/2,i.height/2],s=n;s;){var l=s.getLayout();o[0]+=l.x,o[1]+=l.y,s=s.parentNode}return{x:a.width/2-o[0],y:a.height/2-o[1]}}function su(a,e,t,r,n){var i=a.getLayout(),o=t[n],s=o&&o===a;if(!(o&&!s||n===t.length&&a!==r)){a.setLayout({isInView:!0,invisible:!s&&!e.intersect(i),isAboveViewRoot:s},!0);var l=new ht(e.x-i.x,e.y-i.y,e.width,e.height);gi(a.viewChildren||[],function(u){su(u,l,t,r,n+1)})}}function lu(a){return a.get(Gp)?a.get(zp):0}function Xp(a){a.registerSeriesModel(gp),a.registerChartView(Ap),a.registerVisual(Mp),a.registerLayout(Op),dp(a)}function Zp(a){var e=a.findComponents({mainType:"legend"});!e||!e.length||a.eachSeriesByType("graph",function(t){var r=t.getCategoriesData(),n=t.getGraph(),i=n.data,o=r.mapArray(r.getName);i.filterSelf(function(s){var l=i.getItemModel(s),u=l.getShallow("category");if(u!=null){jt(u)&&(u=o[u]);for(var v=0;v<e.length;v++)if(!e[v].isSelected(u))return!1}return!0})})}function qp(a){var e={};a.eachSeriesByType("graph",function(t){var r=t.getCategoriesData(),n=t.getData(),i={};r.each(function(o){var s=r.getName(o);i["ec-"+s]=o;var l=r.getItemModel(o),u=l.getModel("itemStyle").getItemStyle();u.fill||(u.fill=t.getColorFromPalette(s,e)),r.setItemVisual(o,"style",u);for(var v=["symbol","symbolSize","symbolKeepAspect"],c=0;c<v.length;c++){var h=l.getShallow(v[c],!0);h!=null&&r.setItemVisual(o,v[c],h)}}),r.count()&&n.each(function(o){var s=n.getItemModel(o),l=s.getShallow("category");if(l!=null){rt(l)&&(l=i["ec-"+l]);var u=r.getItemVisual(l,"style"),v=n.ensureUniqueItemVisual(o,"style");U(v,u);for(var c=["symbol","symbolSize","symbolKeepAspect"],h=0;h<c.length;h++)n.setItemVisual(o,c[h],r.getItemVisual(l,c[h]))}})})}function Nr(a){return a instanceof Array||(a=[a,a]),a}function jp(a){a.eachSeriesByType("graph",function(e){var t=e.getGraph(),r=e.getEdgeData(),n=Nr(e.get("edgeSymbol")),i=Nr(e.get("edgeSymbolSize"));r.setVisual("fromSymbol",n&&n[0]),r.setVisual("toSymbol",n&&n[1]),r.setVisual("fromSymbolSize",i&&i[0]),r.setVisual("toSymbolSize",i&&i[1]),r.setVisual("style",e.getModel("lineStyle").getLineStyle()),r.each(function(o){var s=r.getItemModel(o),l=t.getEdgeByIndex(o),u=Nr(s.getShallow("symbol",!0)),v=Nr(s.getShallow("symbolSize",!0)),c=s.getModel("lineStyle").getLineStyle(),h=r.ensureUniqueItemVisual(o,"style");switch(U(h,c),h.stroke){case"source":{var f=l.node1.getVisual("style");h.stroke=f&&f.fill;break}case"target":{var f=l.node2.getVisual("style");h.stroke=f&&f.fill;break}}u[0]&&l.setVisual("fromSymbol",u[0]),u[1]&&l.setVisual("toSymbol",u[1]),v[0]&&l.setVisual("fromSymbolSize",v[0]),v[1]&&l.setVisual("toSymbolSize",v[1])})})}var Nn="-->",wa=function(a){return a.get("autoCurveness")||null},uu=function(a,e){var t=wa(a),r=20,n=[];if(jt(t))r=t;else if(H(t)){a.__curvenessList=t;return}e>r&&(r=e);var i=r%2?r+2:r+3;n=[];for(var o=0;o<i;o++)n.push((o%2?o+1:o)/10*(o%2?-1:1));a.__curvenessList=n},cr=function(a,e,t){var r=[a.id,a.dataIndex].join("."),n=[e.id,e.dataIndex].join(".");return[t.uid,r,n].join(Nn)},vu=function(a){var e=a.split(Nn);return[e[0],e[2],e[1]].join(Nn)},Kp=function(a,e){var t=cr(a.node1,a.node2,e);return e.__edgeMap[t]},Jp=function(a,e){var t=kn(cr(a.node1,a.node2,e),e),r=kn(cr(a.node2,a.node1,e),e);return t+r},kn=function(a,e){var t=e.__edgeMap;return t[a]?t[a].length:0};function Qp(a){wa(a)&&(a.__curvenessList=[],a.__edgeMap={},uu(a))}function td(a,e,t,r){if(wa(t)){var n=cr(a,e,t),i=t.__edgeMap,o=i[vu(n)];i[n]&&!o?i[n].isForward=!0:o&&i[n]&&(o.isForward=!0,i[n].isForward=!1),i[n]=i[n]||[],i[n].push(r)}}function yi(a,e,t,r){var n=wa(e),i=H(n);if(!n)return null;var o=Kp(a,e);if(!o)return null;for(var s=-1,l=0;l<o.length;l++)if(o[l]===t){s=l;break}var u=Jp(a,e);uu(e,u),a.lineStyle=a.lineStyle||{};var v=cr(a.node1,a.node2,e),c=e.__curvenessList,h=i||u%2?0:1;if(o.isForward)return c[h+s];var f=vu(v),p=kn(f,e),d=c[s+p+h];return r?i?n&&n[0]===0?(p+h)%2?d:-d:((p%2?0:1)+h)%2?d:-d:(p+h)%2?d:-d:c[s+p+h]}function cu(a){var e=a.coordinateSystem;if(!(e&&e.type!=="view")){var t=a.getGraph();t.eachNode(function(r){var n=r.getModel();r.setLayout([+n.get("x"),+n.get("y")])}),mi(t,a)}}function mi(a,e){a.eachEdge(function(t,r){var n=xr(t.getModel().get(["lineStyle","curveness"]),-yi(t,e,r,!0),0),i=ce(t.node1.getLayout()),o=ce(t.node2.getLayout()),s=[i,o];+n&&s.push([(i[0]+o[0])/2-(i[1]-o[1])*n,(i[1]+o[1])/2-(o[0]-i[0])*n]),t.setLayout(s)})}function ed(a,e){a.eachSeriesByType("graph",function(t){var r=t.get("layout"),n=t.coordinateSystem;if(n&&n.type!=="view"){var i=t.getData(),o=[];C(n.dimensions,function(h){o=o.concat(i.mapDimensionsAll(h))});for(var s=0;s<i.count();s++){for(var l=[],u=!1,v=0;v<o.length;v++){var c=i.get(o[v],s);isNaN(c)||(u=!0),l.push(c)}u?i.setItemLayout(s,n.dataToPoint(l)):i.setItemLayout(s,[NaN,NaN])}mi(i.graph,t)}else(!r||r==="none")&&cu(t)})}function er(a){var e=a.coordinateSystem;if(e.type!=="view")return 1;var t=a.option.nodeScaleRatio,r=e.scaleX,n=e.getZoom(),i=(n-1)*t+1;return i/r}function rr(a){var e=a.getVisual("symbolSize");return e instanceof Array&&(e=(e[0]+e[1])/2),+e}var Yo=Math.PI,Oa=[];function Si(a,e,t,r){var n=a.coordinateSystem;if(!(n&&n.type!=="view")){var i=n.getBoundingRect(),o=a.getData(),s=o.graph,l=i.width/2+i.x,u=i.height/2+i.y,v=Math.min(i.width,i.height)/2,c=o.count();if(o.setLayout({cx:l,cy:u}),!!c){if(t){var h=n.pointToData(r),f=h[0],p=h[1],d=[f-l,p-u];br(d,d),Gv(d,d,v),t.setLayout([l+d[0],u+d[1]],!0);var g=a.get(["circular","rotateLabel"]);hu(t,g,l,u)}rd[e](a,s,o,v,l,u,c),s.eachEdge(function(m,S){var y=xr(m.getModel().get(["lineStyle","curveness"]),yi(m,a,S),0),b=ce(m.node1.getLayout()),_=ce(m.node2.getLayout()),x,w=(b[0]+_[0])/2,D=(b[1]+_[1])/2;+y&&(y*=3,x=[l*y+w*(1-y),u*y+D*(1-y)]),m.setLayout([b,_,x])})}}}var rd={value:function(a,e,t,r,n,i,o){var s=0,l=t.getSum("value"),u=Math.PI*2/(l||o);e.eachNode(function(v){var c=v.getValue("value"),h=u*(l?c:1)/2;s+=h,v.setLayout([r*Math.cos(s)+n,r*Math.sin(s)+i]),s+=h})},symbolSize:function(a,e,t,r,n,i,o){var s=0;Oa.length=o;var l=er(a);e.eachNode(function(c){var h=rr(c);isNaN(h)&&(h=2),h<0&&(h=0),h*=l;var f=Math.asin(h/2/r);isNaN(f)&&(f=Yo/2),Oa[c.dataIndex]=f,s+=f*2});var u=(2*Yo-s)/o/2,v=0;e.eachNode(function(c){var h=u+Oa[c.dataIndex];v+=h,(!c.getLayout()||!c.getLayout().fixed)&&c.setLayout([r*Math.cos(v)+n,r*Math.sin(v)+i]),v+=h})}};function hu(a,e,t,r){var n=a.getGraphicEl();if(n){var i=a.getModel(),o=i.get(["label","rotate"])||0,s=n.getSymbolPath();if(e){var l=a.getLayout(),u=Math.atan2(l[1]-r,l[0]-t);u<0&&(u=Math.PI*2+u);var v=l[0]<t;v&&(u=u-Math.PI);var c=v?"left":"right";s.setTextConfig({rotation:-u,position:c,origin:"center"});var h=s.ensureState("emphasis");U(h.textConfig||(h.textConfig={}),{position:c})}else s.setTextConfig({rotation:o*=Math.PI/180})}}function ad(a){a.eachSeriesByType("graph",function(e){e.get("layout")==="circular"&&Si(e,"symbolSize")})}var Le=Tn;function nd(a,e,t){for(var r=a,n=e,i=t.rect,o=i.width,s=i.height,l=[i.x+o/2,i.y+s/2],u=t.gravity==null?.1:t.gravity,v=0;v<r.length;v++){var c=r[v];c.p||(c.p=zv(o*(Math.random()-.5)+l[0],s*(Math.random()-.5)+l[1])),c.pp=ce(c.p),c.edges=null}var h=t.friction==null?.6:t.friction,f=h,p,d;return{warmUp:function(){f=h*.8},setFixed:function(g){r[g].fixed=!0},setUnfixed:function(g){r[g].fixed=!1},beforeStep:function(g){p=g},afterStep:function(g){d=g},step:function(g){p&&p(r,n);for(var m=[],S=r.length,y=0;y<n.length;y++){var b=n[y];if(!b.ignoreForceLayout){var _=b.n1,x=b.n2;Ne(m,x.p,_.p);var w=Yi(m)-b.d,D=x.w/(_.w+x.w);isNaN(D)&&(D=0),br(m,m),!_.fixed&&Le(_.p,_.p,m,D*w*f),!x.fixed&&Le(x.p,x.p,m,-(1-D)*w*f)}}for(var y=0;y<S;y++){var T=r[y];T.fixed||(Ne(m,l,T.p),Le(T.p,T.p,m,u*f))}for(var y=0;y<S;y++)for(var _=r[y],I=y+1;I<S;I++){var x=r[I];Ne(m,x.p,_.p);var w=Yi(m);w===0&&(Ov(m,Math.random()-.5,Math.random()-.5),w=1);var A=(_.rep+x.rep)/w/w;!_.fixed&&Le(_.pp,_.pp,m,A),!x.fixed&&Le(x.pp,x.pp,m,-A)}for(var P=[],y=0;y<S;y++){var T=r[y];T.fixed||(Ne(P,T.p,T.pp),Le(T.p,T.p,P,f),xt(T.pp,T.p))}f=f*.992;var E=f<.01;d&&d(r,n,E),g&&g(E)}}}function id(a){a.eachSeriesByType("graph",function(e){var t=e.coordinateSystem;if(!(t&&t.type!=="view"))if(e.get("layout")==="force"){var r=e.preservedPoints||{},n=e.getGraph(),i=n.data,o=n.edgeData,s=e.getModel("force"),l=s.get("initLayout");e.preservedPoints?i.each(function(y){var b=i.getId(y);i.setItemLayout(y,r[b]||[NaN,NaN])}):!l||l==="none"?cu(e):l==="circular"&&Si(e,"value");var u=i.getDataExtent("value"),v=o.getDataExtent("value"),c=s.get("repulsion"),h=s.get("edgeLength"),f=H(c)?c:[c,c],p=H(h)?h:[h,h];p=[p[1],p[0]];var d=i.mapArray("value",function(y,b){var _=i.getItemLayout(b),x=Ht(y,u,f);return isNaN(x)&&(x=(f[0]+f[1])/2),{w:x,rep:x,fixed:i.getItemModel(b).get("fixed"),p:!_||isNaN(_[0])||isNaN(_[1])?null:_}}),g=o.mapArray("value",function(y,b){var _=n.getEdgeByIndex(b),x=Ht(y,v,p);isNaN(x)&&(x=(p[0]+p[1])/2);var w=_.getModel(),D=xr(_.getModel().get(["lineStyle","curveness"]),-yi(_,e,b,!0),0);return{n1:d[_.node1.dataIndex],n2:d[_.node2.dataIndex],d:x,curveness:D,ignoreForceLayout:w.get("ignoreForceLayout")}}),m=t.getBoundingRect(),S=nd(d,g,{rect:m,gravity:s.get("gravity"),friction:s.get("friction")});S.beforeStep(function(y,b){for(var _=0,x=y.length;_<x;_++)y[_].fixed&&xt(y[_].p,n.getNodeByIndex(_).getLayout())}),S.afterStep(function(y,b,_){for(var x=0,w=y.length;x<w;x++)y[x].fixed||n.getNodeByIndex(x).setLayout(y[x].p),r[i.getId(x)]=y[x].p;for(var x=0,w=b.length;x<w;x++){var D=b[x],T=n.getEdgeByIndex(x),I=D.n1.p,A=D.n2.p,P=T.getLayout();P=P?P.slice():[],P[0]=P[0]||[],P[1]=P[1]||[],xt(P[0],I),xt(P[1],A),+D.curveness&&(P[2]=[(I[0]+A[0])/2-(I[1]-A[1])*D.curveness,(I[1]+A[1])/2-(A[0]-I[0])*D.curveness]),T.setLayout(P)}}),e.forceLayout=S,e.preservedPoints=r,S.step()}else e.forceLayout=null})}function od(a,e,t){var r=U(a.getBoxLayoutParams(),{aspect:t});return te(r,{width:e.getWidth(),height:e.getHeight()})}function sd(a,e){var t=[];return a.eachSeriesByType("graph",function(r){var n=r.get("coordinateSystem");if(!n||n==="view"){var i=r.getData(),o=i.mapArray(function(g){var m=i.getItemModel(g);return[+m.get("x"),+m.get("y")]}),s=[],l=[];ya(o,s,l),l[0]-s[0]===0&&(l[0]+=1,s[0]-=1),l[1]-s[1]===0&&(l[1]+=1,s[1]-=1);var u=(l[0]-s[0])/(l[1]-s[1]),v=od(r,e,u);isNaN(u)&&(s=[v.x,v.y],l=[v.x+v.width,v.y+v.height]);var c=l[0]-s[0],h=l[1]-s[1],f=v.width,p=v.height,d=r.coordinateSystem=new Dr;d.zoomLimit=r.get("scaleLimit"),d.setBoundingRect(s[0],s[1],c,h),d.setViewRect(v.x,v.y,f,p),d.setCenter(r.get("center"),e),d.setZoom(r.get("zoom")),t.push(d)}}),t}var Xo=we.prototype,Ba=wl.prototype,fu=function(){function a(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return a}();(function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e})(fu);function Fa(a){return isNaN(+a.cpx1)||isNaN(+a.cpy1)}var ld=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="ec-line",r}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new fu},e.prototype.buildPath=function(t,r){Fa(r)?Xo.buildPath.call(this,t,r):Ba.buildPath.call(this,t,r)},e.prototype.pointAt=function(t){return Fa(this.shape)?Xo.pointAt.call(this,t):Ba.pointAt.call(this,t)},e.prototype.tangentAt=function(t){var r=this.shape,n=Fa(r)?[r.x2-r.x1,r.y2-r.y1]:Ba.tangentAt.call(this,t);return br(n,n)},e}(wt),Wa=["fromSymbol","toSymbol"];function Zo(a){return"_"+a+"Type"}function qo(a,e,t){var r=e.getItemVisual(t,a);if(!r||r==="none")return r;var n=e.getItemVisual(t,a+"Size"),i=e.getItemVisual(t,a+"Rotate"),o=e.getItemVisual(t,a+"Offset"),s=e.getItemVisual(t,a+"KeepAspect"),l=dr(n),u=_r(o||0,l);return r+l+u+(i||"")+(s||"")}function jo(a,e,t){var r=e.getItemVisual(t,a);if(!(!r||r==="none")){var n=e.getItemVisual(t,a+"Size"),i=e.getItemVisual(t,a+"Rotate"),o=e.getItemVisual(t,a+"Offset"),s=e.getItemVisual(t,a+"KeepAspect"),l=dr(n),u=_r(o||0,l),v=qt(r,-l[0]/2+u[0],-l[1]/2+u[1],l[0],l[1],null,s);return v.__specifiedRotation=i==null||isNaN(i)?void 0:+i*Math.PI/180||0,v.name=a,v}}function ud(a){var e=new ld({name:"line",subPixelOptimize:!0});return Vn(e.shape,a),e}function Vn(a,e){a.x1=e[0][0],a.y1=e[0][1],a.x2=e[1][0],a.y2=e[1][1],a.percent=1;var t=e[2];t?(a.cpx1=t[0],a.cpy1=t[1]):(a.cpx1=NaN,a.cpy1=NaN)}var xi=function(a){V(e,a);function e(t,r,n){var i=a.call(this)||this;return i._createLine(t,r,n),i}return e.prototype._createLine=function(t,r,n){var i=t.hostModel,o=t.getItemLayout(r),s=ud(o);s.shape.percent=0,Tt(s,{shape:{percent:1}},i,r),this.add(s),C(Wa,function(l){var u=jo(l,t,r);this.add(u),this[Zo(l)]=qo(l,t,r)},this),this._updateCommonStl(t,r,n)},e.prototype.updateData=function(t,r,n){var i=t.hostModel,o=this.childOfName("line"),s=t.getItemLayout(r),l={shape:{}};Vn(l.shape,s),lt(o,l,i,r),C(Wa,function(u){var v=qo(u,t,r),c=Zo(u);if(this[c]!==v){this.remove(this.childOfName(u));var h=jo(u,t,r);this.add(h)}this[c]=v},this),this._updateCommonStl(t,r,n)},e.prototype.getLinePath=function(){return this.childAt(0)},e.prototype._updateCommonStl=function(t,r,n){var i=t.hostModel,o=this.childOfName("line"),s=n&&n.emphasisLineStyle,l=n&&n.blurLineStyle,u=n&&n.selectLineStyle,v=n&&n.labelStatesModels,c=n&&n.emphasisDisabled,h=n&&n.focus,f=n&&n.blurScope;if(!n||t.hasItemOption){var p=t.getItemModel(r),d=p.getModel("emphasis");s=d.getModel("lineStyle").getLineStyle(),l=p.getModel(["blur","lineStyle"]).getLineStyle(),u=p.getModel(["select","lineStyle"]).getLineStyle(),c=d.get("disabled"),h=d.get("focus"),f=d.get("blurScope"),v=It(p)}var g=t.getItemVisual(r,"style"),m=g.stroke;o.useStyle(g),o.style.fill=null,o.style.strokeNoScale=!0,o.ensureState("emphasis").style=s,o.ensureState("blur").style=l,o.ensureState("select").style=u,C(Wa,function(x){var w=this.childOfName(x);if(w){w.setColor(m),w.style.opacity=g.opacity;for(var D=0;D<In.length;D++){var T=In[D],I=o.getState(T);if(I){var A=I.style||{},P=w.ensureState(T),E=P.style||(P.style={});A.stroke!=null&&(E[w.__isEmptyBrush?"stroke":"fill"]=A.stroke),A.opacity!=null&&(E.opacity=A.opacity)}}w.markRedraw()}},this);var S=i.getRawValue(r);Ot(this,v,{labelDataIndex:r,labelFetcher:{getFormattedLabel:function(x,w){return i.getFormattedLabel(x,w,t.dataType)}},inheritColor:m||"#000",defaultOpacity:g.opacity,defaultText:(S==null?t.getName(r):isFinite(S)?ti(S):S)+""});var y=this.getTextContent();if(y){var b=v.normal;y.__align=y.style.align,y.__verticalAlign=y.style.verticalAlign,y.__position=b.get("position")||"middle";var _=b.get("distance");H(_)||(_=[_,_]),y.__labelDistance=_}this.setTextConfig({position:null,local:!0,inside:!1}),ct(this,h,f,c)},e.prototype.highlight=function(){ei(this)},e.prototype.downplay=function(){ri(this)},e.prototype.updateLayout=function(t,r){this.setLinePoints(t.getItemLayout(r))},e.prototype.setLinePoints=function(t){var r=this.childOfName("line");Vn(r.shape,t),r.dirty()},e.prototype.beforeUpdate=function(){var t=this,r=t.childOfName("fromSymbol"),n=t.childOfName("toSymbol"),i=t.getTextContent();if(!r&&!n&&(!i||i.ignore))return;for(var o=1,s=this.parent;s;)s.scaleX&&(o/=s.scaleX),s=s.parent;var l=t.childOfName("line");if(!this.__dirty&&!l.__dirty)return;var u=l.shape.percent,v=l.pointAt(0),c=l.pointAt(u),h=Ne([],c,v);br(h,h);function f(I,A){var P=I.__specifiedRotation;if(P==null){var E=l.tangentAt(A);I.attr("rotation",(A===1?-1:1)*Math.PI/2-Math.atan2(E[1],E[0]))}else I.attr("rotation",P)}if(r&&(r.setPosition(v),f(r,0),r.scaleX=r.scaleY=o*u,r.markRedraw()),n&&(n.setPosition(c),f(n,1),n.scaleX=n.scaleY=o*u,n.markRedraw()),i&&!i.ignore){i.x=i.y=0,i.originX=i.originY=0;var p=void 0,d=void 0,g=i.__labelDistance,m=g[0]*o,S=g[1]*o,y=u/2,b=l.tangentAt(y),_=[b[1],-b[0]],x=l.pointAt(y);_[1]>0&&(_[0]=-_[0],_[1]=-_[1]);var w=b[0]<0?-1:1;if(i.__position!=="start"&&i.__position!=="end"){var D=-Math.atan2(b[1],b[0]);c[0]<v[0]&&(D=Math.PI+D),i.rotation=D}var T=void 0;switch(i.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":T=-S,d="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":T=S,d="top";break;default:T=0,d="middle"}switch(i.__position){case"end":i.x=h[0]*m+c[0],i.y=h[1]*S+c[1],p=h[0]>.8?"left":h[0]<-.8?"right":"center",d=h[1]>.8?"top":h[1]<-.8?"bottom":"middle";break;case"start":i.x=-h[0]*m+v[0],i.y=-h[1]*S+v[1],p=h[0]>.8?"right":h[0]<-.8?"left":"center",d=h[1]>.8?"bottom":h[1]<-.8?"top":"middle";break;case"insideStartTop":case"insideStart":case"insideStartBottom":i.x=m*w+v[0],i.y=v[1]+T,p=b[0]<0?"right":"left",i.originX=-m*w,i.originY=-T;break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":i.x=x[0],i.y=x[1]+T,p="center",i.originY=-T;break;case"insideEndTop":case"insideEnd":case"insideEndBottom":i.x=-m*w+c[0],i.y=c[1]+T,p=b[0]>=0?"right":"left",i.originX=m*w,i.originY=-T;break}i.scaleX=i.scaleY=o,i.setStyle({verticalAlign:i.__verticalAlign||d,align:i.__align||p})}},e}(Y),bi=function(){function a(e){this.group=new Y,this._LineCtor=e||xi}return a.prototype.updateData=function(e){var t=this;this._progressiveEls=null;var r=this,n=r.group,i=r._lineData;r._lineData=e,i||n.removeAll();var o=Ko(e);e.diff(i).add(function(s){t._doAdd(e,s,o)}).update(function(s,l){t._doUpdate(i,e,l,s,o)}).remove(function(s){n.remove(i.getItemGraphicEl(s))}).execute()},a.prototype.updateLayout=function(){var e=this._lineData;e&&e.eachItemGraphicEl(function(t,r){t.updateLayout(e,r)},this)},a.prototype.incrementalPrepareUpdate=function(e){this._seriesScope=Ko(e),this._lineData=null,this.group.removeAll()},a.prototype.incrementalUpdate=function(e,t){this._progressiveEls=[];function r(s){!s.isGroup&&!vd(s)&&(s.incremental=!0,s.ensureState("emphasis").hoverLayer=!0)}for(var n=e.start;n<e.end;n++){var i=t.getItemLayout(n);if(Ha(i)){var o=new this._LineCtor(t,n,this._seriesScope);o.traverse(r),this.group.add(o),t.setItemGraphicEl(n,o),this._progressiveEls.push(o)}}},a.prototype.remove=function(){this.group.removeAll()},a.prototype.eachRendered=function(e){ma(this._progressiveEls||this.group,e)},a.prototype._doAdd=function(e,t,r){var n=e.getItemLayout(t);if(Ha(n)){var i=new this._LineCtor(e,t,r);e.setItemGraphicEl(t,i),this.group.add(i)}},a.prototype._doUpdate=function(e,t,r,n,i){var o=e.getItemGraphicEl(r);if(!Ha(t.getItemLayout(n))){this.group.remove(o);return}o?o.updateData(t,n,i):o=new this._LineCtor(t,n,i),t.setItemGraphicEl(n,o),this.group.add(o)},a}();function vd(a){return a.animators&&a.animators.length>0}function Ko(a){var e=a.hostModel,t=e.getModel("emphasis");return{lineStyle:e.getModel("lineStyle").getLineStyle(),emphasisLineStyle:t.getModel(["lineStyle"]).getLineStyle(),blurLineStyle:e.getModel(["blur","lineStyle"]).getLineStyle(),selectLineStyle:e.getModel(["select","lineStyle"]).getLineStyle(),emphasisDisabled:t.get("disabled"),blurScope:t.get("blurScope"),focus:t.get("focus"),labelStatesModels:It(e)}}function Jo(a){return isNaN(a[0])||isNaN(a[1])}function Ha(a){return a&&!Jo(a[0])&&!Jo(a[1])}var Ua=[],$a=[],Ya=[],Ce=Ll,Xa=Fv,Qo=Math.abs;function ts(a,e,t){for(var r=a[0],n=a[1],i=a[2],o=1/0,s,l=t*t,u=.1,v=.1;v<=.9;v+=.1){Ua[0]=Ce(r[0],n[0],i[0],v),Ua[1]=Ce(r[1],n[1],i[1],v);var c=Qo(Xa(Ua,e)-l);c<o&&(o=c,s=v)}for(var h=0;h<32;h++){var f=s+u;$a[0]=Ce(r[0],n[0],i[0],s),$a[1]=Ce(r[1],n[1],i[1],s),Ya[0]=Ce(r[0],n[0],i[0],f),Ya[1]=Ce(r[1],n[1],i[1],f);var c=Xa($a,e)-l;if(Qo(c)<.01)break;var p=Xa(Ya,e)-l;u/=2,c<0?p>=0?s=s+u:s=s-u:p>=0?s=s-u:s=s+u}return s}function Za(a,e){var t=[],r=Bv,n=[[],[],[]],i=[[],[]],o=[];e/=2,a.eachEdge(function(s,l){var u=s.getLayout(),v=s.getVisual("fromSymbol"),c=s.getVisual("toSymbol");u.__original||(u.__original=[ce(u[0]),ce(u[1])],u[2]&&u.__original.push(ce(u[2])));var h=u.__original;if(u[2]!=null){if(xt(n[0],h[0]),xt(n[1],h[2]),xt(n[2],h[1]),v&&v!=="none"){var f=rr(s.node1),p=ts(n,h[0],f*e);r(n[0][0],n[1][0],n[2][0],p,t),n[0][0]=t[3],n[1][0]=t[4],r(n[0][1],n[1][1],n[2][1],p,t),n[0][1]=t[3],n[1][1]=t[4]}if(c&&c!=="none"){var f=rr(s.node2),p=ts(n,h[1],f*e);r(n[0][0],n[1][0],n[2][0],p,t),n[1][0]=t[1],n[2][0]=t[2],r(n[0][1],n[1][1],n[2][1],p,t),n[1][1]=t[1],n[2][1]=t[2]}xt(u[0],n[0]),xt(u[1],n[2]),xt(u[2],n[1])}else{if(xt(i[0],h[0]),xt(i[1],h[1]),Ne(o,i[1],i[0]),br(o,o),v&&v!=="none"){var f=rr(s.node1);Tn(i[0],i[0],o,f*e)}if(c&&c!=="none"){var f=rr(s.node2);Tn(i[1],i[1],o,-f*e)}xt(u[0],i[0]),xt(u[1],i[1])}})}function es(a){return a.type==="view"}var cd=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){var n=new la,i=new bi,o=this.group;this._controller=new pa(r.getZr()),this._controllerHost={target:o},o.add(n.group),o.add(i.group),this._symbolDraw=n,this._lineDraw=i,this._firstRender=!0},e.prototype.render=function(t,r,n){var i=this,o=t.coordinateSystem;this._model=t;var s=this._symbolDraw,l=this._lineDraw,u=this.group;if(es(o)){var v={x:o.x,y:o.y,scaleX:o.scaleX,scaleY:o.scaleY};this._firstRender?u.attr(v):lt(u,v,t)}Za(t.getGraph(),er(t));var c=t.getData();s.updateData(c);var h=t.getEdgeData();l.updateData(h),this._updateNodeAndLinkScale(),this._updateController(t,r,n),clearTimeout(this._layoutTimeout);var f=t.forceLayout,p=t.get(["force","layoutAnimation"]);f&&this._startForceLayoutIteration(f,p);var d=t.get("layout");c.graph.eachNode(function(y){var b=y.dataIndex,_=y.getGraphicEl(),x=y.getModel();if(_){_.off("drag").off("dragend");var w=x.get("draggable");w&&_.on("drag",function(T){switch(d){case"force":f.warmUp(),!i._layouting&&i._startForceLayoutIteration(f,p),f.setFixed(b),c.setItemLayout(b,[_.x,_.y]);break;case"circular":c.setItemLayout(b,[_.x,_.y]),y.setLayout({fixed:!0},!0),Si(t,"symbolSize",y,[T.offsetX,T.offsetY]),i.updateLayout(t);break;case"none":default:c.setItemLayout(b,[_.x,_.y]),mi(t.getGraph(),t),i.updateLayout(t);break}}).on("dragend",function(){f&&f.setUnfixed(b)}),_.setDraggable(w,!!x.get("cursor"));var D=x.get(["emphasis","focus"]);D==="adjacency"&&(nt(_).focus=y.getAdjacentDataIndices())}}),c.graph.eachEdge(function(y){var b=y.getGraphicEl(),_=y.getModel().get(["emphasis","focus"]);b&&_==="adjacency"&&(nt(b).focus={edge:[y.dataIndex],node:[y.node1.dataIndex,y.node2.dataIndex]})});var g=t.get("layout")==="circular"&&t.get(["circular","rotateLabel"]),m=c.getLayout("cx"),S=c.getLayout("cy");c.graph.eachNode(function(y){hu(y,g,m,S)}),this._firstRender=!1},e.prototype.dispose=function(){this.remove(),this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype._startForceLayoutIteration=function(t,r){var n=this;(function i(){t.step(function(o){n.updateLayout(n._model),(n._layouting=!o)&&(r?n._layoutTimeout=setTimeout(i,16):i())})})()},e.prototype._updateController=function(t,r,n){var i=this,o=this._controller,s=this._controllerHost,l=this.group;if(o.setPointerChecker(function(u,v,c){var h=l.getBoundingRect();return h.applyTransform(l.transform),h.contain(v,c)&&!jn(u,n,t)}),!es(t.coordinateSystem)){o.disable();return}o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){ui(s,u.dx,u.dy),n.dispatchAction({seriesId:t.id,type:"graphRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){vi(s,u.scale,u.originX,u.originY),n.dispatchAction({seriesId:t.id,type:"graphRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),i._updateNodeAndLinkScale(),Za(t.getGraph(),er(t)),i._lineDraw.updateLayout(),n.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(){var t=this._model,r=t.getData(),n=er(t);r.eachItemGraphicEl(function(i,o){i&&i.setSymbolScale(n)})},e.prototype.updateLayout=function(t){Za(t.getGraph(),er(t)),this._symbolDraw.updateLayout(),this._lineDraw.updateLayout()},e.prototype.remove=function(){clearTimeout(this._layoutTimeout),this._layouting=!1,this._layoutTimeout=null,this._symbolDraw&&this._symbolDraw.remove(),this._lineDraw&&this._lineDraw.remove()},e.type="graph",e}(ft);function Pe(a){return"_EC_"+a}var hd=function(){function a(e){this.type="graph",this.nodes=[],this.edges=[],this._nodesMap={},this._edgesMap={},this._directed=e||!1}return a.prototype.isDirected=function(){return this._directed},a.prototype.addNode=function(e,t){e=e==null?""+t:""+e;var r=this._nodesMap;if(!r[Pe(e)]){var n=new xe(e,t);return n.hostGraph=this,this.nodes.push(n),r[Pe(e)]=n,n}},a.prototype.getNodeByIndex=function(e){var t=this.data.getRawIndex(e);return this.nodes[t]},a.prototype.getNodeById=function(e){return this._nodesMap[Pe(e)]},a.prototype.addEdge=function(e,t,r){var n=this._nodesMap,i=this._edgesMap;if(jt(e)&&(e=this.nodes[e]),jt(t)&&(t=this.nodes[t]),e instanceof xe||(e=n[Pe(e)]),t instanceof xe||(t=n[Pe(t)]),!(!e||!t)){var o=e.id+"-"+t.id,s=new pu(e,t,r);return s.hostGraph=this,this._directed&&(e.outEdges.push(s),t.inEdges.push(s)),e.edges.push(s),e!==t&&t.edges.push(s),this.edges.push(s),i[o]=s,s}},a.prototype.getEdgeByIndex=function(e){var t=this.edgeData.getRawIndex(e);return this.edges[t]},a.prototype.getEdge=function(e,t){e instanceof xe&&(e=e.id),t instanceof xe&&(t=t.id);var r=this._edgesMap;return this._directed?r[e+"-"+t]:r[e+"-"+t]||r[t+"-"+e]},a.prototype.eachNode=function(e,t){for(var r=this.nodes,n=r.length,i=0;i<n;i++)r[i].dataIndex>=0&&e.call(t,r[i],i)},a.prototype.eachEdge=function(e,t){for(var r=this.edges,n=r.length,i=0;i<n;i++)r[i].dataIndex>=0&&r[i].node1.dataIndex>=0&&r[i].node2.dataIndex>=0&&e.call(t,r[i],i)},a.prototype.breadthFirstTraverse=function(e,t,r,n){if(t instanceof xe||(t=this._nodesMap[Pe(t)]),!!t){for(var i=r==="out"?"outEdges":r==="in"?"inEdges":"edges",o=0;o<this.nodes.length;o++)this.nodes[o].__visited=!1;if(!e.call(n,t,null))for(var s=[t];s.length;)for(var l=s.shift(),u=l[i],o=0;o<u.length;o++){var v=u[o],c=v.node1===l?v.node2:v.node1;if(!c.__visited){if(e.call(n,c,l))return;s.push(c),c.__visited=!0}}}},a.prototype.update=function(){for(var e=this.data,t=this.edgeData,r=this.nodes,n=this.edges,i=0,o=r.length;i<o;i++)r[i].dataIndex=-1;for(var i=0,o=e.count();i<o;i++)r[e.getRawIndex(i)].dataIndex=i;t.filterSelf(function(s){var l=n[t.getRawIndex(s)];return l.node1.dataIndex>=0&&l.node2.dataIndex>=0});for(var i=0,o=n.length;i<o;i++)n[i].dataIndex=-1;for(var i=0,o=t.count();i<o;i++)n[t.getRawIndex(i)].dataIndex=i},a.prototype.clone=function(){for(var e=new a(this._directed),t=this.nodes,r=this.edges,n=0;n<t.length;n++)e.addNode(t[n].id,t[n].dataIndex);for(var n=0;n<r.length;n++){var i=r[n];e.addEdge(i.node1.id,i.node2.id,i.dataIndex)}return e},a}(),xe=function(){function a(e,t){this.inEdges=[],this.outEdges=[],this.edges=[],this.dataIndex=-1,this.id=e==null?"":e,this.dataIndex=t==null?-1:t}return a.prototype.degree=function(){return this.edges.length},a.prototype.inDegree=function(){return this.inEdges.length},a.prototype.outDegree=function(){return this.outEdges.length},a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,r=t.data.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getAdjacentDataIndices=function(){for(var e={edge:[],node:[]},t=0;t<this.edges.length;t++){var r=this.edges[t];r.dataIndex<0||(e.edge.push(r.dataIndex),e.node.push(r.node1.dataIndex,r.node2.dataIndex))}return e},a.prototype.getTrajectoryDataIndices=function(){for(var e=j(),t=j(),r=0;r<this.edges.length;r++){var n=this.edges[r];if(!(n.dataIndex<0)){e.set(n.dataIndex,!0);for(var i=[n.node1],o=[n.node2],s=0;s<i.length;){var l=i[s];s++,t.set(l.dataIndex,!0);for(var u=0;u<l.inEdges.length;u++)e.set(l.inEdges[u].dataIndex,!0),i.push(l.inEdges[u].node1)}for(s=0;s<o.length;){var v=o[s];s++,t.set(v.dataIndex,!0);for(var u=0;u<v.outEdges.length;u++)e.set(v.outEdges[u].dataIndex,!0),o.push(v.outEdges[u].node2)}}}return{edge:e.keys(),node:t.keys()}},a}(),pu=function(){function a(e,t,r){this.dataIndex=-1,this.node1=e,this.node2=t,this.dataIndex=r==null?-1:r}return a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,r=t.edgeData.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getAdjacentDataIndices=function(){return{edge:[this.dataIndex],node:[this.node1.dataIndex,this.node2.dataIndex]}},a.prototype.getTrajectoryDataIndices=function(){var e=j(),t=j();e.set(this.dataIndex,!0);for(var r=[this.node1],n=[this.node2],i=0;i<r.length;){var o=r[i];i++,t.set(o.dataIndex,!0);for(var s=0;s<o.inEdges.length;s++)e.set(o.inEdges[s].dataIndex,!0),r.push(o.inEdges[s].node1)}for(i=0;i<n.length;){var l=n[i];i++,t.set(l.dataIndex,!0);for(var s=0;s<l.outEdges.length;s++)e.set(l.outEdges[s].dataIndex,!0),n.push(l.outEdges[s].node2)}return{edge:e.keys(),node:t.keys()}},a}();function du(a,e){return{getValue:function(t){var r=this[a][e];return r.getStore().get(r.getDimensionIndex(t||"value"),this.dataIndex)},setVisual:function(t,r){this.dataIndex>=0&&this[a][e].setItemVisual(this.dataIndex,t,r)},getVisual:function(t){return this[a][e].getItemVisual(this.dataIndex,t)},setLayout:function(t,r){this.dataIndex>=0&&this[a][e].setItemLayout(this.dataIndex,t,r)},getLayout:function(){return this[a][e].getItemLayout(this.dataIndex)},getGraphicEl:function(){return this[a][e].getItemGraphicEl(this.dataIndex)},getRawIndex:function(){return this[a][e].getRawIndex(this.dataIndex)}}}Kt(xe,du("hostGraph","data"));Kt(pu,du("hostGraph","edgeData"));function gu(a,e,t,r,n){for(var i=new hd(r),o=0;o<a.length;o++)i.addNode(kt(a[o].id,a[o].name,o),o);for(var s=[],l=[],u=0,o=0;o<e.length;o++){var v=e[o],c=v.source,h=v.target;i.addEdge(c,h,u)&&(l.push(v),s.push(kt(Sr(v.id,null),c+" > "+h)),u++)}var f=t.get("coordinateSystem"),p;if(f==="cartesian2d"||f==="polar")p=Be(a,t);else{var d=Cl.get(f),g=d?d.dimensions||[]:[];zt(g,"value")<0&&g.concat(["value"]);var m=Qn(a,{coordDimensions:g,encodeDefine:t.getEncode()}).dimensions;p=new Mt(m,t),p.initData(a)}var S=new Mt(["value"],t);return S.initData(l,s),n&&n(p,S),Jl({mainData:p,struct:i,structAttr:"graph",datas:{node:p,edge:S},datasAttr:{node:"data",edge:"edgeData"}}),i.update(),i}var fd=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments);var r=this;function n(){return r._categoriesData}this.legendVisualProvider=new va(n,n),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeDefaultAndTheme=function(t){a.prototype.mergeDefaultAndTheme.apply(this,arguments),ga(t,"edgeLabel",["show"])},e.prototype.getInitialData=function(t,r){var n=t.edges||t.links||[],i=t.data||t.nodes||[],o=this;if(i&&n){Qp(this);var s=gu(i,n,this,!0,l);return C(s.edges,function(u){td(u.node1,u.node2,this,u.dataIndex)},this),s.data}function l(u,v){u.wrapMethod("getItemModel",function(p){var d=o._categoriesModels,g=p.getShallow("category"),m=d[g];return m&&(m.parentModel=p.parentModel,p.parentModel=m),p});var c=Yt.prototype.getModel;function h(p,d){var g=c.call(this,p,d);return g.resolveParentPath=f,g}v.wrapMethod("getItemModel",function(p){return p.resolveParentPath=f,p.getModel=h,p});function f(p){if(p&&(p[0]==="label"||p[1]==="label")){var d=p.slice();return p[0]==="label"?d[0]="edgeLabel":p[1]==="label"&&(d[1]="edgeLabel"),d}return p}}},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.getCategoriesData=function(){return this._categoriesData},e.prototype.formatTooltip=function(t,r,n){if(n==="edge"){var i=this.getData(),o=this.getDataParams(t,n),s=i.graph.getEdgeByIndex(t),l=i.getName(s.node1.dataIndex),u=i.getName(s.node2.dataIndex),v=[];return l!=null&&v.push(l),u!=null&&v.push(u),At("nameValue",{name:v.join(" > "),value:o.value,noValue:o.value==null})}var c=Wv({series:this,dataIndex:t,multipleSeries:r});return c},e.prototype._updateCategoriesData=function(){var t=B(this.option.categories||[],function(n){return n.value!=null?n:U({value:0},n)}),r=new Mt(["value"],this);r.initData(t),this._categoriesData=r,this._categoriesModels=r.mapArray(function(n){return r.getItemModel(n)})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.isAnimationEnabled=function(){return a.prototype.isAnimationEnabled.call(this)&&!(this.get("layout")==="force"&&this.get(["force","layoutAnimation"]))},e.type="series.graph",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={z:2,coordinateSystem:"view",legendHoverLink:!0,layout:null,circular:{rotateLabel:!1},force:{initLayout:null,repulsion:[0,50],gravity:.1,friction:.6,edgeLength:30,layoutAnimation:!0},left:"center",top:"center",symbol:"circle",symbolSize:10,edgeSymbol:["none","none"],edgeSymbolSize:10,edgeLabel:{position:"middle",distance:5},draggable:!1,roam:!1,center:null,zoom:1,nodeScaleRatio:.6,label:{show:!1,formatter:"{b}"},itemStyle:{},lineStyle:{color:"#aaa",width:1,opacity:.5},emphasis:{scale:!0,label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(dt),pd={type:"graphRoam",event:"graphRoam",update:"none"};function dd(a){a.registerChartView(cd),a.registerSeriesModel(fd),a.registerProcessor(Zp),a.registerVisual(qp),a.registerVisual(jp),a.registerLayout(ed),a.registerLayout(a.PRIORITY.VISUAL.POST_CHART_LAYOUT,ad),a.registerLayout(id),a.registerCoordinateSystem("graphView",{dimensions:Dr.dimensions,create:sd}),a.registerAction({type:"focusNodeAdjacency",event:"focusNodeAdjacency",update:"series:focusNodeAdjacency"},or),a.registerAction({type:"unfocusNodeAdjacency",event:"unfocusNodeAdjacency",update:"series:unfocusNodeAdjacency"},or),a.registerAction(pd,function(e,t,r){t.eachComponent({mainType:"series",query:e},function(n){var i=n.coordinateSystem,o=hi(i,e,void 0,r);n.setCenter&&n.setCenter(o.center),n.setZoom&&n.setZoom(o.zoom)})})}var gd=function(){function a(){this.angle=0,this.width=10,this.r=10,this.x=0,this.y=0}return a}(),yd=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="pointer",r}return e.prototype.getDefaultShape=function(){return new gd},e.prototype.buildPath=function(t,r){var n=Math.cos,i=Math.sin,o=r.r,s=r.width,l=r.angle,u=r.x-n(l)*s*(s>=o/3?1:2),v=r.y-i(l)*s*(s>=o/3?1:2);l=r.angle-Math.PI/2,t.moveTo(u,v),t.lineTo(r.x+n(l)*s,r.y+i(l)*s),t.lineTo(r.x+n(r.angle)*o,r.y+i(r.angle)*o),t.lineTo(r.x-n(l)*s,r.y-i(l)*s),t.lineTo(u,v)},e}(wt);function md(a,e){var t=a.get("center"),r=e.getWidth(),n=e.getHeight(),i=Math.min(r,n),o=z(t[0],e.getWidth()),s=z(t[1],e.getHeight()),l=z(a.get("radius"),i/2);return{cx:o,cy:s,r:l}}function kr(a,e){var t=a==null?"":a+"";return e&&(rt(e)?t=e.replace("{value}",t):ot(e)&&(t=e(a))),t}var Sd=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){this.group.removeAll();var i=t.get(["axisLine","lineStyle","color"]),o=md(t,n);this._renderMain(t,r,n,i,o),this._data=t.getData()},e.prototype.dispose=function(){},e.prototype._renderMain=function(t,r,n,i,o){var s=this.group,l=t.get("clockwise"),u=-t.get("startAngle")/180*Math.PI,v=-t.get("endAngle")/180*Math.PI,c=t.getModel("axisLine"),h=c.get("roundCap"),f=h?Xi:sr,p=c.get("show"),d=c.getModel("lineStyle"),g=d.get("width"),m=[u,v];Hv(m,!l),u=m[0],v=m[1];for(var S=v-u,y=u,b=[],_=0;p&&_<i.length;_++){var x=Math.min(Math.max(i[_][0],0),1);v=u+S*x;var w=new f({shape:{startAngle:y,endAngle:v,cx:o.cx,cy:o.cy,clockwise:l,r0:o.r-g,r:o.r},silent:!0});w.setStyle({fill:i[_][1]}),w.setStyle(d.getLineStyle(["color","width"])),b.push(w),y=v}b.reverse(),C(b,function(T){return s.add(T)});var D=function(T){if(T<=0)return i[0][1];var I;for(I=0;I<i.length;I++)if(i[I][0]>=T&&(I===0?0:i[I-1][0])<T)return i[I][1];return i[I-1][1]};this._renderTicks(t,r,n,D,o,u,v,l,g),this._renderTitleAndDetail(t,r,n,D,o),this._renderAnchor(t,o),this._renderPointer(t,r,n,D,o,u,v,l,g)},e.prototype._renderTicks=function(t,r,n,i,o,s,l,u,v){for(var c=this.group,h=o.cx,f=o.cy,p=o.r,d=+t.get("min"),g=+t.get("max"),m=t.getModel("splitLine"),S=t.getModel("axisTick"),y=t.getModel("axisLabel"),b=t.get("splitNumber"),_=S.get("splitNumber"),x=z(m.get("length"),p),w=z(S.get("length"),p),D=s,T=(l-s)/b,I=T/_,A=m.getModel("lineStyle").getLineStyle(),P=S.getModel("lineStyle").getLineStyle(),E=m.get("distance"),L,M,R=0;R<=b;R++){if(L=Math.cos(D),M=Math.sin(D),m.get("show")){var N=E?E+v:v,G=new we({shape:{x1:L*(p-N)+h,y1:M*(p-N)+f,x2:L*(p-x-N)+h,y2:M*(p-x-N)+f},style:A,silent:!0});A.stroke==="auto"&&G.setStyle({stroke:i(R/b)}),c.add(G)}if(y.get("show")){var N=y.get("distance")+E,O=kr(ti(R/b*(g-d)+d),y.get("formatter")),F=i(R/b),W=L*(p-x-N)+h,K=M*(p-x-N)+f,J=y.get("rotate"),Z=0;J==="radial"?(Z=-D+2*Math.PI,Z>Math.PI/2&&(Z+=Math.PI)):J==="tangential"?Z=-D-Math.PI/2:jt(J)&&(Z=J*Math.PI/180),Z===0?c.add(new Vt({style:bt(y,{text:O,x:W,y:K,verticalAlign:M<-.8?"top":M>.8?"bottom":"middle",align:L<-.4?"left":L>.4?"right":"center"},{inheritColor:F}),silent:!0})):c.add(new Vt({style:bt(y,{text:O,x:W,y:K,verticalAlign:"middle",align:"center"},{inheritColor:F}),silent:!0,originX:W,originY:K,rotation:Z}))}if(S.get("show")&&R!==b){var N=S.get("distance");N=N?N+v:v;for(var Q=0;Q<=_;Q++){L=Math.cos(D),M=Math.sin(D);var yt=new we({shape:{x1:L*(p-N)+h,y1:M*(p-N)+f,x2:L*(p-w-N)+h,y2:M*(p-w-N)+f},silent:!0,style:P});P.stroke==="auto"&&yt.setStyle({stroke:i((R+Q/_)/b)}),c.add(yt),D+=I}D-=I}else D+=T}},e.prototype._renderPointer=function(t,r,n,i,o,s,l,u,v){var c=this.group,h=this._data,f=this._progressEls,p=[],d=t.get(["pointer","show"]),g=t.getModel("progress"),m=g.get("show"),S=t.getData(),y=S.mapDimension("value"),b=+t.get("min"),_=+t.get("max"),x=[b,_],w=[s,l];function D(I,A){var P=S.getItemModel(I),E=P.getModel("pointer"),L=z(E.get("width"),o.r),M=z(E.get("length"),o.r),R=t.get(["pointer","icon"]),N=E.get("offsetCenter"),G=z(N[0],o.r),O=z(N[1],o.r),F=E.get("keepAspect"),W;return R?W=qt(R,G-L/2,O-M,L,M,null,F):W=new yd({shape:{angle:-Math.PI/2,width:L,r:M,x:G,y:O}}),W.rotation=-(A+Math.PI/2),W.x=o.cx,W.y=o.cy,W}function T(I,A){var P=g.get("roundCap"),E=P?Xi:sr,L=g.get("overlap"),M=L?g.get("width"):v/S.count(),R=L?o.r-M:o.r-(I+1)*M,N=L?o.r:o.r-I*M,G=new E({shape:{startAngle:s,endAngle:A,cx:o.cx,cy:o.cy,clockwise:u,r0:R,r:N}});return L&&(G.z2=Ht(S.get(y,I),[b,_],[100,0],!0)),G}(m||d)&&(S.diff(h).add(function(I){var A=S.get(y,I);if(d){var P=D(I,s);Tt(P,{rotation:-((isNaN(+A)?w[0]:Ht(A,x,w,!0))+Math.PI/2)},t),c.add(P),S.setItemGraphicEl(I,P)}if(m){var E=T(I,s),L=g.get("clip");Tt(E,{shape:{endAngle:Ht(A,x,w,L)}},t),c.add(E),Zi(t.seriesIndex,S.dataType,I,E),p[I]=E}}).update(function(I,A){var P=S.get(y,I);if(d){var E=h.getItemGraphicEl(A),L=E?E.rotation:s,M=D(I,L);M.rotation=L,lt(M,{rotation:-((isNaN(+P)?w[0]:Ht(P,x,w,!0))+Math.PI/2)},t),c.add(M),S.setItemGraphicEl(I,M)}if(m){var R=f[A],N=R?R.shape.endAngle:s,G=T(I,N),O=g.get("clip");lt(G,{shape:{endAngle:Ht(P,x,w,O)}},t),c.add(G),Zi(t.seriesIndex,S.dataType,I,G),p[I]=G}}).execute(),S.each(function(I){var A=S.getItemModel(I),P=A.getModel("emphasis"),E=P.get("focus"),L=P.get("blurScope"),M=P.get("disabled");if(d){var R=S.getItemGraphicEl(I),N=S.getItemVisual(I,"style"),G=N.fill;if(R instanceof Fe){var O=R.style;R.useStyle(U({image:O.image,x:O.x,y:O.y,width:O.width,height:O.height},N))}else R.useStyle(N),R.type!=="pointer"&&R.setColor(G);R.setStyle(A.getModel(["pointer","itemStyle"]).getItemStyle()),R.style.fill==="auto"&&R.setStyle("fill",i(Ht(S.get(y,I),x,[0,1],!0))),R.z2EmphasisLift=0,Et(R,A),ct(R,E,L,M)}if(m){var F=p[I];F.useStyle(S.getItemVisual(I,"style")),F.setStyle(A.getModel(["progress","itemStyle"]).getItemStyle()),F.z2EmphasisLift=0,Et(F,A),ct(F,E,L,M)}}),this._progressEls=p)},e.prototype._renderAnchor=function(t,r){var n=t.getModel("anchor"),i=n.get("show");if(i){var o=n.get("size"),s=n.get("icon"),l=n.get("offsetCenter"),u=n.get("keepAspect"),v=qt(s,r.cx-o/2+z(l[0],r.r),r.cy-o/2+z(l[1],r.r),o,o,null,u);v.z2=n.get("showAbove")?1:0,v.setStyle(n.getModel("itemStyle").getItemStyle()),this.group.add(v)}},e.prototype._renderTitleAndDetail=function(t,r,n,i,o){var s=this,l=t.getData(),u=l.mapDimension("value"),v=+t.get("min"),c=+t.get("max"),h=new Y,f=[],p=[],d=t.isAnimationEnabled(),g=t.get(["pointer","showAbove"]);l.diff(this._data).add(function(m){f[m]=new Vt({silent:!0}),p[m]=new Vt({silent:!0})}).update(function(m,S){f[m]=s._titleEls[S],p[m]=s._detailEls[S]}).execute(),l.each(function(m){var S=l.getItemModel(m),y=l.get(u,m),b=new Y,_=i(Ht(y,[v,c],[0,1],!0)),x=S.getModel("title");if(x.get("show")){var w=x.get("offsetCenter"),D=o.cx+z(w[0],o.r),T=o.cy+z(w[1],o.r),I=f[m];I.attr({z2:g?0:2,style:bt(x,{x:D,y:T,text:l.getName(m),align:"center",verticalAlign:"middle"},{inheritColor:_})}),b.add(I)}var A=S.getModel("detail");if(A.get("show")){var P=A.get("offsetCenter"),E=o.cx+z(P[0],o.r),L=o.cy+z(P[1],o.r),M=z(A.get("width"),o.r),R=z(A.get("height"),o.r),N=t.get(["progress","show"])?l.getItemVisual(m,"style").fill:_,I=p[m],G=A.get("formatter");I.attr({z2:g?0:2,style:bt(A,{x:E,y:L,text:kr(y,G),width:isNaN(M)?null:M,height:isNaN(R)?null:R,align:"center",verticalAlign:"middle"},{inheritColor:N})}),Uv(I,{normal:A},y,function(F){return kr(F,G)}),d&&gl(I,m,l,t,{getFormattedLabel:function(F,W,K,J,Z,Q){return kr(Q?Q.interpolatedValue:y,G)}}),b.add(I)}h.add(b)}),this.group.add(h),this._titleEls=f,this._detailEls=p},e.type="gauge",e}(ft),xd=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="itemStyle",t}return e.prototype.getInitialData=function(t,r){return gr(this,["value"])},e.type="series.gauge",e.defaultOption={z:2,colorBy:"data",center:["50%","50%"],legendHoverLink:!0,radius:"75%",startAngle:225,endAngle:-45,clockwise:!0,min:0,max:100,splitNumber:10,axisLine:{show:!0,roundCap:!1,lineStyle:{color:[[1,"#E6EBF8"]],width:10}},progress:{show:!1,overlap:!0,width:10,roundCap:!1,clip:!0},splitLine:{show:!0,length:10,distance:10,lineStyle:{color:"#63677A",width:3,type:"solid"}},axisTick:{show:!0,splitNumber:5,length:6,distance:10,lineStyle:{color:"#63677A",width:1,type:"solid"}},axisLabel:{show:!0,distance:15,color:"#464646",fontSize:12,rotate:0},pointer:{icon:null,offsetCenter:[0,0],show:!0,showAbove:!0,length:"60%",width:6,keepAspect:!1},anchor:{show:!1,showAbove:!1,size:6,icon:"circle",offsetCenter:[0,0],keepAspect:!1,itemStyle:{color:"#fff",borderWidth:0,borderColor:"#5470c6"}},title:{show:!0,offsetCenter:[0,"20%"],color:"#464646",fontSize:16,valueAnimation:!1},detail:{show:!0,backgroundColor:"rgba(0,0,0,0)",borderWidth:0,borderColor:"#ccc",width:100,height:null,padding:[5,10],offsetCenter:[0,"40%"],color:"#464646",fontSize:30,fontWeight:"bold",lineHeight:30,valueAnimation:!1}},e}(dt);function bd(a){a.registerChartView(Sd),a.registerSeriesModel(xd)}var _d=["itemStyle","opacity"],wd=function(a){V(e,a);function e(t,r){var n=a.call(this)||this,i=n,o=new ge,s=new Vt;return i.setTextContent(s),n.setTextGuideLine(o),n.updateData(t,r,!0),n}return e.prototype.updateData=function(t,r,n){var i=this,o=t.hostModel,s=t.getItemModel(r),l=t.getItemLayout(r),u=s.getModel("emphasis"),v=s.get(_d);v=v==null?1:v,n||fe(i),i.useStyle(t.getItemVisual(r,"style")),i.style.lineJoin="round",n?(i.setShape({points:l.points}),i.style.opacity=0,Tt(i,{style:{opacity:v}},o,r)):lt(i,{style:{opacity:v},shape:{points:l.points}},o,r),Et(i,s),this._updateLabel(t,r),ct(this,u.get("focus"),u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t,r){var n=this,i=this.getTextGuideLine(),o=n.getTextContent(),s=t.hostModel,l=t.getItemModel(r),u=t.getItemLayout(r),v=u.label,c=t.getItemVisual(r,"style"),h=c.fill;Ot(o,It(l),{labelFetcher:t.hostModel,labelDataIndex:r,defaultOpacity:c.opacity,defaultText:t.getName(r)},{normal:{align:v.textAlign,verticalAlign:v.verticalAlign}}),n.setTextConfig({local:!0,inside:!!v.inside,insideStroke:h,outsideFill:h});var f=v.linePoints;i.setShape({points:f}),n.textGuideLineConfig={anchor:f?new ue(f[0][0],f[0][1]):null},lt(o,{style:{x:v.x,y:v.y}},s,r),o.attr({rotation:v.rotation,originX:v.x,originY:v.y,z2:10}),pl(n,dl(l),{stroke:h})},e}(ne),Dd=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.ignoreLabelLineUpdate=!0,t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this._data,s=this.group;i.diff(o).add(function(l){var u=new wd(i,l);i.setItemGraphicEl(l,u),s.add(u)}).update(function(l,u){var v=o.getItemGraphicEl(u);v.updateData(i,l),s.add(v),i.setItemGraphicEl(l,v)}).remove(function(l){var u=o.getItemGraphicEl(l);$v(u,t,l)}).execute(),this._data=i},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.prototype.dispose=function(){},e.type="funnel",e}(ft),Td=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new va(et(this.getData,this),et(this.getRawData,this)),this._defaultLabelLine(t)},e.prototype.getInitialData=function(t,r){return gr(this,{coordDimensions:["value"],encodeDefaulter:st(bl,this)})},e.prototype._defaultLabelLine=function(t){ga(t,"labelLine",["show"]);var r=t.labelLine,n=t.emphasis.labelLine;r.show=r.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},e.prototype.getDataParams=function(t){var r=this.getData(),n=a.prototype.getDataParams.call(this,t),i=r.mapDimension("value"),o=r.getSum(i);return n.percent=o?+(r.get(i,t)/o*100).toFixed(2):0,n.$vars.push("percent"),n},e.type="series.funnel",e.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",left:80,top:60,right:80,bottom:60,minSize:"0%",maxSize:"100%",sort:"descending",orient:"vertical",gap:0,funnelAlign:"center",label:{show:!0,position:"outer"},labelLine:{show:!0,length:20,lineStyle:{width:1}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(dt);function Id(a,e){return te(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function Ad(a,e){for(var t=a.mapDimension("value"),r=a.mapArray(t,function(l){return l}),n=[],i=e==="ascending",o=0,s=a.count();o<s;o++)n[o]=o;return ot(e)?n.sort(e):e!=="none"&&n.sort(function(l,u){return i?r[l]-r[u]:r[u]-r[l]}),n}function Ld(a){var e=a.hostModel,t=e.get("orient");a.each(function(r){var n=a.getItemModel(r),i=n.getModel("label"),o=i.get("position"),s=n.getModel("labelLine"),l=a.getItemLayout(r),u=l.points,v=o==="inner"||o==="inside"||o==="center"||o==="insideLeft"||o==="insideRight",c,h,f,p;if(v)o==="insideLeft"?(h=(u[0][0]+u[3][0])/2+5,f=(u[0][1]+u[3][1])/2,c="left"):o==="insideRight"?(h=(u[1][0]+u[2][0])/2-5,f=(u[1][1]+u[2][1])/2,c="right"):(h=(u[0][0]+u[1][0]+u[2][0]+u[3][0])/4,f=(u[0][1]+u[1][1]+u[2][1]+u[3][1])/4,c="center"),p=[[h,f],[h,f]];else{var d=void 0,g=void 0,m=void 0,S=void 0,y=s.get("length");o==="left"?(d=(u[3][0]+u[0][0])/2,g=(u[3][1]+u[0][1])/2,m=d-y,h=m-5,c="right"):o==="right"?(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,m=d+y,h=m+5,c="left"):o==="top"?(d=(u[3][0]+u[0][0])/2,g=(u[3][1]+u[0][1])/2,S=g-y,f=S-5,c="center"):o==="bottom"?(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,S=g+y,f=S+5,c="center"):o==="rightTop"?(d=t==="horizontal"?u[3][0]:u[1][0],g=t==="horizontal"?u[3][1]:u[1][1],t==="horizontal"?(S=g-y,f=S-5,c="center"):(m=d+y,h=m+5,c="top")):o==="rightBottom"?(d=u[2][0],g=u[2][1],t==="horizontal"?(S=g+y,f=S+5,c="center"):(m=d+y,h=m+5,c="bottom")):o==="leftTop"?(d=u[0][0],g=t==="horizontal"?u[0][1]:u[1][1],t==="horizontal"?(S=g-y,f=S-5,c="center"):(m=d-y,h=m-5,c="right")):o==="leftBottom"?(d=t==="horizontal"?u[1][0]:u[3][0],g=t==="horizontal"?u[1][1]:u[2][1],t==="horizontal"?(S=g+y,f=S+5,c="center"):(m=d-y,h=m-5,c="right")):(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,t==="horizontal"?(S=g+y,f=S+5,c="center"):(m=d+y,h=m+5,c="left")),t==="horizontal"?(m=d,h=m):(S=g,f=S),p=[[d,g],[m,S]]}l.label={linePoints:p,x:h,y:f,verticalAlign:"middle",textAlign:c,inside:v}})}function Cd(a,e){a.eachSeriesByType("funnel",function(t){var r=t.getData(),n=r.mapDimension("value"),i=t.get("sort"),o=Id(t,e),s=t.get("orient"),l=o.width,u=o.height,v=Ad(r,i),c=o.x,h=o.y,f=s==="horizontal"?[z(t.get("minSize"),u),z(t.get("maxSize"),u)]:[z(t.get("minSize"),l),z(t.get("maxSize"),l)],p=r.getDataExtent(n),d=t.get("min"),g=t.get("max");d==null&&(d=Math.min(p[0],0)),g==null&&(g=p[1]);var m=t.get("funnelAlign"),S=t.get("gap"),y=s==="horizontal"?l:u,b=(y-S*(r.count()-1))/r.count(),_=function(L,M){if(s==="horizontal"){var R=r.get(n,L)||0,N=Ht(R,[d,g],f,!0),G=void 0;switch(m){case"top":G=h;break;case"center":G=h+(u-N)/2;break;case"bottom":G=h+(u-N);break}return[[M,G],[M,G+N]]}var O=r.get(n,L)||0,F=Ht(O,[d,g],f,!0),W;switch(m){case"left":W=c;break;case"center":W=c+(l-F)/2;break;case"right":W=c+l-F;break}return[[W,M],[W+F,M]]};i==="ascending"&&(b=-b,S=-S,s==="horizontal"?c+=l:h+=u,v=v.reverse());for(var x=0;x<v.length;x++){var w=v[x],D=v[x+1],T=r.getItemModel(w);if(s==="horizontal"){var I=T.get(["itemStyle","width"]);I==null?I=b:(I=z(I,l),i==="ascending"&&(I=-I));var A=_(w,c),P=_(D,c+I);c+=I+S,r.setItemLayout(w,{points:A.concat(P.slice().reverse())})}else{var E=T.get(["itemStyle","height"]);E==null?E=b:(E=z(E,u),i==="ascending"&&(E=-E));var A=_(w,h),P=_(D,h+E);h+=E+S,r.setItemLayout(w,{points:A.concat(P.slice().reverse())})}}Ld(r)})}function Pd(a){a.registerChartView(Dd),a.registerSeriesModel(Td),a.registerLayout(Cd),a.registerProcessor(fa("funnel"))}var Md=.3,Ed=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._dataGroup=new Y,t._initialized=!1,t}return e.prototype.init=function(){this.group.add(this._dataGroup)},e.prototype.render=function(t,r,n,i){this._progressiveEls=null;var o=this._dataGroup,s=t.getData(),l=this._data,u=t.coordinateSystem,v=u.dimensions,c=as(t);s.diff(l).add(h).update(f).remove(p).execute();function h(g){var m=rs(s,o,g,v,u);qa(m,s,g,c)}function f(g,m){var S=l.getItemGraphicEl(m),y=yu(s,g,v,u);s.setItemGraphicEl(g,S),lt(S,{shape:{points:y}},t,g),fe(S),qa(S,s,g,c)}function p(g){var m=l.getItemGraphicEl(g);o.remove(m)}if(!this._initialized){this._initialized=!0;var d=Rd(u,t,function(){setTimeout(function(){o.removeClipPath()})});o.setClipPath(d)}this._data=s},e.prototype.incrementalPrepareRender=function(t,r,n){this._initialized=!0,this._data=null,this._dataGroup.removeAll()},e.prototype.incrementalRender=function(t,r,n){for(var i=r.getData(),o=r.coordinateSystem,s=o.dimensions,l=as(r),u=this._progressiveEls=[],v=t.start;v<t.end;v++){var c=rs(i,this._dataGroup,v,s,o);c.incremental=!0,qa(c,i,v,l),u.push(c)}},e.prototype.remove=function(){this._dataGroup&&this._dataGroup.removeAll(),this._data=null},e.type="parallel",e}(ft);function Rd(a,e,t){var r=a.model,n=a.getRect(),i=new Lt({shape:{x:n.x,y:n.y,width:n.width,height:n.height}}),o=r.get("layout")==="horizontal"?"width":"height";return i.setShape(o,0),Tt(i,{shape:{width:n.width,height:n.height}},e,t),i}function yu(a,e,t,r){for(var n=[],i=0;i<t.length;i++){var o=t[i],s=a.get(a.mapDimension(o),e);Nd(s,r.getAxis(o).type)||n.push(r.dataToPoint(s,o))}return n}function rs(a,e,t,r,n){var i=yu(a,t,r,n),o=new ge({shape:{points:i},z2:10});return e.add(o),a.setItemGraphicEl(t,o),o}function as(a){var e=a.get("smooth",!0);return e===!0&&(e=Md),e=Yv(e),Xv(e)&&(e=0),{smooth:e}}function qa(a,e,t,r){a.useStyle(e.getItemVisual(t,"style")),a.style.fill=null,a.setShape("smooth",r.smooth);var n=e.getItemModel(t),i=n.getModel("emphasis");Et(a,n,"lineStyle"),ct(a,i.get("focus"),i.get("blurScope"),i.get("disabled"))}function Nd(a,e){return e==="category"?a==null:a==null||isNaN(a)}var kd=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.getInitialData=function(t,r){return Be(null,this,{useEncodeDefaulter:et(Vd,null,this)})},e.prototype.getRawIndicesByActiveState=function(t){var r=this.coordinateSystem,n=this.getData(),i=[];return r.eachActiveState(n,function(o,s){t===o&&i.push(n.getRawIndex(s))}),i},e.type="series.parallel",e.dependencies=["parallel"],e.defaultOption={z:2,coordinateSystem:"parallel",parallelIndex:0,label:{show:!1},inactiveOpacity:.05,activeOpacity:1,lineStyle:{width:1,opacity:.45,type:"solid"},emphasis:{label:{show:!1}},progressive:500,smooth:!1,animationEasing:"linear"},e}(dt);function Vd(a){var e=a.ecModel.getComponent("parallel",a.get("parallelIndex"));if(e){var t={};return C(e.dimensions,function(r){var n=Gd(r);t[r]=n}),t}}function Gd(a){return+a.replace("dim","")}var zd=["lineStyle","opacity"],Od={seriesType:"parallel",reset:function(a,e){var t=a.coordinateSystem,r={normal:a.get(["lineStyle","opacity"]),active:a.get("activeOpacity"),inactive:a.get("inactiveOpacity")};return{progress:function(n,i){t.eachActiveState(i,function(o,s){var l=r[o];if(o==="normal"&&i.hasItemOption){var u=i.getItemModel(s).get(zd,!0);u!=null&&(l=u)}var v=i.ensureUniqueItemVisual(s,"style");v.opacity=l},n.start,n.end)}}}};function Bd(a){Fd(a),Wd(a)}function Fd(a){if(!a.parallel){var e=!1;C(a.series,function(t){t&&t.type==="parallel"&&(e=!0)}),e&&(a.parallel=[{}])}}function Wd(a){var e=Ut(a.parallelAxis);C(e,function(t){if(wr(t)){var r=t.parallelIndex||0,n=Ut(a.parallel)[r];n&&n.parallelAxisDefault&&Bt(t,n.parallelAxisDefault,!1)}})}var Hd=5,Ud=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){this._model=t,this._api=n,this._handlers||(this._handlers={},C($d,function(i,o){n.getZr().on(o,this._handlers[o]=et(i,this))},this)),Pl(this,"_throttledDispatchExpand",t.get("axisExpandRate"),"fixRate")},e.prototype.dispose=function(t,r){Zv(this,"_throttledDispatchExpand"),C(this._handlers,function(n,i){r.getZr().off(i,n)}),this._handlers=null},e.prototype._throttledDispatchExpand=function(t){this._dispatchExpand(t)},e.prototype._dispatchExpand=function(t){t&&this._api.dispatchAction(U({type:"parallelAxisExpand"},t))},e.type="parallel",e}(Qt),$d={mousedown:function(a){ja(this,"click")&&(this._mouseDownPoint=[a.offsetX,a.offsetY])},mouseup:function(a){var e=this._mouseDownPoint;if(ja(this,"click")&&e){var t=[a.offsetX,a.offsetY],r=Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2);if(r>Hd)return;var n=this._model.coordinateSystem.getSlidedAxisExpandWindow([a.offsetX,a.offsetY]);n.behavior!=="none"&&this._dispatchExpand({axisExpandWindow:n.axisExpandWindow})}this._mouseDownPoint=null},mousemove:function(a){if(!(this._mouseDownPoint||!ja(this,"mousemove"))){var e=this._model,t=e.coordinateSystem.getSlidedAxisExpandWindow([a.offsetX,a.offsetY]),r=t.behavior;r==="jump"&&this._throttledDispatchExpand.debounceNextCall(e.get("axisExpandDebounce")),this._throttledDispatchExpand(r==="none"?null:{axisExpandWindow:t.axisExpandWindow,animation:r==="jump"?null:{duration:0}})}}};function ja(a,e){var t=a._model;return t.get("axisExpandable")&&t.get("axisExpandTriggerOn")===e}var Yd=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){a.prototype.init.apply(this,arguments),this.mergeOption({})},e.prototype.mergeOption=function(t){var r=this.option;t&&Bt(r,t,!0),this._initDimensions()},e.prototype.contains=function(t,r){var n=t.get("parallelIndex");return n!=null&&r.getComponent("parallel",n)===this},e.prototype.setAxisExpand=function(t){C(["axisExpandable","axisExpandCenter","axisExpandCount","axisExpandWidth","axisExpandWindow"],function(r){t.hasOwnProperty(r)&&(this.option[r]=t[r])},this)},e.prototype._initDimensions=function(){var t=this.dimensions=[],r=this.parallelAxisIndex=[],n=Pt(this.ecModel.queryComponents({mainType:"parallelAxis"}),function(i){return(i.get("parallelIndex")||0)===this.componentIndex},this);C(n,function(i){t.push("dim"+i.get("dim")),r.push(i.componentIndex)})},e.type="parallel",e.dependencies=["parallelAxis"],e.layoutMode="box",e.defaultOption={z:0,left:80,top:60,right:80,bottom:60,layout:"horizontal",axisExpandable:!1,axisExpandCenter:null,axisExpandCount:0,axisExpandWidth:50,axisExpandRate:17,axisExpandDebounce:50,axisExpandSlideTriggerArea:[-.15,.05,.4],axisExpandTriggerOn:"click",parallelAxisDefault:null},e}(Jt),Xd=function(a){V(e,a);function e(t,r,n,i,o){var s=a.call(this,t,r,n)||this;return s.type=i||"value",s.axisIndex=o,s}return e.prototype.isHorizontal=function(){return this.coordinateSystem.getModel().get("layout")!=="horizontal"},e}(ha),Ka=C,mu=Math.min,Su=Math.max,ns=Math.floor,Zd=Math.ceil,is=ti,qd=Math.PI,jd=function(){function a(e,t,r){this.type="parallel",this._axesMap=j(),this._axesLayout={},this.dimensions=e.dimensions,this._model=e,this._init(e,t,r)}return a.prototype._init=function(e,t,r){var n=e.dimensions,i=e.parallelAxisIndex;Ka(n,function(o,s){var l=i[s],u=t.getComponent("parallelAxis",l),v=this._axesMap.set(o,new Xd(o,Ml(u),[0,0],u.get("type"),l)),c=v.type==="category";v.onBand=c&&u.get("boundaryGap"),v.inverse=u.get("inverse"),u.axis=v,v.model=u,v.coordinateSystem=u.coordinateSystem=this},this)},a.prototype.update=function(e,t){this._updateAxesFromSeries(this._model,e)},a.prototype.containPoint=function(e){var t=this._makeLayoutInfo(),r=t.axisBase,n=t.layoutBase,i=t.pixelDimIndex,o=e[1-i],s=e[i];return o>=r&&o<=r+t.axisLength&&s>=n&&s<=n+t.layoutLength},a.prototype.getModel=function(){return this._model},a.prototype._updateAxesFromSeries=function(e,t){t.eachSeries(function(r){if(e.contains(r,t)){var n=r.getData();Ka(this.dimensions,function(i){var o=this._axesMap.get(i);o.scale.unionExtentFromData(n,n.mapDimension(i)),El(o.scale,o.model)},this)}},this)},a.prototype.resize=function(e,t){this._rect=te(e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),this._layoutAxes()},a.prototype.getRect=function(){return this._rect},a.prototype._makeLayoutInfo=function(){var e=this._model,t=this._rect,r=["x","y"],n=["width","height"],i=e.get("layout"),o=i==="horizontal"?0:1,s=t[n[o]],l=[0,s],u=this.dimensions.length,v=Vr(e.get("axisExpandWidth"),l),c=Vr(e.get("axisExpandCount")||0,[0,u]),h=e.get("axisExpandable")&&u>3&&u>c&&c>1&&v>0&&s>0,f=e.get("axisExpandWindow"),p;if(f)p=Vr(f[1]-f[0],l),f[1]=f[0]+p;else{p=Vr(v*(c-1),l);var d=e.get("axisExpandCenter")||ns(u/2);f=[v*d-p/2],f[1]=f[0]+p}var g=(s-p)/(u-c);g<3&&(g=0);var m=[ns(is(f[0]/v,1))+1,Zd(is(f[1]/v,1))-1],S=g/v*f[0];return{layout:i,pixelDimIndex:o,layoutBase:t[r[o]],layoutLength:s,axisBase:t[r[1-o]],axisLength:t[n[1-o]],axisExpandable:h,axisExpandWidth:v,axisCollapseWidth:g,axisExpandWindow:f,axisCount:u,winInnerIndices:m,axisExpandWindow0Pos:S}},a.prototype._layoutAxes=function(){var e=this._rect,t=this._axesMap,r=this.dimensions,n=this._makeLayoutInfo(),i=n.layout;t.each(function(o){var s=[0,n.axisLength],l=o.inverse?1:0;o.setExtent(s[l],s[1-l])}),Ka(r,function(o,s){var l=(n.axisExpandable?Jd:Kd)(s,n),u={horizontal:{x:l.position,y:n.axisLength},vertical:{x:0,y:l.position}},v={horizontal:qd/2,vertical:0},c=[u[i].x+e.x,u[i].y+e.y],h=v[i],f=yr();qn(f,f,h),Ge(f,f,c),this._axesLayout[o]={position:c,rotation:h,transform:f,axisNameAvailableWidth:l.axisNameAvailableWidth,axisLabelShow:l.axisLabelShow,nameTruncateMaxWidth:l.nameTruncateMaxWidth,tickDirection:1,labelDirection:1}},this)},a.prototype.getAxis=function(e){return this._axesMap.get(e)},a.prototype.dataToPoint=function(e,t){return this.axisCoordToPoint(this._axesMap.get(t).dataToCoord(e),t)},a.prototype.eachActiveState=function(e,t,r,n){r==null&&(r=0),n==null&&(n=e.count());var i=this._axesMap,o=this.dimensions,s=[],l=[];C(o,function(g){s.push(e.mapDimension(g)),l.push(i.get(g).model)});for(var u=this.hasAxisBrushed(),v=r;v<n;v++){var c=void 0;if(!u)c="normal";else{c="active";for(var h=e.getValues(s,v),f=0,p=o.length;f<p;f++){var d=l[f].getActiveState(h[f]);if(d==="inactive"){c="inactive";break}}}t(c,v)}},a.prototype.hasAxisBrushed=function(){for(var e=this.dimensions,t=this._axesMap,r=!1,n=0,i=e.length;n<i;n++)t.get(e[n]).model.getActiveState()!=="normal"&&(r=!0);return r},a.prototype.axisCoordToPoint=function(e,t){var r=this._axesLayout[t];return Rl([e,0],r.transform)},a.prototype.getAxisLayout=function(e){return $t(this._axesLayout[e])},a.prototype.getSlidedAxisExpandWindow=function(e){var t=this._makeLayoutInfo(),r=t.pixelDimIndex,n=t.axisExpandWindow.slice(),i=n[1]-n[0],o=[0,t.axisExpandWidth*(t.axisCount-1)];if(!this.containPoint(e))return{behavior:"none",axisExpandWindow:n};var s=e[r]-t.layoutBase-t.axisExpandWindow0Pos,l,u="slide",v=t.axisCollapseWidth,c=this._model.get("axisExpandSlideTriggerArea"),h=c[0]!=null;if(v)h&&v&&s<i*c[0]?(u="jump",l=s-i*c[2]):h&&v&&s>i*(1-c[0])?(u="jump",l=s-i*(1-c[2])):(l=s-i*c[1])>=0&&(l=s-i*(1-c[1]))<=0&&(l=0),l*=t.axisExpandWidth/v,l?qv(l,n,o,"all"):u="none";else{var f=n[1]-n[0],p=o[1]*s/f;n=[Su(0,p-f/2)],n[1]=mu(o[1],n[0]+f),n[0]=n[1]-f}return{axisExpandWindow:n,behavior:u}},a}();function Vr(a,e){return mu(Su(a,e[0]),e[1])}function Kd(a,e){var t=e.layoutLength/(e.axisCount-1);return{position:t*a,axisNameAvailableWidth:t,axisLabelShow:!0}}function Jd(a,e){var t=e.layoutLength,r=e.axisExpandWidth,n=e.axisCount,i=e.axisCollapseWidth,o=e.winInnerIndices,s,l=i,u=!1,v;return a<o[0]?(s=a*i,v=i):a<=o[1]?(s=e.axisExpandWindow0Pos+a*r-e.axisExpandWindow[0],l=r,u=!0):(s=t-(n-1-a)*i,v=i),{position:s,axisNameAvailableWidth:l,axisLabelShow:u,nameTruncateMaxWidth:v}}function Qd(a,e){var t=[];return a.eachComponent("parallel",function(r,n){var i=new jd(r,a,e);i.name="parallel_"+n,i.resize(r,e),r.coordinateSystem=i,i.model=r,t.push(i)}),a.eachSeries(function(r){if(r.get("coordinateSystem")==="parallel"){var n=r.getReferringComponents("parallel",da).models[0];r.coordinateSystem=n.coordinateSystem}}),t}var tg={create:Qd},Gn=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.activeIntervals=[],t}return e.prototype.getAreaSelectStyle=function(){return Tl([["fill","color"],["lineWidth","borderWidth"],["stroke","borderColor"],["width","width"],["opacity","opacity"]])(this.getModel("areaSelectStyle"))},e.prototype.setActiveIntervals=function(t){var r=this.activeIntervals=$t(t);if(r)for(var n=r.length-1;n>=0;n--)lr(r[n])},e.prototype.getActiveState=function(t){var r=this.activeIntervals;if(!r.length)return"normal";if(t==null||isNaN(+t))return"inactive";if(r.length===1){var n=r[0];if(n[0]<=t&&t<=n[1])return"active"}else for(var i=0,o=r.length;i<o;i++)if(r[i][0]<=t&&t<=r[i][1])return"active";return"inactive"},e}(Jt);Kt(Gn,Xn);var eg=["axisLine","axisTickLabel","axisName"],rg=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){a.prototype.init.apply(this,arguments),(this._brushController=new Nl(r.getZr())).on("brush",et(this._onBrush,this))},e.prototype.render=function(t,r,n,i){if(!ag(t,r,i)){this.axisModel=t,this.api=n,this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new Y,this.group.add(this._axisGroup),!!t.get("show")){var s=ig(t,r),l=s.coordinateSystem,u=t.getAreaSelectStyle(),v=u.width,c=t.axis.dim,h=l.getAxisLayout(c),f=U({strokeContainThreshold:v},h),p=new Zn(t,f);C(eg,p.add,p),this._axisGroup.add(p.getGroup()),this._refreshBrushController(f,u,t,s,v,n),kl(o,this._axisGroup,t)}}},e.prototype._refreshBrushController=function(t,r,n,i,o,s){var l=n.axis.getExtent(),u=l[1]-l[0],v=Math.min(30,Math.abs(u)*.1),c=ht.create({x:l[0],y:-o/2,width:u,height:o});c.x-=v,c.width+=2*v,this._brushController.mount({enableGlobalPan:!0,rotation:t.rotation,x:t.position[0],y:t.position[1]}).setPanels([{panelId:"pl",clipPath:Jv(c),isTargetByCursor:Kv(c,s,i),getLinearBrushOtherExtent:jv(c,0)}]).enableBrush({brushType:"lineX",brushStyle:r,removeOnClick:!0}).updateCovers(ng(n))},e.prototype._onBrush=function(t){var r=t.areas,n=this.axisModel,i=n.axis,o=B(r,function(s){return[i.coordToData(s.range[0],!0),i.coordToData(s.range[1],!0)]});(!n.option.realtime===t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"axisAreaSelect",parallelAxisId:n.id,intervals:o})},e.prototype.dispose=function(){this._brushController.dispose()},e.type="parallelAxis",e}(Qt);function ag(a,e,t){return t&&t.type==="axisAreaSelect"&&e.findComponents({mainType:"parallelAxis",query:t})[0]===a}function ng(a){var e=a.axis;return B(a.activeIntervals,function(t){return{brushType:"lineX",panelId:"pl",range:[e.dataToCoord(t[0],!0),e.dataToCoord(t[1],!0)]}})}function ig(a,e){return e.getComponent("parallel",a.get("parallelIndex"))}var og={type:"axisAreaSelect",event:"axisAreaSelected"};function sg(a){a.registerAction(og,function(e,t){t.eachComponent({mainType:"parallelAxis",query:e},function(r){r.axis.model.setActiveIntervals(e.intervals)})}),a.registerAction("parallelAxisExpand",function(e,t){t.eachComponent({mainType:"parallel",query:e},function(r){r.setAxisExpand(e)})})}var lg={type:"value",areaSelectStyle:{width:20,borderWidth:1,borderColor:"rgba(160,197,232)",color:"rgba(160,197,232)",opacity:.3},realtime:!0,z:10};function xu(a){a.registerComponentView(Ud),a.registerComponentModel(Yd),a.registerCoordinateSystem("parallel",tg),a.registerPreprocessor(Bd),a.registerComponentModel(Gn),a.registerComponentView(rg),Vl(a,"parallel",Gn,lg),sg(a)}function ug(a){X(xu),a.registerChartView(Ed),a.registerSeriesModel(kd),a.registerVisual(a.PRIORITY.VISUAL.BRUSH,Od)}var vg=function(){function a(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.cpx2=0,this.cpy2=0,this.extent=0}return a}(),cg=function(a){V(e,a);function e(t){return a.call(this,t)||this}return e.prototype.getDefaultShape=function(){return new vg},e.prototype.buildPath=function(t,r){var n=r.extent;t.moveTo(r.x1,r.y1),t.bezierCurveTo(r.cpx1,r.cpy1,r.cpx2,r.cpy2,r.x2,r.y2),r.orient==="vertical"?(t.lineTo(r.x2+n,r.y2),t.bezierCurveTo(r.cpx2+n,r.cpy2,r.cpx1+n,r.cpy1,r.x1+n,r.y1)):(t.lineTo(r.x2,r.y2+n),t.bezierCurveTo(r.cpx2,r.cpy2+n,r.cpx1,r.cpy1+n,r.x1,r.y1+n)),t.closePath()},e.prototype.highlight=function(){ei(this)},e.prototype.downplay=function(){ri(this)},e}(wt),hg=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._focusAdjacencyDisabled=!1,t}return e.prototype.render=function(t,r,n){var i=this,o=t.getGraph(),s=this.group,l=t.layoutInfo,u=l.width,v=l.height,c=t.getData(),h=t.getData("edge"),f=t.get("orient");this._model=t,s.removeAll(),s.x=l.x,s.y=l.y,o.eachEdge(function(p){var d=new cg,g=nt(d);g.dataIndex=p.dataIndex,g.seriesIndex=t.seriesIndex,g.dataType="edge";var m=p.getModel(),S=m.getModel("lineStyle"),y=S.get("curveness"),b=p.node1.getLayout(),_=p.node1.getModel(),x=_.get("localX"),w=_.get("localY"),D=p.node2.getLayout(),T=p.node2.getModel(),I=T.get("localX"),A=T.get("localY"),P=p.getLayout(),E,L,M,R,N,G,O,F;d.shape.extent=Math.max(1,P.dy),d.shape.orient=f,f==="vertical"?(E=(x!=null?x*u:b.x)+P.sy,L=(w!=null?w*v:b.y)+b.dy,M=(I!=null?I*u:D.x)+P.ty,R=A!=null?A*v:D.y,N=E,G=L*(1-y)+R*y,O=M,F=L*y+R*(1-y)):(E=(x!=null?x*u:b.x)+b.dx,L=(w!=null?w*v:b.y)+P.sy,M=I!=null?I*u:D.x,R=(A!=null?A*v:D.y)+P.ty,N=E*(1-y)+M*y,G=L,O=E*y+M*(1-y),F=R),d.setShape({x1:E,y1:L,x2:M,y2:R,cpx1:N,cpy1:G,cpx2:O,cpy2:F}),d.useStyle(S.getItemStyle()),os(d.style,f,p);var W=""+m.get("value"),K=It(m,"edgeLabel");Ot(d,K,{labelFetcher:{getFormattedLabel:function(Q,yt,se,q,$,at){return t.getFormattedLabel(Q,yt,"edge",q,xr($,K.normal&&K.normal.get("formatter"),W),at)}},labelDataIndex:p.dataIndex,defaultText:W}),d.setTextConfig({position:"inside"});var J=m.getModel("emphasis");Et(d,m,"lineStyle",function(Q){var yt=Q.getItemStyle();return os(yt,f,p),yt}),s.add(d),h.setItemGraphicEl(p.dataIndex,d);var Z=J.get("focus");ct(d,Z==="adjacency"?p.getAdjacentDataIndices():Z==="trajectory"?p.getTrajectoryDataIndices():Z,J.get("blurScope"),J.get("disabled"))}),o.eachNode(function(p){var d=p.getLayout(),g=p.getModel(),m=g.get("localX"),S=g.get("localY"),y=g.getModel("emphasis"),b=g.get(["itemStyle","borderRadius"])||0,_=new Lt({shape:{x:m!=null?m*u:d.x,y:S!=null?S*v:d.y,width:d.dx,height:d.dy,r:b},style:g.getModel("itemStyle").getItemStyle(),z2:10});Ot(_,It(g),{labelFetcher:{getFormattedLabel:function(w,D){return t.getFormattedLabel(w,D,"node")}},labelDataIndex:p.dataIndex,defaultText:p.id}),_.disableLabelAnimation=!0,_.setStyle("fill",p.getVisual("color")),_.setStyle("decal",p.getVisual("style").decal),Et(_,g),s.add(_),c.setItemGraphicEl(p.dataIndex,_),nt(_).dataType="node";var x=y.get("focus");ct(_,x==="adjacency"?p.getAdjacentDataIndices():x==="trajectory"?p.getTrajectoryDataIndices():x,y.get("blurScope"),y.get("disabled"))}),c.eachItemGraphicEl(function(p,d){var g=c.getItemModel(d);g.get("draggable")&&(p.drift=function(m,S){i._focusAdjacencyDisabled=!0,this.shape.x+=m,this.shape.y+=S,this.dirty(),n.dispatchAction({type:"dragNode",seriesId:t.id,dataIndex:c.getRawIndex(d),localX:this.shape.x/u,localY:this.shape.y/v})},p.ondragend=function(){i._focusAdjacencyDisabled=!1},p.draggable=!0,p.cursor="move")}),!this._data&&t.isAnimationEnabled()&&s.setClipPath(fg(s.getBoundingRect(),t,function(){s.removeClipPath()})),this._data=t.getData()},e.prototype.dispose=function(){},e.type="sankey",e}(ft);function os(a,e,t){switch(a.fill){case"source":a.fill=t.node1.getVisual("color"),a.decal=t.node1.getVisual("style").decal;break;case"target":a.fill=t.node2.getVisual("color"),a.decal=t.node2.getVisual("style").decal;break;case"gradient":var r=t.node1.getVisual("color"),n=t.node2.getVisual("color");rt(r)&&rt(n)&&(a.fill=new yl(0,0,+(e==="horizontal"),+(e==="vertical"),[{color:r,offset:0},{color:n,offset:1}]))}}function fg(a,e,t){var r=new Lt({shape:{x:a.x-10,y:a.y-10,width:0,height:a.height+20}});return Tt(r,{shape:{width:a.width+20}},e,t),r}var pg=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){var n=t.edges||t.links||[],i=t.data||t.nodes||[],o=t.levels||[];this.levelModels=[];for(var s=this.levelModels,l=0;l<o.length;l++)o[l].depth!=null&&o[l].depth>=0&&(s[o[l].depth]=new Yt(o[l],this,r));var u=gu(i,n,this,!0,v);return u.data;function v(c,h){c.wrapMethod("getItemModel",function(f,p){var d=f.parentModel,g=d.getData().getItemLayout(p);if(g){var m=g.depth,S=d.levelModels[m];S&&(f.parentModel=S)}return f}),h.wrapMethod("getItemModel",function(f,p){var d=f.parentModel,g=d.getGraph().getEdgeByIndex(p),m=g.node1.getLayout();if(m){var S=m.depth,y=d.levelModels[S];y&&(f.parentModel=y)}return f})}},e.prototype.setNodePosition=function(t,r){var n=this.option.data||this.option.nodes,i=n[t];i.localX=r[0],i.localY=r[1]},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.formatTooltip=function(t,r,n){function i(f){return isNaN(f)||f==null}if(n==="edge"){var o=this.getDataParams(t,n),s=o.data,l=o.value,u=s.source+" -- "+s.target;return At("nameValue",{name:u,value:l,noValue:i(l)})}else{var v=this.getGraph().getNodeByIndex(t),c=v.getLayout().value,h=this.getDataParams(t,n).data.name;return At("nameValue",{name:h!=null?h+"":null,value:c,noValue:i(c)})}},e.prototype.optionUpdated=function(){},e.prototype.getDataParams=function(t,r){var n=a.prototype.getDataParams.call(this,t,r);if(n.value==null&&r==="node"){var i=this.getGraph().getNodeByIndex(t),o=i.getLayout().value;n.value=o}return n},e.type="series.sankey",e.defaultOption={z:2,coordinateSystem:"view",left:"5%",top:"5%",right:"20%",bottom:"5%",orient:"horizontal",nodeWidth:20,nodeGap:8,draggable:!0,layoutIterations:32,label:{show:!0,position:"right",fontSize:12},edgeLabel:{show:!1,fontSize:12},levels:[],nodeAlign:"justify",lineStyle:{color:"#314656",opacity:.2,curveness:.5},emphasis:{label:{show:!0},lineStyle:{opacity:.5}},select:{itemStyle:{borderColor:"#212121"}},animationEasing:"linear",animationDuration:1e3},e}(dt);function dg(a,e){a.eachSeriesByType("sankey",function(t){var r=t.get("nodeWidth"),n=t.get("nodeGap"),i=gg(t,e);t.layoutInfo=i;var o=i.width,s=i.height,l=t.getGraph(),u=l.nodes,v=l.edges;mg(u);var c=Pt(u,function(d){return d.getLayout().value===0}),h=c.length!==0?0:t.get("layoutIterations"),f=t.get("orient"),p=t.get("nodeAlign");yg(u,v,r,n,o,s,h,f,p)})}function gg(a,e){return te(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function yg(a,e,t,r,n,i,o,s,l){Sg(a,e,t,n,i,s,l),wg(a,e,i,n,r,o,s),Eg(a,s)}function mg(a){C(a,function(e){var t=he(e.outEdges,Jr),r=he(e.inEdges,Jr),n=e.getValue()||0,i=Math.max(t,r,n);e.setLayout({value:i},!0)})}function Sg(a,e,t,r,n,i,o){for(var s=[],l=[],u=[],v=[],c=0,h=0;h<e.length;h++)s[h]=1;for(var h=0;h<a.length;h++)l[h]=a[h].inEdges.length,l[h]===0&&u.push(a[h]);for(var f=-1;u.length;){for(var p=0;p<u.length;p++){var d=u[p],g=d.hostGraph.data.getRawDataItem(d.dataIndex),m=g.depth!=null&&g.depth>=0;m&&g.depth>f&&(f=g.depth),d.setLayout({depth:m?g.depth:c},!0),i==="vertical"?d.setLayout({dy:t},!0):d.setLayout({dx:t},!0);for(var S=0;S<d.outEdges.length;S++){var y=d.outEdges[S],b=e.indexOf(y);s[b]=0;var _=y.node2,x=a.indexOf(_);--l[x]===0&&v.indexOf(_)<0&&v.push(_)}}++c,u=v,v=[]}for(var h=0;h<s.length;h++)if(s[h]===1)throw new Error("Sankey is a DAG, the original data has cycle!");var w=f>c-1?f:c-1;o&&o!=="left"&&xg(a,o,i,w);var D=i==="vertical"?(n-t)/w:(r-t)/w;_g(a,D,i)}function bu(a){var e=a.hostGraph.data.getRawDataItem(a.dataIndex);return e.depth!=null&&e.depth>=0}function xg(a,e,t,r){if(e==="right"){for(var n=[],i=a,o=0;i.length;){for(var s=0;s<i.length;s++){var l=i[s];l.setLayout({skNodeHeight:o},!0);for(var u=0;u<l.inEdges.length;u++){var v=l.inEdges[u];n.indexOf(v.node1)<0&&n.push(v.node1)}}i=n,n=[],++o}C(a,function(c){bu(c)||c.setLayout({depth:Math.max(0,r-c.getLayout().skNodeHeight)},!0)})}else e==="justify"&&bg(a,r)}function bg(a,e){C(a,function(t){!bu(t)&&!t.outEdges.length&&t.setLayout({depth:e},!0)})}function _g(a,e,t){C(a,function(r){var n=r.getLayout().depth*e;t==="vertical"?r.setLayout({y:n},!0):r.setLayout({x:n},!0)})}function wg(a,e,t,r,n,i,o){var s=Dg(a,o);Tg(s,e,t,r,n,o),Ja(s,n,t,r,o);for(var l=1;i>0;i--)l*=.99,Ig(s,l,o),Ja(s,n,t,r,o),Mg(s,l,o),Ja(s,n,t,r,o)}function Dg(a,e){var t=[],r=e==="vertical"?"y":"x",n=An(a,function(i){return i.getLayout()[r]});return n.keys.sort(function(i,o){return i-o}),C(n.keys,function(i){t.push(n.buckets.get(i))}),t}function Tg(a,e,t,r,n,i){var o=1/0;C(a,function(s){var l=s.length,u=0;C(s,function(c){u+=c.getLayout().value});var v=i==="vertical"?(r-(l-1)*n)/u:(t-(l-1)*n)/u;v<o&&(o=v)}),C(a,function(s){C(s,function(l,u){var v=l.getLayout().value*o;i==="vertical"?(l.setLayout({x:u},!0),l.setLayout({dx:v},!0)):(l.setLayout({y:u},!0),l.setLayout({dy:v},!0))})}),C(e,function(s){var l=+s.getValue()*o;s.setLayout({dy:l},!0)})}function Ja(a,e,t,r,n){var i=n==="vertical"?"x":"y";C(a,function(o){o.sort(function(d,g){return d.getLayout()[i]-g.getLayout()[i]});for(var s,l,u,v=0,c=o.length,h=n==="vertical"?"dx":"dy",f=0;f<c;f++)l=o[f],u=v-l.getLayout()[i],u>0&&(s=l.getLayout()[i]+u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),v=l.getLayout()[i]+l.getLayout()[h]+e;var p=n==="vertical"?r:t;if(u=v-e-p,u>0){s=l.getLayout()[i]-u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0),v=s;for(var f=c-2;f>=0;--f)l=o[f],u=l.getLayout()[i]+l.getLayout()[h]+e-v,u>0&&(s=l.getLayout()[i]-u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),v=l.getLayout()[i]}})}function Ig(a,e,t){C(a.slice().reverse(),function(r){C(r,function(n){if(n.outEdges.length){var i=he(n.outEdges,Ag,t)/he(n.outEdges,Jr);if(isNaN(i)){var o=n.outEdges.length;i=o?he(n.outEdges,Lg,t)/o:0}if(t==="vertical"){var s=n.getLayout().x+(i-pe(n,t))*e;n.setLayout({x:s},!0)}else{var l=n.getLayout().y+(i-pe(n,t))*e;n.setLayout({y:l},!0)}}})})}function Ag(a,e){return pe(a.node2,e)*a.getValue()}function Lg(a,e){return pe(a.node2,e)}function Cg(a,e){return pe(a.node1,e)*a.getValue()}function Pg(a,e){return pe(a.node1,e)}function pe(a,e){return e==="vertical"?a.getLayout().x+a.getLayout().dx/2:a.getLayout().y+a.getLayout().dy/2}function Jr(a){return a.getValue()}function he(a,e,t){for(var r=0,n=a.length,i=-1;++i<n;){var o=+e(a[i],t);isNaN(o)||(r+=o)}return r}function Mg(a,e,t){C(a,function(r){C(r,function(n){if(n.inEdges.length){var i=he(n.inEdges,Cg,t)/he(n.inEdges,Jr);if(isNaN(i)){var o=n.inEdges.length;i=o?he(n.inEdges,Pg,t)/o:0}if(t==="vertical"){var s=n.getLayout().x+(i-pe(n,t))*e;n.setLayout({x:s},!0)}else{var l=n.getLayout().y+(i-pe(n,t))*e;n.setLayout({y:l},!0)}}})})}function Eg(a,e){var t=e==="vertical"?"x":"y";C(a,function(r){r.outEdges.sort(function(n,i){return n.node2.getLayout()[t]-i.node2.getLayout()[t]}),r.inEdges.sort(function(n,i){return n.node1.getLayout()[t]-i.node1.getLayout()[t]})}),C(a,function(r){var n=0,i=0;C(r.outEdges,function(o){o.setLayout({sy:n},!0),n+=o.getLayout().dy}),C(r.inEdges,function(o){o.setLayout({ty:i},!0),i+=o.getLayout().dy})})}function Rg(a){a.eachSeriesByType("sankey",function(e){var t=e.getGraph(),r=t.nodes,n=t.edges;if(r.length){var i=1/0,o=-1/0;C(r,function(s){var l=s.getLayout().value;l<i&&(i=l),l>o&&(o=l)}),C(r,function(s){var l=new Al({type:"color",mappingMethod:"linear",dataExtent:[i,o],visual:e.get("color")}),u=l.mapValueToVisual(s.getLayout().value),v=s.getModel().get(["itemStyle","color"]);v!=null?(s.setVisual("color",v),s.setVisual("style",{fill:v})):(s.setVisual("color",u),s.setVisual("style",{fill:u}))})}n.length&&C(n,function(s){var l=s.getModel().get("lineStyle");s.setVisual("style",l)})})}function Ng(a){a.registerChartView(hg),a.registerSeriesModel(pg),a.registerLayout(dg),a.registerVisual(Rg),a.registerAction({type:"dragNode",event:"dragnode",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"sankey",query:e},function(r){r.setNodePosition(e.dataIndex,[e.localX,e.localY])})})}var _u=function(){function a(){}return a.prototype._hasEncodeRule=function(e){var t=this.getEncode();return t&&t.get(e)!=null},a.prototype.getInitialData=function(e,t){var r,n=t.getComponent("xAxis",this.get("xAxisIndex")),i=t.getComponent("yAxis",this.get("yAxisIndex")),o=n.get("type"),s=i.get("type"),l;o==="category"?(e.layout="horizontal",r=n.getOrdinalMeta(),l=!this._hasEncodeRule("x")):s==="category"?(e.layout="vertical",r=i.getOrdinalMeta(),l=!this._hasEncodeRule("y")):e.layout=e.layout||"horizontal";var u=["x","y"],v=e.layout==="horizontal"?0:1,c=this._baseAxisDim=u[v],h=u[1-v],f=[n,i],p=f[v].get("type"),d=f[1-v].get("type"),g=e.data;if(g&&l){var m=[];C(g,function(b,_){var x;H(b)?(x=b.slice(),b.unshift(_)):H(b.value)?(x=U({},b),x.value=x.value.slice(),b.value.unshift(_)):x=b,m.push(x)}),e.data=m}var S=this.defaultValueDimensions,y=[{name:c,type:Ln(p),ordinalMeta:r,otherDims:{tooltip:!1,itemName:0},dimsDef:["base"]},{name:h,type:Ln(d),dimsDef:S.slice()}];return gr(this,{coordDimensions:y,dimensionsCount:S.length+1,encodeDefaulter:st(Qv,y,this)})},a.prototype.getBaseAxis=function(){var e=this._baseAxisDim;return this.ecModel.getComponent(e+"Axis",this.get(e+"AxisIndex")).axis},a}(),wu=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.defaultValueDimensions=[{name:"min",defaultTooltip:!0},{name:"Q1",defaultTooltip:!0},{name:"median",defaultTooltip:!0},{name:"Q3",defaultTooltip:!0},{name:"max",defaultTooltip:!0}],t.visualDrawType="stroke",t}return e.type="series.boxplot",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,boxWidth:[7,50],itemStyle:{color:"#fff",borderWidth:1},emphasis:{scale:!0,itemStyle:{borderWidth:2,shadowBlur:5,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}},animationDuration:800},e}(dt);Kt(wu,_u,!0);var kg=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this.group,s=this._data;this._data||o.removeAll();var l=t.get("layout")==="horizontal"?1:0;i.diff(s).add(function(u){if(i.hasValue(u)){var v=i.getItemLayout(u),c=ss(v,i,u,l,!0);i.setItemGraphicEl(u,c),o.add(c)}}).update(function(u,v){var c=s.getItemGraphicEl(v);if(!i.hasValue(u)){o.remove(c);return}var h=i.getItemLayout(u);c?(fe(c),Du(h,c,i,u)):c=ss(h,i,u,l),o.add(c),i.setItemGraphicEl(u,c)}).remove(function(u){var v=s.getItemGraphicEl(u);v&&o.remove(v)}).execute(),this._data=i},e.prototype.remove=function(t){var r=this.group,n=this._data;this._data=null,n&&n.eachItemGraphicEl(function(i){i&&r.remove(i)})},e.type="boxplot",e}(ft),Vg=function(){function a(){}return a}(),Gg=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="boxplotBoxPath",r}return e.prototype.getDefaultShape=function(){return new Vg},e.prototype.buildPath=function(t,r){var n=r.points,i=0;for(t.moveTo(n[i][0],n[i][1]),i++;i<4;i++)t.lineTo(n[i][0],n[i][1]);for(t.closePath();i<n.length;i++)t.moveTo(n[i][0],n[i][1]),i++,t.lineTo(n[i][0],n[i][1])},e}(wt);function ss(a,e,t,r,n){var i=a.ends,o=new Gg({shape:{points:n?zg(i,r,a):i}});return Du(a,o,e,t,n),o}function Du(a,e,t,r,n){var i=t.hostModel,o=ua[n?"initProps":"updateProps"];o(e,{shape:{points:a.ends}},i,r),e.useStyle(t.getItemVisual(r,"style")),e.style.strokeNoScale=!0,e.z2=100;var s=t.getItemModel(r),l=s.getModel("emphasis");Et(e,s),ct(e,l.get("focus"),l.get("blurScope"),l.get("disabled"))}function zg(a,e,t){return B(a,function(r){return r=r.slice(),r[e]=t.initBaseline,r})}var ar=C;function Og(a){var e=Bg(a);ar(e,function(t){var r=t.seriesModels;r.length&&(Fg(t),ar(r,function(n,i){Wg(n,t.boxOffsetList[i],t.boxWidthList[i])}))})}function Bg(a){var e=[],t=[];return a.eachSeriesByType("boxplot",function(r){var n=r.getBaseAxis(),i=zt(t,n);i<0&&(i=t.length,t[i]=n,e[i]={axis:n,seriesModels:[]}),e[i].seriesModels.push(r)}),e}function Fg(a){var e=a.axis,t=a.seriesModels,r=t.length,n=a.boxWidthList=[],i=a.boxOffsetList=[],o=[],s;if(e.type==="category")s=e.getBandWidth();else{var l=0;ar(t,function(p){l=Math.max(l,p.getData().count())});var u=e.getExtent();s=Math.abs(u[1]-u[0])/l}ar(t,function(p){var d=p.get("boxWidth");H(d)||(d=[d,d]),o.push([z(d[0],s)||0,z(d[1],s)||0])});var v=s*.8-2,c=v/r*.3,h=(v-c*(r-1))/r,f=h/2-v/2;ar(t,function(p,d){i.push(f),f+=c+h,n.push(Math.min(Math.max(h,o[d][0]),o[d][1]))})}function Wg(a,e,t){var r=a.coordinateSystem,n=a.getData(),i=t/2,o=a.get("layout")==="horizontal"?0:1,s=1-o,l=["x","y"],u=n.mapDimension(l[o]),v=n.mapDimensionsAll(l[s]);if(u==null||v.length<5)return;for(var c=0;c<n.count();c++){var h=n.get(u,c),f=y(h,v[2],c),p=y(h,v[0],c),d=y(h,v[1],c),g=y(h,v[3],c),m=y(h,v[4],c),S=[];b(S,d,!1),b(S,g,!0),S.push(p,d,m,g),_(S,p),_(S,m),_(S,f),n.setItemLayout(c,{initBaseline:f[s],ends:S})}function y(x,w,D){var T=n.get(w,D),I=[];I[o]=x,I[s]=T;var A;return isNaN(x)||isNaN(T)?A=[NaN,NaN]:(A=r.dataToPoint(I),A[o]+=e),A}function b(x,w,D){var T=w.slice(),I=w.slice();T[o]+=i,I[o]-=i,D?x.push(T,I):x.push(I,T)}function _(x,w){var D=w.slice(),T=w.slice();D[o]-=i,T[o]+=i,x.push(D,T)}}function Hg(a,e){e=e||{};for(var t=[],r=[],n=e.boundIQR,i=n==="none"||n===0,o=0;o<a.length;o++){var s=lr(a[o].slice()),l=Ta(s,.25),u=Ta(s,.5),v=Ta(s,.75),c=s[0],h=s[s.length-1],f=(n==null?1.5:n)*(v-l),p=i?c:Math.max(c,l-f),d=i?h:Math.min(h,v+f),g=e.itemNameFormatter,m=ot(g)?g({value:o}):rt(g)?g.replace("{value}",o+""):o+"";t.push([m,p,l,u,v,d]);for(var S=0;S<s.length;S++){var y=s[S];if(y<p||y>d){var b=[m,y];r.push(b)}}}return{boxData:t,outliers:r}}var Ug={type:"echarts:boxplot",transform:function(e){var t=e.upstream;if(t.sourceFormat!==Gl){var r="";ut(r)}var n=Hg(t.getRawData(),e.config);return[{dimensions:["ItemName","Low","Q1","Q2","Q3","High"],data:n.boxData},{data:n.outliers}]}};function $g(a){a.registerSeriesModel(wu),a.registerChartView(kg),a.registerLayout(Og),a.registerTransform(Ug)}var Yg=["itemStyle","borderColor"],Xg=["itemStyle","borderColor0"],Zg=["itemStyle","borderColorDoji"],qg=["itemStyle","color"],jg=["itemStyle","color0"];function _i(a,e){return e.get(a>0?qg:jg)}function wi(a,e){return e.get(a===0?Zg:a>0?Yg:Xg)}var Kg={seriesType:"candlestick",plan:ai(),performRawSeries:!0,reset:function(a,e){if(!e.isSeriesFiltered(a)){var t=a.pipelineContext.large;return!t&&{progress:function(r,n){for(var i;(i=r.next())!=null;){var o=n.getItemModel(i),s=n.getItemLayout(i).sign,l=o.getItemStyle();l.fill=_i(s,o),l.stroke=wi(s,o)||l.fill;var u=n.ensureUniqueItemVisual(i,"style");U(u,l)}}}}}},Jg=["color","borderColor"],Qg=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){this.group.removeClipPath(),this._progressiveEls=null,this._updateDrawMode(t),this._isLargeDraw?this._renderLarge(t):this._renderNormal(t)},e.prototype.incrementalPrepareRender=function(t,r,n){this._clear(),this._updateDrawMode(t)},e.prototype.incrementalRender=function(t,r,n,i){this._progressiveEls=[],this._isLargeDraw?this._incrementalRenderLarge(t,r):this._incrementalRenderNormal(t,r)},e.prototype.eachRendered=function(t){ma(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var r=t.pipelineContext.large;(this._isLargeDraw==null||r!==this._isLargeDraw)&&(this._isLargeDraw=r,this._clear())},e.prototype._renderNormal=function(t){var r=t.getData(),n=this._data,i=this.group,o=r.getLayout("isSimpleBox"),s=t.get("clip",!0),l=t.coordinateSystem,u=l.getArea&&l.getArea();this._data||i.removeAll(),r.diff(n).add(function(v){if(r.hasValue(v)){var c=r.getItemLayout(v);if(s&&ls(u,c))return;var h=Qa(c,v,!0);Tt(h,{shape:{points:c.ends}},t,v),tn(h,r,v,o),i.add(h),r.setItemGraphicEl(v,h)}}).update(function(v,c){var h=n.getItemGraphicEl(c);if(!r.hasValue(v)){i.remove(h);return}var f=r.getItemLayout(v);if(s&&ls(u,f)){i.remove(h);return}h?(lt(h,{shape:{points:f.ends}},t,v),fe(h)):h=Qa(f),tn(h,r,v,o),i.add(h),r.setItemGraphicEl(v,h)}).remove(function(v){var c=n.getItemGraphicEl(v);c&&i.remove(c)}).execute(),this._data=r},e.prototype._renderLarge=function(t){this._clear(),us(t,this.group);var r=t.get("clip",!0)?Sa(t.coordinateSystem,!1,t):null;r?this.group.setClipPath(r):this.group.removeClipPath()},e.prototype._incrementalRenderNormal=function(t,r){for(var n=r.getData(),i=n.getLayout("isSimpleBox"),o;(o=t.next())!=null;){var s=n.getItemLayout(o),l=Qa(s);tn(l,n,o,i),l.incremental=!0,this.group.add(l),this._progressiveEls.push(l)}},e.prototype._incrementalRenderLarge=function(t,r){us(r,this.group,this._progressiveEls,!0)},e.prototype.remove=function(t){this._clear()},e.prototype._clear=function(){this.group.removeAll(),this._data=null},e.type="candlestick",e}(ft),ty=function(){function a(){}return a}(),ey=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="normalCandlestickBox",r}return e.prototype.getDefaultShape=function(){return new ty},e.prototype.buildPath=function(t,r){var n=r.points;this.__simpleBox?(t.moveTo(n[4][0],n[4][1]),t.lineTo(n[6][0],n[6][1])):(t.moveTo(n[0][0],n[0][1]),t.lineTo(n[1][0],n[1][1]),t.lineTo(n[2][0],n[2][1]),t.lineTo(n[3][0],n[3][1]),t.closePath(),t.moveTo(n[4][0],n[4][1]),t.lineTo(n[5][0],n[5][1]),t.moveTo(n[6][0],n[6][1]),t.lineTo(n[7][0],n[7][1]))},e}(wt);function Qa(a,e,t){var r=a.ends;return new ey({shape:{points:t?ry(r,a):r},z2:100})}function ls(a,e){for(var t=!0,r=0;r<e.ends.length;r++)if(a.contain(e.ends[r][0],e.ends[r][1])){t=!1;break}return t}function tn(a,e,t,r){var n=e.getItemModel(t);a.useStyle(e.getItemVisual(t,"style")),a.style.strokeNoScale=!0,a.__simpleBox=r,Et(a,n);var i=e.getItemLayout(t).sign;C(a.states,function(s,l){var u=n.getModel(l),v=_i(i,u),c=wi(i,u)||v,h=s.style||(s.style={});v&&(h.fill=v),c&&(h.stroke=c)});var o=n.getModel("emphasis");ct(a,o.get("focus"),o.get("blurScope"),o.get("disabled"))}function ry(a,e){return B(a,function(t){return t=t.slice(),t[1]=e.initBaseline,t})}var ay=function(){function a(){}return a}(),en=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="largeCandlestickBox",r}return e.prototype.getDefaultShape=function(){return new ay},e.prototype.buildPath=function(t,r){for(var n=r.points,i=0;i<n.length;)if(this.__sign===n[i++]){var o=n[i++];t.moveTo(o,n[i++]),t.lineTo(o,n[i++])}else i+=3},e}(wt);function us(a,e,t,r){var n=a.getData(),i=n.getLayout("largePoints"),o=new en({shape:{points:i},__sign:1,ignoreCoarsePointer:!0});e.add(o);var s=new en({shape:{points:i},__sign:-1,ignoreCoarsePointer:!0});e.add(s);var l=new en({shape:{points:i},__sign:0,ignoreCoarsePointer:!0});e.add(l),rn(1,o,a),rn(-1,s,a),rn(0,l,a),r&&(o.incremental=!0,s.incremental=!0),t&&t.push(o,s)}function rn(a,e,t,r){var n=wi(a,t)||_i(a,t),i=t.getModel("itemStyle").getItemStyle(Jg);e.useStyle(i),e.style.fill=null,e.style.stroke=n}var Tu=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.defaultValueDimensions=[{name:"open",defaultTooltip:!0},{name:"close",defaultTooltip:!0},{name:"lowest",defaultTooltip:!0},{name:"highest",defaultTooltip:!0}],t}return e.prototype.getShadowDim=function(){return"open"},e.prototype.brushSelector=function(t,r,n){var i=r.getItemLayout(t);return i&&n.rect(i.brushRect)},e.type="series.candlestick",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,clip:!0,itemStyle:{color:"#eb5454",color0:"#47b262",borderColor:"#eb5454",borderColor0:"#47b262",borderColorDoji:null,borderWidth:1},emphasis:{itemStyle:{borderWidth:2}},barMaxWidth:null,barMinWidth:null,barWidth:null,large:!0,largeThreshold:600,progressive:3e3,progressiveThreshold:1e4,progressiveChunkMode:"mod",animationEasing:"linear",animationDuration:300},e}(dt);Kt(Tu,_u,!0);function ny(a){!a||!H(a.series)||C(a.series,function(e){wr(e)&&e.type==="k"&&(e.type="candlestick")})}var iy={seriesType:"candlestick",plan:ai(),reset:function(a){var e=a.coordinateSystem,t=a.getData(),r=oy(a,t),n=0,i=1,o=["x","y"],s=t.getDimensionIndex(t.mapDimension(o[n])),l=B(t.mapDimensionsAll(o[i]),t.getDimensionIndex,t),u=l[0],v=l[1],c=l[2],h=l[3];if(t.setLayout({candleWidth:r,isSimpleBox:r<=1.3}),s<0||l.length<4)return;return{progress:a.pipelineContext.large?p:f};function f(d,g){for(var m,S=g.getStore();(m=d.next())!=null;){var y=S.get(s,m),b=S.get(u,m),_=S.get(v,m),x=S.get(c,m),w=S.get(h,m),D=Math.min(b,_),T=Math.max(b,_),I=N(D,y),A=N(T,y),P=N(x,y),E=N(w,y),L=[];G(L,A,0),G(L,I,1),L.push(F(E),F(A),F(P),F(I));var M=g.getItemModel(m),R=!!M.get(["itemStyle","borderColorDoji"]);g.setItemLayout(m,{sign:vs(S,m,b,_,v,R),initBaseline:b>_?A[i]:I[i],ends:L,brushRect:O(x,w,y)})}function N(W,K){var J=[];return J[n]=K,J[i]=W,isNaN(K)||isNaN(W)?[NaN,NaN]:e.dataToPoint(J)}function G(W,K,J){var Z=K.slice(),Q=K.slice();Z[n]=Ia(Z[n]+r/2,1,!1),Q[n]=Ia(Q[n]-r/2,1,!0),J?W.push(Z,Q):W.push(Q,Z)}function O(W,K,J){var Z=N(W,J),Q=N(K,J);return Z[n]-=r/2,Q[n]-=r/2,{x:Z[0],y:Z[1],width:r,height:Q[1]-Z[1]}}function F(W){return W[n]=Ia(W[n],1),W}}function p(d,g){for(var m=tc(d.count*4),S=0,y,b=[],_=[],x,w=g.getStore(),D=!!a.get(["itemStyle","borderColorDoji"]);(x=d.next())!=null;){var T=w.get(s,x),I=w.get(u,x),A=w.get(v,x),P=w.get(c,x),E=w.get(h,x);if(isNaN(T)||isNaN(P)||isNaN(E)){m[S++]=NaN,S+=3;continue}m[S++]=vs(w,x,I,A,v,D),b[n]=T,b[i]=P,y=e.dataToPoint(b,null,_),m[S++]=y?y[0]:NaN,m[S++]=y?y[1]:NaN,b[i]=E,y=e.dataToPoint(b,null,_),m[S++]=y?y[1]:NaN}g.setLayout("largePoints",m)}}};function vs(a,e,t,r,n,i){var o;return t>r?o=-1:t<r?o=1:o=i?0:e>0?a.get(n,e-1)<=r?1:-1:1,o}function oy(a,e){var t=a.getBaseAxis(),r,n=t.type==="category"?t.getBandWidth():(r=t.getExtent(),Math.abs(r[1]-r[0])/e.count()),i=z(Zt(a.get("barMaxWidth"),n),n),o=z(Zt(a.get("barMinWidth"),1),n),s=a.get("barWidth");return s!=null?z(s,n):Math.max(Math.min(n/2,i),o)}function sy(a){a.registerChartView(Qg),a.registerSeriesModel(Tu),a.registerPreprocessor(ny),a.registerVisual(Kg),a.registerLayout(iy)}function cs(a,e){var t=e.rippleEffectColor||e.color;a.eachChild(function(r){r.attr({z:e.z,zlevel:e.zlevel,style:{stroke:e.brushType==="stroke"?t:null,fill:e.brushType==="fill"?t:null}})})}var ly=function(a){V(e,a);function e(t,r){var n=a.call(this)||this,i=new _l(t,r),o=new Y;return n.add(i),n.add(o),n.updateData(t,r),n}return e.prototype.stopEffectAnimation=function(){this.childAt(1).removeAll()},e.prototype.startEffectAnimation=function(t){for(var r=t.symbolType,n=t.color,i=t.rippleNumber,o=this.childAt(1),s=0;s<i;s++){var l=qt(r,-1,-1,2,2,n);l.attr({style:{strokeNoScale:!0},z2:99,silent:!0,scaleX:.5,scaleY:.5});var u=-s/i*t.period+t.effectOffset;l.animate("",!0).when(t.period,{scaleX:t.rippleScale/2,scaleY:t.rippleScale/2}).delay(u).start(),l.animateStyle(!0).when(t.period,{opacity:0}).delay(u).start(),o.add(l)}cs(o,t)},e.prototype.updateEffectAnimation=function(t){for(var r=this._effectCfg,n=this.childAt(1),i=["symbolType","period","rippleScale","rippleNumber"],o=0;o<i.length;o++){var s=i[o];if(r[s]!==t[s]){this.stopEffectAnimation(),this.startEffectAnimation(t);return}}cs(n,t)},e.prototype.highlight=function(){ei(this)},e.prototype.downplay=function(){ri(this)},e.prototype.getSymbolType=function(){var t=this.childAt(0);return t&&t.getSymbolType()},e.prototype.updateData=function(t,r){var n=this,i=t.hostModel;this.childAt(0).updateData(t,r);var o=this.childAt(1),s=t.getItemModel(r),l=t.getItemVisual(r,"symbol"),u=dr(t.getItemVisual(r,"symbolSize")),v=t.getItemVisual(r,"style"),c=v&&v.fill,h=s.getModel("emphasis");o.setScale(u),o.traverse(function(g){g.setStyle("fill",c)});var f=_r(t.getItemVisual(r,"symbolOffset"),u);f&&(o.x=f[0],o.y=f[1]);var p=t.getItemVisual(r,"symbolRotate");o.rotation=(p||0)*Math.PI/180||0;var d={};d.showEffectOn=i.get("showEffectOn"),d.rippleScale=s.get(["rippleEffect","scale"]),d.brushType=s.get(["rippleEffect","brushType"]),d.period=s.get(["rippleEffect","period"])*1e3,d.effectOffset=r/t.count(),d.z=i.getShallow("z")||0,d.zlevel=i.getShallow("zlevel")||0,d.symbolType=l,d.color=c,d.rippleEffectColor=s.get(["rippleEffect","color"]),d.rippleNumber=s.get(["rippleEffect","number"]),d.showEffectOn==="render"?(this._effectCfg?this.updateEffectAnimation(d):this.startEffectAnimation(d),this._effectCfg=d):(this._effectCfg=null,this.stopEffectAnimation(),this.onHoverStateChange=function(g){g==="emphasis"?d.showEffectOn!=="render"&&n.startEffectAnimation(d):g==="normal"&&d.showEffectOn!=="render"&&n.stopEffectAnimation()}),this._effectCfg=d,ct(this,h.get("focus"),h.get("blurScope"),h.get("disabled"))},e.prototype.fadeOut=function(t){t&&t()},e}(Y),uy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this._symbolDraw=new la(ly)},e.prototype.render=function(t,r,n){var i=t.getData(),o=this._symbolDraw;o.updateData(i,{clipShape:this._getClipShape(t)}),this.group.add(o.group)},e.prototype._getClipShape=function(t){var r=t.coordinateSystem,n=r&&r.getArea&&r.getArea();return t.get("clip",!0)?n:null},e.prototype.updateTransform=function(t,r,n){var i=t.getData();this.group.dirty();var o=sa("").reset(t,r,n);o.progress&&o.progress({start:0,end:i.count(),count:i.count()},i),this._symbolDraw.updateLayout()},e.prototype._updateGroupTransform=function(t){var r=t.coordinateSystem;r&&r.getRoamTransform&&(this.group.transform=ec(r.getRoamTransform()),this.group.decomposeTransform())},e.prototype.remove=function(t,r){this._symbolDraw&&this._symbolDraw.remove(!0)},e.type="effectScatter",e}(ft),vy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t,r){return Be(null,this,{useEncodeDefaulter:!0})},e.prototype.brushSelector=function(t,r,n){return n.point(r.getItemLayout(t))},e.type="series.effectScatter",e.dependencies=["grid","polar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,effectType:"ripple",progressive:0,showEffectOn:"render",clip:!0,rippleEffect:{period:4,scale:2.5,brushType:"fill",number:3},universalTransition:{divideShape:"clone"},symbolSize:10},e}(dt);function cy(a){a.registerChartView(uy),a.registerSeriesModel(vy),a.registerLayout(sa("effectScatter"))}var Iu=function(a){V(e,a);function e(t,r,n){var i=a.call(this)||this;return i.add(i.createLine(t,r,n)),i._updateEffectSymbol(t,r),i}return e.prototype.createLine=function(t,r,n){return new xi(t,r,n)},e.prototype._updateEffectSymbol=function(t,r){var n=t.getItemModel(r),i=n.getModel("effect"),o=i.get("symbolSize"),s=i.get("symbol");H(o)||(o=[o,o]);var l=t.getItemVisual(r,"style"),u=i.get("color")||l&&l.stroke,v=this.childAt(1);this._symbolType!==s&&(this.remove(v),v=qt(s,-.5,-.5,1,1,u),v.z2=100,v.culling=!0,this.add(v)),v&&(v.setStyle("shadowColor",u),v.setStyle(i.getItemStyle(["color"])),v.scaleX=o[0],v.scaleY=o[1],v.setColor(u),this._symbolType=s,this._symbolScale=o,this._updateEffectAnimation(t,i,r))},e.prototype._updateEffectAnimation=function(t,r,n){var i=this.childAt(1);if(i){var o=t.getItemLayout(n),s=r.get("period")*1e3,l=r.get("loop"),u=r.get("roundTrip"),v=r.get("constantSpeed"),c=kt(r.get("delay"),function(f){return f/t.count()*s/3});if(i.ignore=!0,this._updateAnimationPoints(i,o),v>0&&(s=this._getLineLength(i)/v*1e3),s!==this._period||l!==this._loop||u!==this._roundTrip){i.stopAnimation();var h=void 0;ot(c)?h=c(n):h=c,i.__t>0&&(h=-s*i.__t),this._animateSymbol(i,s,h,l,u)}this._period=s,this._loop=l,this._roundTrip=u}},e.prototype._animateSymbol=function(t,r,n,i,o){if(r>0){t.__t=0;var s=this,l=t.animate("",i).when(o?r*2:r,{__t:o?2:1}).delay(n).during(function(){s._updateSymbolPosition(t)});i||l.done(function(){s.remove(t)}),l.start()}},e.prototype._getLineLength=function(t){return Qe(t.__p1,t.__cp1)+Qe(t.__cp1,t.__p2)},e.prototype._updateAnimationPoints=function(t,r){t.__p1=r[0],t.__p2=r[1],t.__cp1=r[2]||[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]},e.prototype.updateData=function(t,r,n){this.childAt(0).updateData(t,r,n),this._updateEffectSymbol(t,r)},e.prototype._updateSymbolPosition=function(t){var r=t.__p1,n=t.__p2,i=t.__cp1,o=t.__t<1?t.__t:2-t.__t,s=[t.x,t.y],l=s.slice(),u=Ll,v=rc;s[0]=u(r[0],i[0],n[0],o),s[1]=u(r[1],i[1],n[1],o);var c=t.__t<1?v(r[0],i[0],n[0],o):v(n[0],i[0],r[0],1-o),h=t.__t<1?v(r[1],i[1],n[1],o):v(n[1],i[1],r[1],1-o);t.rotation=-Math.atan2(h,c)-Math.PI/2,(this._symbolType==="line"||this._symbolType==="rect"||this._symbolType==="roundRect")&&(t.__lastT!==void 0&&t.__lastT<t.__t?(t.scaleY=Qe(l,s)*1.05,o===1&&(s[0]=l[0]+(s[0]-l[0])/2,s[1]=l[1]+(s[1]-l[1])/2)):t.__lastT===1?t.scaleY=2*Qe(r,s):t.scaleY=this._symbolScale[1]),t.__lastT=t.__t,t.ignore=!1,t.x=s[0],t.y=s[1]},e.prototype.updateLayout=function(t,r){this.childAt(0).updateLayout(t,r);var n=t.getItemModel(r).getModel("effect");this._updateEffectAnimation(t,n,r)},e}(Y),Au=function(a){V(e,a);function e(t,r,n){var i=a.call(this)||this;return i._createPolyline(t,r,n),i}return e.prototype._createPolyline=function(t,r,n){var i=t.getItemLayout(r),o=new ge({shape:{points:i}});this.add(o),this._updateCommonStl(t,r,n)},e.prototype.updateData=function(t,r,n){var i=t.hostModel,o=this.childAt(0),s={shape:{points:t.getItemLayout(r)}};lt(o,s,i,r),this._updateCommonStl(t,r,n)},e.prototype._updateCommonStl=function(t,r,n){var i=this.childAt(0),o=t.getItemModel(r),s=n&&n.emphasisLineStyle,l=n&&n.focus,u=n&&n.blurScope,v=n&&n.emphasisDisabled;if(!n||t.hasItemOption){var c=o.getModel("emphasis");s=c.getModel("lineStyle").getLineStyle(),v=c.get("disabled"),l=c.get("focus"),u=c.get("blurScope")}i.useStyle(t.getItemVisual(r,"style")),i.style.fill=null,i.style.strokeNoScale=!0;var h=i.ensureState("emphasis");h.style=s,ct(this,l,u,v)},e.prototype.updateLayout=function(t,r){var n=this.childAt(0);n.setShape("points",t.getItemLayout(r))},e}(Y),hy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t._lastFrame=0,t._lastFramePercent=0,t}return e.prototype.createLine=function(t,r,n){return new Au(t,r,n)},e.prototype._updateAnimationPoints=function(t,r){this._points=r;for(var n=[0],i=0,o=1;o<r.length;o++){var s=r[o-1],l=r[o];i+=Qe(s,l),n.push(i)}if(i===0){this._length=0;return}for(var o=0;o<n.length;o++)n[o]/=i;this._offsets=n,this._length=i},e.prototype._getLineLength=function(){return this._length},e.prototype._updateSymbolPosition=function(t){var r=t.__t<1?t.__t:2-t.__t,n=this._points,i=this._offsets,o=n.length;if(i){var s=this._lastFrame,l;if(r<this._lastFramePercent){var u=Math.min(s+1,o-1);for(l=u;l>=0&&!(i[l]<=r);l--);l=Math.min(l,o-2)}else{for(l=s;l<o&&!(i[l]>r);l++);l=Math.min(l-1,o-2)}var v=(r-i[l])/(i[l+1]-i[l]),c=n[l],h=n[l+1];t.x=c[0]*(1-v)+v*h[0],t.y=c[1]*(1-v)+v*h[1];var f=t.__t<1?h[0]-c[0]:c[0]-h[0],p=t.__t<1?h[1]-c[1]:c[1]-h[1];t.rotation=-Math.atan2(p,f)-Math.PI/2,this._lastFrame=l,this._lastFramePercent=r,t.ignore=!1}},e}(Iu),fy=function(){function a(){this.polyline=!1,this.curveness=0,this.segs=[]}return a}(),py=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r._off=0,r.hoverDataIdx=-1,r}return e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new fy},e.prototype.buildPath=function(t,r){var n=r.segs,i=r.curveness,o;if(r.polyline)for(o=this._off;o<n.length;){var s=n[o++];if(s>0){t.moveTo(n[o++],n[o++]);for(var l=1;l<s;l++)t.lineTo(n[o++],n[o++])}}else for(o=this._off;o<n.length;){var u=n[o++],v=n[o++],c=n[o++],h=n[o++];if(t.moveTo(u,v),i>0){var f=(u+c)/2-(v-h)*i,p=(v+h)/2-(c-u)*i;t.quadraticCurveTo(f,p,c,h)}else t.lineTo(c,h)}this.incremental&&(this._off=o,this.notClear=!0)},e.prototype.findDataIndex=function(t,r){var n=this.shape,i=n.segs,o=n.curveness,s=this.style.lineWidth;if(n.polyline)for(var l=0,u=0;u<i.length;){var v=i[u++];if(v>0)for(var c=i[u++],h=i[u++],f=1;f<v;f++){var p=i[u++],d=i[u++];if(qi(c,h,p,d,s,t,r))return l}l++}else for(var l=0,u=0;u<i.length;){var c=i[u++],h=i[u++],p=i[u++],d=i[u++];if(o>0){var g=(c+p)/2-(h-d)*o,m=(h+d)/2-(p-c)*o;if(ac(c,h,g,m,p,d,s,t,r))return l}else if(qi(c,h,p,d,s,t,r))return l;l++}return-1},e.prototype.contain=function(t,r){var n=this.transformCoordToLocal(t,r),i=this.getBoundingRect();if(t=n[0],r=n[1],i.contain(t,r)){var o=this.hoverDataIdx=this.findDataIndex(t,r);return o>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var r=this.shape,n=r.segs,i=1/0,o=1/0,s=-1/0,l=-1/0,u=0;u<n.length;){var v=n[u++],c=n[u++];i=Math.min(v,i),s=Math.max(v,s),o=Math.min(c,o),l=Math.max(c,l)}t=this._rect=new ht(i,o,s,l)}return t},e}(wt),dy=function(){function a(){this.group=new Y}return a.prototype.updateData=function(e){this._clear();var t=this._create();t.setShape({segs:e.getLayout("linesPoints")}),this._setCommon(t,e)},a.prototype.incrementalPrepareUpdate=function(e){this.group.removeAll(),this._clear()},a.prototype.incrementalUpdate=function(e,t){var r=this._newAdded[0],n=t.getLayout("linesPoints"),i=r&&r.shape.segs;if(i&&i.length<2e4){var o=i.length,s=new Float32Array(o+n.length);s.set(i),s.set(n,o),r.setShape({segs:s})}else{this._newAdded=[];var l=this._create();l.incremental=!0,l.setShape({segs:n}),this._setCommon(l,t),l.__startIndex=e.start}},a.prototype.remove=function(){this._clear()},a.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},a.prototype._create=function(){var e=new py({cursor:"default",ignoreCoarsePointer:!0});return this._newAdded.push(e),this.group.add(e),e},a.prototype._setCommon=function(e,t,r){var n=t.hostModel;e.setShape({polyline:n.get("polyline"),curveness:n.get(["lineStyle","curveness"])}),e.useStyle(n.getModel("lineStyle").getLineStyle()),e.style.strokeNoScale=!0;var i=t.getVisual("style");i&&i.stroke&&e.setStyle("stroke",i.stroke),e.setStyle("fill",null);var o=nt(e);o.seriesIndex=n.seriesIndex,e.on("mousemove",function(s){o.dataIndex=null;var l=e.hoverDataIdx;l>0&&(o.dataIndex=l+e.__startIndex)})},a.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},a}(),Lu={seriesType:"lines",plan:ai(),reset:function(a){var e=a.coordinateSystem;if(e){var t=a.get("polyline"),r=a.pipelineContext.large;return{progress:function(n,i){var o=[];if(r){var s=void 0,l=n.end-n.start;if(t){for(var u=0,v=n.start;v<n.end;v++)u+=a.getLineCoordsCount(v);s=new Float32Array(l+u*2)}else s=new Float32Array(l*4);for(var c=0,h=[],v=n.start;v<n.end;v++){var f=a.getLineCoords(v,o);t&&(s[c++]=f);for(var p=0;p<f;p++)h=e.dataToPoint(o[p],!1,h),s[c++]=h[0],s[c++]=h[1]}i.setLayout("linesPoints",s)}else for(var v=n.start;v<n.end;v++){var d=i.getItemModel(v),f=a.getLineCoords(v,o),g=[];if(t)for(var m=0;m<f;m++)g.push(e.dataToPoint(o[m]));else{g[0]=e.dataToPoint(o[0]),g[1]=e.dataToPoint(o[1]);var S=d.get(["lineStyle","curveness"]);+S&&(g[2]=[(g[0][0]+g[1][0])/2-(g[0][1]-g[1][1])*S,(g[0][1]+g[1][1])/2-(g[1][0]-g[0][0])*S])}i.setItemLayout(v,g)}}}}}},gy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this._updateLineDraw(i,t),s=t.get("zlevel"),l=t.get(["effect","trailLength"]),u=n.getZr(),v=u.painter.getType()==="svg";v||u.painter.getLayer(s).clear(!0),this._lastZlevel!=null&&!v&&u.configLayer(this._lastZlevel,{motionBlur:!1}),this._showEffect(t)&&l>0&&(v||u.configLayer(s,{motionBlur:!0,lastFrameAlpha:Math.max(Math.min(l/10+.9,1),0)})),o.updateData(i);var c=t.get("clip",!0)&&Sa(t.coordinateSystem,!1,t);c?this.group.setClipPath(c):this.group.removeClipPath(),this._lastZlevel=s,this._finished=!0},e.prototype.incrementalPrepareRender=function(t,r,n){var i=t.getData(),o=this._updateLineDraw(i,t);o.incrementalPrepareUpdate(i),this._clearLayer(n),this._finished=!1},e.prototype.incrementalRender=function(t,r,n){this._lineDraw.incrementalUpdate(t,r.getData()),this._finished=t.end===r.getData().count()},e.prototype.eachRendered=function(t){this._lineDraw&&this._lineDraw.eachRendered(t)},e.prototype.updateTransform=function(t,r,n){var i=t.getData(),o=t.pipelineContext;if(!this._finished||o.large||o.progressiveRender)return{update:!0};var s=Lu.reset(t,r,n);s.progress&&s.progress({start:0,end:i.count(),count:i.count()},i),this._lineDraw.updateLayout(),this._clearLayer(n)},e.prototype._updateLineDraw=function(t,r){var n=this._lineDraw,i=this._showEffect(r),o=!!r.get("polyline"),s=r.pipelineContext,l=s.large;return(!n||i!==this._hasEffet||o!==this._isPolyline||l!==this._isLargeDraw)&&(n&&n.remove(),n=this._lineDraw=l?new dy:new bi(o?i?hy:Au:i?Iu:xi),this._hasEffet=i,this._isPolyline=o,this._isLargeDraw=l),this.group.add(n.group),n},e.prototype._showEffect=function(t){return!!t.get(["effect","show"])},e.prototype._clearLayer=function(t){var r=t.getZr(),n=r.painter.getType()==="svg";!n&&this._lastZlevel!=null&&r.painter.getLayer(this._lastZlevel).clear(!0)},e.prototype.remove=function(t,r){this._lineDraw&&this._lineDraw.remove(),this._lineDraw=null,this._clearLayer(r)},e.prototype.dispose=function(t,r){this.remove(t,r)},e.type="lines",e}(ft),yy=typeof Uint32Array=="undefined"?Array:Uint32Array,my=typeof Float64Array=="undefined"?Array:Float64Array;function hs(a){var e=a.data;e&&e[0]&&e[0][0]&&e[0][0].coord&&(a.data=B(e,function(t){var r=[t[0].coord,t[1].coord],n={coords:r};return t[0].name&&(n.fromName=t[0].name),t[1].name&&(n.toName=t[1].name),Jn([n,t[0],t[1]])}))}var Sy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.init=function(t){t.data=t.data||[],hs(t);var r=this._processFlatCoordsArray(t.data);this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset,r.flatCoords&&(t.data=new Float32Array(r.count)),a.prototype.init.apply(this,arguments)},e.prototype.mergeOption=function(t){if(hs(t),t.data){var r=this._processFlatCoordsArray(t.data);this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset,r.flatCoords&&(t.data=new Float32Array(r.count))}a.prototype.mergeOption.apply(this,arguments)},e.prototype.appendData=function(t){var r=this._processFlatCoordsArray(t.data);r.flatCoords&&(this._flatCoords?(this._flatCoords=Ur(this._flatCoords,r.flatCoords),this._flatCoordsOffset=Ur(this._flatCoordsOffset,r.flatCoordsOffset)):(this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset),t.data=new Float32Array(r.count)),this.getRawData().appendData(t.data)},e.prototype._getCoordsFromItemModel=function(t){var r=this.getData().getItemModel(t),n=r.option instanceof Array?r.option:r.getShallow("coords");return n},e.prototype.getLineCoordsCount=function(t){return this._flatCoordsOffset?this._flatCoordsOffset[t*2+1]:this._getCoordsFromItemModel(t).length},e.prototype.getLineCoords=function(t,r){if(this._flatCoordsOffset){for(var n=this._flatCoordsOffset[t*2],i=this._flatCoordsOffset[t*2+1],o=0;o<i;o++)r[o]=r[o]||[],r[o][0]=this._flatCoords[n+o*2],r[o][1]=this._flatCoords[n+o*2+1];return i}else{for(var s=this._getCoordsFromItemModel(t),o=0;o<s.length;o++)r[o]=r[o]||[],r[o][0]=s[o][0],r[o][1]=s[o][1];return s.length}},e.prototype._processFlatCoordsArray=function(t){var r=0;if(this._flatCoords&&(r=this._flatCoords.length),jt(t[0])){for(var n=t.length,i=new yy(n),o=new my(n),s=0,l=0,u=0,v=0;v<n;){u++;var c=t[v++];i[l++]=s+r,i[l++]=c;for(var h=0;h<c;h++){var f=t[v++],p=t[v++];o[s++]=f,o[s++]=p}}return{flatCoordsOffset:new Uint32Array(i.buffer,0,l),flatCoords:o,count:u}}return{flatCoordsOffset:null,flatCoords:null,count:t.length}},e.prototype.getInitialData=function(t,r){var n=new Mt(["value"],this);return n.hasItemOption=!1,n.initData(t.data,[],function(i,o,s,l){if(i instanceof Array)return NaN;n.hasItemOption=!0;var u=i.value;if(u!=null)return u instanceof Array?u[l]:u}),n},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=i.getItemModel(t),s=o.get("name");if(s)return s;var l=o.get("fromName"),u=o.get("toName"),v=[];return l!=null&&v.push(l),u!=null&&v.push(u),At("nameValue",{name:v.join(" > ")})},e.prototype.preventIncremental=function(){return!!this.get(["effect","show"])},e.prototype.getProgressive=function(){var t=this.option.progressive;return t==null?this.option.large?1e4:this.get("progressive"):t},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return t==null?this.option.large?2e4:this.get("progressiveThreshold"):t},e.prototype.getZLevelKey=function(){var t=this.getModel("effect"),r=t.get("trailLength");return this.getData().count()>this.getProgressiveThreshold()?this.id:t.get("show")&&r>0?r+"":""},e.type="series.lines",e.dependencies=["grid","polar","geo","calendar"],e.defaultOption={coordinateSystem:"geo",z:2,legendHoverLink:!0,xAxisIndex:0,yAxisIndex:0,symbol:["none","none"],symbolSize:[10,10],geoIndex:0,effect:{show:!1,period:4,constantSpeed:0,symbol:"circle",symbolSize:3,loop:!0,trailLength:.2},large:!1,largeThreshold:2e3,polyline:!1,clip:!0,label:{show:!1,position:"end"},lineStyle:{opacity:.5}},e}(dt);function Gr(a){return a instanceof Array||(a=[a,a]),a}var xy={seriesType:"lines",reset:function(a){var e=Gr(a.get("symbol")),t=Gr(a.get("symbolSize")),r=a.getData();r.setVisual("fromSymbol",e&&e[0]),r.setVisual("toSymbol",e&&e[1]),r.setVisual("fromSymbolSize",t&&t[0]),r.setVisual("toSymbolSize",t&&t[1]);function n(i,o){var s=i.getItemModel(o),l=Gr(s.getShallow("symbol",!0)),u=Gr(s.getShallow("symbolSize",!0));l[0]&&i.setItemVisual(o,"fromSymbol",l[0]),l[1]&&i.setItemVisual(o,"toSymbol",l[1]),u[0]&&i.setItemVisual(o,"fromSymbolSize",u[0]),u[1]&&i.setItemVisual(o,"toSymbolSize",u[1])}return{dataEach:r.hasItemOption?n:null}}};function by(a){a.registerChartView(gy),a.registerSeriesModel(Sy),a.registerLayout(Lu),a.registerVisual(xy)}var _y=256,wy=function(){function a(){this.blurSize=30,this.pointSize=20,this.maxOpacity=1,this.minOpacity=0,this._gradientPixels={inRange:null,outOfRange:null};var e=ji.createCanvas();this.canvas=e}return a.prototype.update=function(e,t,r,n,i,o){var s=this._getBrush(),l=this._getGradient(i,"inRange"),u=this._getGradient(i,"outOfRange"),v=this.pointSize+this.blurSize,c=this.canvas,h=c.getContext("2d"),f=e.length;c.width=t,c.height=r;for(var p=0;p<f;++p){var d=e[p],g=d[0],m=d[1],S=d[2],y=n(S);h.globalAlpha=y,h.drawImage(s,g-v,m-v)}if(!c.width||!c.height)return c;for(var b=h.getImageData(0,0,c.width,c.height),_=b.data,x=0,w=_.length,D=this.minOpacity,T=this.maxOpacity,I=T-D;x<w;){var y=_[x+3]/256,A=Math.floor(y*(_y-1))*4;if(y>0){var P=o(y)?l:u;y>0&&(y=y*I+D),_[x++]=P[A],_[x++]=P[A+1],_[x++]=P[A+2],_[x++]=P[A+3]*y*256}else x+=4}return h.putImageData(b,0,0),c},a.prototype._getBrush=function(){var e=this._brushCanvas||(this._brushCanvas=ji.createCanvas()),t=this.pointSize+this.blurSize,r=t*2;e.width=r,e.height=r;var n=e.getContext("2d");return n.clearRect(0,0,r,r),n.shadowOffsetX=r,n.shadowBlur=this.blurSize,n.shadowColor="#000",n.beginPath(),n.arc(-t,t,this.pointSize,0,Math.PI*2,!0),n.closePath(),n.fill(),e},a.prototype._getGradient=function(e,t){for(var r=this._gradientPixels,n=r[t]||(r[t]=new Uint8ClampedArray(256*4)),i=[0,0,0,0],o=0,s=0;s<256;s++)e[t](s/255,!0,i),n[o++]=i[0],n[o++]=i[1],n[o++]=i[2],n[o++]=i[3];return n},a}();function Dy(a,e,t){var r=a[1]-a[0];e=B(e,function(o){return{interval:[(o.interval[0]-a[0])/r,(o.interval[1]-a[0])/r]}});var n=e.length,i=0;return function(o){var s;for(s=i;s<n;s++){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){i=s;break}}if(s===n)for(s=i-1;s>=0;s--){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){i=s;break}}return s>=0&&s<n&&t[s]}}function Ty(a,e){var t=a[1]-a[0];return e=[(e[0]-a[0])/t,(e[1]-a[0])/t],function(r){return r>=e[0]&&r<=e[1]}}function fs(a){var e=a.dimensions;return e[0]==="lng"&&e[1]==="lat"}var Iy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i;r.eachComponent("visualMap",function(s){s.eachTargetSeries(function(l){l===t&&(i=s)})}),this._progressiveEls=null,this.group.removeAll();var o=t.coordinateSystem;o.type==="cartesian2d"||o.type==="calendar"?this._renderOnCartesianAndCalendar(t,n,0,t.getData().count()):fs(o)&&this._renderOnGeo(o,t,i,n)},e.prototype.incrementalPrepareRender=function(t,r,n){this.group.removeAll()},e.prototype.incrementalRender=function(t,r,n,i){var o=r.coordinateSystem;o&&(fs(o)?this.render(r,n,i):(this._progressiveEls=[],this._renderOnCartesianAndCalendar(r,i,t.start,t.end,!0)))},e.prototype.eachRendered=function(t){ma(this._progressiveEls||this.group,t)},e.prototype._renderOnCartesianAndCalendar=function(t,r,n,i,o){var s=t.coordinateSystem,l=xa(s,"cartesian2d"),u,v,c,h;if(l){var f=s.getAxis("x"),p=s.getAxis("y");u=f.getBandWidth()+.5,v=p.getBandWidth()+.5,c=f.scale.getExtent(),h=p.scale.getExtent()}for(var d=this.group,g=t.getData(),m=t.getModel(["emphasis","itemStyle"]).getItemStyle(),S=t.getModel(["blur","itemStyle"]).getItemStyle(),y=t.getModel(["select","itemStyle"]).getItemStyle(),b=t.get(["itemStyle","borderRadius"]),_=It(t),x=t.getModel("emphasis"),w=x.get("focus"),D=x.get("blurScope"),T=x.get("disabled"),I=l?[g.mapDimension("x"),g.mapDimension("y"),g.mapDimension("value")]:[g.mapDimension("time"),g.mapDimension("value")],A=n;A<i;A++){var P=void 0,E=g.getItemVisual(A,"style");if(l){var L=g.get(I[0],A),M=g.get(I[1],A);if(isNaN(g.get(I[2],A))||isNaN(L)||isNaN(M)||L<c[0]||L>c[1]||M<h[0]||M>h[1])continue;var R=s.dataToPoint([L,M]);P=new Lt({shape:{x:R[0]-u/2,y:R[1]-v/2,width:u,height:v},style:E})}else{if(isNaN(g.get(I[1],A)))continue;P=new Lt({z2:1,shape:s.dataToRect([g.get(I[0],A)]).contentShape,style:E})}if(g.hasItemOption){var N=g.getItemModel(A),G=N.getModel("emphasis");m=G.getModel("itemStyle").getItemStyle(),S=N.getModel(["blur","itemStyle"]).getItemStyle(),y=N.getModel(["select","itemStyle"]).getItemStyle(),b=N.get(["itemStyle","borderRadius"]),w=G.get("focus"),D=G.get("blurScope"),T=G.get("disabled"),_=It(N)}P.shape.r=b;var O=t.getRawValue(A),F="-";O&&O[2]!=null&&(F=O[2]+""),Ot(P,_,{labelFetcher:t,labelDataIndex:A,defaultOpacity:E.opacity,defaultText:F}),P.ensureState("emphasis").style=m,P.ensureState("blur").style=S,P.ensureState("select").style=y,ct(P,w,D,T),P.incremental=o,o&&(P.states.emphasis.hoverLayer=!0),d.add(P),g.setItemGraphicEl(A,P),this._progressiveEls&&this._progressiveEls.push(P)}},e.prototype._renderOnGeo=function(t,r,n,i){var o=n.targetVisuals.inRange,s=n.targetVisuals.outOfRange,l=r.getData(),u=this._hmLayer||this._hmLayer||new wy;u.blurSize=r.get("blurSize"),u.pointSize=r.get("pointSize"),u.minOpacity=r.get("minOpacity"),u.maxOpacity=r.get("maxOpacity");var v=t.getViewRect().clone(),c=t.getRoamTransform();v.applyTransform(c);var h=Math.max(v.x,0),f=Math.max(v.y,0),p=Math.min(v.width+v.x,i.getWidth()),d=Math.min(v.height+v.y,i.getHeight()),g=p-h,m=d-f,S=[l.mapDimension("lng"),l.mapDimension("lat"),l.mapDimension("value")],y=l.mapArray(S,function(w,D,T){var I=t.dataToPoint([w,D]);return I[0]-=h,I[1]-=f,I.push(T),I}),b=n.getExtent(),_=n.type==="visualMap.continuous"?Ty(b,n.option.range):Dy(b,n.getPieceList(),n.option.selected);u.update(y,g,m,o.color.getNormalizer(),{inRange:o.color.getColorMapper(),outOfRange:s.color.getColorMapper()},_);var x=new Fe({style:{width:g,height:m,x:h,y:f,image:u.canvas},silent:!0});this.group.add(x)},e.type="heatmap",e}(ft),Ay=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){return Be(null,this,{generateCoord:"value"})},e.prototype.preventIncremental=function(){var t=Cl.get(this.get("coordinateSystem"));if(t&&t.dimensions)return t.dimensions[0]==="lng"&&t.dimensions[1]==="lat"},e.type="series.heatmap",e.dependencies=["grid","geo","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,geoIndex:0,blurSize:30,pointSize:20,maxOpacity:1,minOpacity:0,select:{itemStyle:{borderColor:"#212121"}}},e}(dt);function Ly(a){a.registerChartView(Iy),a.registerSeriesModel(Ay)}var Cy=["itemStyle","borderWidth"],ps=[{xy:"x",wh:"width",index:0,posDesc:["left","right"]},{xy:"y",wh:"height",index:1,posDesc:["top","bottom"]}],an=new ca,Py=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=this.group,o=t.getData(),s=this._data,l=t.coordinateSystem,u=l.getBaseAxis(),v=u.isHorizontal(),c=l.master.getRect(),h={ecSize:{width:n.getWidth(),height:n.getHeight()},seriesModel:t,coordSys:l,coordSysExtent:[[c.x,c.x+c.width],[c.y,c.y+c.height]],isHorizontal:v,valueDim:ps[+v],categoryDim:ps[1-+v]};o.diff(s).add(function(p){if(o.hasValue(p)){var d=gs(o,p),g=ds(o,p,d,h),m=ys(o,h,g);o.setItemGraphicEl(p,m),i.add(m),Ss(m,h,g)}}).update(function(p,d){var g=s.getItemGraphicEl(d);if(!o.hasValue(p)){i.remove(g);return}var m=gs(o,p),S=ds(o,p,m,h),y=Nu(o,S);g&&y!==g.__pictorialShapeStr&&(i.remove(g),o.setItemGraphicEl(p,null),g=null),g?Gy(g,h,S):g=ys(o,h,S,!0),o.setItemGraphicEl(p,g),g.__pictorialSymbolMeta=S,i.add(g),Ss(g,h,S)}).remove(function(p){var d=s.getItemGraphicEl(p);d&&ms(s,p,d.__pictorialSymbolMeta.animationModel,d)}).execute();var f=t.get("clip",!0)?Sa(t.coordinateSystem,!1,t):null;return f?i.setClipPath(f):i.removeClipPath(),this._data=o,this.group},e.prototype.remove=function(t,r){var n=this.group,i=this._data;t.get("animation")?i&&i.eachItemGraphicEl(function(o){ms(i,nt(o).dataIndex,t,o)}):n.removeAll()},e.type="pictorialBar",e}(ft);function ds(a,e,t,r){var n=a.getItemLayout(e),i=t.get("symbolRepeat"),o=t.get("symbolClip"),s=t.get("symbolPosition")||"start",l=t.get("symbolRotate"),u=(l||0)*Math.PI/180||0,v=t.get("symbolPatternSize")||2,c=t.isAnimationEnabled(),h={dataIndex:e,layout:n,itemModel:t,symbolType:a.getItemVisual(e,"symbol")||"circle",style:a.getItemVisual(e,"style"),symbolClip:o,symbolRepeat:i,symbolRepeatDirection:t.get("symbolRepeatDirection"),symbolPatternSize:v,rotation:u,animationModel:c?t:null,hoverScale:c&&t.get(["emphasis","scale"]),z2:t.getShallow("z",!0)||0};My(t,i,n,r,h),Ey(a,e,n,i,o,h.boundingLength,h.pxSign,v,r,h),Ry(t,h.symbolScale,u,r,h);var f=h.symbolSize,p=_r(t.get("symbolOffset"),f);return Ny(t,f,n,i,o,p,s,h.valueLineWidth,h.boundingLength,h.repeatCutLength,r,h),h}function My(a,e,t,r,n){var i=r.valueDim,o=a.get("symbolBoundingData"),s=r.coordSys.getOtherAxis(r.coordSys.getBaseAxis()),l=s.toGlobalCoord(s.dataToCoord(0)),u=1-+(t[i.wh]<=0),v;if(H(o)){var c=[nn(s,o[0])-l,nn(s,o[1])-l];c[1]<c[0]&&c.reverse(),v=c[u]}else o!=null?v=nn(s,o)-l:e?v=r.coordSysExtent[i.index][u]-l:v=t[i.wh];n.boundingLength=v,e&&(n.repeatCutLength=t[i.wh]);var h=i.xy==="x",f=s.inverse;n.pxSign=h&&!f||!h&&f?v>=0?1:-1:v>0?1:-1}function nn(a,e){return a.toGlobalCoord(a.dataToCoord(a.scale.parse(e)))}function Ey(a,e,t,r,n,i,o,s,l,u){var v=l.valueDim,c=l.categoryDim,h=Math.abs(t[c.wh]),f=a.getItemVisual(e,"symbolSize"),p;H(f)?p=f.slice():f==null?p=["100%","100%"]:p=[f,f],p[c.index]=z(p[c.index],h),p[v.index]=z(p[v.index],r?h:Math.abs(i)),u.symbolSize=p;var d=u.symbolScale=[p[0]/s,p[1]/s];d[v.index]*=(l.isHorizontal?-1:1)*o}function Ry(a,e,t,r,n){var i=a.get(Cy)||0;i&&(an.attr({scaleX:e[0],scaleY:e[1],rotation:t}),an.updateTransform(),i/=an.getLineScale(),i*=e[r.valueDim.index]),n.valueLineWidth=i||0}function Ny(a,e,t,r,n,i,o,s,l,u,v,c){var h=v.categoryDim,f=v.valueDim,p=c.pxSign,d=Math.max(e[f.index]+s,0),g=d;if(r){var m=Math.abs(l),S=kt(a.get("symbolMargin"),"15%")+"",y=!1;S.lastIndexOf("!")===S.length-1&&(y=!0,S=S.slice(0,S.length-1));var b=z(S,e[f.index]),_=Math.max(d+b*2,0),x=y?0:b*2,w=nc(r),D=w?r:xs((m+x)/_),T=m-D*d;b=T/2/(y?D:Math.max(D-1,1)),_=d+b*2,x=y?0:b*2,!w&&r!=="fixed"&&(D=u?xs((Math.abs(u)+x)/_):0),g=D*_-x,c.repeatTimes=D,c.symbolMargin=b}var I=p*(g/2),A=c.pathPosition=[];A[h.index]=t[h.wh]/2,A[f.index]=o==="start"?I:o==="end"?l-I:l/2,i&&(A[0]+=i[0],A[1]+=i[1]);var P=c.bundlePosition=[];P[h.index]=t[h.xy],P[f.index]=t[f.xy];var E=c.barRectShape=U({},t);E[f.wh]=p*Math.max(Math.abs(t[f.wh]),Math.abs(A[f.index]+I)),E[h.wh]=t[h.wh];var L=c.clipShape={};L[h.xy]=-t[h.xy],L[h.wh]=v.ecSize[h.wh],L[f.xy]=0,L[f.wh]=t[f.wh]}function Cu(a){var e=a.symbolPatternSize,t=qt(a.symbolType,-e/2,-e/2,e,e);return t.attr({culling:!0}),t.type!=="image"&&t.setStyle({strokeNoScale:!0}),t}function Pu(a,e,t,r){var n=a.__pictorialBundle,i=t.symbolSize,o=t.valueLineWidth,s=t.pathPosition,l=e.valueDim,u=t.repeatTimes||0,v=0,c=i[e.valueDim.index]+o+t.symbolMargin*2;for(Di(a,function(d){d.__pictorialAnimationIndex=v,d.__pictorialRepeatTimes=u,v<u?Ve(d,null,p(v),t,r):Ve(d,null,{scaleX:0,scaleY:0},t,r,function(){n.remove(d)}),v++});v<u;v++){var h=Cu(t);h.__pictorialAnimationIndex=v,h.__pictorialRepeatTimes=u,n.add(h);var f=p(v);Ve(h,{x:f.x,y:f.y,scaleX:0,scaleY:0},{scaleX:f.scaleX,scaleY:f.scaleY,rotation:f.rotation},t,r)}function p(d){var g=s.slice(),m=t.pxSign,S=d;return(t.symbolRepeatDirection==="start"?m>0:m<0)&&(S=u-1-d),g[l.index]=c*(S-u/2+.5)+s[l.index],{x:g[0],y:g[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation}}}function Mu(a,e,t,r){var n=a.__pictorialBundle,i=a.__pictorialMainPath;i?Ve(i,null,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation},t,r):(i=a.__pictorialMainPath=Cu(t),n.add(i),Ve(i,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:0,scaleY:0,rotation:t.rotation},{scaleX:t.symbolScale[0],scaleY:t.symbolScale[1]},t,r))}function Eu(a,e,t){var r=U({},e.barRectShape),n=a.__pictorialBarRect;n?Ve(n,null,{shape:r},e,t):(n=a.__pictorialBarRect=new Lt({z2:2,shape:r,silent:!0,style:{stroke:"transparent",fill:"transparent",lineWidth:0}}),n.disableMorphing=!0,a.add(n))}function Ru(a,e,t,r){if(t.symbolClip){var n=a.__pictorialClipPath,i=U({},t.clipShape),o=e.valueDim,s=t.animationModel,l=t.dataIndex;if(n)lt(n,{shape:i},s,l);else{i[o.wh]=0,n=new Lt({shape:i}),a.__pictorialBundle.setClipPath(n),a.__pictorialClipPath=n;var u={};u[o.wh]=t.clipShape[o.wh],ua[r?"updateProps":"initProps"](n,{shape:u},s,l)}}}function gs(a,e){var t=a.getItemModel(e);return t.getAnimationDelayParams=ky,t.isAnimationEnabled=Vy,t}function ky(a){return{index:a.__pictorialAnimationIndex,count:a.__pictorialRepeatTimes}}function Vy(){return this.parentModel.isAnimationEnabled()&&!!this.getShallow("animation")}function ys(a,e,t,r){var n=new Y,i=new Y;return n.add(i),n.__pictorialBundle=i,i.x=t.bundlePosition[0],i.y=t.bundlePosition[1],t.symbolRepeat?Pu(n,e,t):Mu(n,e,t),Eu(n,t,r),Ru(n,e,t,r),n.__pictorialShapeStr=Nu(a,t),n.__pictorialSymbolMeta=t,n}function Gy(a,e,t){var r=t.animationModel,n=t.dataIndex,i=a.__pictorialBundle;lt(i,{x:t.bundlePosition[0],y:t.bundlePosition[1]},r,n),t.symbolRepeat?Pu(a,e,t,!0):Mu(a,e,t,!0),Eu(a,t,!0),Ru(a,e,t,!0)}function ms(a,e,t,r){var n=r.__pictorialBarRect;n&&n.removeTextContent();var i=[];Di(r,function(o){i.push(o)}),r.__pictorialMainPath&&i.push(r.__pictorialMainPath),r.__pictorialClipPath&&(t=null),C(i,function(o){$r(o,{scaleX:0,scaleY:0},t,e,function(){r.parent&&r.parent.remove(r)})}),a.setItemGraphicEl(e,null)}function Nu(a,e){return[a.getItemVisual(e.dataIndex,"symbol")||"none",!!e.symbolRepeat,!!e.symbolClip].join(":")}function Di(a,e,t){C(a.__pictorialBundle.children(),function(r){r!==a.__pictorialBarRect&&e.call(t,r)})}function Ve(a,e,t,r,n,i){e&&a.attr(e),r.symbolClip&&!n?t&&a.attr(t):t&&ua[n?"updateProps":"initProps"](a,t,r.animationModel,r.dataIndex,i)}function Ss(a,e,t){var r=t.dataIndex,n=t.itemModel,i=n.getModel("emphasis"),o=i.getModel("itemStyle").getItemStyle(),s=n.getModel(["blur","itemStyle"]).getItemStyle(),l=n.getModel(["select","itemStyle"]).getItemStyle(),u=n.getShallow("cursor"),v=i.get("focus"),c=i.get("blurScope"),h=i.get("scale");Di(a,function(d){if(d instanceof Fe){var g=d.style;d.useStyle(U({image:g.image,x:g.x,y:g.y,width:g.width,height:g.height},t.style))}else d.useStyle(t.style);var m=d.ensureState("emphasis");m.style=o,h&&(m.scaleX=d.scaleX*1.1,m.scaleY=d.scaleY*1.1),d.ensureState("blur").style=s,d.ensureState("select").style=l,u&&(d.cursor=u),d.z2=t.z2});var f=e.valueDim.posDesc[+(t.boundingLength>0)],p=a.__pictorialBarRect;p.ignoreClip=!0,Ot(p,It(n),{labelFetcher:e.seriesModel,labelDataIndex:r,defaultText:Cn(e.seriesModel.getData(),r),inheritColor:t.style.fill,defaultOpacity:t.style.opacity,defaultOutsidePosition:f}),ct(a,v,c,i.get("disabled"))}function xs(a){var e=Math.round(a);return Math.abs(a-e)<1e-4?e:Math.ceil(a)}var zy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t.defaultSymbol="roundRect",t}return e.prototype.getInitialData=function(t){return t.stack=null,a.prototype.getInitialData.apply(this,arguments)},e.type="series.pictorialBar",e.dependencies=["grid"],e.defaultOption=zl(Ki.defaultOption,{symbol:"circle",symbolSize:null,symbolRotate:null,symbolPosition:null,symbolOffset:null,symbolMargin:null,symbolRepeat:!1,symbolRepeatDirection:"end",symbolClip:!1,symbolBoundingData:null,symbolPatternSize:400,barGap:"-100%",clip:!1,progressive:0,emphasis:{scale:!1},select:{itemStyle:{borderColor:"#212121"}}}),e}(Ki);function Oy(a){a.registerChartView(Py),a.registerSeriesModel(zy),a.registerLayout(a.PRIORITY.VISUAL.LAYOUT,st(ic,"pictorialBar")),a.registerLayout(a.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,oc("pictorialBar"))}var By=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._layers=[],t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this,s=this.group,l=t.getLayerSeries(),u=i.getLayout("layoutInfo"),v=u.rect,c=u.boundaryGap;s.x=0,s.y=v.y+c[0];function h(g){return g.name}var f=new Oe(this._layersSeries||[],l,h,h),p=[];f.add(et(d,this,"add")).update(et(d,this,"update")).remove(et(d,this,"remove")).execute();function d(g,m,S){var y=o._layers;if(g==="remove"){s.remove(y[m]);return}for(var b=[],_=[],x,w=l[m].indices,D=0;D<w.length;D++){var T=i.getItemLayout(w[D]),I=T.x,A=T.y0,P=T.y;b.push(I,A),_.push(I,A+P),x=i.getItemVisual(w[D],"style")}var E,L=i.getItemLayout(w[0]),M=t.getModel("label"),R=M.get("margin"),N=t.getModel("emphasis");if(g==="add"){var G=p[m]=new Y;E=new sc({shape:{points:b,stackedOnPoints:_,smooth:.4,stackedOnSmooth:.4,smoothConstraint:!1},z2:0}),G.add(E),s.add(G),t.isAnimationEnabled()&&E.setClipPath(Fy(E.getBoundingRect(),t,function(){E.removeClipPath()}))}else{var G=y[S];E=G.childAt(0),s.add(G),p[m]=G,lt(E,{shape:{points:b,stackedOnPoints:_}},t),fe(E)}Ot(E,It(t),{labelDataIndex:w[D-1],defaultText:i.getName(w[D-1]),inheritColor:x.fill},{normal:{verticalAlign:"middle"}}),E.setTextConfig({position:null,local:!0});var O=E.getTextContent();O&&(O.x=L.x-R,O.y=L.y0+L.y/2),E.useStyle(x),i.setItemGraphicEl(m,E),Et(E,t),ct(E,N.get("focus"),N.get("blurScope"),N.get("disabled"))}this._layersSeries=l,this._layers=p},e.type="themeRiver",e}(ft);function Fy(a,e,t){var r=new Lt({shape:{x:a.x-10,y:a.y-10,width:0,height:a.height+20}});return Tt(r,{shape:{x:a.x-50,width:a.width+100,height:a.height+20}},e,t),r}var on=2,Wy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new va(et(this.getData,this),et(this.getRawData,this))},e.prototype.fixData=function(t){var r=t.length,n={},i=An(t,function(h){return n.hasOwnProperty(h[0]+"")||(n[h[0]+""]=-1),h[2]}),o=[];i.buckets.each(function(h,f){o.push({name:f,dataList:h})});for(var s=o.length,l=0;l<s;++l){for(var u=o[l].name,v=0;v<o[l].dataList.length;++v){var c=o[l].dataList[v][0]+"";n[c]=l}for(var c in n)n.hasOwnProperty(c)&&n[c]!==l&&(n[c]=l,t[r]=[c,0,u],r++)}return t},e.prototype.getInitialData=function(t,r){for(var n=this.getReferringComponents("singleAxis",da).models[0],i=n.get("type"),o=Pt(t.data,function(p){return p[2]!==void 0}),s=this.fixData(o||[]),l=[],u=this.nameMap=j(),v=0,c=0;c<s.length;++c)l.push(s[c][on]),u.get(s[c][on])||(u.set(s[c][on],v),v++);var h=Qn(s,{coordDimensions:["single"],dimensionsDefine:[{name:"time",type:Ln(i)},{name:"value",type:"float"},{name:"name",type:"ordinal"}],encodeDefine:{single:0,value:1,itemName:2}}).dimensions,f=new Mt(h,this);return f.initData(s),f},e.prototype.getLayerSeries=function(){for(var t=this.getData(),r=t.count(),n=[],i=0;i<r;++i)n[i]=i;var o=t.mapDimension("single"),s=An(n,function(u){return t.get("name",u)}),l=[];return s.buckets.each(function(u,v){u.sort(function(c,h){return t.get(o,c)-t.get(o,h)}),l.push({name:v,indices:u})}),l},e.prototype.getAxisTooltipData=function(t,r,n){H(t)||(t=t?[t]:[]);for(var i=this.getData(),o=this.getLayerSeries(),s=[],l=o.length,u,v=0;v<l;++v){for(var c=Number.MAX_VALUE,h=-1,f=o[v].indices.length,p=0;p<f;++p){var d=i.get(t[0],o[v].indices[p]),g=Math.abs(d-r);g<=c&&(u=d,c=g,h=o[v].indices[p])}s.push(h)}return{dataIndices:s,nestestValue:u}},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=i.getName(t),s=i.get(i.mapDimension("value"),t);return At("nameValue",{name:o,value:s})},e.type="series.themeRiver",e.dependencies=["singleAxis"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"singleAxis",boundaryGap:["10%","10%"],singleAxisIndex:0,animationEasing:"linear",label:{margin:4,show:!0,position:"left",fontSize:11},emphasis:{label:{show:!0}}},e}(dt);function Hy(a,e){a.eachSeriesByType("themeRiver",function(t){var r=t.getData(),n=t.coordinateSystem,i={},o=n.getRect();i.rect=o;var s=t.get("boundaryGap"),l=n.getAxis();if(i.boundaryGap=s,l.orient==="horizontal"){s[0]=z(s[0],o.height),s[1]=z(s[1],o.height);var u=o.height-s[0]-s[1];bs(r,t,u)}else{s[0]=z(s[0],o.width),s[1]=z(s[1],o.width);var v=o.width-s[0]-s[1];bs(r,t,v)}r.setLayout("layoutInfo",i)})}function bs(a,e,t){if(a.count())for(var r=e.coordinateSystem,n=e.getLayerSeries(),i=a.mapDimension("single"),o=a.mapDimension("value"),s=B(n,function(g){return B(g.indices,function(m){var S=r.dataToPoint(a.get(i,m));return S[1]=a.get(o,m),S})}),l=Uy(s),u=l.y0,v=t/l.max,c=n.length,h=n[0].indices.length,f,p=0;p<h;++p){f=u[p]*v,a.setItemLayout(n[0].indices[p],{layerIndex:0,x:s[0][p][0],y0:f,y:s[0][p][1]*v});for(var d=1;d<c;++d)f+=s[d-1][p][1]*v,a.setItemLayout(n[d].indices[p],{layerIndex:d,x:s[d][p][0],y0:f,y:s[d][p][1]*v})}}function Uy(a){for(var e=a.length,t=a[0].length,r=[],n=[],i=0,o=0;o<t;++o){for(var s=0,l=0;l<e;++l)s+=a[l][o][1];s>i&&(i=s),r.push(s)}for(var u=0;u<t;++u)n[u]=(i-r[u])/2;i=0;for(var v=0;v<t;++v){var c=r[v]+n[v];c>i&&(i=c)}return{y0:n,max:i}}function $y(a){a.registerChartView(By),a.registerSeriesModel(Wy),a.registerLayout(Hy),a.registerProcessor(fa("themeRiver"))}var Yy=2,Xy=4,_s=function(a){V(e,a);function e(t,r,n,i){var o=a.call(this)||this;o.z2=Yy,o.textConfig={inside:!0},nt(o).seriesIndex=r.seriesIndex;var s=new Vt({z2:Xy,silent:t.getModel().get(["label","silent"])});return o.setTextContent(s),o.updateData(!0,t,r,n,i),o}return e.prototype.updateData=function(t,r,n,i,o){this.node=r,r.piece=this,n=n||this._seriesModel,i=i||this._ecModel;var s=this;nt(s).dataIndex=r.dataIndex;var l=r.getModel(),u=l.getModel("emphasis"),v=r.getLayout(),c=U({},v);c.label=null;var h=r.getVisual("style");h.lineJoin="bevel";var f=r.getVisual("decal");f&&(h.decal=Kn(f,o));var p=Ji(l.getModel("itemStyle"),c,!0);U(c,p),C(In,function(S){var y=s.ensureState(S),b=l.getModel([S,"itemStyle"]);y.style=b.getItemStyle();var _=Ji(b,c);_&&(y.shape=_)}),t?(s.setShape(c),s.shape.r=v.r0,Tt(s,{shape:{r:v.r}},n,r.dataIndex)):(lt(s,{shape:c},n),fe(s)),s.useStyle(h),this._updateLabel(n);var d=l.getShallow("cursor");d&&s.attr("cursor",d),this._seriesModel=n||this._seriesModel,this._ecModel=i||this._ecModel;var g=u.get("focus"),m=g==="relative"?Ur(r.getAncestorsIndices(),r.getDescendantIndices()):g==="ancestor"?r.getAncestorsIndices():g==="descendant"?r.getDescendantIndices():g;ct(this,m,u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t){var r=this,n=this.node.getModel(),i=n.getModel("label"),o=this.node.getLayout(),s=o.endAngle-o.startAngle,l=(o.startAngle+o.endAngle)/2,u=Math.cos(l),v=Math.sin(l),c=this,h=c.getTextContent(),f=this.node.dataIndex,p=i.get("minAngle")/180*Math.PI,d=i.get("show")&&!(p!=null&&Math.abs(s)<p);h.ignore=!d,C(uc,function(m){var S=m==="normal"?n.getModel("label"):n.getModel([m,"label"]),y=m==="normal",b=y?h:h.ensureState(m),_=t.getFormattedLabel(f,m);y&&(_=_||r.node.name),b.style=bt(S,{},null,m!=="normal",!0),_&&(b.style.text=_);var x=S.get("show");x!=null&&!y&&(b.ignore=!x);var w=g(S,"position"),D=y?c:c.states[m],T=D.style.fill;D.textConfig={outsideFill:S.get("color")==="inherit"?T:null,inside:w!=="outside"};var I,A=g(S,"distance")||0,P=g(S,"align"),E=g(S,"rotate"),L=Math.PI*.5,M=Math.PI*1.5,R=Je(E==="tangential"?Math.PI/2-l:l),N=R>L&&!lc(R-L)&&R<M;w==="outside"?(I=o.r+A,P=N?"right":"left"):!P||P==="center"?(s===2*Math.PI&&o.r0===0?I=0:I=(o.r+o.r0)/2,P="center"):P==="left"?(I=o.r0+A,P=N?"right":"left"):P==="right"&&(I=o.r-A,P=N?"left":"right"),b.style.align=P,b.style.verticalAlign=g(S,"verticalAlign")||"middle",b.x=I*u+o.cx,b.y=I*v+o.cy;var G=0;E==="radial"?G=Je(-l)+(N?Math.PI:0):E==="tangential"?G=Je(Math.PI/2-l)+(N?Math.PI:0):jt(E)&&(G=E*Math.PI/180),b.rotation=Je(G)});function g(m,S){var y=m.get(S);return y==null?i.get(S):y}h.dirtyStyle()},e}(sr),zn="sunburstRootToNode",ws="sunburstHighlight",Zy="sunburstUnhighlight";function qy(a){a.registerAction({type:zn,update:"updateView"},function(e,t){t.eachComponent({mainType:"series",subType:"sunburst",query:e},r);function r(n,i){var o=ur(e,[zn],n);if(o){var s=n.getViewRoot();s&&(e.direction=di(s,o.node)?"rollUp":"drillDown"),n.resetViewRoot(o.node)}}}),a.registerAction({type:ws,update:"none"},function(e,t,r){e=U({},e),t.eachComponent({mainType:"series",subType:"sunburst",query:e},n);function n(i){var o=ur(e,[ws],i);o&&(e.dataIndex=o.node.dataIndex)}r.dispatchAction(U(e,{type:"highlight"}))}),a.registerAction({type:Zy,update:"updateView"},function(e,t,r){e=U({},e),r.dispatchAction(U(e,{type:"downplay"}))})}var jy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n,i){var o=this;this.seriesModel=t,this.api=n,this.ecModel=r;var s=t.getData(),l=s.tree.root,u=t.getViewRoot(),v=this.group,c=t.get("renderLabelForZeroData"),h=[];u.eachNode(function(S){h.push(S)});var f=this._oldChildren||[];p(h,f),m(l,u),this._initEvents(),this._oldChildren=h;function p(S,y){if(S.length===0&&y.length===0)return;new Oe(y,S,b,b).add(_).update(_).remove(st(_,null)).execute();function b(x){return x.getId()}function _(x,w){var D=x==null?null:S[x],T=w==null?null:y[w];d(D,T)}}function d(S,y){if(!c&&S&&!S.getValue()&&(S=null),S!==l&&y!==l){if(y&&y.piece)S?(y.piece.updateData(!1,S,t,r,n),s.setItemGraphicEl(S.dataIndex,y.piece)):g(y);else if(S){var b=new _s(S,t,r,n);v.add(b),s.setItemGraphicEl(S.dataIndex,b)}}}function g(S){S&&S.piece&&(v.remove(S.piece),S.piece=null)}function m(S,y){y.depth>0?(o.virtualPiece?o.virtualPiece.updateData(!1,S,t,r,n):(o.virtualPiece=new _s(S,t,r,n),v.add(o.virtualPiece)),y.piece.off("click"),o.virtualPiece.on("click",function(b){o._rootToNode(y.parentNode)})):o.virtualPiece&&(v.remove(o.virtualPiece),o.virtualPiece=null)}},e.prototype._initEvents=function(){var t=this;this.group.off("click"),this.group.on("click",function(r){var n=!1,i=t.seriesModel.getViewRoot();i.eachNode(function(o){if(!n&&o.piece&&o.piece===r.target){var s=o.getModel().get("nodeClick");if(s==="rootToNode")t._rootToNode(o);else if(s==="link"){var l=o.getModel(),u=l.get("link");if(u){var v=l.get("target",!0)||"_blank";Dl(u,v)}}n=!0}})})},e.prototype._rootToNode=function(t){t!==this.seriesModel.getViewRoot()&&this.api.dispatchAction({type:zn,from:this.uid,seriesId:this.seriesModel.id,targetNode:t})},e.prototype.containPoint=function(t,r){var n=r.getData(),i=n.getItemLayout(0);if(i){var o=t[0]-i.cx,s=t[1]-i.cy,l=Math.sqrt(o*o+s*s);return l<=i.r&&l>=i.r0}},e.type="sunburst",e}(ft),Ky=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t,r){var n={name:t.name,children:t.data};ku(n);var i=this._levelModels=B(t.levels||[],function(l){return new Yt(l,this,r)},this),o=pi.createTree(n,this,s);function s(l){l.wrapMethod("getItemModel",function(u,v){var c=o.getNodeByDataIndex(v),h=i[c.depth];return h&&(u.parentModel=h),u})}return o.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return r.treePathInfo=_a(n,this),r},e.prototype.getLevelModel=function(t){return this._levelModels&&this._levelModels[t.depth]},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var r=this.getRawData().tree.root;(!t||t!==r&&!r.contains(t))&&(this._viewRoot=r)},e.prototype.enableAriaDecal=function(){eu(this)},e.type="series.sunburst",e.defaultOption={z:2,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,stillShowZeroSum:!0,nodeClick:"rootToNode",renderLabelForZeroData:!1,label:{rotate:"radial",show:!0,opacity:1,align:"center",position:"inside",distance:5,silent:!0},itemStyle:{borderWidth:1,borderColor:"white",borderType:"solid",shadowBlur:0,shadowColor:"rgba(0, 0, 0, 0.2)",shadowOffsetX:0,shadowOffsetY:0,opacity:1},emphasis:{focus:"descendant"},blur:{itemStyle:{opacity:.2},label:{opacity:.1}},animationType:"expansion",animationDuration:1e3,animationDurationUpdate:500,data:[],sort:"desc"},e}(dt);function ku(a){var e=0;C(a.children,function(r){ku(r);var n=r.value;H(n)&&(n=n[0]),e+=n});var t=a.value;H(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),H(a.value)?a.value[0]=t:a.value=t}var Ds=Math.PI/180;function Jy(a,e,t){e.eachSeriesByType(a,function(r){var n=r.get("center"),i=r.get("radius");H(i)||(i=[0,i]),H(n)||(n=[n,n]);var o=t.getWidth(),s=t.getHeight(),l=Math.min(o,s),u=z(n[0],o),v=z(n[1],s),c=z(i[0],l/2),h=z(i[1],l/2),f=-r.get("startAngle")*Ds,p=r.get("minAngle")*Ds,d=r.getData().tree.root,g=r.getViewRoot(),m=g.depth,S=r.get("sort");S!=null&&Vu(g,S);var y=0;C(g.children,function(R){!isNaN(R.getValue())&&y++});var b=g.getValue(),_=Math.PI/(b||y)*2,x=g.depth>0,w=g.height-(x?-1:1),D=(h-c)/(w||1),T=r.get("clockwise"),I=r.get("stillShowZeroSum"),A=T?1:-1,P=function(R,N){if(R){var G=N;if(R!==d){var O=R.getValue(),F=b===0&&I?_:O*_;F<p&&(F=p),G=N+A*F;var W=R.depth-m-(x?-1:1),K=c+D*W,J=c+D*(W+1),Z=r.getLevelModel(R);if(Z){var Q=Z.get("r0",!0),yt=Z.get("r",!0),se=Z.get("radius",!0);se!=null&&(Q=se[0],yt=se[1]),Q!=null&&(K=z(Q,l/2)),yt!=null&&(J=z(yt,l/2))}R.setLayout({angle:F,startAngle:N,endAngle:G,clockwise:T,cx:u,cy:v,r0:K,r:J})}if(R.children&&R.children.length){var q=0;C(R.children,function($){q+=P($,N+q)})}return G-N}};if(x){var E=c,L=c+D,M=Math.PI*2;d.setLayout({angle:M,startAngle:f,endAngle:f+M,clockwise:T,cx:u,cy:v,r0:E,r:L})}P(g,f)})}function Vu(a,e){var t=a.children||[];a.children=Qy(t,e),t.length&&C(a.children,function(r){Vu(r,e)})}function Qy(a,e){if(ot(e)){var t=B(a,function(n,i){var o=n.getValue();return{params:{depth:n.depth,height:n.height,dataIndex:n.dataIndex,getValue:function(){return o}},index:i}});return t.sort(function(n,i){return e(n.params,i.params)}),B(t,function(n){return a[n.index]})}else{var r=e==="asc";return a.sort(function(n,i){var o=(n.getValue()-i.getValue())*(r?1:-1);return o===0?(n.dataIndex-i.dataIndex)*(r?-1:1):o})}}function tm(a){var e={};function t(r,n,i){for(var o=r;o&&o.depth>1;)o=o.parentNode;var s=n.getColorFromPalette(o.name||o.dataIndex+"",e);return r.depth>1&&rt(s)&&(s=vc(s,(r.depth-1)/(i-1)*.5)),s}a.eachSeriesByType("sunburst",function(r){var n=r.getData(),i=n.tree;i.eachNode(function(o){var s=o.getModel(),l=s.getModel("itemStyle").getItemStyle();l.fill||(l.fill=t(o,r,i.root.height));var u=n.ensureUniqueItemVisual(o.dataIndex,"style");U(u,l)})})}function em(a){a.registerChartView(jy),a.registerSeriesModel(Ky),a.registerLayout(st(Jy,"sunburst")),a.registerProcessor(st(fa,"sunburst")),a.registerVisual(tm),qy(a)}var Ts={color:"fill",borderColor:"stroke"},rm={symbol:1,symbolSize:1,symbolKeepAspect:1,legendIcon:1,visualMeta:1,liftZ:1,decal:1},re=_t(),am=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){this.currentZLevel=this.get("zlevel",!0),this.currentZ=this.get("z",!0)},e.prototype.getInitialData=function(t,r){return Be(null,this)},e.prototype.getDataParams=function(t,r,n){var i=a.prototype.getDataParams.call(this,t,r);return n&&(i.info=re(n).info),i},e.type="series.custom",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,clip:!1},e}(dt);function nm(a,e){return e=e||[0,0],B(["x","y"],function(t,r){var n=this.getAxis(t),i=e[r],o=a[r]/2;return n.type==="category"?n.getBandWidth():Math.abs(n.dataToCoord(i-o)-n.dataToCoord(i+o))},this)}function im(a){var e=a.master.getRect();return{coordSys:{type:"cartesian2d",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return a.dataToPoint(t)},size:et(nm,a)}}}function om(a,e){return e=e||[0,0],B([0,1],function(t){var r=e[t],n=a[t]/2,i=[],o=[];return i[t]=r-n,o[t]=r+n,i[1-t]=o[1-t]=e[1-t],Math.abs(this.dataToPoint(i)[t]-this.dataToPoint(o)[t])},this)}function sm(a){var e=a.getBoundingRect();return{coordSys:{type:"geo",x:e.x,y:e.y,width:e.width,height:e.height,zoom:a.getZoom()},api:{coord:function(t){return a.dataToPoint(t)},size:et(om,a)}}}function lm(a,e){var t=this.getAxis(),r=e instanceof Array?e[0]:e,n=(a instanceof Array?a[0]:a)/2;return t.type==="category"?t.getBandWidth():Math.abs(t.dataToCoord(r-n)-t.dataToCoord(r+n))}function um(a){var e=a.getRect();return{coordSys:{type:"singleAxis",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return a.dataToPoint(t)},size:et(lm,a)}}}function vm(a,e){return e=e||[0,0],B(["Radius","Angle"],function(t,r){var n="get"+t+"Axis",i=this[n](),o=e[r],s=a[r]/2,l=i.type==="category"?i.getBandWidth():Math.abs(i.dataToCoord(o-s)-i.dataToCoord(o+s));return t==="Angle"&&(l=l*Math.PI/180),l},this)}function cm(a){var e=a.getRadiusAxis(),t=a.getAngleAxis(),r=e.getExtent();return r[0]>r[1]&&r.reverse(),{coordSys:{type:"polar",cx:a.cx,cy:a.cy,r:r[1],r0:r[0]},api:{coord:function(n){var i=e.dataToRadius(n[0]),o=t.dataToAngle(n[1]),s=a.coordToPoint([i,o]);return s.push(i,o*Math.PI/180),s},size:et(vm,a)}}}function hm(a){var e=a.getRect(),t=a.getRangeInfo();return{coordSys:{type:"calendar",x:e.x,y:e.y,width:e.width,height:e.height,cellWidth:a.getCellWidth(),cellHeight:a.getCellHeight(),rangeInfo:{start:t.start,end:t.end,weeks:t.weeks,dayCount:t.allDay}},api:{coord:function(r,n){return a.dataToPoint(r,n)}}}}var ae="emphasis",ve="normal",Ti="blur",Ii="select",de=[ve,ae,Ti,Ii],sn={normal:["itemStyle"],emphasis:[ae,"itemStyle"],blur:[Ti,"itemStyle"],select:[Ii,"itemStyle"]},ln={normal:["label"],emphasis:[ae,"label"],blur:[Ti,"label"],select:[Ii,"label"]},fm=["x","y"],pm="e\0\0",Nt={normal:{},emphasis:{},blur:{},select:{}},dm={cartesian2d:im,geo:sm,single:um,polar:cm,calendar:hm};function On(a){return a instanceof wt}function Bn(a){return a instanceof ir}function gm(a,e){e.copyTransform(a),Bn(e)&&Bn(a)&&(e.setStyle(a.style),e.z=a.z,e.z2=a.z2,e.zlevel=a.zlevel,e.invisible=a.invisible,e.ignore=a.ignore,On(e)&&On(a)&&e.setShape(a.shape))}var ym=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n,i){this._progressiveEls=null;var o=this._data,s=t.getData(),l=this.group,u=Is(t,s,r,n);o||l.removeAll(),s.diff(o).add(function(c){un(n,null,c,u(c,i),t,l,s)}).remove(function(c){var h=o.getItemGraphicEl(c);h&&ni(h,re(h).option,t)}).update(function(c,h){var f=o.getItemGraphicEl(h);un(n,f,c,u(c,i),t,l,s)}).execute();var v=t.get("clip",!0)?Sa(t.coordinateSystem,!1,t):null;v?l.setClipPath(v):l.removeClipPath(),this._data=s},e.prototype.incrementalPrepareRender=function(t,r,n){this.group.removeAll(),this._data=null},e.prototype.incrementalRender=function(t,r,n,i,o){var s=r.getData(),l=Is(r,s,n,i),u=this._progressiveEls=[];function v(f){f.isGroup||(f.incremental=!0,f.ensureState("emphasis").hoverLayer=!0)}for(var c=t.start;c<t.end;c++){var h=un(null,null,c,l(c,o),r,this.group,s);h&&(h.traverse(v),u.push(h))}},e.prototype.eachRendered=function(t){ma(this._progressiveEls||this.group,t)},e.prototype.filterForExposedEvent=function(t,r,n,i){var o=r.element;if(o==null||n.name===o)return!0;for(;(n=n.__hostTarget||n.parent)&&n!==this.group;)if(n.name===o)return!0;return!1},e.type="custom",e}(ft);function Ai(a){var e=a.type,t;if(e==="path"){var r=a.shape,n=r.width!=null&&r.height!=null?{x:r.x||0,y:r.y||0,width:r.width,height:r.height}:null,i=Ou(r);t=fc(i,null,n,r.layout||"center"),re(t).customPathData=i}else if(e==="image")t=new Fe({}),re(t).customImagePath=a.style.image;else if(e==="text")t=new Vt({});else if(e==="group")t=new Y;else{if(e==="compoundPath")throw new Error('"compoundPath" is not supported yet.');var o=pc(e);if(!o){var s="";ut(s)}t=new o}return re(t).customGraphicType=e,t.name=a.name,t.z2EmphasisLift=1,t.z2SelectLift=1,t}function Li(a,e,t,r,n,i,o){dc(e);var s=n&&n.normal.cfg;s&&e.setTextConfig(s),r&&r.transition==null&&(r.transition=fm);var l=r&&r.style;if(l){if(e.type==="text"){var u=l;gt(u,"textFill")&&(u.fill=u.textFill),gt(u,"textStroke")&&(u.stroke=u.textStroke)}var v=void 0,c=On(e)?l.decal:null;a&&c&&(c.dirty=!0,v=Kn(c,a)),l.__decalPattern=v}if(Bn(e)&&l){var v=l.__decalPattern;v&&(l.decal=v)}gc(e,r,i,{dataIndex:t,isInit:o,clearStyle:!0}),yc(e,r.keyframeAnimation,i)}function Gu(a,e,t,r,n){var i=e.isGroup?null:e,o=n&&n[a].cfg;if(i){var s=i.ensureState(a);if(r===!1){var l=i.getState(a);l&&(l.style=null)}else s.style=r||null;o&&(s.textConfig=o),ze(i)}}function mm(a,e,t){if(!a.isGroup){var r=a,n=t.currentZ,i=t.currentZLevel;r.z=n,r.zlevel=i;var o=e.z2;o!=null&&(r.z2=o||0);for(var s=0;s<de.length;s++)Sm(r,e,de[s])}}function Sm(a,e,t){var r=t===ve,n=r?e:Qr(e,t),i=n?n.z2:null,o;i!=null&&(o=r?a:a.ensureState(t),o.z2=i||0)}function Is(a,e,t,r){var n=a.get("renderItem"),i=a.coordinateSystem,o={};i&&(o=i.prepareCustoms?i.prepareCustoms(i):dm[i.type](i));for(var s=it({getWidth:r.getWidth,getHeight:r.getHeight,getZr:r.getZr,getDevicePixelRatio:r.getDevicePixelRatio,value:b,style:x,ordinalRawValue:_,styleEmphasis:w,visual:I,barLayout:A,currentSeriesIndices:P,font:E},o.api||{}),l={context:{},seriesId:a.id,seriesName:a.name,seriesIndex:a.seriesIndex,coordSys:o.coordSys,dataInsideLength:e.count(),encode:xm(a.getData())},u,v,c={},h={},f={},p={},d=0;d<de.length;d++){var g=de[d];f[g]=a.getModel(sn[g]),p[g]=a.getModel(ln[g])}function m(L){return L===u?v||(v=e.getItemModel(L)):e.getItemModel(L)}function S(L,M){return e.hasItemOption?L===u?c[M]||(c[M]=m(L).getModel(sn[M])):m(L).getModel(sn[M]):f[M]}function y(L,M){return e.hasItemOption?L===u?h[M]||(h[M]=m(L).getModel(ln[M])):m(L).getModel(ln[M]):p[M]}return function(L,M){return u=L,v=null,c={},h={},n&&n(it({dataIndexInside:L,dataIndex:e.getRawIndex(L),actionType:M?M.type:null},l),s)};function b(L,M){return M==null&&(M=u),e.getStore().get(e.getDimensionIndex(L||0),M)}function _(L,M){M==null&&(M=u),L=L||0;var R=e.getDimensionInfo(L);if(!R){var N=e.getDimensionIndex(L);return N>=0?e.getStore().get(N,M):void 0}var G=e.get(R.name,M),O=R&&R.ordinalMeta;return O?O.categories[G]:G}function x(L,M){M==null&&(M=u);var R=e.getItemVisual(M,"style"),N=R&&R.fill,G=R&&R.opacity,O=S(M,ve).getItemStyle();N!=null&&(O.fill=N),G!=null&&(O.opacity=G);var F={inheritColor:rt(N)?N:"#000"},W=y(M,ve),K=bt(W,null,F,!1,!0);K.text=W.getShallow("show")?Zt(a.getFormattedLabel(M,ve),Cn(e,M)):null;var J=Qi(W,F,!1);return T(L,O),O=to(O,K,J),L&&D(O,L),O.legacy=!0,O}function w(L,M){M==null&&(M=u);var R=S(M,ae).getItemStyle(),N=y(M,ae),G=bt(N,null,null,!0,!0);G.text=N.getShallow("show")?xr(a.getFormattedLabel(M,ae),a.getFormattedLabel(M,ve),Cn(e,M)):null;var O=Qi(N,null,!0);return T(L,R),R=to(R,G,O),L&&D(R,L),R.legacy=!0,R}function D(L,M){for(var R in M)gt(M,R)&&(L[R]=M[R])}function T(L,M){L&&(L.textFill&&(M.textFill=L.textFill),L.textPosition&&(M.textPosition=L.textPosition))}function I(L,M){if(M==null&&(M=u),gt(Ts,L)){var R=e.getItemVisual(M,"style");return R?R[Ts[L]]:null}if(gt(rm,L))return e.getItemVisual(M,L)}function A(L){if(i.type==="cartesian2d"){var M=i.getBaseAxis();return cc(it({axis:M},L))}}function P(){return t.getCurrentSeriesIndices()}function E(L){return hc(L,t)}}function xm(a){var e={};return C(a.dimensions,function(t){var r=a.getDimensionInfo(t);if(!r.isExtraCoord){var n=r.coordDim,i=e[n]=e[n]||[];i[r.coordDimIndex]=a.getDimensionIndex(t)}}),e}function un(a,e,t,r,n,i,o){if(!r){i.remove(e);return}var s=Ci(a,e,t,r,n,i);return s&&o.setItemGraphicEl(t,s),s&&ct(s,r.focus,r.blurScope,r.emphasisDisabled),s}function Ci(a,e,t,r,n,i){var o=-1,s=e;e&&zu(e,r,n)&&(o=zt(i.childrenRef(),e),e=null);var l=!e,u=e;u?u.clearStates():(u=Ai(r),s&&gm(s,u)),r.morph===!1?u.disableMorphing=!0:u.disableMorphing&&(u.disableMorphing=!1),Nt.normal.cfg=Nt.normal.conOpt=Nt.emphasis.cfg=Nt.emphasis.conOpt=Nt.blur.cfg=Nt.blur.conOpt=Nt.select.cfg=Nt.select.conOpt=null,Nt.isLegacy=!1,_m(u,t,r,n,l,Nt),bm(u,t,r,n,l),Li(a,u,t,r,Nt,n,l),gt(r,"info")&&(re(u).info=r.info);for(var v=0;v<de.length;v++){var c=de[v];if(c!==ve){var h=Qr(r,c),f=Pi(r,h,c);Gu(c,u,h,f,Nt)}}return mm(u,r,n),r.type==="group"&&wm(a,u,t,r,n),o>=0?i.replaceAt(u,o):i.add(u),u}function zu(a,e,t){var r=re(a),n=e.type,i=e.shape,o=e.style;return t.isUniversalTransitionEnabled()||n!=null&&n!==r.customGraphicType||n==="path"&&Am(i)&&Ou(i)!==r.customPathData||n==="image"&&gt(o,"image")&&o.image!==r.customImagePath}function bm(a,e,t,r,n){var i=t.clipPath;if(i===!1)a&&a.getClipPath()&&a.removeClipPath();else if(i){var o=a.getClipPath();o&&zu(o,i,r)&&(o=null),o||(o=Ai(i),a.setClipPath(o)),Li(null,o,e,i,null,r,n)}}function _m(a,e,t,r,n,i){if(!a.isGroup){As(t,null,i),As(t,ae,i);var o=i.normal.conOpt,s=i.emphasis.conOpt,l=i.blur.conOpt,u=i.select.conOpt;if(o!=null||s!=null||u!=null||l!=null){var v=a.getTextContent();if(o===!1)v&&a.removeTextContent();else{o=i.normal.conOpt=o||{type:"text"},v?v.clearStates():(v=Ai(o),a.setTextContent(v)),Li(null,v,e,o,null,r,n);for(var c=o&&o.style,h=0;h<de.length;h++){var f=de[h];if(f!==ve){var p=i[f].conOpt;Gu(f,v,p,Pi(o,p,f),null)}}c?v.dirty():v.markRedraw()}}}}function As(a,e,t){var r=e?Qr(a,e):a,n=e?Pi(a,r,ae):a.style,i=a.type,o=r?r.textConfig:null,s=a.textContent,l=s?e?Qr(s,e):s:null;if(n&&(t.isLegacy||mc(n,i,!!o,!!l))){t.isLegacy=!0;var u=Sc(n,i,!e);!o&&u.textConfig&&(o=u.textConfig),!l&&u.textContent&&(l=u.textContent)}if(!e&&l){var v=l;!v.type&&(v.type="text")}var c=e?t[e]:t.normal;c.cfg=o,c.conOpt=l}function Qr(a,e){return e?a?a[e]:null:a}function Pi(a,e,t){var r=e&&e.style;return r==null&&t===ae&&a&&(r=a.styleEmphasis),r}function wm(a,e,t,r,n){var i=r.children,o=i?i.length:0,s=r.$mergeChildren,l=s==="byName"||r.diffChildrenByName,u=s===!1;if(!(!o&&!l&&!u)){if(l){Tm({api:a,oldChildren:e.children()||[],newChildren:i||[],dataIndex:t,seriesModel:n,group:e});return}u&&e.removeAll();for(var v=0;v<o;v++){var c=i[v],h=e.childAt(v);c?(c.ignore==null&&(c.ignore=!1),Ci(a,h,t,c,n,e)):h.ignore=!0}for(var f=e.childCount()-1;f>=v;f--){var p=e.childAt(f);Dm(e,p,n)}}}function Dm(a,e,t){e&&ni(e,re(a).option,t)}function Tm(a){new Oe(a.oldChildren,a.newChildren,Ls,Ls,a).add(Cs).update(Cs).remove(Im).execute()}function Ls(a,e){var t=a&&a.name;return t!=null?t:pm+e}function Cs(a,e){var t=this.context,r=a!=null?t.newChildren[a]:null,n=e!=null?t.oldChildren[e]:null;Ci(t.api,n,t.dataIndex,r,t.seriesModel,t.group)}function Im(a){var e=this.context,t=e.oldChildren[a];t&&ni(t,re(t).option,e.seriesModel)}function Ou(a){return a&&(a.pathData||a.d)}function Am(a){return a&&(gt(a,"pathData")||gt(a,"d"))}function Lm(a){a.registerChartView(ym),a.registerSeriesModel(am)}function Fn(a,e){e=e||{};var t=a.coordinateSystem,r=a.axis,n={},i=r.position,o=r.orient,s=t.getRect(),l=[s.x,s.x+s.width,s.y,s.y+s.height],u={horizontal:{top:l[2],bottom:l[3]},vertical:{left:l[0],right:l[1]}};n.position=[o==="vertical"?u.vertical[i]:l[0],o==="horizontal"?u.horizontal[i]:l[3]];var v={horizontal:0,vertical:1};n.rotation=Math.PI/2*v[o];var c={top:-1,bottom:1,right:1,left:-1};n.labelDirection=n.tickDirection=n.nameDirection=c[i],a.get(["axisTick","inside"])&&(n.tickDirection=-n.tickDirection),kt(e.labelInside,a.get(["axisLabel","inside"]))&&(n.labelDirection=-n.labelDirection);var h=e.rotate;return h==null&&(h=a.get(["axisLabel","rotate"])),n.labelRotation=i==="top"?-h:h,n.z2=1,n}var Cm=["axisLine","axisTickLabel","axisName"],Pm=["splitArea","splitLine"],Mm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="SingleAxisPointer",t}return e.prototype.render=function(t,r,n,i){var o=this.group;o.removeAll();var s=this._axisGroup;this._axisGroup=new Y;var l=Fn(t),u=new Zn(t,l);C(Cm,u.add,u),o.add(this._axisGroup),o.add(u.getGroup()),C(Pm,function(v){t.get([v,"show"])&&Em[v](this,this.group,this._axisGroup,t)},this),kl(s,this._axisGroup,t),a.prototype.render.call(this,t,r,n,i)},e.prototype.remove=function(){xc(this)},e.type="singleAxis",e}(Ol),Em={splitLine:function(a,e,t,r){var n=r.axis;if(!n.scale.isBlank()){var i=r.getModel("splitLine"),o=i.getModel("lineStyle"),s=o.get("color");s=s instanceof Array?s:[s];for(var l=o.get("width"),u=r.coordinateSystem.getRect(),v=n.isHorizontal(),c=[],h=0,f=n.getTicksCoords({tickModel:i}),p=[],d=[],g=0;g<f.length;++g){var m=n.toGlobalCoord(f[g].coord);v?(p[0]=m,p[1]=u.y,d[0]=m,d[1]=u.y+u.height):(p[0]=u.x,p[1]=m,d[0]=u.x+u.width,d[1]=m);var S=new we({shape:{x1:p[0],y1:p[1],x2:d[0],y2:d[1]},silent:!0});_c(S.shape,l);var y=h++%s.length;c[y]=c[y]||[],c[y].push(S)}for(var b=o.getLineStyle(["color"]),g=0;g<c.length;++g)e.add(xn(c[g],{style:it({stroke:s[g%s.length]},b),silent:!0}))}},splitArea:function(a,e,t,r){bc(a,t,r,r)}},Hr=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getCoordSysModel=function(){return this},e.type="singleAxis",e.layoutMode="box",e.defaultOption={left:"5%",top:"5%",right:"5%",bottom:"5%",type:"value",position:"bottom",orient:"horizontal",axisLine:{show:!0,lineStyle:{width:1,type:"solid"}},tooltip:{show:!0},axisTick:{show:!0,length:6,lineStyle:{width:1}},axisLabel:{show:!0,interval:"auto"},splitLine:{show:!0,lineStyle:{type:"dashed",opacity:.2}}},e}(Jt);Kt(Hr,Xn.prototype);var Rm=function(a){V(e,a);function e(t,r,n,i,o){var s=a.call(this,t,r,n)||this;return s.type=i||"value",s.position=o||"bottom",s}return e.prototype.isHorizontal=function(){var t=this.position;return t==="top"||t==="bottom"},e.prototype.pointToData=function(t,r){return this.coordinateSystem.pointToData(t)[0]},e}(ha),Bu=["single"],Nm=function(){function a(e,t,r){this.type="single",this.dimension="single",this.dimensions=Bu,this.axisPointerEnabled=!0,this.model=e,this._init(e,t,r)}return a.prototype._init=function(e,t,r){var n=this.dimension,i=new Rm(n,Ml(e),[0,0],e.get("type"),e.get("position")),o=i.type==="category";i.onBand=o&&e.get("boundaryGap"),i.inverse=e.get("inverse"),i.orient=e.get("orient"),e.axis=i,i.model=e,i.coordinateSystem=this,this._axis=i},a.prototype.update=function(e,t){e.eachSeries(function(r){if(r.coordinateSystem===this){var n=r.getData();C(n.mapDimensionsAll(this.dimension),function(i){this._axis.scale.unionExtentFromData(n,i)},this),El(this._axis.scale,this._axis.model)}},this)},a.prototype.resize=function(e,t){this._rect=te({left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")},{width:t.getWidth(),height:t.getHeight()}),this._adjustAxis()},a.prototype.getRect=function(){return this._rect},a.prototype._adjustAxis=function(){var e=this._rect,t=this._axis,r=t.isHorizontal(),n=r?[0,e.width]:[0,e.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),this._updateAxisTransform(t,r?e.x:e.y)},a.prototype._updateAxisTransform=function(e,t){var r=e.getExtent(),n=r[0]+r[1],i=e.isHorizontal();e.toGlobalCoord=i?function(o){return o+t}:function(o){return n-o+t},e.toLocalCoord=i?function(o){return o-t}:function(o){return n-o+t}},a.prototype.getAxis=function(){return this._axis},a.prototype.getBaseAxis=function(){return this._axis},a.prototype.getAxes=function(){return[this._axis]},a.prototype.getTooltipAxes=function(){return{baseAxes:[this.getAxis()],otherAxes:[]}},a.prototype.containPoint=function(e){var t=this.getRect(),r=this.getAxis(),n=r.orient;return n==="horizontal"?r.contain(r.toLocalCoord(e[0]))&&e[1]>=t.y&&e[1]<=t.y+t.height:r.contain(r.toLocalCoord(e[1]))&&e[0]>=t.y&&e[0]<=t.y+t.height},a.prototype.pointToData=function(e){var t=this.getAxis();return[t.coordToData(t.toLocalCoord(e[t.orient==="horizontal"?0:1]))]},a.prototype.dataToPoint=function(e){var t=this.getAxis(),r=this.getRect(),n=[],i=t.orient==="horizontal"?0:1;return e instanceof Array&&(e=e[0]),n[i]=t.toGlobalCoord(t.dataToCoord(+e)),n[1-i]=i===0?r.y+r.height/2:r.x+r.width/2,n},a.prototype.convertToPixel=function(e,t,r){var n=Ps(t);return n===this?this.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var n=Ps(t);return n===this?this.pointToData(r):null},a}();function Ps(a){var e=a.seriesModel,t=a.singleAxisModel;return t&&t.coordinateSystem||e&&e.coordinateSystem}function km(a,e){var t=[];return a.eachComponent("singleAxis",function(r,n){var i=new Nm(r,a,e);i.name="single_"+n,i.resize(r,e),r.coordinateSystem=i,t.push(i)}),a.eachSeries(function(r){if(r.get("coordinateSystem")==="singleAxis"){var n=r.getReferringComponents("singleAxis",da).models[0];r.coordinateSystem=n&&n.coordinateSystem}}),t}var Vm={create:km,dimensions:Bu},Ms=["x","y"],Gm=["width","height"],zm=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,r,n,i,o){var s=n.axis,l=s.coordinateSystem,u=vn(l,1-ta(s)),v=l.dataToPoint(r)[0],c=i.get("type");if(c&&c!=="none"){var h=wc(i),f=Om[c](s,v,u);f.style=h,t.graphicKey=f.type,t.pointer=f}var p=Fn(n);Dc(r,t,p,n,i,o)},e.prototype.getHandleTransform=function(t,r,n){var i=Fn(r,{labelInside:!1});i.labelMargin=n.get(["handle","margin"]);var o=Tc(r.axis,t,i);return{x:o[0],y:o[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},e.prototype.updateHandleTransform=function(t,r,n,i){var o=n.axis,s=o.coordinateSystem,l=ta(o),u=vn(s,l),v=[t.x,t.y];v[l]+=r[l],v[l]=Math.min(u[1],v[l]),v[l]=Math.max(u[0],v[l]);var c=vn(s,1-l),h=(c[1]+c[0])/2,f=[h,h];return f[l]=v[l],{x:v[0],y:v[1],rotation:t.rotation,cursorPoint:f,tooltipOption:{verticalAlign:"middle"}}},e}(Ic),Om={line:function(a,e,t){var r=Lc([e,t[0]],[e,t[1]],ta(a));return{type:"Line",subPixelOptimize:!0,shape:r}},shadow:function(a,e,t){var r=a.getBandWidth(),n=t[1]-t[0];return{type:"Rect",shape:Ac([e-r/2,t[0]],[r,n],ta(a))}}};function ta(a){return a.isHorizontal()?0:1}function vn(a,e){var t=a.getRect();return[t[Ms[e]],t[Ms[e]]+t[Gm[e]]]}var Bm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="single",e}(Qt);function Fm(a){X(Bl),Ol.registerAxisPointerClass("SingleAxisPointer",zm),a.registerComponentView(Bm),a.registerComponentView(Mm),a.registerComponentModel(Hr),Vl(a,"single",Hr,Hr.defaultOption),a.registerCoordinateSystem("single",Vm)}var Wm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r,n){var i=Cc(t);a.prototype.init.apply(this,arguments),Es(t,i)},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),Es(this.option,t)},e.prototype.getCellSize=function(){return this.option.cellSize},e.type="calendar",e.defaultOption={z:2,left:80,top:60,cellSize:20,orient:"horizontal",splitLine:{show:!0,lineStyle:{color:"#000",width:1,type:"solid"}},itemStyle:{color:"#fff",borderWidth:1,borderColor:"#ccc"},dayLabel:{show:!0,firstDay:0,position:"start",margin:"50%",color:"#000"},monthLabel:{show:!0,position:"start",margin:5,align:"center",formatter:null,color:"#000"},yearLabel:{show:!0,position:null,margin:30,formatter:null,color:"#ccc",fontFamily:"sans-serif",fontWeight:"bolder",fontSize:20}},e}(Jt);function Es(a,e){var t=a.cellSize,r;H(t)?r=t:r=a.cellSize=[t,t],r.length===1&&(r[1]=r[0]);var n=B([0,1],function(i){return Pc(e,i)&&(r[i]="auto"),r[i]!=null&&r[i]!=="auto"});Mc(a,e,{type:"box",ignoreSize:n})}var Hm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=this.group;i.removeAll();var o=t.coordinateSystem,s=o.getRangeInfo(),l=o.getOrient(),u=r.getLocaleModel();this._renderDayRect(t,s,i),this._renderLines(t,s,l,i),this._renderYearText(t,s,l,i),this._renderMonthText(t,u,l,i),this._renderWeekText(t,u,s,l,i)},e.prototype._renderDayRect=function(t,r,n){for(var i=t.coordinateSystem,o=t.getModel("itemStyle").getItemStyle(),s=i.getCellWidth(),l=i.getCellHeight(),u=r.start.time;u<=r.end.time;u=i.getNextNDay(u,1).time){var v=i.dataToRect([u],!1).tl,c=new Lt({shape:{x:v[0],y:v[1],width:s,height:l},cursor:"default",style:o});n.add(c)}},e.prototype._renderLines=function(t,r,n,i){var o=this,s=t.coordinateSystem,l=t.getModel(["splitLine","lineStyle"]).getLineStyle(),u=t.get(["splitLine","show"]),v=l.lineWidth;this._tlpoints=[],this._blpoints=[],this._firstDayOfMonth=[],this._firstDayPoints=[];for(var c=r.start,h=0;c.time<=r.end.time;h++){p(c.formatedDate),h===0&&(c=s.getDateInfo(r.start.y+"-"+r.start.m));var f=c.date;f.setMonth(f.getMonth()+1),c=s.getDateInfo(f)}p(s.getNextNDay(r.end.time,1).formatedDate);function p(d){o._firstDayOfMonth.push(s.getDateInfo(d)),o._firstDayPoints.push(s.dataToRect([d],!1).tl);var g=o._getLinePointsOfOneWeek(t,d,n);o._tlpoints.push(g[0]),o._blpoints.push(g[g.length-1]),u&&o._drawSplitline(g,l,i)}u&&this._drawSplitline(o._getEdgesPoints(o._tlpoints,v,n),l,i),u&&this._drawSplitline(o._getEdgesPoints(o._blpoints,v,n),l,i)},e.prototype._getEdgesPoints=function(t,r,n){var i=[t[0].slice(),t[t.length-1].slice()],o=n==="horizontal"?0:1;return i[0][o]=i[0][o]-r/2,i[1][o]=i[1][o]+r/2,i},e.prototype._drawSplitline=function(t,r,n){var i=new ge({z2:20,shape:{points:t},style:r});n.add(i)},e.prototype._getLinePointsOfOneWeek=function(t,r,n){for(var i=t.coordinateSystem,o=i.getDateInfo(r),s=[],l=0;l<7;l++){var u=i.getNextNDay(o.time,l),v=i.dataToRect([u.time],!1);s[2*u.day]=v.tl,s[2*u.day+1]=v[n==="horizontal"?"bl":"tr"]}return s},e.prototype._formatterLabel=function(t,r){return rt(t)&&t?Ec(t,r):ot(t)?t(r):r.nameMap},e.prototype._yearTextPositionControl=function(t,r,n,i,o){var s=r[0],l=r[1],u=["center","bottom"];i==="bottom"?(l+=o,u=["center","top"]):i==="left"?s-=o:i==="right"?(s+=o,u=["center","top"]):l-=o;var v=0;return(i==="left"||i==="right")&&(v=Math.PI/2),{rotation:v,x:s,y:l,style:{align:u[0],verticalAlign:u[1]}}},e.prototype._renderYearText=function(t,r,n,i){var o=t.getModel("yearLabel");if(o.get("show")){var s=o.get("margin"),l=o.get("position");l||(l=n!=="horizontal"?"top":"left");var u=[this._tlpoints[this._tlpoints.length-1],this._blpoints[0]],v=(u[0][0]+u[1][0])/2,c=(u[0][1]+u[1][1])/2,h=n==="horizontal"?0:1,f={top:[v,u[h][1]],bottom:[v,u[1-h][1]],left:[u[1-h][0],c],right:[u[h][0],c]},p=r.start.y;+r.end.y>+r.start.y&&(p=p+"-"+r.end.y);var d=o.get("formatter"),g={start:r.start.y,end:r.end.y,nameMap:p},m=this._formatterLabel(d,g),S=new Vt({z2:30,style:bt(o,{text:m}),silent:o.get("silent")});S.attr(this._yearTextPositionControl(S,f[l],n,l,s)),i.add(S)}},e.prototype._monthTextPositionControl=function(t,r,n,i,o){var s="left",l="top",u=t[0],v=t[1];return n==="horizontal"?(v=v+o,r&&(s="center"),i==="start"&&(l="bottom")):(u=u+o,r&&(l="middle"),i==="start"&&(s="right")),{x:u,y:v,align:s,verticalAlign:l}},e.prototype._renderMonthText=function(t,r,n,i){var o=t.getModel("monthLabel");if(o.get("show")){var s=o.get("nameMap"),l=o.get("margin"),u=o.get("position"),v=o.get("align"),c=[this._tlpoints,this._blpoints];(!s||rt(s))&&(s&&(r=eo(s)||r),s=r.get(["time","monthAbbr"])||[]);var h=u==="start"?0:1,f=n==="horizontal"?0:1;l=u==="start"?-l:l;for(var p=v==="center",d=o.get("silent"),g=0;g<c[h].length-1;g++){var m=c[h][g].slice(),S=this._firstDayOfMonth[g];if(p){var y=this._firstDayPoints[g];m[f]=(y[f]+c[0][g+1][f])/2}var b=o.get("formatter"),_=s[+S.m-1],x={yyyy:S.y,yy:(S.y+"").slice(2),MM:S.m,M:+S.m,nameMap:_},w=this._formatterLabel(b,x),D=new Vt({z2:30,style:U(bt(o,{text:w}),this._monthTextPositionControl(m,p,n,u,l)),silent:d});i.add(D)}}},e.prototype._weekTextPositionControl=function(t,r,n,i,o){var s="center",l="middle",u=t[0],v=t[1],c=n==="start";return r==="horizontal"?(u=u+i+(c?1:-1)*o[0]/2,s=c?"right":"left"):(v=v+i+(c?1:-1)*o[1]/2,l=c?"bottom":"top"),{x:u,y:v,align:s,verticalAlign:l}},e.prototype._renderWeekText=function(t,r,n,i,o){var s=t.getModel("dayLabel");if(s.get("show")){var l=t.coordinateSystem,u=s.get("position"),v=s.get("nameMap"),c=s.get("margin"),h=l.getFirstDayOfWeek();if(!v||rt(v)){v&&(r=eo(v)||r);var f=r.get(["time","dayOfWeekShort"]);v=f||B(r.get(["time","dayOfWeekAbbr"]),function(x){return x[0]})}var p=l.getNextNDay(n.end.time,7-n.lweek).time,d=[l.getCellWidth(),l.getCellHeight()];c=z(c,Math.min(d[1],d[0])),u==="start"&&(p=l.getNextNDay(n.start.time,-(7+n.fweek)).time,c=-c);for(var g=s.get("silent"),m=0;m<7;m++){var S=l.getNextNDay(p,m),y=l.dataToRect([S.time],!1).center,b=m;b=Math.abs((m+h)%7);var _=new Vt({z2:30,style:U(bt(s,{text:v[b]}),this._weekTextPositionControl(y,i,u,c,d)),silent:g});o.add(_)}}},e.type="calendar",e}(Qt),cn=864e5,Um=function(){function a(e,t,r){this.type="calendar",this.dimensions=a.dimensions,this.getDimensionsInfo=a.getDimensionsInfo,this._model=e}return a.getDimensionsInfo=function(){return[{name:"time",type:"time"},"value"]},a.prototype.getRangeInfo=function(){return this._rangeInfo},a.prototype.getModel=function(){return this._model},a.prototype.getRect=function(){return this._rect},a.prototype.getCellWidth=function(){return this._sw},a.prototype.getCellHeight=function(){return this._sh},a.prototype.getOrient=function(){return this._orient},a.prototype.getFirstDayOfWeek=function(){return this._firstDayOfWeek},a.prototype.getDateInfo=function(e){e=Rc(e);var t=e.getFullYear(),r=e.getMonth()+1,n=r<10?"0"+r:""+r,i=e.getDate(),o=i<10?"0"+i:""+i,s=e.getDay();return s=Math.abs((s+7-this.getFirstDayOfWeek())%7),{y:t+"",m:n,d:o,day:s,time:e.getTime(),formatedDate:t+"-"+n+"-"+o,date:e}},a.prototype.getNextNDay=function(e,t){return t=t||0,t===0?this.getDateInfo(e):(e=new Date(this.getDateInfo(e).time),e.setDate(e.getDate()+t),this.getDateInfo(e))},a.prototype.update=function(e,t){this._firstDayOfWeek=+this._model.getModel("dayLabel").get("firstDay"),this._orient=this._model.get("orient"),this._lineWidth=this._model.getModel("itemStyle").getItemStyle().lineWidth||0,this._rangeInfo=this._getRangeInfo(this._initRangeOption());var r=this._rangeInfo.weeks||1,n=["width","height"],i=this._model.getCellSize().slice(),o=this._model.getBoxLayoutParams(),s=this._orient==="horizontal"?[r,7]:[7,r];C([0,1],function(c){v(i,c)&&(o[n[c]]=i[c]*s[c])});var l={width:t.getWidth(),height:t.getHeight()},u=this._rect=te(o,l);C([0,1],function(c){v(i,c)||(i[c]=u[n[c]]/s[c])});function v(c,h){return c[h]!=null&&c[h]!=="auto"}this._sw=i[0],this._sh=i[1]},a.prototype.dataToPoint=function(e,t){H(e)&&(e=e[0]),t==null&&(t=!0);var r=this.getDateInfo(e),n=this._rangeInfo,i=r.formatedDate;if(t&&!(r.time>=n.start.time&&r.time<n.end.time+cn))return[NaN,NaN];var o=r.day,s=this._getRangeInfo([n.start.time,i]).nthWeek;return this._orient==="vertical"?[this._rect.x+o*this._sw+this._sw/2,this._rect.y+s*this._sh+this._sh/2]:[this._rect.x+s*this._sw+this._sw/2,this._rect.y+o*this._sh+this._sh/2]},a.prototype.pointToData=function(e){var t=this.pointToDate(e);return t&&t.time},a.prototype.dataToRect=function(e,t){var r=this.dataToPoint(e,t);return{contentShape:{x:r[0]-(this._sw-this._lineWidth)/2,y:r[1]-(this._sh-this._lineWidth)/2,width:this._sw-this._lineWidth,height:this._sh-this._lineWidth},center:r,tl:[r[0]-this._sw/2,r[1]-this._sh/2],tr:[r[0]+this._sw/2,r[1]-this._sh/2],br:[r[0]+this._sw/2,r[1]+this._sh/2],bl:[r[0]-this._sw/2,r[1]+this._sh/2]}},a.prototype.pointToDate=function(e){var t=Math.floor((e[0]-this._rect.x)/this._sw)+1,r=Math.floor((e[1]-this._rect.y)/this._sh)+1,n=this._rangeInfo.range;return this._orient==="vertical"?this._getDateByWeeksAndDay(r,t-1,n):this._getDateByWeeksAndDay(t,r-1,n)},a.prototype.convertToPixel=function(e,t,r){var n=Rs(t);return n===this?n.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var n=Rs(t);return n===this?n.pointToData(r):null},a.prototype.containPoint=function(e){return console.warn("Not implemented."),!1},a.prototype._initRangeOption=function(){var e=this._model.get("range"),t;if(H(e)&&e.length===1&&(e=e[0]),H(e))t=e;else{var r=e.toString();if(/^\d{4}$/.test(r)&&(t=[r+"-01-01",r+"-12-31"]),/^\d{4}[\/|-]\d{1,2}$/.test(r)){var n=this.getDateInfo(r),i=n.date;i.setMonth(i.getMonth()+1);var o=this.getNextNDay(i,-1);t=[n.formatedDate,o.formatedDate]}/^\d{4}[\/|-]\d{1,2}[\/|-]\d{1,2}$/.test(r)&&(t=[r,r])}if(!t)return e;var s=this._getRangeInfo(t);return s.start.time>s.end.time&&t.reverse(),t},a.prototype._getRangeInfo=function(e){var t=[this.getDateInfo(e[0]),this.getDateInfo(e[1])],r;t[0].time>t[1].time&&(r=!0,t.reverse());var n=Math.floor(t[1].time/cn)-Math.floor(t[0].time/cn)+1,i=new Date(t[0].time),o=i.getDate(),s=t[1].date.getDate();i.setDate(o+n-1);var l=i.getDate();if(l!==s)for(var u=i.getTime()-t[1].time>0?1:-1;(l=i.getDate())!==s&&(i.getTime()-t[1].time)*u>0;)n-=u,i.setDate(l-u);var v=Math.floor((n+t[0].day+6)/7),c=r?-v+1:v-1;return r&&t.reverse(),{range:[t[0].formatedDate,t[1].formatedDate],start:t[0],end:t[1],allDay:n,weeks:v,nthWeek:c,fweek:t[0].day,lweek:t[1].day}},a.prototype._getDateByWeeksAndDay=function(e,t,r){var n=this._getRangeInfo(r);if(e>n.weeks||e===0&&t<n.fweek||e===n.weeks&&t>n.lweek)return null;var i=(e-1)*7-n.fweek+t,o=new Date(n.start.time);return o.setDate(+n.start.d+i),this.getDateInfo(o)},a.create=function(e,t){var r=[];return e.eachComponent("calendar",function(n){var i=new a(n);r.push(i),n.coordinateSystem=i}),e.eachSeries(function(n){n.get("coordinateSystem")==="calendar"&&(n.coordinateSystem=r[n.get("calendarIndex")||0])}),r},a.dimensions=["time","value"],a}();function Rs(a){var e=a.calendarModel,t=a.seriesModel,r=e?e.coordinateSystem:t?t.coordinateSystem:null;return r}function $m(a){a.registerComponentModel(Wm),a.registerComponentView(Hm),a.registerCoordinateSystem("calendar",Um)}var Ym=["rect","polygon","keep","clear"];function Xm(a,e){var t=Ut(a?a.brush:[]);if(t.length){var r=[];C(t,function(l){var u=l.hasOwnProperty("toolbox")?l.toolbox:[];u instanceof Array&&(r=r.concat(u))});var n=a&&a.toolbox;H(n)&&(n=n[0]),n||(n={feature:{}},a.toolbox=[n]);var i=n.feature||(n.feature={}),o=i.brush||(i.brush={}),s=o.type||(o.type=[]);s.push.apply(s,r),Zm(s),e&&!s.length&&s.push.apply(s,Ym)}}function Zm(a){var e={};C(a,function(t){e[t]=1}),a.length=0,C(e,function(t,r){a.push(r)})}function qm(a){var e=a.brushType,t={point:function(r){return Ns[e].point(r,t,a)},rect:function(r){return Ns[e].rect(r,t,a)}};return t}var Ns={lineX:ks(0),lineY:ks(1),rect:{point:function(a,e,t){return a&&t.boundingRect.contain(a[0],a[1])},rect:function(a,e,t){return a&&t.boundingRect.intersect(a)}},polygon:{point:function(a,e,t){return a&&t.boundingRect.contain(a[0],a[1])&&We(t.range,a[0],a[1])},rect:function(a,e,t){var r=t.range;if(!a||r.length<=1)return!1;var n=a.x,i=a.y,o=a.width,s=a.height,l=r[0];if(We(r,n,i)||We(r,n+o,i)||We(r,n,i+s)||We(r,n+o,i+s)||ht.create(a).contain(l[0],l[1])||Ir(n,i,n+o,i,r)||Ir(n,i,n,i+s,r)||Ir(n+o,i,n+o,i+s,r)||Ir(n,i+s,n+o,i+s,r))return!0}}};function ks(a){var e=["x","y"],t=["width","height"];return{point:function(r,n,i){if(r){var o=i.range,s=r[a];return Ke(s,o)}},rect:function(r,n,i){if(r){var o=i.range,s=[r[e[a]],r[e[a]]+r[t[a]]];return s[1]<s[0]&&s.reverse(),Ke(s[0],o)||Ke(s[1],o)||Ke(o[0],s)||Ke(o[1],s)}}}}function Ke(a,e){return e[0]<=a&&a<=e[1]}var Vs=["inBrush","outOfBrush"],hn="__ecBrushSelect",Wn="__ecInBrushSelectEvent";function Fu(a){a.eachComponent({mainType:"brush"},function(e){var t=e.brushTargetManager=new Vc(e.option,a);t.setInputRanges(e.areas,a)})}function jm(a,e,t){var r=[],n,i;a.eachComponent({mainType:"brush"},function(o){t&&t.type==="takeGlobalCursor"&&o.setBrushOption(t.key==="brush"?t.brushOption:{brushType:!1})}),Fu(a),a.eachComponent({mainType:"brush"},function(o,s){var l={brushId:o.id,brushIndex:s,brushName:o.name,areas:$t(o.areas),selected:[]};r.push(l);var u=o.option,v=u.brushLink,c=[],h=[],f=[],p=!1;s||(n=u.throttleType,i=u.throttleDelay);var d=B(o.areas,function(_){var x=t0[_.brushType],w=it({boundingRect:x?x(_):void 0},_);return w.selectors=qm(w),w}),g=Nc(o.option,Vs,function(_){_.mappingMethod="fixed"});H(v)&&C(v,function(_){c[_]=1});function m(_){return v==="all"||!!c[_]}function S(_){return!!_.length}a.eachSeries(function(_,x){var w=f[x]=[];_.subType==="parallel"?y(_,x):b(_,x,w)});function y(_,x){var w=_.coordinateSystem;p=p||w.hasAxisBrushed(),m(x)&&w.eachActiveState(_.getData(),function(D,T){D==="active"&&(h[T]=1)})}function b(_,x,w){if(!(!_.brushSelector||Qm(o,x))&&(C(d,function(T){o.brushTargetManager.controlSeries(T,_,a)&&w.push(T),p=p||S(w)}),m(x)&&S(w))){var D=_.getData();D.each(function(T){Gs(_,w,D,T)&&(h[T]=1)})}}a.eachSeries(function(_,x){var w={seriesId:_.id,seriesIndex:x,seriesName:_.name,dataIndex:[]};l.selected.push(w);var D=f[x],T=_.getData(),I=m(x)?function(A){return h[A]?(w.dataIndex.push(T.getRawIndex(A)),"inBrush"):"outOfBrush"}:function(A){return Gs(_,D,T,A)?(w.dataIndex.push(T.getRawIndex(A)),"inBrush"):"outOfBrush"};(m(x)?p:S(D))&&kc(Vs,g,T,I)})}),Km(e,n,i,r,t)}function Km(a,e,t,r,n){if(n){var i=a.getZr();if(!i[Wn]){i[hn]||(i[hn]=Jm);var o=Pl(i,hn,t,e);o(a,r)}}}function Jm(a,e){if(!a.isDisposed()){var t=a.getZr();t[Wn]=!0,a.dispatchAction({type:"brushSelect",batch:e}),t[Wn]=!1}}function Gs(a,e,t,r){for(var n=0,i=e.length;n<i;n++){var o=e[n];if(a.brushSelector(r,t,o.selectors,o))return!0}}function Qm(a,e){var t=a.option.seriesIndex;return t!=null&&t!=="all"&&(H(t)?zt(t,e)<0:e!==t)}var t0={rect:function(a){return zs(a.range)},polygon:function(a){for(var e,t=a.range,r=0,n=t.length;r<n;r++){e=e||[[1/0,-1/0],[1/0,-1/0]];var i=t[r];i[0]<e[0][0]&&(e[0][0]=i[0]),i[0]>e[0][1]&&(e[0][1]=i[0]),i[1]<e[1][0]&&(e[1][0]=i[1]),i[1]>e[1][1]&&(e[1][1]=i[1])}return e&&zs(e)}};function zs(a){return new ht(a[0][0],a[1][0],a[0][1]-a[0][0],a[1][1]-a[1][0])}var e0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){this.ecModel=t,this.api=r,this.model,(this._brushController=new Nl(r.getZr())).on("brush",et(this._onBrush,this)).mount()},e.prototype.render=function(t,r,n,i){this.model=t,this._updateController(t,r,n,i)},e.prototype.updateTransform=function(t,r,n,i){Fu(r),this._updateController(t,r,n,i)},e.prototype.updateVisual=function(t,r,n,i){this.updateTransform(t,r,n,i)},e.prototype.updateView=function(t,r,n,i){this._updateController(t,r,n,i)},e.prototype._updateController=function(t,r,n,i){(!i||i.$from!==t.id)&&this._brushController.setPanels(t.brushTargetManager.makePanelOpts(n)).enableBrush(t.brushOption).updateCovers(t.areas.slice())},e.prototype.dispose=function(){this._brushController.dispose()},e.prototype._onBrush=function(t){var r=this.model.id,n=this.model.brushTargetManager.setOutputRanges(t.areas,this.ecModel);(!t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"brush",brushId:r,areas:$t(n),$from:r}),t.isEnd&&this.api.dispatchAction({type:"brushEnd",brushId:r,areas:$t(n),$from:r})},e.type="brush",e}(Qt),r0="#ddd",a0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.areas=[],t.brushOption={},t}return e.prototype.optionUpdated=function(t,r){var n=this.option;!r&&Gc(n,t,["inBrush","outOfBrush"]);var i=n.inBrush=n.inBrush||{};n.outOfBrush=n.outOfBrush||{color:r0},i.hasOwnProperty("liftZ")||(i.liftZ=5)},e.prototype.setAreas=function(t){t&&(this.areas=B(t,function(r){return Os(this.option,r)},this))},e.prototype.setBrushOption=function(t){this.brushOption=Os(this.option,t),this.brushType=this.brushOption.brushType},e.type="brush",e.dependencies=["geo","grid","xAxis","yAxis","parallel","series"],e.defaultOption={seriesIndex:"all",brushType:"rect",brushMode:"single",transformable:!0,brushStyle:{borderWidth:1,color:"rgba(210,219,238,0.3)",borderColor:"#D2DBEE"},throttleType:"fixRate",throttleDelay:0,removeOnClick:!0,z:1e4},e}(Jt);function Os(a,e){return Bt({brushType:a.brushType,brushMode:a.brushMode,transformable:a.transformable,brushStyle:new Yt(a.brushStyle).getItemStyle(),removeOnClick:a.removeOnClick,z:a.z},e,!0)}var n0=["rect","polygon","lineX","lineY","keep","clear"],i0=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.render=function(t,r,n){var i,o,s;r.eachComponent({mainType:"brush"},function(l){i=l.brushType,o=l.brushOption.brushMode||"single",s=s||!!l.areas.length}),this._brushType=i,this._brushMode=o,C(t.get("type",!0),function(l){t.setIconStatus(l,(l==="keep"?o==="multiple":l==="clear"?s:l===i)?"emphasis":"normal")})},e.prototype.updateView=function(t,r,n){this.render(t,r,n)},e.prototype.getIcons=function(){var t=this.model,r=t.get("icon",!0),n={};return C(t.get("type",!0),function(i){r[i]&&(n[i]=r[i])}),n},e.prototype.onclick=function(t,r,n){var i=this._brushType,o=this._brushMode;n==="clear"?(r.dispatchAction({type:"axisAreaSelect",intervals:[]}),r.dispatchAction({type:"brush",command:"clear",areas:[]})):r.dispatchAction({type:"takeGlobalCursor",key:"brush",brushOption:{brushType:n==="keep"?i:i===n?!1:n,brushMode:n==="keep"?o==="multiple"?"single":"multiple":o}})},e.getDefaultOption=function(t){var r={show:!0,type:n0.slice(),icon:{rect:"M7.3,34.7 M0.4,10V-0.2h9.8 M89.6,10V-0.2h-9.8 M0.4,60v10.2h9.8 M89.6,60v10.2h-9.8 M12.3,22.4V10.5h13.1 M33.6,10.5h7.8 M49.1,10.5h7.8 M77.5,22.4V10.5h-13 M12.3,31.1v8.2 M77.7,31.1v8.2 M12.3,47.6v11.9h13.1 M33.6,59.5h7.6 M49.1,59.5 h7.7 M77.5,47.6v11.9h-13",polygon:"M55.2,34.9c1.7,0,3.1,1.4,3.1,3.1s-1.4,3.1-3.1,3.1 s-3.1-1.4-3.1-3.1S53.5,34.9,55.2,34.9z M50.4,51c1.7,0,3.1,1.4,3.1,3.1c0,1.7-1.4,3.1-3.1,3.1c-1.7,0-3.1-1.4-3.1-3.1 C47.3,52.4,48.7,51,50.4,51z M55.6,37.1l1.5-7.8 M60.1,13.5l1.6-8.7l-7.8,4 M59,19l-1,5.3 M24,16.1l6.4,4.9l6.4-3.3 M48.5,11.6 l-5.9,3.1 M19.1,12.8L9.7,5.1l1.1,7.7 M13.4,29.8l1,7.3l6.6,1.6 M11.6,18.4l1,6.1 M32.8,41.9 M26.6,40.4 M27.3,40.2l6.1,1.6 M49.9,52.1l-5.6-7.6l-4.9-1.2",lineX:"M15.2,30 M19.7,15.6V1.9H29 M34.8,1.9H40.4 M55.3,15.6V1.9H45.9 M19.7,44.4V58.1H29 M34.8,58.1H40.4 M55.3,44.4 V58.1H45.9 M12.5,20.3l-9.4,9.6l9.6,9.8 M3.1,29.9h16.5 M62.5,20.3l9.4,9.6L62.3,39.7 M71.9,29.9H55.4",lineY:"M38.8,7.7 M52.7,12h13.2v9 M65.9,26.6V32 M52.7,46.3h13.2v-9 M24.9,12H11.8v9 M11.8,26.6V32 M24.9,46.3H11.8v-9 M48.2,5.1l-9.3-9l-9.4,9.2 M38.9-3.9V12 M48.2,53.3l-9.3,9l-9.4-9.2 M38.9,62.3V46.4",keep:"M4,10.5V1h10.3 M20.7,1h6.1 M33,1h6.1 M55.4,10.5V1H45.2 M4,17.3v6.6 M55.6,17.3v6.6 M4,30.5V40h10.3 M20.7,40 h6.1 M33,40h6.1 M55.4,30.5V40H45.2 M21,18.9h62.9v48.6H21V18.9z",clear:"M22,14.7l30.9,31 M52.9,14.7L22,45.7 M4.7,16.8V4.2h13.1 M26,4.2h7.8 M41.6,4.2h7.8 M70.3,16.8V4.2H57.2 M4.7,25.9v8.6 M70.3,25.9v8.6 M4.7,43.2v12.6h13.1 M26,55.8h7.8 M41.6,55.8h7.8 M70.3,43.2v12.6H57.2"},title:t.getLocaleModel().get(["toolbox","brush","title"])};return r},e}(zc);function o0(a){a.registerComponentView(e0),a.registerComponentModel(a0),a.registerPreprocessor(Xm),a.registerVisual(a.PRIORITY.VISUAL.BRUSH,jm),a.registerAction({type:"brush",event:"brush",update:"updateVisual"},function(e,t){t.eachComponent({mainType:"brush",query:e},function(r){r.setAreas(e.areas)})}),a.registerAction({type:"brushSelect",event:"brushSelected",update:"none"},or),a.registerAction({type:"brushEnd",event:"brushEnd",update:"none"},or),Oc("brush",i0)}var Bs=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.layoutMode="box",t}return e.prototype.init=function(t,r,n){this.mergeDefaultAndTheme(t,n),this._initData()},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),this._initData()},e.prototype.setCurrentIndex=function(t){t==null&&(t=this.option.currentIndex);var r=this._data.count();this.option.loop?t=(t%r+r)%r:(t>=r&&(t=r-1),t<0&&(t=0)),this.option.currentIndex=t},e.prototype.getCurrentIndex=function(){return this.option.currentIndex},e.prototype.isIndexMax=function(){return this.getCurrentIndex()>=this._data.count()-1},e.prototype.setPlayState=function(t){this.option.autoPlay=!!t},e.prototype.getPlayState=function(){return!!this.option.autoPlay},e.prototype._initData=function(){var t=this.option,r=t.data||[],n=t.axisType,i=this._names=[],o;n==="category"?(o=[],C(r,function(u,v){var c=Sr(Bc(u),""),h;wr(u)?(h=$t(u),h.value=v):h=v,o.push(h),i.push(c)})):o=r;var s={category:"ordinal",time:"time",value:"number"}[n]||"number",l=this._data=new Mt([{name:"value",type:s}],this);l.initData(o,i)},e.prototype.getData=function(){return this._data},e.prototype.getCategories=function(){if(this.get("axisType")==="category")return this._names.slice()},e.type="timeline",e.defaultOption={z:4,show:!0,axisType:"time",realtime:!0,left:"20%",top:null,right:"20%",bottom:0,width:null,height:40,padding:5,controlPosition:"left",autoPlay:!1,rewind:!1,loop:!0,playInterval:2e3,currentIndex:0,itemStyle:{},label:{color:"#000"},data:[]},e}(Jt),Wu=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="timeline.slider",e.defaultOption=zl(Bs.defaultOption,{backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,orient:"horizontal",inverse:!1,tooltip:{trigger:"item"},symbol:"circle",symbolSize:12,lineStyle:{show:!0,width:2,color:"#DAE1F5"},label:{position:"auto",show:!0,interval:"auto",rotate:0,color:"#A4B1D7"},itemStyle:{color:"#A4B1D7",borderWidth:1},checkpointStyle:{symbol:"circle",symbolSize:15,color:"#316bf3",borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0, 0, 0, 0.3)",animation:!0,animationDuration:300,animationEasing:"quinticInOut"},controlStyle:{show:!0,showPlayBtn:!0,showPrevBtn:!0,showNextBtn:!0,itemSize:24,itemGap:12,position:"left",playIcon:"path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z",stopIcon:"path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z",nextIcon:"M2,18.5A1.52,1.52,0,0,1,.92,18a1.49,1.49,0,0,1,0-2.12L7.81,9.36,1,3.11A1.5,1.5,0,1,1,3,.89l8,7.34a1.48,1.48,0,0,1,.49,1.09,1.51,1.51,0,0,1-.46,1.1L3,18.08A1.5,1.5,0,0,1,2,18.5Z",prevIcon:"M10,.5A1.52,1.52,0,0,1,11.08,1a1.49,1.49,0,0,1,0,2.12L4.19,9.64,11,15.89a1.5,1.5,0,1,1-2,2.22L1,10.77A1.48,1.48,0,0,1,.5,9.68,1.51,1.51,0,0,1,1,8.58L9,.92A1.5,1.5,0,0,1,10,.5Z",prevBtnSize:18,nextBtnSize:18,color:"#A4B1D7",borderColor:"#A4B1D7",borderWidth:1},emphasis:{label:{show:!0,color:"#6f778d"},itemStyle:{color:"#316BF3"},controlStyle:{color:"#316BF3",borderColor:"#316BF3",borderWidth:2}},progress:{lineStyle:{color:"#316BF3"},itemStyle:{color:"#316BF3"},label:{color:"#6f778d"}},data:[]}),e}(Bs);Kt(Wu,ii.prototype);var s0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="timeline",e}(Qt),l0=function(a){V(e,a);function e(t,r,n,i){var o=a.call(this,t,r,n)||this;return o.type=i||"value",o}return e.prototype.getLabelModel=function(){return this.model.getModel("label")},e.prototype.isHorizontal=function(){return this.model.get("orient")==="horizontal"},e}(ha),fn=Math.PI,Fs=_t(),u0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){this.api=r},e.prototype.render=function(t,r,n){if(this.model=t,this.api=n,this.ecModel=r,this.group.removeAll(),t.get("show",!0)){var i=this._layout(t,n),o=this._createGroup("_mainGroup"),s=this._createGroup("_labelGroup"),l=this._axis=this._createAxis(i,t);t.formatTooltip=function(u){var v=l.scale.getLabel({value:u});return At("nameValue",{noName:!0,value:v})},C(["AxisLine","AxisTick","Control","CurrentPointer"],function(u){this["_render"+u](i,o,l,t)},this),this._renderAxisLabel(i,s,l,t),this._position(i,t)}this._doPlayStop(),this._updateTicksStatus()},e.prototype.remove=function(){this._clearTimer(),this.group.removeAll()},e.prototype.dispose=function(){this._clearTimer()},e.prototype._layout=function(t,r){var n=t.get(["label","position"]),i=t.get("orient"),o=c0(t,r),s;n==null||n==="auto"?s=i==="horizontal"?o.y+o.height/2<r.getHeight()/2?"-":"+":o.x+o.width/2<r.getWidth()/2?"+":"-":rt(n)?s={horizontal:{top:"-",bottom:"+"},vertical:{left:"-",right:"+"}}[i][n]:s=n;var l={horizontal:"center",vertical:s>=0||s==="+"?"left":"right"},u={horizontal:s>=0||s==="+"?"top":"bottom",vertical:"middle"},v={horizontal:0,vertical:fn/2},c=i==="vertical"?o.height:o.width,h=t.getModel("controlStyle"),f=h.get("show",!0),p=f?h.get("itemSize"):0,d=f?h.get("itemGap"):0,g=p+d,m=t.get(["label","rotate"])||0;m=m*fn/180;var S,y,b,_=h.get("position",!0),x=f&&h.get("showPlayBtn",!0),w=f&&h.get("showPrevBtn",!0),D=f&&h.get("showNextBtn",!0),T=0,I=c;_==="left"||_==="bottom"?(x&&(S=[0,0],T+=g),w&&(y=[T,0],T+=g),D&&(b=[I-p,0],I-=g)):(x&&(S=[I-p,0],I-=g),w&&(y=[0,0],T+=g),D&&(b=[I-p,0],I-=g));var A=[T,I];return t.get("inverse")&&A.reverse(),{viewRect:o,mainLength:c,orient:i,rotation:v[i],labelRotation:m,labelPosOpt:s,labelAlign:t.get(["label","align"])||l[i],labelBaseline:t.get(["label","verticalAlign"])||t.get(["label","baseline"])||u[i],playPosition:S,prevBtnPosition:y,nextBtnPosition:b,axisExtent:A,controlSize:p,controlGap:d}},e.prototype._position=function(t,r){var n=this._mainGroup,i=this._labelGroup,o=t.viewRect;if(t.orient==="vertical"){var s=yr(),l=o.x,u=o.y+o.height;Ge(s,s,[-l,-u]),qn(s,s,-fn/2),Ge(s,s,[l,u]),o=o.clone(),o.applyTransform(s)}var v=S(o),c=S(n.getBoundingRect()),h=S(i.getBoundingRect()),f=[n.x,n.y],p=[i.x,i.y];p[0]=f[0]=v[0][0];var d=t.labelPosOpt;if(d==null||rt(d)){var g=d==="+"?0:1;y(f,c,v,1,g),y(p,h,v,1,1-g)}else{var g=d>=0?0:1;y(f,c,v,1,g),p[1]=f[1]+d}n.setPosition(f),i.setPosition(p),n.rotation=i.rotation=t.rotation,m(n),m(i);function m(b){b.originX=v[0][0]-b.x,b.originY=v[1][0]-b.y}function S(b){return[[b.x,b.x+b.width],[b.y,b.y+b.height]]}function y(b,_,x,w,D){b[w]+=x[w][D]-_[w][D]}},e.prototype._createAxis=function(t,r){var n=r.getData(),i=r.get("axisType"),o=v0(r,i);o.getTicks=function(){return n.mapArray(["value"],function(u){return{value:u}})};var s=n.getDataExtent("value");o.setExtent(s[0],s[1]),o.calcNiceTicks();var l=new l0("value",o,t.axisExtent,i);return l.model=r,l},e.prototype._createGroup=function(t){var r=this[t]=new Y;return this.group.add(r),r},e.prototype._renderAxisLine=function(t,r,n,i){var o=n.getExtent();if(i.get(["lineStyle","show"])){var s=new we({shape:{x1:o[0],y1:0,x2:o[1],y2:0},style:U({lineCap:"round"},i.getModel("lineStyle").getLineStyle()),silent:!0,z2:1});r.add(s);var l=this._progressLine=new we({shape:{x1:o[0],x2:this._currentPointer?this._currentPointer.x:o[0],y1:0,y2:0},style:it({lineCap:"round",lineWidth:s.style.lineWidth},i.getModel(["progress","lineStyle"]).getLineStyle()),silent:!0,z2:1});r.add(l)}},e.prototype._renderAxisTick=function(t,r,n,i){var o=this,s=i.getData(),l=n.scale.getTicks();this._tickSymbols=[],C(l,function(u){var v=n.dataToCoord(u.value),c=s.getItemModel(u.value),h=c.getModel("itemStyle"),f=c.getModel(["emphasis","itemStyle"]),p=c.getModel(["progress","itemStyle"]),d={x:v,y:0,onclick:et(o._changeTimeline,o,u.value)},g=Ws(c,h,r,d);g.ensureState("emphasis").style=f.getItemStyle(),g.ensureState("progress").style=p.getItemStyle(),Aa(g);var m=nt(g);c.get("tooltip")?(m.dataIndex=u.value,m.dataModel=i):m.dataIndex=m.dataModel=null,o._tickSymbols.push(g)})},e.prototype._renderAxisLabel=function(t,r,n,i){var o=this,s=n.getLabelModel();if(s.get("show")){var l=i.getData(),u=n.getViewLabels();this._tickLabels=[],C(u,function(v){var c=v.tickValue,h=l.getItemModel(c),f=h.getModel("label"),p=h.getModel(["emphasis","label"]),d=h.getModel(["progress","label"]),g=n.dataToCoord(v.tickValue),m=new Vt({x:g,y:0,rotation:t.labelRotation-t.rotation,onclick:et(o._changeTimeline,o,c),silent:!1,style:bt(f,{text:v.formattedLabel,align:t.labelAlign,verticalAlign:t.labelBaseline})});m.ensureState("emphasis").style=bt(p),m.ensureState("progress").style=bt(d),r.add(m),Aa(m),Fs(m).dataIndex=c,o._tickLabels.push(m)})}},e.prototype._renderControl=function(t,r,n,i){var o=t.controlSize,s=t.rotation,l=i.getModel("controlStyle").getItemStyle(),u=i.getModel(["emphasis","controlStyle"]).getItemStyle(),v=i.getPlayState(),c=i.get("inverse",!0);h(t.nextBtnPosition,"next",et(this._changeTimeline,this,c?"-":"+")),h(t.prevBtnPosition,"prev",et(this._changeTimeline,this,c?"+":"-")),h(t.playPosition,v?"stop":"play",et(this._handlePlayClick,this,!v),!0);function h(f,p,d,g){if(f){var m=Fc(Zt(i.get(["controlStyle",p+"BtnSize"]),o),o),S=[0,-m/2,m,m],y=h0(i,p+"Icon",S,{x:f[0],y:f[1],originX:o/2,originY:0,rotation:g?-s:0,rectHover:!0,style:l,onclick:d});y.ensureState("emphasis").style=u,r.add(y),Aa(y)}}},e.prototype._renderCurrentPointer=function(t,r,n,i){var o=i.getData(),s=i.getCurrentIndex(),l=o.getItemModel(s).getModel("checkpointStyle"),u=this,v={onCreate:function(c){c.draggable=!0,c.drift=et(u._handlePointerDrag,u),c.ondragend=et(u._handlePointerDragend,u),Hs(c,u._progressLine,s,n,i,!0)},onUpdate:function(c){Hs(c,u._progressLine,s,n,i)}};this._currentPointer=Ws(l,l,this._mainGroup,{},this._currentPointer,v)},e.prototype._handlePlayClick=function(t){this._clearTimer(),this.api.dispatchAction({type:"timelinePlayChange",playState:t,from:this.uid})},e.prototype._handlePointerDrag=function(t,r,n){this._clearTimer(),this._pointerChangeTimeline([n.offsetX,n.offsetY])},e.prototype._handlePointerDragend=function(t){this._pointerChangeTimeline([t.offsetX,t.offsetY],!0)},e.prototype._pointerChangeTimeline=function(t,r){var n=this._toAxisCoord(t)[0],i=this._axis,o=lr(i.getExtent().slice());n>o[1]&&(n=o[1]),n<o[0]&&(n=o[0]),this._currentPointer.x=n,this._currentPointer.markRedraw();var s=this._progressLine;s&&(s.shape.x2=n,s.dirty());var l=this._findNearestTick(n),u=this.model;(r||l!==u.getCurrentIndex()&&u.get("realtime"))&&this._changeTimeline(l)},e.prototype._doPlayStop=function(){var t=this;this._clearTimer(),this.model.getPlayState()&&(this._timer=setTimeout(function(){var r=t.model;t._changeTimeline(r.getCurrentIndex()+(r.get("rewind",!0)?-1:1))},this.model.get("playInterval")))},e.prototype._toAxisCoord=function(t){var r=this._mainGroup.getLocalTransform();return Rl(t,r,!0)},e.prototype._findNearestTick=function(t){var r=this.model.getData(),n=1/0,i,o=this._axis;return r.each(["value"],function(s,l){var u=o.dataToCoord(s),v=Math.abs(u-t);v<n&&(n=v,i=l)}),i},e.prototype._clearTimer=function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},e.prototype._changeTimeline=function(t){var r=this.model.getCurrentIndex();t==="+"?t=r+1:t==="-"&&(t=r-1),this.api.dispatchAction({type:"timelineChange",currentIndex:t,from:this.uid})},e.prototype._updateTicksStatus=function(){var t=this.model.getCurrentIndex(),r=this._tickSymbols,n=this._tickLabels;if(r)for(var i=0;i<r.length;i++)r&&r[i]&&r[i].toggleState("progress",i<t);if(n)for(var i=0;i<n.length;i++)n&&n[i]&&n[i].toggleState("progress",Fs(n[i]).dataIndex<=t)},e.type="timeline.slider",e}(s0);function v0(a,e){if(e=e||a.get("type"),e)switch(e){case"category":return new Hc({ordinalMeta:a.getCategories(),extent:[1/0,-1/0]});case"time":return new Wc({locale:a.ecModel.getLocaleModel(),useUTC:a.ecModel.get("useUTC")});default:return new bn}}function c0(a,e){return te(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()},a.get("padding"))}function h0(a,e,t,r){var n=r.style,i=Uc(a.get(["controlStyle",e]),r||{},new ht(t[0],t[1],t[2],t[3]));return n&&i.setStyle(n),i}function Ws(a,e,t,r,n,i){var o=e.get("color");if(n)n.setColor(o),t.add(n),i&&i.onUpdate(n);else{var s=a.get("symbol");n=qt(s,-1,-1,2,2,o),n.setStyle("strokeNoScale",!0),t.add(n),i&&i.onCreate(n)}var l=e.getItemStyle(["color"]);n.setStyle(l),r=Bt({rectHover:!0,z2:100},r,!0);var u=dr(a.get("symbolSize"));r.scaleX=u[0]/2,r.scaleY=u[1]/2;var v=_r(a.get("symbolOffset"),u);v&&(r.x=(r.x||0)+v[0],r.y=(r.y||0)+v[1]);var c=a.get("symbolRotate");return r.rotation=(c||0)*Math.PI/180||0,n.attr(r),n.updateTransform(),n}function Hs(a,e,t,r,n,i){if(!a.dragging){var o=n.getModel("checkpointStyle"),s=r.dataToCoord(n.getData().get("value",t));if(i||!o.get("animation",!0))a.attr({x:s,y:0}),e&&e.attr({shape:{x2:s}});else{var l={duration:o.get("animationDuration",!0),easing:o.get("animationEasing",!0)};a.stopAnimation(null,!0),a.animateTo({x:s,y:0},l),e&&e.animateTo({shape:{x2:s}},l)}}}function f0(a){a.registerAction({type:"timelineChange",event:"timelineChanged",update:"prepareAndUpdate"},function(e,t,r){var n=t.getComponent("timeline");return n&&e.currentIndex!=null&&(n.setCurrentIndex(e.currentIndex),!n.get("loop",!0)&&n.isIndexMax()&&n.getPlayState()&&(n.setPlayState(!1),r.dispatchAction({type:"timelinePlayChange",playState:!1,from:e.from}))),t.resetOption("timeline",{replaceMerge:n.get("replaceMerge",!0)}),it({currentIndex:n.option.currentIndex},e)}),a.registerAction({type:"timelinePlayChange",event:"timelinePlayChanged",update:"update"},function(e,t){var r=t.getComponent("timeline");r&&e.playState!=null&&r.setPlayState(e.playState)})}function p0(a){var e=a&&a.timeline;H(e)||(e=e?[e]:[]),C(e,function(t){t&&d0(t)})}function d0(a){var e=a.type,t={number:"value",time:"time"};if(t[e]&&(a.axisType=t[e],delete a.type),Us(a),_e(a,"controlPosition")){var r=a.controlStyle||(a.controlStyle={});_e(r,"position")||(r.position=a.controlPosition),r.position==="none"&&!_e(r,"show")&&(r.show=!1,delete r.position),delete a.controlPosition}C(a.data||[],function(n){wr(n)&&!H(n)&&(!_e(n,"value")&&_e(n,"name")&&(n.value=n.name),Us(n))})}function Us(a){var e=a.itemStyle||(a.itemStyle={}),t=e.emphasis||(e.emphasis={}),r=a.label||a.label||{},n=r.normal||(r.normal={}),i={normal:1,emphasis:1};C(r,function(o,s){!i[s]&&!_e(n,s)&&(n[s]=o)}),t.label&&!_e(r,"emphasis")&&(r.emphasis=t.label,delete t.label)}function _e(a,e){return a.hasOwnProperty(e)}function g0(a){a.registerComponentModel(Wu),a.registerComponentView(u0),a.registerSubTypeDefaulter("timeline",function(){return"slider"}),f0(a),a.registerPreprocessor(p0)}function Mi(a,e){if(!a)return!1;for(var t=H(a)?a:[a],r=0;r<t.length;r++)if(t[r]&&t[r][e])return!0;return!1}function zr(a){ga(a,"label",["show"])}var Or=_t(),oe=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.createdBySelf=!1,t}return e.prototype.init=function(t,r,n){this.mergeDefaultAndTheme(t,n),this._mergeOption(t,n,!1,!0)},e.prototype.isAnimationEnabled=function(){if($c.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},e.prototype.mergeOption=function(t,r){this._mergeOption(t,r,!1,!1)},e.prototype._mergeOption=function(t,r,n,i){var o=this.mainType;n||r.eachSeries(function(s){var l=s.get(this.mainType,!0),u=Or(s)[o];if(!l||!l.data){Or(s)[o]=null;return}u?u._mergeOption(l,r,!0):(i&&zr(l),C(l.data,function(v){v instanceof Array?(zr(v[0]),zr(v[1])):zr(v)}),u=this.createMarkerModelFromSeries(l,this,r),U(u,{mainType:this.mainType,seriesIndex:s.seriesIndex,name:s.name,createdBySelf:!0}),u.__hostSeries=s),Or(s)[o]=u},this)},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=this.getRawValue(t),s=i.getName(t);return At("section",{header:this.name,blocks:[At("nameValue",{name:s,value:o,noName:!s,noValue:o==null})]})},e.prototype.getData=function(){return this._data},e.prototype.setData=function(t){this._data=t},e.prototype.getDataParams=function(t,r){var n=ii.prototype.getDataParams.call(this,t,r),i=this.__hostSeries;return i&&(n.seriesId=i.id,n.seriesName=i.name,n.seriesType=i.subType),n},e.getMarkerModelFromSeries=function(t,r){return Or(t)[r]},e.type="marker",e.dependencies=["series","grid","polar","geo"],e}(Jt);Kt(oe,ii.prototype);var y0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,n){return new e(t,r,n)},e.type="markPoint",e.defaultOption={z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}},e}(oe);function Hn(a){return!(isNaN(parseFloat(a.x))&&isNaN(parseFloat(a.y)))}function m0(a){return!isNaN(parseFloat(a.x))&&!isNaN(parseFloat(a.y))}function Br(a,e,t,r,n,i){var o=[],s=Yc(e,r),l=s?e.getCalculationInfo("stackResultDimension"):r,u=Ei(e,l,a),v=e.indicesOfNearest(l,u)[0];o[n]=e.get(t,v),o[i]=e.get(l,v);var c=e.get(r,v),h=Xc(e.get(r,v));return h=Math.min(h,20),h>=0&&(o[i]=+o[i].toFixed(h)),[o,c]}var pn={min:st(Br,"min"),max:st(Br,"max"),average:st(Br,"average"),median:st(Br,"median")};function hr(a,e){if(e){var t=a.getData(),r=a.coordinateSystem,n=r&&r.dimensions;if(!m0(e)&&!H(e.coord)&&H(n)){var i=Hu(e,t,r,a);if(e=$t(e),e.type&&pn[e.type]&&i.baseAxis&&i.valueAxis){var o=zt(n,i.baseAxis.dim),s=zt(n,i.valueAxis.dim),l=pn[e.type](t,i.baseDataDim,i.valueDataDim,o,s);e.coord=l[0],e.value=l[1]}else e.coord=[e.xAxis!=null?e.xAxis:e.radiusAxis,e.yAxis!=null?e.yAxis:e.angleAxis]}if(e.coord==null||!H(n))e.coord=[];else for(var u=e.coord,v=0;v<2;v++)pn[u[v]]&&(u[v]=Ei(t,t.mapDimension(n[v]),u[v]));return e}}function Hu(a,e,t,r){var n={};return a.valueIndex!=null||a.valueDim!=null?(n.valueDataDim=a.valueIndex!=null?e.getDimension(a.valueIndex):a.valueDim,n.valueAxis=t.getAxis(S0(r,n.valueDataDim)),n.baseAxis=t.getOtherAxis(n.valueAxis),n.baseDataDim=e.mapDimension(n.baseAxis.dim)):(n.baseAxis=r.getBaseAxis(),n.valueAxis=t.getOtherAxis(n.baseAxis),n.baseDataDim=e.mapDimension(n.baseAxis.dim),n.valueDataDim=e.mapDimension(n.valueAxis.dim)),n}function S0(a,e){var t=a.getData().getDimensionInfo(e);return t&&t.coordDim}function fr(a,e){return a&&a.containData&&e.coord&&!Hn(e)?a.containData(e.coord):!0}function x0(a,e,t){return a&&a.containZone&&e.coord&&t.coord&&!Hn(e)&&!Hn(t)?a.containZone(e.coord,t.coord):!0}function Uu(a,e){return a?function(t,r,n,i){var o=i<2?t.coord&&t.coord[i]:t.value;return Yr(o,e[i])}:function(t,r,n,i){return Yr(t.value,e[i])}}function Ei(a,e,t){if(t==="average"){var r=0,n=0;return a.each(e,function(i,o){isNaN(i)||(r+=i,n++)}),r/n}else return t==="median"?a.getMedian(e):a.getDataExtent(e)[t==="max"?1:0]}var dn=_t(),Ri=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this.markerGroupMap=j()},e.prototype.render=function(t,r,n){var i=this,o=this.markerGroupMap;o.each(function(s){dn(s).keep=!1}),r.eachSeries(function(s){var l=oe.getMarkerModelFromSeries(s,i.type);l&&i.renderSeries(s,l,r,n)}),o.each(function(s){!dn(s).keep&&i.group.remove(s.group)})},e.prototype.markKeep=function(t){dn(t).keep=!0},e.prototype.toggleBlurSeries=function(t,r){var n=this;C(t,function(i){var o=oe.getMarkerModelFromSeries(i,n.type);if(o){var s=o.getData();s.eachItemGraphicEl(function(l){l&&(r?Zc(l):qc(l))})}})},e.type="marker",e}(Qt);function $s(a,e,t){var r=e.coordinateSystem;a.each(function(n){var i=a.getItemModel(n),o,s=z(i.get("x"),t.getWidth()),l=z(i.get("y"),t.getHeight());if(!isNaN(s)&&!isNaN(l))o=[s,l];else if(e.getMarkerPosition)o=e.getMarkerPosition(a.getValues(a.dimensions,n));else if(r){var u=a.get(r.dimensions[0],n),v=a.get(r.dimensions[1],n);o=r.dataToPoint([u,v])}isNaN(s)||(o[0]=s),isNaN(l)||(o[1]=l),a.setItemLayout(n,o)})}var b0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,n){r.eachSeries(function(i){var o=oe.getMarkerModelFromSeries(i,"markPoint");o&&($s(o.getData(),i,n),this.markerGroupMap.get(i.id).updateLayout())},this)},e.prototype.renderSeries=function(t,r,n,i){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,v=u.get(s)||u.set(s,new la),c=_0(o,t,r);r.setData(c),$s(r.getData(),t,i),c.each(function(h){var f=c.getItemModel(h),p=f.getShallow("symbol"),d=f.getShallow("symbolSize"),g=f.getShallow("symbolRotate"),m=f.getShallow("symbolOffset"),S=f.getShallow("symbolKeepAspect");if(ot(p)||ot(d)||ot(g)||ot(m)){var y=r.getRawValue(h),b=r.getDataParams(h);ot(p)&&(p=p(y,b)),ot(d)&&(d=d(y,b)),ot(g)&&(g=g(y,b)),ot(m)&&(m=m(y,b))}var _=f.getModel("itemStyle").getItemStyle(),x=oi(l,"color");_.fill||(_.fill=x),c.setItemVisual(h,{symbol:p,symbolSize:d,symbolRotate:g,symbolOffset:m,symbolKeepAspect:S,style:_})}),v.updateData(c),this.group.add(v.group),c.eachItemGraphicEl(function(h){h.traverse(function(f){nt(f).dataModel=r})}),this.markKeep(v),v.group.silent=r.get("silent")||t.get("silent")},e.type="markPoint",e}(Ri);function _0(a,e,t){var r;a?r=B(a&&a.dimensions,function(s){var l=e.getData().getDimensionInfo(e.getData().mapDimension(s))||{};return U(U({},l),{name:s,ordinalMeta:null})}):r=[{name:"value",type:"float"}];var n=new Mt(r,t),i=B(t.get("data"),st(hr,e));a&&(i=Pt(i,st(fr,a)));var o=Uu(!!a,r);return n.initData(i,null,o),n}function w0(a){a.registerComponentModel(y0),a.registerComponentView(b0),a.registerPreprocessor(function(e){Mi(e.series,"markPoint")&&(e.markPoint=e.markPoint||{})})}var D0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,n){return new e(t,r,n)},e.type="markLine",e.defaultOption={z:5,symbol:["circle","arrow"],symbolSize:[8,16],symbolOffset:0,precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"},e}(oe),Fr=_t(),T0=function(a,e,t,r){var n=a.getData(),i;if(H(r))i=r;else{var o=r.type;if(o==="min"||o==="max"||o==="average"||o==="median"||r.xAxis!=null||r.yAxis!=null){var s=void 0,l=void 0;if(r.yAxis!=null||r.xAxis!=null)s=e.getAxis(r.yAxis!=null?"y":"x"),l=kt(r.yAxis,r.xAxis);else{var u=Hu(r,n,e,a);s=u.valueAxis;var v=jc(n,u.valueDataDim);l=Ei(n,v,o)}var c=s.dim==="x"?0:1,h=1-c,f=$t(r),p={coord:[]};f.type=null,f.coord=[],f.coord[h]=-1/0,p.coord[h]=1/0;var d=t.get("precision");d>=0&&jt(l)&&(l=+l.toFixed(Math.min(d,20))),f.coord[c]=p.coord[c]=l,i=[f,p,{type:o,valueIndex:r.valueIndex,value:l}]}else i=[]}var g=[hr(a,i[0]),hr(a,i[1]),U({},i[2])];return g[2].type=g[2].type||null,Bt(g[2],g[0]),Bt(g[2],g[1]),g};function ea(a){return!isNaN(a)&&!isFinite(a)}function Ys(a,e,t,r){var n=1-a,i=r.dimensions[a];return ea(e[n])&&ea(t[n])&&e[a]===t[a]&&r.getAxis(i).containData(e[a])}function I0(a,e){if(a.type==="cartesian2d"){var t=e[0].coord,r=e[1].coord;if(t&&r&&(Ys(1,t,r,a)||Ys(0,t,r,a)))return!0}return fr(a,e[0])&&fr(a,e[1])}function gn(a,e,t,r,n){var i=r.coordinateSystem,o=a.getItemModel(e),s,l=z(o.get("x"),n.getWidth()),u=z(o.get("y"),n.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(r.getMarkerPosition)s=r.getMarkerPosition(a.getValues(a.dimensions,e));else{var v=i.dimensions,c=a.get(v[0],e),h=a.get(v[1],e);s=i.dataToPoint([c,h])}if(xa(i,"cartesian2d")){var f=i.getAxis("x"),p=i.getAxis("y"),v=i.dimensions;ea(a.get(v[0],e))?s[0]=f.toGlobalCoord(f.getExtent()[t?0:1]):ea(a.get(v[1],e))&&(s[1]=p.toGlobalCoord(p.getExtent()[t?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}a.setItemLayout(e,s)}var A0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,n){r.eachSeries(function(i){var o=oe.getMarkerModelFromSeries(i,"markLine");if(o){var s=o.getData(),l=Fr(o).from,u=Fr(o).to;l.each(function(v){gn(l,v,!0,i,n),gn(u,v,!1,i,n)}),s.each(function(v){s.setItemLayout(v,[l.getItemLayout(v),u.getItemLayout(v)])}),this.markerGroupMap.get(i.id).updateLayout()}},this)},e.prototype.renderSeries=function(t,r,n,i){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,v=u.get(s)||u.set(s,new bi);this.group.add(v.group);var c=L0(o,t,r),h=c.from,f=c.to,p=c.line;Fr(r).from=h,Fr(r).to=f,r.setData(p);var d=r.get("symbol"),g=r.get("symbolSize"),m=r.get("symbolRotate"),S=r.get("symbolOffset");H(d)||(d=[d,d]),H(g)||(g=[g,g]),H(m)||(m=[m,m]),H(S)||(S=[S,S]),c.from.each(function(b){y(h,b,!0),y(f,b,!1)}),p.each(function(b){var _=p.getItemModel(b).getModel("lineStyle").getLineStyle();p.setItemLayout(b,[h.getItemLayout(b),f.getItemLayout(b)]),_.stroke==null&&(_.stroke=h.getItemVisual(b,"style").fill),p.setItemVisual(b,{fromSymbolKeepAspect:h.getItemVisual(b,"symbolKeepAspect"),fromSymbolOffset:h.getItemVisual(b,"symbolOffset"),fromSymbolRotate:h.getItemVisual(b,"symbolRotate"),fromSymbolSize:h.getItemVisual(b,"symbolSize"),fromSymbol:h.getItemVisual(b,"symbol"),toSymbolKeepAspect:f.getItemVisual(b,"symbolKeepAspect"),toSymbolOffset:f.getItemVisual(b,"symbolOffset"),toSymbolRotate:f.getItemVisual(b,"symbolRotate"),toSymbolSize:f.getItemVisual(b,"symbolSize"),toSymbol:f.getItemVisual(b,"symbol"),style:_})}),v.updateData(p),c.line.eachItemGraphicEl(function(b){nt(b).dataModel=r,b.traverse(function(_){nt(_).dataModel=r})});function y(b,_,x){var w=b.getItemModel(_);gn(b,_,x,t,i);var D=w.getModel("itemStyle").getItemStyle();D.fill==null&&(D.fill=oi(l,"color")),b.setItemVisual(_,{symbolKeepAspect:w.get("symbolKeepAspect"),symbolOffset:Zt(w.get("symbolOffset",!0),S[x?0:1]),symbolRotate:Zt(w.get("symbolRotate",!0),m[x?0:1]),symbolSize:Zt(w.get("symbolSize"),g[x?0:1]),symbol:Zt(w.get("symbol",!0),d[x?0:1]),style:D})}this.markKeep(v),v.group.silent=r.get("silent")||t.get("silent")},e.type="markLine",e}(Ri);function L0(a,e,t){var r;a?r=B(a&&a.dimensions,function(u){var v=e.getData().getDimensionInfo(e.getData().mapDimension(u))||{};return U(U({},v),{name:u,ordinalMeta:null})}):r=[{name:"value",type:"float"}];var n=new Mt(r,t),i=new Mt(r,t),o=new Mt([],t),s=B(t.get("data"),st(T0,e,a,t));a&&(s=Pt(s,st(I0,a)));var l=Uu(!!a,r);return n.initData(B(s,function(u){return u[0]}),null,l),i.initData(B(s,function(u){return u[1]}),null,l),o.initData(B(s,function(u){return u[2]})),o.hasItemOption=!0,{from:n,to:i,line:o}}function C0(a){a.registerComponentModel(D0),a.registerComponentView(A0),a.registerPreprocessor(function(e){Mi(e.series,"markLine")&&(e.markLine=e.markLine||{})})}var P0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,n){return new e(t,r,n)},e.type="markArea",e.defaultOption={z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}},e}(oe),Wr=_t(),M0=function(a,e,t,r){var n=r[0],i=r[1];if(!(!n||!i)){var o=hr(a,n),s=hr(a,i),l=o.coord,u=s.coord;l[0]=kt(l[0],-1/0),l[1]=kt(l[1],-1/0),u[0]=kt(u[0],1/0),u[1]=kt(u[1],1/0);var v=Jn([{},o,s]);return v.coord=[o.coord,s.coord],v.x0=o.x,v.y0=o.y,v.x1=s.x,v.y1=s.y,v}};function ra(a){return!isNaN(a)&&!isFinite(a)}function Xs(a,e,t,r){var n=1-a;return ra(e[n])&&ra(t[n])}function E0(a,e){var t=e.coord[0],r=e.coord[1],n={coord:t,x:e.x0,y:e.y0},i={coord:r,x:e.x1,y:e.y1};return xa(a,"cartesian2d")?t&&r&&(Xs(1,t,r)||Xs(0,t,r))?!0:x0(a,n,i):fr(a,n)||fr(a,i)}function Zs(a,e,t,r,n){var i=r.coordinateSystem,o=a.getItemModel(e),s,l=z(o.get(t[0]),n.getWidth()),u=z(o.get(t[1]),n.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(r.getMarkerPosition){var v=a.getValues(["x0","y0"],e),c=a.getValues(["x1","y1"],e),h=i.clampData(v),f=i.clampData(c),p=[];t[0]==="x0"?p[0]=h[0]>f[0]?c[0]:v[0]:p[0]=h[0]>f[0]?v[0]:c[0],t[1]==="y0"?p[1]=h[1]>f[1]?c[1]:v[1]:p[1]=h[1]>f[1]?v[1]:c[1],s=r.getMarkerPosition(p,t,!0)}else{var d=a.get(t[0],e),g=a.get(t[1],e),m=[d,g];i.clampData&&i.clampData(m,m),s=i.dataToPoint(m,!0)}if(xa(i,"cartesian2d")){var S=i.getAxis("x"),y=i.getAxis("y"),d=a.get(t[0],e),g=a.get(t[1],e);ra(d)?s[0]=S.toGlobalCoord(S.getExtent()[t[0]==="x0"?0:1]):ra(g)&&(s[1]=y.toGlobalCoord(y.getExtent()[t[1]==="y0"?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}return s}var qs=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]],R0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,n){r.eachSeries(function(i){var o=oe.getMarkerModelFromSeries(i,"markArea");if(o){var s=o.getData();s.each(function(l){var u=B(qs,function(c){return Zs(s,l,c,i,n)});s.setItemLayout(l,u);var v=s.getItemGraphicEl(l);v.setShape("points",u)})}},this)},e.prototype.renderSeries=function(t,r,n,i){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,v=u.get(s)||u.set(s,{group:new Y});this.group.add(v.group),this.markKeep(v);var c=N0(o,t,r);r.setData(c),c.each(function(h){var f=B(qs,function(D){return Zs(c,h,D,t,i)}),p=o.getAxis("x").scale,d=o.getAxis("y").scale,g=p.getExtent(),m=d.getExtent(),S=[p.parse(c.get("x0",h)),p.parse(c.get("x1",h))],y=[d.parse(c.get("y0",h)),d.parse(c.get("y1",h))];lr(S),lr(y);var b=!(g[0]>S[1]||g[1]<S[0]||m[0]>y[1]||m[1]<y[0]),_=!b;c.setItemLayout(h,{points:f,allClipped:_});var x=c.getItemModel(h).getModel("itemStyle").getItemStyle(),w=oi(l,"color");x.fill||(x.fill=w,rt(x.fill)&&(x.fill=Dn(x.fill,.4))),x.stroke||(x.stroke=w),c.setItemVisual(h,"style",x)}),c.diff(Wr(v).data).add(function(h){var f=c.getItemLayout(h);if(!f.allClipped){var p=new ne({shape:{points:f.points}});c.setItemGraphicEl(h,p),v.group.add(p)}}).update(function(h,f){var p=Wr(v).data.getItemGraphicEl(f),d=c.getItemLayout(h);d.allClipped?p&&v.group.remove(p):(p?lt(p,{shape:{points:d.points}},r,h):p=new ne({shape:{points:d.points}}),c.setItemGraphicEl(h,p),v.group.add(p))}).remove(function(h){var f=Wr(v).data.getItemGraphicEl(h);v.group.remove(f)}).execute(),c.eachItemGraphicEl(function(h,f){var p=c.getItemModel(f),d=c.getItemVisual(f,"style");h.useStyle(c.getItemVisual(f,"style")),Ot(h,It(p),{labelFetcher:r,labelDataIndex:f,defaultText:c.getName(f)||"",inheritColor:rt(d.fill)?Dn(d.fill,1):"#000"}),Et(h,p),ct(h,null,null,p.get(["emphasis","disabled"])),nt(h).dataModel=r}),Wr(v).data=c,v.group.silent=r.get("silent")||t.get("silent")},e.type="markArea",e}(Ri);function N0(a,e,t){var r,n,i=["x0","y0","x1","y1"];if(a){var o=B(a&&a.dimensions,function(u){var v=e.getData(),c=v.getDimensionInfo(v.mapDimension(u))||{};return U(U({},c),{name:u,ordinalMeta:null})});n=B(i,function(u,v){return{name:u,type:o[v%2].type}}),r=new Mt(n,t)}else n=[{name:"value",type:"float"}],r=new Mt(n,t);var s=B(t.get("data"),st(M0,e,a,t));a&&(s=Pt(s,st(E0,a)));var l=a?function(u,v,c,h){var f=u.coord[Math.floor(h/2)][h%2];return Yr(f,n[h])}:function(u,v,c,h){return Yr(u.value,n[h])};return r.initData(s,null,l),r.hasItemOption=!0,r}function k0(a){a.registerComponentModel(P0),a.registerComponentView(R0),a.registerPreprocessor(function(e){Mi(e.series,"markArea")&&(e.markArea=e.markArea||{})})}var V0={label:{enabled:!0},decal:{show:!1}},js=_t(),G0={};function z0(a,e){var t=a.getModel("aria");if(!t.get("enabled"))return;var r=$t(V0);Bt(r.label,a.getLocaleModel().get("aria"),!1),Bt(t.option,r,!1),n(),i();function n(){var u=t.getModel("decal"),v=u.get("show");if(v){var c=j();a.eachSeries(function(h){if(!h.isColorBySeries()){var f=c.get(h.type);f||(f={},c.set(h.type,f)),js(h).scope=f}}),a.eachRawSeries(function(h){if(a.isSeriesFiltered(h))return;if(ot(h.enableAriaDecal)){h.enableAriaDecal();return}var f=h.getData();if(h.isColorBySeries()){var S=wn(h.ecModel,h.name,G0,a.getSeriesCount()),y=f.getVisual("decal");f.setVisual("decal",b(y,S))}else{var p=h.getRawData(),d={},g=js(h).scope;f.each(function(_){var x=f.getRawIndex(_);d[x]=_});var m=p.count();p.each(function(_){var x=d[_],w=p.getName(_)||_+"",D=wn(h.ecModel,w,g,m),T=f.getItemVisual(x,"decal");f.setItemVisual(x,"decal",b(T,D))})}function b(_,x){var w=_?U(U({},x),_):x;return w.dirty=!0,w}})}}function i(){var u=e.getZr().dom;if(u){var v=a.getLocaleModel().get("aria"),c=t.getModel("label");if(c.option=it(c.option,v),!!c.get("enabled")){if(u.setAttribute("role","img"),c.get("description")){u.setAttribute("aria-label",c.get("description"));return}var h=a.getSeriesCount(),f=c.get(["data","maxCount"])||10,p=c.get(["series","maxCount"])||10,d=Math.min(h,p),g;if(!(h<1)){var m=s();if(m){var S=c.get(["general","withTitle"]);g=o(S,{title:m})}else g=c.get(["general","withoutTitle"]);var y=[],b=h>1?c.get(["series","multiple","prefix"]):c.get(["series","single","prefix"]);g+=o(b,{seriesCount:h}),a.eachSeries(function(D,T){if(T<d){var I=void 0,A=D.get("name"),P=A?"withName":"withoutName";I=h>1?c.get(["series","multiple",P]):c.get(["series","single",P]),I=o(I,{seriesId:D.seriesIndex,seriesName:D.get("name"),seriesType:l(D.subType)});var E=D.getData();if(E.count()>f){var L=c.get(["data","partialData"]);I+=o(L,{displayCnt:f})}else I+=c.get(["data","allData"]);for(var M=c.get(["data","separator","middle"]),R=c.get(["data","separator","end"]),N=c.get(["data","excludeDimensionId"]),G=[],O=0;O<E.count();O++)if(O<f){var F=E.getName(O),W=N?Pt(E.getValues(O),function(J,Z){return zt(N,Z)===-1}):E.getValues(O),K=c.get(["data",F?"withName":"withoutName"]);G.push(o(K,{name:F,value:W.join(M)}))}I+=G.join(M)+R,y.push(I)}});var _=c.getModel(["series","multiple","separator"]),x=_.get("middle"),w=_.get("end");g+=y.join(x)+w,u.setAttribute("aria-label",g)}}}}function o(u,v){if(!rt(u))return u;var c=u;return C(v,function(h,f){c=c.replace(new RegExp("\\{\\s*"+f+"\\s*\\}","g"),h)}),c}function s(){var u=a.get("title");return u&&u.length&&(u=u[0]),u&&u.text}function l(u){var v=a.getLocaleModel().get(["series","typeNames"]);return v[u]||v.chart}}function O0(a){if(!(!a||!a.aria)){var e=a.aria;e.show!=null&&(e.enabled=e.show),e.label=e.label||{},C(["description","general","series","data"],function(t){e[t]!=null&&(e.label[t]=e[t])})}}function B0(a){a.registerPreprocessor(O0),a.registerVisual(a.PRIORITY.VISUAL.ARIA,z0)}var Ks={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},F0=function(){function a(e){var t=this._condVal=rt(e)?new RegExp(e):Qc(e)?e:null;if(t==null){var r="";ut(r)}}return a.prototype.evaluate=function(e){var t=typeof e;return rt(t)?this._condVal.test(e):jt(t)?this._condVal.test(e+""):!1},a}(),W0=function(){function a(){}return a.prototype.evaluate=function(){return this.value},a}(),H0=function(){function a(){}return a.prototype.evaluate=function(){for(var e=this.children,t=0;t<e.length;t++)if(!e[t].evaluate())return!1;return!0},a}(),U0=function(){function a(){}return a.prototype.evaluate=function(){for(var e=this.children,t=0;t<e.length;t++)if(e[t].evaluate())return!0;return!1},a}(),$0=function(){function a(){}return a.prototype.evaluate=function(){return!this.child.evaluate()},a}(),Y0=function(){function a(){}return a.prototype.evaluate=function(){for(var e=!!this.valueParser,t=this.getValue,r=t(this.valueGetterParam),n=e?this.valueParser(r):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(e?n:r))return!1;return!0},a}();function Ni(a,e){if(a===!0||a===!1){var t=new W0;return t.value=a,t}var r="";return $u(a)||ut(r),a.and?Js("and",a,e):a.or?Js("or",a,e):a.not?X0(a,e):Z0(a,e)}function Js(a,e,t){var r=e[a],n="";H(r)||ut(n),r.length||ut(n);var i=a==="and"?new H0:new U0;return i.children=B(r,function(o){return Ni(o,t)}),i.children.length||ut(n),i}function X0(a,e){var t=a.not,r="";$u(t)||ut(r);var n=new $0;return n.child=Ni(t,e),n.child||ut(r),n}function Z0(a,e){for(var t="",r=e.prepareGetValue(a),n=[],i=pr(a),o=a.parser,s=o?Fl(o):null,l=0;l<i.length;l++){var u=i[l];if(!(u==="parser"||e.valueGetterAttrMap.get(u))){var v=gt(Ks,u)?Ks[u]:u,c=a[u],h=s?s(c):c,f=Jc(v,h)||v==="reg"&&new F0(h);f||ut(t),n.push(f)}}n.length||ut(t);var p=new Y0;return p.valueGetterParam=r,p.valueParser=s,p.getValue=e.getValue,p.subCondList=n,p}function $u(a){return wr(a)&&!Kc(a)}var q0=function(){function a(e,t){this._cond=Ni(e,t)}return a.prototype.evaluate=function(){return this._cond.evaluate()},a}();function j0(a,e){return new q0(a,e)}var K0={type:"echarts:filter",transform:function(a){for(var e=a.upstream,t,r=j0(a.config,{valueGetterAttrMap:j({dimension:!0}),prepareGetValue:function(s){var l="",u=s.dimension;gt(s,"dimension")||ut(l);var v=e.getDimensionInfo(u);return v||ut(l),{dimIdx:v.index}},getValue:function(s){return e.retrieveValueFromItem(t,s.dimIdx)}}),n=[],i=0,o=e.count();i<o;i++)t=e.getRawDataItem(i),r.evaluate()&&n.push(t);return{data:n}}},J0={type:"echarts:sort",transform:function(a){var e=a.upstream,t=a.config,r="",n=Ut(t);n.length||ut(r);var i=[];C(n,function(v){var c=v.dimension,h=v.order,f=v.parser,p=v.incomparable;if(c==null&&ut(r),h!=="asc"&&h!=="desc"&&ut(r),p&&p!=="min"&&p!=="max"){var d="";ut(d)}if(h!=="asc"&&h!=="desc"){var g="";ut(g)}var m=e.getDimensionInfo(c);m||ut(r);var S=f?Fl(f):null;f&&!S&&ut(r),i.push({dimIdx:m.index,parser:S,comparator:new th(h,p)})});var o=e.sourceFormat;o!==Gl&&o!==eh&&ut(r);for(var s=[],l=0,u=e.count();l<u;l++)s.push(e.getRawDataItem(l));return s.sort(function(v,c){for(var h=0;h<i.length;h++){var f=i[h],p=e.retrieveValueFromItem(v,f.dimIdx),d=e.retrieveValueFromItem(c,f.dimIdx);f.parser&&(p=f.parser(p),d=f.parser(d));var g=f.comparator.evaluate(p,d);if(g!==0)return g}return 0}),{data:s}}};function Q0(a){a.registerTransform(K0),a.registerTransform(J0)}var tS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="dataset",t}return e.prototype.init=function(t,r,n){a.prototype.init.call(this,t,r,n),this._sourceManager=new rh(this),ro(this)},e.prototype.mergeOption=function(t,r){a.prototype.mergeOption.call(this,t,r),ro(this)},e.prototype.optionUpdated=function(){this._sourceManager.dirty()},e.prototype.getSourceManager=function(){return this._sourceManager},e.type="dataset",e.defaultOption={seriesLayoutBy:ah},e}(Jt),eS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="dataset",t}return e.type="dataset",e}(Qt);function rS(a){a.registerComponentModel(tS),a.registerComponentView(eS)}var Xt=nh.CMD;function ke(a,e){return Math.abs(a-e)<1e-5}function Un(a){var e=a.data,t=a.len(),r=[],n,i=0,o=0,s=0,l=0;function u(E,L){n&&n.length>2&&r.push(n),n=[E,L]}function v(E,L,M,R){ke(E,M)&&ke(L,R)||n.push(E,L,M,R,M,R)}function c(E,L,M,R,N,G){var O=Math.abs(L-E),F=Math.tan(O/4)*4/3,W=L<E?-1:1,K=Math.cos(E),J=Math.sin(E),Z=Math.cos(L),Q=Math.sin(L),yt=K*N+M,se=J*G+R,q=Z*N+M,$=Q*G+R,at=N*F*W,tt=G*F*W;n.push(yt-at*J,se+tt*K,q+at*Q,$-tt*Z,q,$)}for(var h,f,p,d,g=0;g<t;){var m=e[g++],S=g===1;switch(S&&(i=e[g],o=e[g+1],s=i,l=o,(m===Xt.L||m===Xt.C||m===Xt.Q)&&(n=[s,l])),m){case Xt.M:i=s=e[g++],o=l=e[g++],u(s,l);break;case Xt.L:h=e[g++],f=e[g++],v(i,o,h,f),i=h,o=f;break;case Xt.C:n.push(e[g++],e[g++],e[g++],e[g++],i=e[g++],o=e[g++]);break;case Xt.Q:h=e[g++],f=e[g++],p=e[g++],d=e[g++],n.push(i+2/3*(h-i),o+2/3*(f-o),p+2/3*(h-p),d+2/3*(f-d),p,d),i=p,o=d;break;case Xt.A:var y=e[g++],b=e[g++],_=e[g++],x=e[g++],w=e[g++],D=e[g++]+w;g+=1;var T=!e[g++];h=Math.cos(w)*_+y,f=Math.sin(w)*x+b,S?(s=h,l=f,u(s,l)):v(i,o,h,f),i=Math.cos(D)*_+y,o=Math.sin(D)*x+b;for(var I=(T?-1:1)*Math.PI/2,A=w;T?A>D:A<D;A+=I){var P=T?Math.max(A+I,D):Math.min(A+I,D);c(A,P,y,b,_,x)}break;case Xt.R:s=i=e[g++],l=o=e[g++],h=s+e[g++],f=l+e[g++],u(h,l),v(h,l,h,f),v(h,f,s,f),v(s,f,s,l),v(s,l,h,l);break;case Xt.Z:n&&v(i,o,s,l),i=s,o=l;break}}return n&&n.length>2&&r.push(n),r}function $n(a,e,t,r,n,i,o,s,l,u){if(ke(a,t)&&ke(e,r)&&ke(n,o)&&ke(i,s)){l.push(o,s);return}var v=2/u,c=v*v,h=o-a,f=s-e,p=Math.sqrt(h*h+f*f);h/=p,f/=p;var d=t-a,g=r-e,m=n-o,S=i-s,y=d*d+g*g,b=m*m+S*S;if(y<c&&b<c){l.push(o,s);return}var _=h*d+f*g,x=-h*m-f*S,w=y-_*_,D=b-x*x;if(w<c&&_>=0&&D<c&&x>=0){l.push(o,s);return}var T=[],I=[];Xr(a,t,n,o,.5,T),Xr(e,r,i,s,.5,I),$n(T[0],I[0],T[1],I[1],T[2],I[2],T[3],I[3],l,u),$n(T[4],I[4],T[5],I[5],T[6],I[6],T[7],I[7],l,u)}function aS(a,e){var t=Un(a),r=[];e=e||1;for(var n=0;n<t.length;n++){var i=t[n],o=[],s=i[0],l=i[1];o.push(s,l);for(var u=2;u<i.length;){var v=i[u++],c=i[u++],h=i[u++],f=i[u++],p=i[u++],d=i[u++];$n(s,l,v,c,h,f,p,d,o,e),s=p,l=d}r.push(o)}return r}function Yu(a,e,t){var r=a[e],n=a[1-e],i=Math.abs(r/n),o=Math.ceil(Math.sqrt(i*t)),s=Math.floor(t/o);s===0&&(s=1,o=t);for(var l=[],u=0;u<o;u++)l.push(s);var v=o*s,c=t-v;if(c>0)for(var u=0;u<c;u++)l[u%o]+=1;return l}function Qs(a,e,t){for(var r=a.r0,n=a.r,i=a.startAngle,o=a.endAngle,s=Math.abs(o-i),l=s*n,u=n-r,v=l>Math.abs(u),c=Yu([l,u],v?0:1,e),h=(v?s:u)/c.length,f=0;f<c.length;f++)for(var p=(v?u:s)/c[f],d=0;d<c[f];d++){var g={};v?(g.startAngle=i+h*f,g.endAngle=i+h*(f+1),g.r0=r+p*d,g.r=r+p*(d+1)):(g.startAngle=i+p*d,g.endAngle=i+p*(d+1),g.r0=r+h*f,g.r=r+h*(f+1)),g.clockwise=a.clockwise,g.cx=a.cx,g.cy=a.cy,t.push(g)}}function nS(a,e,t){for(var r=a.width,n=a.height,i=r>n,o=Yu([r,n],i?0:1,e),s=i?"width":"height",l=i?"height":"width",u=i?"x":"y",v=i?"y":"x",c=a[s]/o.length,h=0;h<o.length;h++)for(var f=a[l]/o[h],p=0;p<o[h];p++){var d={};d[u]=h*c,d[v]=p*f,d[s]=c,d[l]=f,d.x+=a.x,d.y+=a.y,t.push(d)}}function tl(a,e,t,r){return a*r-t*e}function iS(a,e,t,r,n,i,o,s){var l=t-a,u=r-e,v=o-n,c=s-i,h=tl(v,c,l,u);if(Math.abs(h)<1e-6)return null;var f=a-n,p=e-i,d=tl(f,p,v,c)/h;return d<0||d>1?null:new ue(d*l+a,d*u+e)}function oS(a,e,t){var r=new ue;ue.sub(r,t,e),r.normalize();var n=new ue;ue.sub(n,a,e);var i=n.dot(r);return i}function Me(a,e){var t=a[a.length-1];t&&t[0]===e[0]&&t[1]===e[1]||a.push(e)}function sS(a,e,t){for(var r=a.length,n=[],i=0;i<r;i++){var o=a[i],s=a[(i+1)%r],l=iS(o[0],o[1],s[0],s[1],e.x,e.y,t.x,t.y);l&&n.push({projPt:oS(l,e,t),pt:l,idx:i})}if(n.length<2)return[{points:a},{points:a}];n.sort(function(g,m){return g.projPt-m.projPt});var u=n[0],v=n[n.length-1];if(v.idx<u.idx){var c=u;u=v,v=c}for(var h=[u.pt.x,u.pt.y],f=[v.pt.x,v.pt.y],p=[h],d=[f],i=u.idx+1;i<=v.idx;i++)Me(p,a[i].slice());Me(p,f),Me(p,h);for(var i=v.idx+1;i<=u.idx+r;i++)Me(d,a[i%r].slice());return Me(d,h),Me(d,f),[{points:p},{points:d}]}function el(a){var e=a.points,t=[],r=[];ya(e,t,r);var n=new ht(t[0],t[1],r[0]-t[0],r[1]-t[1]),i=n.width,o=n.height,s=n.x,l=n.y,u=new ue,v=new ue;return i>o?(u.x=v.x=s+i/2,u.y=l,v.y=l+o):(u.y=v.y=l+o/2,u.x=s,v.x=s+i),sS(e,u,v)}function aa(a,e,t,r){if(t===1)r.push(e);else{var n=Math.floor(t/2),i=a(e);aa(a,i[0],n,r),aa(a,i[1],t-n,r)}return r}function lS(a,e){for(var t=[],r=0;r<e;r++)t.push(si(a));return t}function uS(a,e){e.setStyle(a.style),e.z=a.z,e.z2=a.z2,e.zlevel=a.zlevel}function vS(a){for(var e=[],t=0;t<a.length;)e.push([a[t++],a[t++]]);return e}function cS(a,e){var t=[],r=a.shape,n;switch(a.type){case"rect":nS(r,e,t),n=Lt;break;case"sector":Qs(r,e,t),n=sr;break;case"circle":Qs({r0:0,r:r.r,startAngle:0,endAngle:Math.PI*2,cx:r.cx,cy:r.cy},e,t),n=sr;break;default:var i=a.getComputedTransform(),o=i?Math.sqrt(Math.max(i[0]*i[0]+i[1]*i[1],i[2]*i[2]+i[3]*i[3])):1,s=B(aS(a.getUpdatedPathProxy(),o),function(m){return vS(m)}),l=s.length;if(l===0)aa(el,{points:s[0]},e,t);else if(l===e)for(var u=0;u<l;u++)t.push({points:s[u]});else{var v=0,c=B(s,function(m){var S=[],y=[];ya(m,S,y);var b=(y[1]-S[1])*(y[0]-S[0]);return v+=b,{poly:m,area:b}});c.sort(function(m,S){return S.area-m.area});for(var h=e,u=0;u<l;u++){var f=c[u];if(h<=0)break;var p=u===l-1?h:Math.ceil(f.area/v*e);p<0||(aa(el,{points:f.poly},p,t),h-=p)}}n=ne;break}if(!n)return lS(a,e);for(var d=[],u=0;u<t.length;u++){var g=new n;g.setShape(t[u]),uS(a,g),d.push(g)}return d}function hS(a,e){var t=a.length,r=e.length;if(t===r)return[a,e];for(var n=[],i=[],o=t<r?a:e,s=Math.min(t,r),l=Math.abs(r-t)/6,u=(s-2)/6,v=Math.ceil(l/u)+1,c=[o[0],o[1]],h=l,f=2;f<s;){var p=o[f-2],d=o[f-1],g=o[f++],m=o[f++],S=o[f++],y=o[f++],b=o[f++],_=o[f++];if(h<=0){c.push(g,m,S,y,b,_);continue}for(var x=Math.min(h,v-1)+1,w=1;w<=x;w++){var D=w/x;Xr(p,g,S,b,D,n),Xr(d,m,y,_,D,i),p=n[3],d=i[3],c.push(n[1],i[1],n[2],i[2],p,d),g=n[5],m=i[5],S=n[6],y=i[6]}h-=x-1}return o===a?[c,e]:[a,c]}function rl(a,e){for(var t=a.length,r=a[t-2],n=a[t-1],i=[],o=0;o<e.length;)i[o++]=r,i[o++]=n;return i}function fS(a,e){for(var t,r,n,i=[],o=[],s=0;s<Math.max(a.length,e.length);s++){var l=a[s],u=e[s],v=void 0,c=void 0;l?u?(t=hS(l,u),v=t[0],c=t[1],r=v,n=c):(c=rl(n||l,l),v=l):(v=rl(r||u,u),c=u),i.push(v),o.push(c)}return[i,o]}function al(a){for(var e=0,t=0,r=0,n=a.length,i=0,o=n-2;i<n;o=i,i+=2){var s=a[o],l=a[o+1],u=a[i],v=a[i+1],c=s*v-u*l;e+=c,t+=(s+u)*c,r+=(l+v)*c}return e===0?[a[0]||0,a[1]||0]:[t/e/3,r/e/3,e]}function pS(a,e,t,r){for(var n=(a.length-2)/6,i=1/0,o=0,s=a.length,l=s-2,u=0;u<n;u++){for(var v=u*6,c=0,h=0;h<s;h+=2){var f=h===0?v:(v+h-2)%l+2,p=a[f]-t[0],d=a[f+1]-t[1],g=e[h]-r[0],m=e[h+1]-r[1],S=g-p,y=m-d;c+=S*S+y*y}c<i&&(i=c,o=u)}return o}function dS(a){for(var e=[],t=a.length,r=0;r<t;r+=2)e[r]=a[t-r-2],e[r+1]=a[t-r-1];return e}function gS(a,e,t,r){for(var n=[],i,o=0;o<a.length;o++){var s=a[o],l=e[o],u=al(s),v=al(l);i==null&&(i=u[2]<0!=v[2]<0);var c=[],h=[],f=0,p=1/0,d=[],g=s.length;i&&(s=dS(s));for(var m=pS(s,l,u,v)*6,S=g-2,y=0;y<S;y+=2){var b=(m+y)%S+2;c[y+2]=s[b]-u[0],c[y+3]=s[b+1]-u[1]}c[0]=s[m]-u[0],c[1]=s[m+1]-u[1];for(var _=r/t,x=-r/2;x<=r/2;x+=_){for(var w=Math.sin(x),D=Math.cos(x),T=0,y=0;y<s.length;y+=2){var I=c[y],A=c[y+1],P=l[y]-v[0],E=l[y+1]-v[1],L=P*D-E*w,M=P*w+E*D;d[y]=L,d[y+1]=M;var R=L-I,N=M-A;T+=R*R+N*N}if(T<p){p=T,f=x;for(var G=0;G<d.length;G++)h[G]=d[G]}}n.push({from:c,to:h,fromCp:u,toCp:v,rotation:-f})}return n}function na(a){return a.__isCombineMorphing}var Xu="__mOriginal_";function ia(a,e,t){var r=Xu+e,n=a[r]||a[e];a[r]||(a[r]=a[e]);var i=t.replace,o=t.after,s=t.before;a[e]=function(){var l=arguments,u;return s&&s.apply(this,l),i?u=i.apply(this,l):u=n.apply(this,l),o&&o.apply(this,l),u}}function nr(a,e){var t=Xu+e;a[t]&&(a[e]=a[t],a[t]=null)}function nl(a,e){for(var t=0;t<a.length;t++)for(var r=a[t],n=0;n<r.length;){var i=r[n],o=r[n+1];r[n++]=e[0]*i+e[2]*o+e[4],r[n++]=e[1]*i+e[3]*o+e[5]}}function Zu(a,e){var t=a.getUpdatedPathProxy(),r=e.getUpdatedPathProxy(),n=fS(Un(t),Un(r)),i=n[0],o=n[1],s=a.getComputedTransform(),l=e.getComputedTransform();function u(){this.transform=null}s&&nl(i,s),l&&nl(o,l),ia(e,"updateTransform",{replace:u}),e.transform=null;var v=gS(i,o,10,Math.PI),c=[];ia(e,"buildPath",{replace:function(h){for(var f=e.__morphT,p=1-f,d=[],g=0;g<v.length;g++){var m=v[g],S=m.from,y=m.to,b=m.rotation*f,_=m.fromCp,x=m.toCp,w=Math.sin(b),D=Math.cos(b);ih(d,_,x,f);for(var T=0;T<S.length;T+=2){var I=S[T],A=S[T+1],P=y[T],E=y[T+1],L=I*p+P*f,M=A*p+E*f;c[T]=L*D-M*w+d[0],c[T+1]=L*w+M*D+d[1]}var R=c[0],N=c[1];h.moveTo(R,N);for(var T=2;T<S.length;){var P=c[T++],E=c[T++],G=c[T++],O=c[T++],F=c[T++],W=c[T++];R===P&&N===E&&G===F&&O===W?h.lineTo(F,W):h.bezierCurveTo(P,E,G,O,F,W),R=F,N=W}}}})}function ki(a,e,t){if(!a||!e)return e;var r=t.done,n=t.during;Zu(a,e),e.__morphT=0;function i(){nr(e,"buildPath"),nr(e,"updateTransform"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape()}return e.animateTo({__morphT:1},it({during:function(o){e.dirtyShape(),n&&n(o)},done:function(){i(),r&&r()}},t)),e}function yS(a,e,t,r,n,i){var o=16;a=n===t?0:Math.round(32767*(a-t)/(n-t)),e=i===r?0:Math.round(32767*(e-r)/(i-r));for(var s=0,l,u=(1<<o)/2;u>0;u/=2){var v=0,c=0;(a&u)>0&&(v=1),(e&u)>0&&(c=1),s+=u*u*(3*v^c),c===0&&(v===1&&(a=u-1-a,e=u-1-e),l=a,a=e,e=l)}return s}function oa(a){var e=1/0,t=1/0,r=-1/0,n=-1/0,i=B(a,function(s){var l=s.getBoundingRect(),u=s.getComputedTransform(),v=l.x+l.width/2+(u?u[4]:0),c=l.y+l.height/2+(u?u[5]:0);return e=Math.min(v,e),t=Math.min(c,t),r=Math.max(v,r),n=Math.max(c,n),[v,c]}),o=B(i,function(s,l){return{cp:s,z:yS(s[0],s[1],e,t,r,n),path:a[l]}});return o.sort(function(s,l){return s.z-l.z}).map(function(s){return s.path})}function qu(a){return cS(a.path,a.count)}function Yn(){return{fromIndividuals:[],toIndividuals:[],count:0}}function mS(a,e,t){var r=[];function n(_){for(var x=0;x<_.length;x++){var w=_[x];na(w)?n(w.childrenRef()):w instanceof wt&&r.push(w)}}n(a);var i=r.length;if(!i)return Yn();var o=t.dividePath||qu,s=o({path:e,count:i});if(s.length!==i)return console.error("Invalid morphing: unmatched splitted path"),Yn();r=oa(r),s=oa(s);for(var l=t.done,u=t.during,v=t.individualDelay,c=new Re,h=0;h<i;h++){var f=r[h],p=s[h];p.parent=e,p.copyTransform(c),v||Zu(f,p)}e.__isCombineMorphing=!0,e.childrenRef=function(){return s};function d(_){for(var x=0;x<s.length;x++)s[x].addSelfToZr(_)}ia(e,"addSelfToZr",{after:function(_){d(_)}}),ia(e,"removeSelfFromZr",{after:function(_){for(var x=0;x<s.length;x++)s[x].removeSelfFromZr(_)}});function g(){e.__isCombineMorphing=!1,e.__morphT=-1,e.childrenRef=null,nr(e,"addSelfToZr"),nr(e,"removeSelfFromZr")}var m=s.length;if(v)for(var S=m,y=function(){S--,S===0&&(g(),l&&l())},h=0;h<m;h++){var b=v?it({delay:(t.delay||0)+v(h,m,r[h],s[h]),done:y},t):t;ki(r[h],s[h],b)}else e.__morphT=0,e.animateTo({__morphT:1},it({during:function(_){for(var x=0;x<m;x++){var w=s[x];w.__morphT=e.__morphT,w.dirtyShape()}u&&u(_)},done:function(){g();for(var _=0;_<a.length;_++)nr(a[_],"updateTransform");l&&l()}},t));return e.__zr&&d(e.__zr),{fromIndividuals:r,toIndividuals:s,count:m}}function SS(a,e,t){var r=e.length,n=[],i=t.dividePath||qu;function o(f){for(var p=0;p<f.length;p++){var d=f[p];na(d)?o(d.childrenRef()):d instanceof wt&&n.push(d)}}if(na(a)){o(a.childrenRef());var s=n.length;if(s<r)for(var l=0,u=s;u<r;u++)n.push(si(n[l++%s]));n.length=r}else{n=i({path:a,count:r});for(var v=a.getComputedTransform(),u=0;u<n.length;u++)n[u].setLocalTransform(v);if(n.length!==r)return console.error("Invalid morphing: unmatched splitted path"),Yn()}n=oa(n),e=oa(e);for(var c=t.individualDelay,u=0;u<r;u++){var h=c?it({delay:(t.delay||0)+c(u,r,n[u],e[u])},t):t;ki(n[u],e[u],h)}return{fromIndividuals:n,toIndividuals:e,count:e.length}}function il(a){return H(a[0])}function ol(a,e){for(var t=[],r=a.length,n=0;n<r;n++)t.push({one:a[n],many:[]});for(var n=0;n<e.length;n++){var i=e[n].length,o=void 0;for(o=0;o<i;o++)t[o%r].many.push(e[n][o])}for(var s=0,n=r-1;n>=0;n--)if(!t[n].many.length){var l=t[s].many;if(l.length<=1)if(s)s=0;else return t;var i=l.length,u=Math.ceil(i/2);t[n].many=l.slice(u,i),t[s].many=l.slice(0,u),s++}return t}var xS={clone:function(a){for(var e=[],t=1-Math.pow(1-a.path.style.opacity,1/a.count),r=0;r<a.count;r++){var n=si(a.path);n.setStyle("opacity",t),e.push(n)}return e},split:null};function yn(a,e,t,r,n,i){if(!a.length||!e.length)return;var o=li("update",r,n);if(!(o&&o.duration>0))return;var s=r.getModel("universalTransition").get("delay"),l=Object.assign({setToFinal:!0},o),u,v;il(a)&&(u=a,v=e),il(e)&&(u=e,v=a);function c(m,S,y,b,_){var x=m.many,w=m.one;if(x.length===1&&!_){var D=S?x[0]:w,T=S?w:x[0];if(na(D))c({many:[D],one:T},!0,y,b,!0);else{var I=s?it({delay:s(y,b)},l):l;ki(D,T,I),i(D,T,D,T,I)}}else for(var A=it({dividePath:xS[t],individualDelay:s&&function(N,G,O,F){return s(N+y,b)}},l),P=S?mS(x,w,A):SS(w,x,A),E=P.fromIndividuals,L=P.toIndividuals,M=E.length,R=0;R<M;R++){var I=s?it({delay:s(R,M)},l):l;i(E[R],L[R],S?x[R]:m.one,S?m.one:x[R],I)}}for(var h=u?u===a:a.length>e.length,f=u?ol(v,u):ol(h?e:a,[h?a:e]),p=0,d=0;d<f.length;d++)p+=f[d].many.length;for(var g=0,d=0;d<f.length;d++)c(f[d],h,g,p),g+=f[d].many.length}function be(a){if(!a)return[];if(H(a)){for(var e=[],t=0;t<a.length;t++)e.push(be(a[t]));return e}var r=[];return a.traverse(function(n){n instanceof wt&&!n.disableMorphing&&!n.invisible&&!n.ignore&&r.push(n)}),r}var ju=1e4,bS=0,sl=1,ll=2,_S=_t();function wS(a,e){for(var t=a.dimensions,r=0;r<t.length;r++){var n=a.getDimensionInfo(t[r]);if(n&&n.otherDims[e]===0)return t[r]}}function DS(a,e,t){var r=a.getDimensionInfo(t),n=r&&r.ordinalMeta;if(r){var i=a.get(r.name,e);return n&&n.categories[i]||i+""}}function ul(a,e,t,r){var n=r?"itemChildGroupId":"itemGroupId",i=wS(a,n);if(i){var o=DS(a,e,i);return o}var s=a.getRawDataItem(e),l=r?"childGroupId":"groupId";if(s&&s[l])return s[l]+"";if(!r)return t||a.getId(e)}function vl(a){var e=[];return C(a,function(t){var r=t.data,n=t.dataGroupId;if(!(r.count()>ju))for(var i=r.getIndices(),o=0;o<i.length;o++)e.push({data:r,groupId:ul(r,o,n,!1),childGroupId:ul(r,o,n,!0),divide:t.divide,dataIndex:o})}),e}function mn(a,e,t){a.traverse(function(r){r instanceof wt&&Tt(r,{style:{opacity:0}},e,{dataIndex:t,isFrom:!0})})}function Sn(a){if(a.parent){var e=a.getComputedTransform();a.setLocalTransform(e),a.parent.remove(a)}}function Ee(a){a.stopAnimation(),a.isGroup&&a.traverse(function(e){e.stopAnimation()})}function TS(a,e,t){var r=li("update",t,e);r&&a.traverse(function(n){if(n instanceof ir){var i=oh(n);i&&n.animateFrom({style:i},r)}})}function IS(a,e){var t=a.length;if(t!==e.length)return!1;for(var r=0;r<t;r++){var n=a[r],i=e[r];if(n.data.getId(n.dataIndex)!==i.data.getId(i.dataIndex))return!1}return!0}function Ku(a,e,t){var r=vl(a),n=vl(e);function i(y,b,_,x,w){(_||y)&&b.animateFrom({style:_&&_!==y?U(U({},_.style),y.style):y.style},w)}var o=!1,s=bS,l=j(),u=j();r.forEach(function(y){y.groupId&&l.set(y.groupId,!0),y.childGroupId&&u.set(y.childGroupId,!0)});for(var v=0;v<n.length;v++){var c=n[v].groupId;if(u.get(c)){s=sl;break}var h=n[v].childGroupId;if(h&&l.get(h)){s=ll;break}}function f(y,b){return function(_){var x=_.data,w=_.dataIndex;return b?x.getId(w):y?s===sl?_.childGroupId:_.groupId:s===ll?_.childGroupId:_.groupId}}var p=IS(r,n),d={};if(!p)for(var v=0;v<n.length;v++){var g=n[v],m=g.data.getItemGraphicEl(g.dataIndex);m&&(d[m.id]=!0)}function S(y,b){var _=r[b],x=n[y],w=x.data.hostModel,D=_.data.getItemGraphicEl(_.dataIndex),T=x.data.getItemGraphicEl(x.dataIndex);if(D===T){T&&TS(T,x.dataIndex,w);return}D&&d[D.id]||T&&(Ee(T),D?(Ee(D),Sn(D),o=!0,yn(be(D),be(T),x.divide,w,y,i)):mn(T,w,y))}new Oe(r,n,f(!0,p),f(!1,p),null,"multiple").update(S).updateManyToOne(function(y,b){var _=n[y],x=_.data,w=x.hostModel,D=x.getItemGraphicEl(_.dataIndex),T=Pt(B(b,function(I){return r[I].data.getItemGraphicEl(r[I].dataIndex)}),function(I){return I&&I!==D&&!d[I.id]});D&&(Ee(D),T.length?(C(T,function(I){Ee(I),Sn(I)}),o=!0,yn(be(T),be(D),_.divide,w,y,i)):mn(D,w,_.dataIndex))}).updateOneToMany(function(y,b){var _=r[b],x=_.data.getItemGraphicEl(_.dataIndex);if(!(x&&d[x.id])){var w=Pt(B(y,function(T){return n[T].data.getItemGraphicEl(n[T].dataIndex)}),function(T){return T&&T!==x}),D=n[y[0]].data.hostModel;w.length&&(C(w,function(T){return Ee(T)}),x?(Ee(x),Sn(x),o=!0,yn(be(x),be(w),_.divide,D,y[0],i)):C(w,function(T){return mn(T,D,y[0])}))}}).updateManyToMany(function(y,b){new Oe(b,y,function(_){return r[_].data.getId(r[_].dataIndex)},function(_){return n[_].data.getId(n[_].dataIndex)}).update(function(_,x){S(y[_],b[x])}).execute()}).execute(),o&&C(e,function(y){var b=y.data,_=b.hostModel,x=_&&t.getViewOfSeriesModel(_),w=li("update",_,0);x&&_.isAnimationEnabled()&&w&&w.duration>0&&x.group.traverse(function(D){D instanceof wt&&!D.animators.length&&D.animateFrom({style:{opacity:0}},w)})})}function cl(a){var e=a.getModel("universalTransition").get("seriesKey");return e||a.id}function hl(a){return H(a)?a.sort().join(","):a}function le(a){if(a.hostModel)return a.hostModel.getModel("universalTransition").get("divideShape")}function AS(a,e){var t=j(),r=j(),n=j();return C(a.oldSeries,function(i,o){var s=a.oldDataGroupIds[o],l=a.oldData[o],u=cl(i),v=hl(u);r.set(v,{dataGroupId:s,data:l}),H(u)&&C(u,function(c){n.set(c,{key:v,dataGroupId:s,data:l})})}),C(e.updatedSeries,function(i){if(i.isUniversalTransitionEnabled()&&i.isAnimationEnabled()){var o=i.get("dataGroupId"),s=i.getData(),l=cl(i),u=hl(l),v=r.get(u);if(v)t.set(u,{oldSeries:[{dataGroupId:v.dataGroupId,divide:le(v.data),data:v.data}],newSeries:[{dataGroupId:o,divide:le(s),data:s}]});else if(H(l)){var c=[];C(l,function(p){var d=r.get(p);d.data&&c.push({dataGroupId:d.dataGroupId,divide:le(d.data),data:d.data})}),c.length&&t.set(u,{oldSeries:c,newSeries:[{dataGroupId:o,data:s,divide:le(s)}]})}else{var h=n.get(l);if(h){var f=t.get(h.key);f||(f={oldSeries:[{dataGroupId:h.dataGroupId,data:h.data,divide:le(h.data)}],newSeries:[]},t.set(h.key,f)),f.newSeries.push({dataGroupId:o,data:s,divide:le(s)})}}}}),t}function fl(a,e){for(var t=0;t<a.length;t++){var r=e.seriesIndex!=null&&e.seriesIndex===a[t].seriesIndex||e.seriesId!=null&&e.seriesId===a[t].id;if(r)return t}}function LS(a,e,t,r){var n=[],i=[];C(Ut(a.from),function(o){var s=fl(e.oldSeries,o);s>=0&&n.push({dataGroupId:e.oldDataGroupIds[s],data:e.oldData[s],divide:le(e.oldData[s]),groupIdDim:o.dimension})}),C(Ut(a.to),function(o){var s=fl(t.updatedSeries,o);if(s>=0){var l=t.updatedSeries[s].getData();i.push({dataGroupId:e.oldDataGroupIds[s],data:l,divide:le(l),groupIdDim:o.dimension})}}),n.length>0&&i.length>0&&Ku(n,i,r)}function CS(a){a.registerUpdateLifecycle("series:beforeupdate",function(e,t,r){C(Ut(r.seriesTransition),function(n){C(Ut(n.to),function(i){for(var o=r.updatedSeries,s=0;s<o.length;s++)(i.seriesIndex!=null&&i.seriesIndex===o[s].seriesIndex||i.seriesId!=null&&i.seriesId===o[s].id)&&(o[s][La]=!0)})})}),a.registerUpdateLifecycle("series:transition",function(e,t,r){var n=_S(t);if(n.oldSeries&&r.updatedSeries&&r.optionChanged){var i=r.seriesTransition;if(i)C(Ut(i),function(f){LS(f,n,r,t)});else{var o=AS(n,r);C(o.keys(),function(f){var p=o.get(f);Ku(p.oldSeries,p.newSeries,t)})}C(r.updatedSeries,function(f){f[La]&&(f[La]=!1)})}for(var s=e.getSeries(),l=n.oldSeries=[],u=n.oldDataGroupIds=[],v=n.oldData=[],c=0;c<s.length;c++){var h=s[c].getData();h.count()<ju&&(l.push(s[c]),u.push(s[c].get("dataGroupId")),v.push(h))}})}X([sh]);X([lh]);X([uh,vh,ch,$h,rf,Bf,pp,Xp,dd,bd,Pd,ug,Ng,$g,sy,cy,by,Ly,Oy,$y,em,Lm]);X(hh);X(fh);X(jl);X(Fm);X(xu);X($m);X(ph);X(dh);X(gh);X(Bl);X(o0);X(yh);X(g0);X(w0);X(C0);X(k0);X(mh);X(Sh);X(xh);X(bh);X(_h);X(wh);X(Dh);X(B0);X(Q0);X(rS);X(CS);X(Oh);class Ju{static getDashboardData(){return Te(this,arguments,function*(e="week",t={}){return Ar.request("get",`${this.baseURL}/data`,{params:Oi({period:e},t)})})}static getStats(e="week"){return Te(this,null,function*(){return Ar.request("get",`${this.baseURL}/stats`,{params:{period:e}})})}static getCharts(e="week"){return Te(this,null,function*(){return Ar.request("get",`${this.baseURL}/charts`,{params:{period:e}})})}static getSummary(){return Te(this,null,function*(){return Ar.request("get",`${this.baseURL}/summary`)})}}Bi(Ju,"baseURL","/api/auth/dashboard");const PS=a=>({"7d":"week","30d":"month","3m":"quarter"})[a]||"week",MS={key:0,class:"flex justify-center items-center h-screen text-2xl text-gray-500 font-inter"},ES={key:1,class:"min-h-screen bg-slate-50"},RS={class:"relative bg-gradient-to-br from-indigo-500 via-purple-600 to-purple-700 px-8 pt-12 pb-16 overflow-hidden"},NS={class:"relative z-10 w-full flex flex-col lg:flex-row justify-between items-start lg:items-center gap-8"},kS={class:"flex flex-col gap-6 items-center lg:items-end w-full lg:w-auto"},VS={class:"flex flex-col gap-2"},GS={class:"relative z-20 -mt-16 px-8 mx-auto"},zS={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},OS={class:"bg-gradient-to-br from-indigo-500 to-purple-600 text-white rounded-3xl p-6 shadow-xl hover:-translate-y-1 transition-all duration-300"},BS={class:"text-4xl font-extrabold mb-2"},FS={class:"flex items-center gap-4"},WS={class:"text-center"},HS={class:"text-xl font-bold"},US={class:"text-center"},$S={class:"text-xl font-bold"},YS={class:"bg-white rounded-3xl p-6 shadow-lg border border-slate-200 hover:-translate-y-1 hover:shadow-xl transition-all duration-300"},XS={class:"text-3xl font-extrabold text-gray-900 mb-2"},ZS={class:"text-sm text-gray-500"},qS={class:"bg-white rounded-3xl p-6 shadow-lg border border-slate-200 hover:-translate-y-1 hover:shadow-xl transition-all duration-300"},jS={class:"text-3xl font-extrabold text-gray-900 mb-2"},KS={class:"text-sm text-gray-500"},JS={class:"bg-white rounded-3xl p-6 shadow-lg border border-slate-200 hover:-translate-y-1 hover:shadow-xl transition-all duration-300"},QS={class:"text-3xl font-extrabold text-gray-900 mb-2"},t1={class:"text-sm text-gray-500"},e1={class:"px-8 pt-5 mx-auto"},r1={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},a1={class:"bg-white rounded-3xl p-6 shadow-lg border border-slate-200"},n1={class:"bg-white rounded-3xl p-6 shadow-lg border border-slate-200"},i1={class:"px-8 py-5 mx-auto"},o1={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},s1={class:"bg-white rounded-3xl p-6 shadow-lg border border-slate-200"},l1={class:"flex justify-between items-start mb-6"},u1={class:"text-center"},v1={class:"text-lg font-bold text-gray-900"},c1={class:"bg-slate-50 rounded-xl p-3 flex items-center gap-2"},h1={class:"text-sm text-gray-900 font-semibold"},f1={class:"bg-white rounded-3xl p-6 shadow-lg border border-slate-200"},p1={class:"flex justify-between items-start mb-6"},d1={class:"flex justify-between items-center mb-2"},g1={class:"text-base font-semibold"},y1={class:"text-sm text-gray-500 font-normal"},m1={class:"h-2 bg-slate-100 rounded-full overflow-hidden mb-2"},S1={class:"text-xs text-gray-500 text-center"},x1=Th({__name:"index",setup(a){const e=Ie(!0),t=Ie("7d"),r=Ih({stats:{storage:{quotaLimitMB:1,totalUsedMB:0,documentsSizeMB:0,attachmentsSizeMB:0,remainingQuotaMB:1}},charts:{}}),n=Ie(),i=Ie(),o=Ie(),s=Ie();let l,u,v,c;const h=Lr(()=>{const x=r.stats.storage;return!x||!x.quotaLimitMB?0:Math.round(x.totalUsedMB/x.quotaLimitMB*100)}),f=x=>x?x<1024?`${x.toFixed(1)} MB`:`${(x/1024).toFixed(1)} GB`:"0 MB",p=Lr(()=>{var x;return f((x=r.stats.storage)==null?void 0:x.totalUsedMB)}),d=Lr(()=>{var x;return f((x=r.stats.storage)==null?void 0:x.quotaLimitMB)}),g=Lr(()=>{var x;return f((x=r.stats.storage)==null?void 0:x.remainingQuotaMB)}),m=()=>Te(null,null,function*(){e.value=!0;try{const x=PS(t.value),w=yield Ju.getDashboardData(x);if(w.success)Object.assign(r,w.data);else throw new Error(w.message||"Failed to load dashboard data")}catch(x){console.error("Failed to load dashboard data:",x)}e.value=!1,yield Rh(),b()}),S=x=>{t.value=x,m()},y=()=>{alert("Chức năng xuất báo cáo đang được phát triển!")},b=()=>{var x,w,D,T,I,A,P;if(n.value&&r.charts.tokenTrend){l=Cr(n.value);const E=r.charts.tokenTrend;l.setOption({title:{text:"Token Usage Trend",textStyle:{fontSize:16,fontWeight:"normal"},left:"center"},tooltip:{trigger:"axis"},legend:{data:E.datasets.map(L=>L.label),top:30},grid:{top:70,right:20,bottom:30,left:50},xAxis:{type:"category",data:E.labels},yAxis:{type:"value"},series:E.datasets.map(L=>({name:L.label,type:"line",smooth:!0,data:L.data,itemStyle:{color:L.borderColor}}))})}if(i.value&&r.charts.conversationTrend){u=Cr(i.value);const E=r.charts.conversationTrend;u.setOption({title:{text:"Conversation Volume",textStyle:{fontSize:16,fontWeight:"normal"},left:"center"},tooltip:{trigger:"axis"},grid:{top:50,right:20,bottom:30,left:40},xAxis:{type:"category",data:E.labels},yAxis:{type:"value"},series:E.datasets.map(L=>({name:L.label,type:"bar",data:L.data,itemStyle:{color:L.borderColor,borderRadius:[4,4,0,0]}}))})}o.value&&(v=Cr(o.value),v.setOption({tooltip:{trigger:"item"},series:[{name:"File Types",type:"pie",radius:"70%",center:["50%","55%"],data:[{value:(x=r.stats.knowledge)==null?void 0:x.fileTypes.pdf,name:"PDF"},{value:(w=r.stats.knowledge)==null?void 0:w.fileTypes.docx,name:"DOCX"},{value:(D=r.stats.knowledge)==null?void 0:D.fileTypes.txt,name:"TXT"},{value:(T=r.stats.knowledge)==null?void 0:T.fileTypes.other,name:"Other"}]}]})),s.value&&(c=Cr(s.value),c.setOption({tooltip:{trigger:"item"},series:[{name:"Storage",type:"pie",radius:["40%","70%"],center:["50%","55%"],data:[{value:(I=r.stats.storage)==null?void 0:I.documentsSizeMB,name:"Documents"},{value:(A=r.stats.storage)==null?void 0:A.attachmentsSizeMB,name:"Attachments"},{value:(P=r.stats.storage)==null?void 0:P.remainingQuotaMB,name:"Available"}],label:{show:!1},labelLine:{show:!1}}]}))},_=()=>{l==null||l.resize(),u==null||u.resize(),v==null||v.resize(),c==null||c.resize()};return Ah(()=>{m(),window.addEventListener("resize",_)}),Lh(()=>{window.removeEventListener("resize",_)}),(x,w)=>{var D,T,I,A,P,E,L,M,R,N;return e.value?(no(),ao("div",MS," Đang tải dữ liệu Dashboard... ")):(no(),ao("div",ES,[k("div",RS,[w[7]||(w[7]=k("div",{class:"absolute inset-0 opacity-60"},[k("div",{class:"absolute top-0 left-0 w-96 h-96 bg-white/10 rounded-full -translate-x-1/2 -translate-y-1/2"}),k("div",{class:"absolute top-20 right-0 w-64 h-64 bg-white/10 rounded-full translate-x-1/2 -translate-y-1/2"})],-1)),k("div",NS,[w[6]||(w[6]=Ch('<div class="text-center lg:text-left" data-v-0f4a4c73><div class="inline-flex items-center gap-2 bg-white/15 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 text-white text-sm font-medium mb-6" data-v-0f4a4c73><span class="text-base" data-v-0f4a4c73>📊</span><span data-v-0f4a4c73>Analytics Dashboard</span></div><h1 class="text-4xl lg:text-6xl font-extrabold text-white leading-tight mb-4" data-v-0f4a4c73> Chatbot Performance <span class="bg-gradient-to-r from-yellow-300 to-orange-400 bg-clip-text text-transparent" data-v-0f4a4c73>Insights</span></h1><p class="text-xl text-white/80 leading-relaxed max-w-lg" data-v-0f4a4c73> Comprehensive analytics and performance metrics for your AI chatbots </p></div>',1)),k("div",kS,[k("div",VS,[w[4]||(w[4]=k("label",{class:"text-white/80 text-sm font-medium"},"Time Period",-1)),He($e(Ph),{modelValue:t.value,"onUpdate:modelValue":w[0]||(w[0]=G=>t.value=G),class:"modern-radio-group",onChange:S},{default:Ue(()=>[He($e(Ca),{value:"7d"},{default:Ue(()=>w[1]||(w[1]=[Ye("7 Days")])),_:1,__:[1]}),He($e(Ca),{value:"30d"},{default:Ue(()=>w[2]||(w[2]=[Ye("30 Days")])),_:1,__:[2]}),He($e(Ca),{value:"3m"},{default:Ue(()=>w[3]||(w[3]=[Ye("3 Months")])),_:1,__:[3]})]),_:1},8,["modelValue"])]),He($e(Mh),{type:"primary",class:"export-button bg-gradient-to-r from-yellow-300 to-orange-400 border-none text-gray-800 font-semibold px-6 py-3 rounded-xl hover:-translate-y-0.5 hover:shadow-lg transition-all duration-300",onClick:y},{default:Ue(()=>w[5]||(w[5]=[Ye(" Export Report ")])),_:1,__:[5]})])])]),k("div",GS,[k("div",zS,[k("div",OS,[w[12]||(w[12]=k("div",{class:"flex justify-between items-start mb-4"},[k("div",{class:"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center"},[k("span",{class:"text-2xl"},"🤖")]),k("div",{class:"flex items-center gap-1 bg-white/20 rounded-lg px-2 py-1 text-xs font-semibold"},[k("span",null,"▲"),k("span",null,"+12%")])],-1)),k("div",null,[k("h3",BS,mt((D=r.stats.chatbot)==null?void 0:D.total),1),w[11]||(w[11]=k("p",{class:"text-lg font-semibold mb-4"},"Active Chatbots",-1)),k("div",FS,[k("div",WS,[k("div",HS,mt((T=r.stats.chatbot)==null?void 0:T.active),1),w[8]||(w[8]=k("div",{class:"text-xs uppercase tracking-wide opacity-80"}," Active ",-1))]),w[10]||(w[10]=k("div",{class:"w-px h-8 bg-white/30"},null,-1)),k("div",US,[k("div",$S,mt((I=r.stats.chatbot)==null?void 0:I.draft),1),w[9]||(w[9]=k("div",{class:"text-xs uppercase tracking-wide opacity-80"}," Draft ",-1))])])])]),k("div",YS,[w[14]||(w[14]=k("div",{class:"flex justify-between items-start mb-4"},[k("div",{class:"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center"},[k("span",{class:"text-2xl text-green-600"},"💬")]),k("div",{class:"flex items-center gap-1 bg-green-100 text-green-600 rounded-lg px-2 py-1 text-xs font-semibold"},[k("span",null,"▲"),k("span",null,"+8%")])],-1)),k("div",null,[k("h3",XS,mt((A=r.stats.conversation)==null?void 0:A.total.toLocaleString()),1),w[13]||(w[13]=k("p",{class:"text-base font-semibold text-gray-600 mb-4"}," Conversations ",-1)),k("div",ZS,mt((P=r.stats.conversation)==null?void 0:P.today)+" today ",1)])]),k("div",qS,[w[16]||(w[16]=k("div",{class:"flex justify-between items-start mb-4"},[k("div",{class:"w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center"},[k("span",{class:"text-2xl text-yellow-600"},"⚙️")]),k("div",{class:"flex items-center gap-1 bg-gray-100 text-gray-600 rounded-lg px-2 py-1 text-xs font-semibold"},[k("span",null,"-"),k("span",null,"0%")])],-1)),k("div",null,[k("h3",jS,mt((E=r.stats.token)==null?void 0:E.total.toLocaleString()),1),w[15]||(w[15]=k("p",{class:"text-base font-semibold text-gray-600 mb-4"}," Tokens Used ",-1)),k("div",KS," $"+mt((L=r.stats.token)==null?void 0:L.estimatedCost)+" cost ",1)])]),k("div",JS,[w[18]||(w[18]=k("div",{class:"flex justify-between items-start mb-4"},[k("div",{class:"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center"},[k("span",{class:"text-2xl text-blue-600"},"💾")]),k("div",{class:"flex items-center gap-1 bg-green-100 text-green-600 rounded-lg px-2 py-1 text-xs font-semibold"},[k("span",null,"▲"),k("span",null,"+5%")])],-1)),k("div",null,[k("h3",QS,mt(p.value),1),w[17]||(w[17]=k("p",{class:"text-base font-semibold text-gray-600 mb-4"}," Storage Used ",-1)),k("div",t1,mt((M=r.stats.knowledge)==null?void 0:M.totalDocuments)+" documents ",1)])])])]),k("div",e1,[k("div",r1,[k("div",a1,[k("div",{ref_key:"tokenChartRef",ref:n,class:"h-80 w-full"},null,512)]),k("div",n1,[k("div",{ref_key:"conversationChartRef",ref:i,class:"h-80 w-full"},null,512)])])]),k("div",i1,[k("div",o1,[k("div",s1,[k("div",l1,[w[20]||(w[20]=k("div",null,[k("h3",{class:"text-xl font-bold text-gray-900 mb-1"}," Knowledge Base "),k("p",{class:"text-sm text-gray-600"},"Document type distribution")],-1)),k("div",u1,[k("div",v1,mt((R=r.stats.knowledge)==null?void 0:R.totalDocuments),1),w[19]||(w[19]=k("div",{class:"text-xs uppercase tracking-wide text-gray-500"}," Documents ",-1))])]),k("div",null,[k("div",{ref_key:"knowledgeChartRef",ref:o,class:"h-64 w-full mb-4"},null,512),k("div",c1,[w[21]||(w[21]=k("span",{class:"text-yellow-400 text-base"},"⭐",-1)),w[22]||(w[22]=k("span",{class:"text-sm text-gray-600 font-medium"},"Most Queried:",-1)),k("span",h1,mt((N=r.stats.knowledge)==null?void 0:N.mostQueriedDoc),1)])])]),k("div",f1,[k("div",p1,[w[23]||(w[23]=k("div",null,[k("h3",{class:"text-xl font-bold text-gray-900 mb-1"}," Storage Usage "),k("p",{class:"text-sm text-gray-600"},"Space utilization breakdown")],-1)),k("div",null,[k("div",{class:io(["px-3 py-1 rounded-xl text-xs font-semibold",h.value>80?"bg-red-100 text-red-600":"bg-green-100 text-green-600"])},mt(h.value)+"% Used ",3)])]),k("div",null,[k("div",{ref_key:"storageChartRef",ref:s,class:"h-64 w-full mb-4"},null,512),k("div",null,[k("div",d1,[k("div",g1,[Ye(mt(p.value)+" ",1),k("span",y1,"/ "+mt(d.value),1)])]),k("div",m1,[k("div",{class:io(["h-full rounded-full transition-all duration-500",h.value>80?"bg-gradient-to-r from-red-600 to-red-400":"bg-gradient-to-r from-green-600 to-green-400"]),style:Eh({width:h.value+"%"})},null,6)]),k("div",S1,mt(g.value)+" remaining ",1)])])])])])]))}}}),w1=Nh(x1,[["__scopeId","data-v-0f4a4c73"]]);export{w1 as default};
