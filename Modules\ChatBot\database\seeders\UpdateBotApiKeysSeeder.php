<?php

namespace Modules\ChatBot\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\ChatBot\Models\Bot;
use Illuminate\Support\Str;

class UpdateBotApiKeysSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Updating API keys for existing bots...');

        $bots = Bot::whereNull('api_key')->get();

        foreach ($bots as $bot) {
            $bot->api_key = 'pk_' . Str::random(48);
            $bot->save();
            
            $this->command->info("Updated API key for bot: {$bot->name} (UUID: {$bot->uuid})");
        }

        $this->command->info("Updated API keys for {$bots->count()} bots.");
    }
}
